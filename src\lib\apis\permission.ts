import { ACCOUNT_ENDPOINTS, ROLE_ENDPOINTS } from "@/constants/endpoints";

import { privateApi } from "../api_helper";
import { Role } from "./types/role";

// Types for permissions and modules
export interface Permission {
  id: string;
  name: string;
  description?: string;
  checked: boolean;
  group?: "CRUD" | "ACTION" | "OTHER";
}

export interface Module {
  id: string;
  name: string;
  permissions: Permission[];
}

// New type for group-based structure
export interface PermissionGroup {
  id: string;
  name: string;
  modules: Module[];
}

// API response types
interface ApiPermission {
  label: string;
  description: string;
  is_enabled: boolean;
  type: string;
  group: string;
  module?: string;
}

export interface ApiPermissionsResponse {
  [key: string]: ApiPermission;
}

// New type for user role permission response
interface UserRolePermissionResponse {
  roles: Role[];
  permissions: ApiPermissionsResponse;
}

// Transform API response to Group-based structure for new tab layout
const transformApiPermissionsToGroups = (
  apiPermissions: ApiPermissionsResponse
): PermissionGroup[] => {
  // First, group permissions by their group
  const groupedPermissions: {
    [group: string]: { [module: string]: { [type: string]: { [key: string]: ApiPermission } } };
  } = {};

  Object.entries(apiPermissions).forEach(([permissionKey, permission]) => {
    const group = permission.group;
    const moduleName = permission.module || "default";
    const type = permission.type;

    if (!groupedPermissions[group]) {
      groupedPermissions[group] = {};
    }
    if (!groupedPermissions[group][moduleName]) {
      groupedPermissions[group][moduleName] = {};
    }
    if (!groupedPermissions[group][moduleName][type]) {
      groupedPermissions[group][moduleName][type] = {};
    }
    groupedPermissions[group][moduleName][type][permissionKey] = permission;
  });
  console.log(groupedPermissions);

  // Convert to Group format - each group contains multiple modules, each module contains multiple types
  return Object.entries(groupedPermissions).map(([groupName, moduleGroups]) => {
    const modules: Module[] = Object.entries(moduleGroups).map(([moduleName, typeGroups]) => {
      const modulePermissions: Permission[] = [];

      // Flatten all permissions from all types within this module
      Object.entries(typeGroups).forEach(([typeName, permissions]) => {
        Object.entries(permissions).forEach(([permissionKey, permission]) => {
          modulePermissions.push({
            id: permissionKey,
            name: permission.label,
            description: permission.description,
            checked: permission.is_enabled,
            group: permission.type as "CRUD" | "ACTION" | "OTHER",
          });
        });
      });

      return {
        id: moduleName.toLowerCase().replace(/_/g, "_"),
        name: moduleName.replace(/_/g, " "),
        permissions: modulePermissions,
      };
    });

    return {
      id: groupName.toLowerCase().replace(/_/g, "_"),
      name: groupName.replace(/_/g, " "),
      modules: modules,
    };
  });
};

type UserRolePermission = {
  roles: Role[];
  permissions: ApiPermissionsResponse;
};

// API function for fetching permissions
export const permissionsApi = {
  list: async (): Promise<PermissionGroup[]> => {
    try {
      const response = await privateApi.get<ApiPermissionsResponse>(
        ACCOUNT_ENDPOINTS.NEW_PERMISSION
      );
      return transformApiPermissionsToGroups(response);
    } catch (error) {
      console.error("Error fetching permissions:", error);
      throw error;
    }
  },
  listUserRolePermission: async (): Promise<ApiPermissionsResponse> => {
    try {
      const response = await privateApi.get<{ data: UserRolePermissionResponse }>(
        ROLE_ENDPOINTS.USER_PERMISSION
      );
      // Extract permissions from the response data
      return response.data.permissions;
    } catch (error) {
      console.error("Error fetching user permissions:", error);
      throw error;
    }
  },
};
