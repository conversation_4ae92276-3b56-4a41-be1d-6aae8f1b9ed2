import { useQuery } from "@tanstack/react-query";

import { subscriptionApi } from "@/lib/apis/crm_plan/subscription";

export const useCurrentSubscription = () => {
  const {
    data: subscription,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["subscription"],
    queryFn: () => subscriptionApi.getCurrent(),
  });
  return {
    subscription: subscription?.data,
    isLoading,
    error,
  };
};
