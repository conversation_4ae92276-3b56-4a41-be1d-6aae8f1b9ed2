"use client";

import { CheckCircle, XCircle } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import { Employee } from "../../types/employee";

interface ToggleStatusDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  employee: Employee | null;
  onSubmit: () => any;
  loading?: boolean;
}

export function ToggleStatusDialog({
  open,
  onOpenChange,
  employee,
  onSubmit,
  loading = false,
}: ToggleStatusDialogProps) {
  const { t } = useTranslation();

  if (!employee) return null;

  const isActive = employee.status === "ENABLED";
  const actionText = isActive
    ? t("pages.employeePermission.deactivateEmployee")
    : t("pages.employeePermission.activateEmployee");

  const description = isActive
    ? t("pages.employeePermission.deactivateEmployeeDescription")
    : t("pages.employeePermission.activateEmployeeDescription");

  const icon = isActive ? (
    <XCircle className="size-6 text-destructive" />
  ) : (
    <CheckCircle className="size-6 text-primary" />
  );

  const buttonVariant = isActive ? "destructive" : "default";
  const buttonText = isActive
    ? t("pages.employeePermission.deactivate")
    : t("pages.employeePermission.activate");

  const handleSubmit = async () => {
    try {
      await onSubmit();
    } catch (error) {
      console.error("Error toggling status:", error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[512px] p-6">
        <DialogHeader className="space-y-2">
          <DialogTitle className="text-lg font-semibold leading-[1.56]">{actionText}</DialogTitle>
          <DialogDescription className="text-sm leading-[1.43] text-muted-foreground">
            {description}
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className="flex gap-2 !space-x-0">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
            className="px-3 py-2 text-sm font-medium">
            {t("common.cancel")}
          </Button>
          <Button
            type="button"
            variant={buttonVariant}
            onClick={handleSubmit}
            loading={loading}
            className="px-3 py-2 text-sm font-medium">
            {buttonText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
