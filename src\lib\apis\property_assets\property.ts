import { ENDPOINTS } from "@/constants/endpoints";

import { privateApi } from "../../api_helper";
import { ResponseAxiosDetail, ResponseList } from "../types/common";
import type { CreateProperty, Property, UpdateProperty } from "../types/property_assets/property";

// Property APIs
export const propertyApi = {
  // Get all properties with optional filtering and pagination
  list: async (params?: Record<string, unknown>) => {
    return await privateApi.get<ResponseList<Property>>(ENDPOINTS.PROPERTY_ASSETS.PROPERTIES, {
      params,
    });
  },

  // Create a new property
  create: async (data: CreateProperty) => {
    return await privateApi.post<ResponseAxiosDetail<Property>>(
      ENDPOINTS.PROPERTY_ASSETS.CREATE_PROPERTY,
      data
    );
  },

  // Update an existing property
  update: async (id: string, data: UpdateProperty) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.UPDATE_PROPERTY.replace(":id", id);
    return await privateApi.put<ResponseAxiosDetail<Property>>(url, data);
  },

  // Delete a property
  delete: async (id: string) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.DELETE_PROPERTY.replace(":id", id);
    return await privateApi.delete<{ success: boolean; message?: string }>(url);
  },

  // Get property by ID
  getById: async (id: string) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.GET_PROPERTY.replace(":id", id);
    return await privateApi.get<ResponseAxiosDetail<Property>>(url);
  },

  // Get properties by type
  getByType: async (type: string, params?: Record<string, unknown>) => {
    return await privateApi.get<ResponseList<Property>>(ENDPOINTS.PROPERTY_ASSETS.PROPERTIES, {
      params: { ...params, type },
    });
  },

  // Get properties by status
  getByStatus: async (status: string, params?: Record<string, unknown>) => {
    return await privateApi.get<ResponseList<Property>>(ENDPOINTS.PROPERTY_ASSETS.PROPERTIES, {
      params: { ...params, status },
    });
  },

  // Search properties by address
  searchByAddress: async (addressQuery: string, params?: Record<string, unknown>) => {
    return await privateApi.get<ResponseList<Property>>(ENDPOINTS.PROPERTY_ASSETS.PROPERTIES, {
      params: { ...params, search: addressQuery },
    });
  },
};
