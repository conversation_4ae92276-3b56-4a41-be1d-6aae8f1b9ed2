"use client";

import { useState } from "react";
import {
  Calendar,
  Download,
  Edit,
  Eye,
  FileText,
  Filter,
  MoreHorizontal,
  Search,
  TrendingDown,
  TrendingUp,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface ValuationHistoryProps {
  propertyId?: string;
  className?: string;
}

export function ValuationHistory({ propertyId, className }: ValuationHistoryProps) {
  const { t } = useTranslation();
  const [selectedProperty, setSelectedProperty] = useState<string>(propertyId || "all");
  const [selectedPeriod, setSelectedPeriod] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");

  // Mock valuation history data
  const valuationRecords = [
    {
      id: "1",
      propertyName: "Sunset Apartments",
      date: "2024-01-15",
      value: 2850000,
      previousValue: 2650000,
      change: 200000,
      changePercent: 7.5,
      method: "Professional Appraisal",
      appraiser: "Metro Property Valuers",
      reason: "Annual Revaluation",
      notes: "Market conditions improved significantly",
      confidence: 95,
      status: "Approved",
    },
    {
      id: "2",
      propertyName: "Sunset Apartments",
      date: "2023-07-20",
      value: 2650000,
      previousValue: 2350000,
      change: 300000,
      changePercent: 12.8,
      method: "Comparative Market Analysis",
      appraiser: "Internal Assessment",
      reason: "Market Review",
      notes: "Comparable sales showing strong growth",
      confidence: 88,
      status: "Approved",
    },
    {
      id: "3",
      propertyName: "Downtown Lofts",
      date: "2023-11-20",
      value: 1950000,
      previousValue: 1850000,
      change: 100000,
      changePercent: 5.4,
      method: "Income Approach",
      appraiser: "City Real Estate Appraisers",
      reason: "Refinancing Requirement",
      notes: "Rental income increased due to market trends",
      confidence: 92,
      status: "Approved",
    },
    {
      id: "4",
      propertyName: "Garden View Complex",
      date: "2023-09-10",
      value: 1450000,
      previousValue: 1380000,
      change: 70000,
      changePercent: 5.1,
      method: "Cost Approach",
      appraiser: "Property Assessment Co.",
      reason: "Insurance Valuation",
      notes: "Recent renovations added value",
      confidence: 85,
      status: "Under Review",
    },
    {
      id: "5",
      propertyName: "Downtown Lofts",
      date: "2023-05-15",
      value: 1850000,
      previousValue: 1750000,
      change: 100000,
      changePercent: 5.7,
      method: "Hybrid Approach",
      appraiser: "Metro Property Valuers",
      reason: "Portfolio Review",
      notes: "Strong rental market performance",
      confidence: 90,
      status: "Approved",
    },
  ];

  // Generate trend data for selected property
  const generateTrendData = () => {
    if (selectedProperty === "all") {
      // Aggregate data for all properties
      const aggregated = valuationRecords.reduce((acc, record) => {
        const existing = acc.find((item) => item.date === record.date);
        if (existing) {
          existing.totalValue += record.value;
          existing.count += 1;
        } else {
          acc.push({
            date: record.date,
            totalValue: record.value,
            count: 1,
            avgValue: record.value,
          });
        }
        return acc;
      }, [] as any[]);

      return aggregated
        .map((item) => ({
          ...item,
          avgValue: item.totalValue / item.count,
        }))
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    } else {
      return valuationRecords
        .filter(
          (record) =>
            selectedProperty === "all" || record.propertyName === getPropertyName(selectedProperty)
        )
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    }
  };

  const getPropertyName = (propertyId: string) => {
    const propertyMap = {
      "1": "Sunset Apartments",
      "2": "Downtown Lofts",
      "3": "Garden View Complex",
    };
    return propertyMap[propertyId as keyof typeof propertyMap] || "";
  };

  const filteredRecords = valuationRecords.filter((record) => {
    const matchesProperty =
      selectedProperty === "all" || record.propertyName === getPropertyName(selectedProperty);
    const matchesSearch =
      searchTerm === "" ||
      record.propertyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.appraiser.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.method.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesProperty && matchesSearch;
  });

  const trendData = generateTrendData();

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Approved":
        return "bg-success/10 text-success border-success/20";
      case "Under Review":
        return "bg-warning/10 text-warning border-warning/20";
      case "Rejected":
        return "bg-destructive/10 text-destructive border-destructive/20";
      default:
        return "bg-muted/50 text-muted-foreground border-muted/20";
    }
  };

  const getMethodColor = (method: string) => {
    switch (method) {
      case "Professional Appraisal":
        return "bg-primary/10 text-primary";
      case "Comparative Market Analysis":
        return "bg-secondary/10 text-secondary-foreground";
      case "Income Approach":
        return "bg-success/10 text-success";
      case "Cost Approach":
        return "bg-warning/10 text-warning";
      default:
        return "bg-muted/50 text-muted-foreground";
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Filters */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h3 className="text-lg font-semibold text-foreground">Valuation History</h3>
          <p className="text-sm text-muted-foreground">
            Complete history of property valuations and assessments
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search valuations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64 pl-10"
            />
          </div>
          <Select value={selectedProperty} onValueChange={setSelectedProperty}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="All Properties" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Properties</SelectItem>
              <SelectItem value="1">Sunset Apartments</SelectItem>
              <SelectItem value="2">Downtown Lofts</SelectItem>
              <SelectItem value="3">Garden View Complex</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Time</SelectItem>
              <SelectItem value="year">This Year</SelectItem>
              <SelectItem value="6months">6 Months</SelectItem>
              <SelectItem value="3months">3 Months</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="mr-2 size-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Valuation Trend Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="size-5" />
            <span>Valuation Trend</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={trendData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="date"
                tickFormatter={(value) => new Date(value).toLocaleDateString()}
              />
              <YAxis tickFormatter={(value) => `$${(value / 1000000).toFixed(1)}M`} />
              <Tooltip
                formatter={(value: any) => [`$${value.toLocaleString()}`, "Value"]}
                labelFormatter={(value) => new Date(value).toLocaleDateString()}
              />
              <Legend />
              <Line
                type="monotone"
                dataKey={selectedProperty === "all" ? "avgValue" : "value"}
                stroke="#8884d8"
                strokeWidth={3}
                dot={{ fill: "#8884d8", strokeWidth: 2, r: 5 }}
                name={selectedProperty === "all" ? "Average Value" : "Property Value"}
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Valuation Records Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="size-5" />
            <span>Valuation Records</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Property</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Value</TableHead>
                  <TableHead>Change</TableHead>
                  <TableHead>Method</TableHead>
                  <TableHead>Appraiser</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Confidence</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRecords.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell className="font-medium">{record.propertyName}</TableCell>
                    <TableCell>{new Date(record.date).toLocaleDateString()}</TableCell>
                    <TableCell className="font-semibold">
                      ${record.value.toLocaleString()}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        {record.change > 0 ? (
                          <TrendingUp className="size-3 text-success" />
                        ) : record.change < 0 ? (
                          <TrendingDown className="size-3 text-destructive" />
                        ) : null}
                        <span
                          className={
                            record.change > 0
                              ? "text-success"
                              : record.change < 0
                                ? "text-destructive"
                                : "text-muted-foreground"
                          }>
                          {record.change > 0 ? "+" : ""}${record.change.toLocaleString()}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          ({record.changePercent > 0 ? "+" : ""}
                          {record.changePercent}%)
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className={getMethodColor(record.method)}>
                        {record.method}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-sm">{record.appraiser}</TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(record.status)}>{record.status}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <div className="h-2 w-12 rounded-full bg-muted/30">
                          <div
                            className="h-2 rounded-full bg-primary"
                            style={{ width: `${record.confidence}%` }}></div>
                        </div>
                        <span className="text-xs">{record.confidence}%</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="size-8 p-0">
                            <MoreHorizontal className="size-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Eye className="mr-2 size-4" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Edit className="mr-2 size-4" />
                            Edit Notes
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Download className="mr-2 size-4" />
                            Download Report
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Total Valuations</p>
              <p className="text-2xl font-bold">{filteredRecords.length}</p>
              <p className="text-xs text-muted-foreground">
                {filteredRecords.filter((r) => r.status === "Approved").length} approved
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Average Change</p>
              <p className="text-2xl font-bold text-success">
                +
                {(
                  filteredRecords.reduce((sum, r) => sum + r.changePercent, 0) /
                  filteredRecords.length
                ).toFixed(1)}
                %
              </p>
              <p className="text-xs text-muted-foreground">Per valuation period</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Avg Confidence</p>
              <p className="text-2xl font-bold">
                {Math.round(
                  filteredRecords.reduce((sum, r) => sum + r.confidence, 0) / filteredRecords.length
                )}
                %
              </p>
              <p className="text-xs text-muted-foreground">Assessment reliability</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
