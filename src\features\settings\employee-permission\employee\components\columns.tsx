import { useCallback, useState } from "react";
import { Row } from "@tanstack/react-table";
import { CheckCircle, CircleX, Pencil } from "lucide-react";
import { toast } from "sonner";

import { DateColumn, TextColumn } from "@/components/custom-table/container/common-column";
import ActionGroup from "@/components/data-table/action-group";
import { CustomColumn } from "@/components/data-table/data-table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui";
import { StatusBadge } from "@/components/ui/status-badge";
import { getSimpleUserFromLocalStorage } from "@/lib/apis/users";

import { useResetPassword } from "../hooks/use-employee";
import { Employee } from "../types/employee";
import { AddEmployeeDialog } from "./add-employee-dialog";
// Import the dialogs
import { ResetPasswordDialog } from "./dialogs/reset-password-dialog";
import { ToggleStatusDialog } from "./dialogs/toggle-status-dialog";

export const columns = (
  t: any,
  mutations: {
    toggleEmployeeStatusMutation: any;
    deleteEmployeeMutation: any;
  }
): CustomColumn<Employee>[] => [
  {
    id: "employee_info",
    accessorKey: "user_name",
    header: t("pages.settings.employeeInfo"),
    isMainColumn: true,
    cell: ({ row }: { row: Row<Employee> }) => {
      const employee = row.original;
      return (
        <div className="flex min-w-0 items-center gap-3 truncate">
          <Avatar className="overflow-hidden rounded-lg">
            <AvatarImage className="overflow-hidden rounded-lg" src={employee.picture || ""} />
            <AvatarFallback className="overflow-hidden rounded-lg">
              {employee.name.charAt(0).toUpperCase() || employee.username.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className="flex min-w-0 flex-col truncate">
            <span className="truncate font-medium text-foreground">
              {employee.name || employee.username}
            </span>
            {employee.email && (
              <span className="truncate text-sm text-muted-foreground">{employee.email}</span>
            )}
          </div>
        </div>
      );
    },
  },
  {
    id: "username",
    accessorKey: "user_name",
    header: t("pages.settings.username"),
    cell: ({ row }: { row: Row<Employee> }) => (
      <div className="w-full">
        <TextColumn text={row.original.username || "-"} className="font-medium text-primary" />
      </div>
    ),
  },
  {
    id: "role",
    accessorKey: "role",
    header: t("pages.staff.role"),
    cell: ({ row }: { row: Row<Employee> }) => {
      const role_list = row.original.role_list || [];
      const role_name = role_list.map((role: any) => role.name).join(", ");
      return (
        <div className="w-full">
          <TextColumn text={role_name || "---"} />
        </div>
      );
    },
  },
  {
    id: "status",
    accessorKey: "status",
    header: t("common.status.status"),
    cell: ({ row }: { row: Row<Employee> }) => (
      <div className="w-full text-center">
        <StatusBadge status={row.original.status.toLowerCase()}>
          {t(`common.status.${row.original.status.toLowerCase()}`)}
        </StatusBadge>
      </div>
    ),
  },
  {
    id: "updated_at",
    sorter: true,
    accessorKey: "updated_at",
    header: t("common.updatedAt"),
    cell: ({ row }: { row: Row<Employee> }) => (
      <div className="w-full text-center">
        {row.original.updated_at ? <DateColumn date={row.original.updated_at} /> : "-"}
      </div>
    ),
  },
  {
    id: "actions",
    header: t("common.actions"),
    cell: ({ row }: { row: Row<Employee> }) => <ActionCell row={row} t={t} mutations={mutations} />,
  },
];

const ActionCell = ({
  row,
  t,
  mutations,
}: {
  row: Row<Employee>;
  t: any;
  mutations: {
    toggleEmployeeStatusMutation: any;
    deleteEmployeeMutation: any;
  };
}) => {
  const employee = row.original;
  const { toggleEmployeeStatusMutation, deleteEmployeeMutation } = mutations;
  // Hooks
  const currentUser = getSimpleUserFromLocalStorage();

  const resetPasswordMutation = useResetPassword();

  // Dialog states
  const [showResetPassword, setShowResetPassword] = useState(false);
  const [showToggleStatus, setShowToggleStatus] = useState(false);
  const [showViewEmployee, setShowViewEmployee] = useState(false);

  const handleView = useCallback(() => {
    setShowViewEmployee(true);
  }, []);

  const handleResetPassword = useCallback(() => {
    setShowResetPassword(true);
  }, []);

  const handleToggleStatus = useCallback(() => {
    setShowToggleStatus(true);
  }, []);

  const handleResetPasswordSubmit = async (data: any) => {
    try {
      if (!employee.username) return;
      await resetPasswordMutation.mutateAsync({
        username: employee.username,
        password: data.password,
      });
      setShowResetPassword(false);
      toast.success(t("pages.employeePermission.passwordResetSuccess"));
    } catch (error) {
      console.error("Error resetting password:", error);
      toast.error(t("pages.employeePermission.passwordResetError"));
    }
  };

  const handleToggleStatusSubmit = async () => {
    try {
      if (!employee.id) return;
      await toggleEmployeeStatusMutation.mutateAsync({
        userId: employee.id,
        enabled: employee.status === "ENABLED",
      });
      const action = employee.status === "ENABLED" ? "deactivated" : "activated";
      toast.success(
        t(
          `pages.employeePermission.employee${action.charAt(0).toUpperCase() + action.slice(1)}Success`
        )
      );
    } catch (error) {
      toast.error(t("pages.employeePermission.toggleStatusError"));
    } finally {
      setShowToggleStatus(false);
    }
  };

  const handleDeleteSubmit = () => {
    deleteEmployeeMutation.mutate(employee.id);
  };
  if (currentUser.id === employee.id) {
    return null;
  }
  return (
    <>
      <ActionGroup
        actions={[
          {
            type: "view",
            onClick: handleView,
          },
          {
            type: "resetPassword",
            title: t("common.resetPassword"),
            customIcon: <Pencil className="size-4" />,
            onClick: handleResetPassword,
          },
          {
            type: employee.status === "ENABLED" ? "deactivate" : "activate",
            title:
              employee.status === "ENABLED"
                ? t("pages.employeePermission.deactivate")
                : t("pages.employeePermission.activate"),
            customIcon:
              employee.status === "ENABLED" ? (
                <CircleX className="size-4" />
              ) : (
                <CheckCircle className="size-4" />
              ),
            onClick: handleToggleStatus,
          },
          {
            type: "delete",
            loading: deleteEmployeeMutation.isPending,
            onClick: handleDeleteSubmit,
            confirmationDescription: t("pages.employeePermission.deleteEmployeeDescription"),
          },
        ]}
      />

      {/* Reset Password Dialog */}
      <ResetPasswordDialog
        loading={resetPasswordMutation.isLoading}
        open={showResetPassword}
        onOpenChange={setShowResetPassword}
        employee={employee}
        onSubmit={handleResetPasswordSubmit}
      />

      {/* Toggle Status Dialog */}
      <ToggleStatusDialog
        loading={toggleEmployeeStatusMutation.isPending}
        open={showToggleStatus}
        onOpenChange={setShowToggleStatus}
        employee={employee}
        onSubmit={handleToggleStatusSubmit}
      />

      {/* View Employee Dialog */}
      <AddEmployeeDialog
        open={showViewEmployee}
        onOpenChange={setShowViewEmployee}
        employee_id={employee.id}
        mode="edit"
        loading={false}
      />
    </>
  );
};
