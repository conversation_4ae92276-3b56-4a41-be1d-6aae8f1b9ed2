import {
  InfiniteData,
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

import { propertyApi } from "@/lib/apis/property_assets/property";
import { ResponseList } from "@/lib/apis/types/common";
import type { CreateProperty, Property } from "@/lib/apis/types/property_assets/property";

import { IGetPropertiesParams, propertyKeys } from "./keys";

interface UsePropertiesOptions extends Partial<IGetPropertiesParams> {
  enabled?: boolean;
}

export function useProperties(options: UsePropertiesOptions = {}) {
  const { limit = 20, enabled = true, ...restOptions } = options;
  const queryClient = useQueryClient();

  const query = useInfiniteQuery({
    queryKey: propertyKeys.list({ limit, ...restOptions }),
    queryFn: ({ pageParam = 0 }) =>
      propertyApi.list({
        page: pageParam as number,
        limit,
        ...restOptions,
      }),
    getNextPageParam: (lastPage) => {
      const totalPages = Math.ceil(lastPage.total / Number(lastPage.limit));
      if (lastPage.page < totalPages) {
        return lastPage.page + 1;
      }
      return undefined;
    },
    initialPageParam: 0,
    enabled,
  });

  const properties = query.data?.pages.flatMap((page) => page.items) ?? [];
  const total = query.data?.pages[0]?.total ?? 0;

  const useDeletePropertyMutation = useMutation({
    mutationFn: async (id: string) => {
      return propertyApi.delete(id);
    },
    onSuccess: (_, deletedId) => {
      queryClient.setQueryData<InfiniteData<ResponseList<Property>>>(
        propertyKeys.list({ limit, ...restOptions }),
        (oldData) => {
          if (!oldData) return oldData;
          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.filter((item) => item.id !== deletedId),
            total: page.total - 1,
          }));
          return {
            ...oldData,
            pages: newPages,
          };
        }
      );
      toast.success("Property deleted successfully");
    },
    onError: () => {
      toast.error("Property deletion failed");
    },
  });

  return {
    ...query,
    properties,
    total,
    useDeletePropertyMutation,
  };
}

interface UseAddPropertyOptions {
  onSuccess?: (data: Property) => void;
  onError?: (error: Error) => void;
}

export function useAddProperty(options: UseAddPropertyOptions = {}) {
  const { onSuccess, onError } = options;
  const queryClient = useQueryClient();

  return useMutation<Property, Error, CreateProperty>({
    mutationFn: async (data: CreateProperty) => {
      const response = await propertyApi.create(data);
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: propertyKeys.lists() });
      onSuccess?.(data);
    },
    onError,
  });
}

interface UpdatePropertyPayload {
  id: string;
  data: Partial<import("@/lib/apis/types/property_assets/property").UpdateProperty>;
}

interface UseUpdatePropertyOptions {
  onSuccess?: (data: Property) => void;
  onError?: (error: Error) => void;
}

export function useUpdateProperty(options: UseUpdatePropertyOptions = {}) {
  const { onSuccess, onError } = options;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: UpdatePropertyPayload) => {
      const response = await propertyApi.update(id, data);
      return response.data;
    },
    onSuccess: (updatedProperty) => {
      queryClient.setQueryData<InfiniteData<ResponseList<Property>>>(
        propertyKeys.lists(),
        (oldData) => {
          if (!oldData) return oldData;
          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.map((item) =>
              item.id === updatedProperty.id ? updatedProperty : item
            ),
          }));
          return { ...oldData, pages: newPages };
        }
      );
      queryClient.setQueryData(propertyKeys.detail(updatedProperty.id), updatedProperty);
      onSuccess?.(updatedProperty);
    },
    onError,
  });
}

export function useProperty(id: string) {
  return useQuery<Property>({
    queryKey: propertyKeys.detail(id),
    queryFn: async () => {
      const response = await propertyApi.getById(id);
      return response.data;
    },
    enabled: !!id,
  });
}
