import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";
import { z } from "zod";

import { useZodForm } from "@/components/ui/form";
import { authProtectedPaths } from "@/constants/paths";
import type { CreateUnit, Unit, UpdateUnit } from "@/lib/apis/types/property_assets/property_unit";

import { useCreateUnit, useUpdateUnit } from "../hooks/use-units";

// Validation schema
const unitFormSchema = z.object({
  property_id: z.string().min(1, "Property is required"),
  unit_number: z.string().min(1, "Unit number is required"),
  type: z.enum(["studio", "1br", "2br", "3br", "commercial"]),
  description: z.string().optional(),
  floor_number: z.number().min(1, "Floor number must be at least 1"),
  square_footage: z.number().min(0, "Square footage must be non-negative"),
  bedrooms: z.number().min(0, "Bedrooms must be non-negative"),
  bathroom: z.number().min(1, "At least one bathroom is required"),
  amount: z.object({
    rent: z.number().min(0, "Rent amount must be non-negative"),
    deposit: z.number().min(0, "Deposit amount must be non-negative"),
  }),
  status: z.enum(["AVAILABLE", "OCCUPIED", "MAINTENANCE", "RESERVED"]),
  amenities: z.array(z.string()).default([]),
  images: z.array(z.string()).default([]),
});

type UnitFormData = z.infer<typeof unitFormSchema>;

interface UseUnitFormProps {
  initialData?: Unit;
  isEditing?: boolean;
}

export const useUnitForm = ({ initialData, isEditing = false }: UseUnitFormProps) => {
  const { t } = useTranslation();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Mutations
  const createUnitMutation = useCreateUnit();
  const updateUnitMutation = useUpdateUnit();

  // Form setup
  const form = useZodForm({
    schema: unitFormSchema,
    defaultValues:
      isEditing && initialData
        ? {
            property_id: initialData.property_id,
            unit_number: initialData.unit_number,
            type: initialData.type as "studio" | "1br" | "2br" | "3br" | "commercial",
            description: initialData.description,
            floor_number: initialData.floor_number,
            square_footage: initialData.square_footage,
            bedrooms: initialData.bedrooms,
            bathroom: initialData.bathroom,
            amount: {
              rent: initialData.amount?.rent || 0,
              deposit: initialData.amount?.deposit || 0,
            },
            status: initialData.status as "AVAILABLE" | "OCCUPIED" | "MAINTENANCE" | "RESERVED",
            amenities: initialData.amenities || [],
            images: initialData.images || [],
          }
        : {
            property_id: "",
            unit_number: "",
            type: "1br" as const,
            description: "",
            floor_number: 1,
            square_footage: 0,
            bedrooms: 0,
            bathroom: 1,
            amount: {
              rent: 0,
              deposit: 0,
            },
            status: "AVAILABLE" as const,
            amenities: [],
            images: [],
          },
  });

  // Form submission
  const onSubmit = async (values: UnitFormData) => {
    setIsSubmitting(true);

    try {
      if (isEditing && initialData?.id) {
        await updateUnitMutation.mutateAsync({
          id: initialData.id,
          data: values as UpdateUnit,
        });
        toast.success(t("pages.units.updateSuccess") || "Unit updated successfully");
      } else {
        await createUnitMutation.mutateAsync(values as CreateUnit);
        toast.success(t("pages.units.createSuccess") || "Unit created successfully");
      }

      router.push(authProtectedPaths.UNITS as any);
    } catch (error) {
      console.error("Unit form submission error:", error);
      toast.error(
        isEditing
          ? t("pages.units.updateError") || "Failed to update unit"
          : t("pages.units.createError") || "Failed to create unit"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Check if form is valid for submission
  const isFormValid = () => {
    const values = form.getValues();
    const errors = form.formState.errors;

    if (isEditing) {
      // For edit mode: check if form is dirty and all required fields are valid
      const isDirty = form.formState.isDirty;
      const hasErrors = Object.keys(errors).length > 0;
      return isDirty && !hasErrors;
    } else {
      // For add mode: check if all required fields are filled and valid
      const requiredFields = ["property_id", "unit_number", "type", "floor_number", "bathroom"];

      const hasRequiredFields = requiredFields.every((field) => {
        const value = values[field as keyof UnitFormData];
        if (field === "property_id" || field === "unit_number") {
          return typeof value === "string" && value.trim().length > 0;
        }
        if (field === "floor_number" || field === "bathroom") {
          return typeof value === "number" && value > 0;
        }
        return value !== undefined && value !== null;
      });

      const hasErrors = Object.keys(errors).length > 0;
      return hasRequiredFields && !hasErrors;
    }
  };

  // Watch form values for reactive validation
  const watchedValues = form.watch();
  const formErrors = form.formState.errors;
  const isDirty = form.formState.isDirty;

  // Reactive form validation
  const isFormValidReactive = (() => {
    if (isEditing) {
      // For edit mode: check if form is dirty and all required fields are valid
      const hasErrors = Object.keys(formErrors).length > 0;
      return isDirty && !hasErrors;
    } else {
      // For add mode: check if all required fields are filled and valid
      const requiredFields = ["property_id", "unit_number", "type", "floor_number", "bathroom"];

      const hasRequiredFields = requiredFields.every((field) => {
        const value = watchedValues[field as keyof UnitFormData];
        if (field === "property_id" || field === "unit_number") {
          return typeof value === "string" && value.trim().length > 0;
        }
        if (field === "floor_number" || field === "bathroom") {
          return typeof value === "number" && value > 0;
        }
        return value !== undefined && value !== null;
      });

      const hasErrors = Object.keys(formErrors).length > 0;
      return hasRequiredFields && !hasErrors;
    }
  })();

  return {
    form,
    isSubmitting,
    onSubmit,
    defaultValues: form.getValues(),
    isFormValid: isFormValidReactive,
  };
};
