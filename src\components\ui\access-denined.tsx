import { AlertTriangle } from "lucide-react";
import { useTranslation } from "react-i18next";

export default function AccessDenied({ deniedMessage }: { deniedMessage?: string }) {
  const { t } = useTranslation();
  return (
    <div className="flex flex-col items-center justify-center space-y-3 py-8">
      <div className="flex size-16 items-center justify-center rounded-full bg-red-50">
        <AlertTriangle className="size-8 text-destructive" />
      </div>
      <div className="text-center">
        <h3 className="text-lg font-semibold text-destructive">{t("common.accessDenied")}</h3>
        <p className="mt-1 max-w-md text-sm text-muted-foreground">
          {deniedMessage || t("common.noPermission")}
        </p>
      </div>
      {/* <Button variant="outline" size="sm" onClick={onRequestAccess}>
            {t("common.requestAccess")}
          </Button> */}
    </div>
  );
}
