"use client";

import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import type { Permission, PermissionGroup } from "@/lib/apis/permission";

// Import components
import {
  AddRoleDialogSkeleton,
  DialogFooterComponent,
  DialogHeaderComponent,
  PermissionsSection,
  RoleForm,
} from "./components";
// Import role hook
import { useRole } from "./hooks/use-role";
// Import types and API functions from separate file
import {
  getDefaultActiveTab,
  getDefaultExpandedModules,
  toggleGroupPermissions,
  toggleModulePermissions,
  usePermissions,
} from "./permissions";

interface AddRoleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  sourceRoleId: string | null;
  mode: "add" | "edit" | "duplicate";
  createRoleMutation: any;
  updateRoleMutation: any;
}

export default function AddRoleDialog({
  open,
  onOpenChange,
  sourceRoleId,
  mode,
  createRoleMutation,
  updateRoleMutation,
}: AddRoleDialogProps) {
  const { t } = useTranslation();
  const { data: groups = [], isLoading: permissionsLoading, error } = usePermissions();

  // Use the useRole hook to fetch role data (only when editing or duplicating)
  const {
    data: sourceRole,
    isLoading: isLoadingRole,
    error: roleError,
  } = useRole(sourceRoleId && mode !== "add" ? sourceRoleId : "");

  const [activeTab, setActiveTab] = useState("");
  const [roleName, setRoleName] = useState("");
  const [note, setNote] = useState("");
  const [localGroups, setLocalGroups] = useState<PermissionGroup[]>([]);
  const [expandedModules, setExpandedModules] = useState<Set<string>>(new Set());

  // Reset form when dialog opens
  useEffect(() => {
    if (open && mode === "add") {
      // Reset form for add mode
      setRoleName("");
      setNote("");
      setExpandedModules(new Set());
    }
  }, [open, mode]);

  // Update local state when groups are fetched or when editing/duplicating
  useEffect(() => {
    if (groups.length > 0) {
      setLocalGroups(groups);
      setActiveTab(getDefaultActiveTab(groups));
      setExpandedModules(getDefaultExpandedModules(groups));
    }
  }, [groups]);

  // Update form when editing or duplicating a role
  useEffect(() => {
    if (sourceRole && groups.length > 0 && (mode === "edit" || mode === "duplicate")) {
      setRoleName(mode === "duplicate" ? `${sourceRole.name} (Copy)` : sourceRole.name);
      setNote(sourceRole.note || "");

      // Set permissions based on the role being edited/duplicated
      const updatedGroups = groups.map((group) => ({
        ...group,
        modules: group.modules.map((module) => ({
          ...module,
          permissions: module.permissions.map((permission) => ({
            ...permission,
            checked: sourceRole.permissions?.[permission.id]?.is_enabled || false,
          })),
        })),
      }));
      setLocalGroups(updatedGroups);
    }
  }, [sourceRole, groups, mode]);

  // Handle permission toggle
  const handlePermissionToggle = (groupId: string, moduleId: string, permissionId: string) => {
    setLocalGroups((prevGroups) =>
      prevGroups.map((group) =>
        group.id === groupId
          ? {
              ...group,
              modules: group.modules.map((module) =>
                module.id === moduleId
                  ? {
                      ...module,
                      permissions: module.permissions.map((permission: Permission) =>
                        permission.id === permissionId
                          ? { ...permission, checked: !permission.checked }
                          : permission
                      ),
                    }
                  : module
              ),
            }
          : group
      )
    );
  };

  // Handle module-level permission toggle (select all permissions in module)
  const handleModulePermissionToggle = (groupId: string, moduleId: string) => {
    setLocalGroups((prevGroups) => toggleModulePermissions(prevGroups, groupId, moduleId));
  };

  // Handle group permission toggle (CRUD, Action)
  const handleGroupPermissionToggle = (
    groupId: string,
    moduleId: string,
    typeName: "CRUD" | "ACTION"
  ) => {
    setLocalGroups((prevGroups) => toggleGroupPermissions(prevGroups, groupId, moduleId, typeName));
  };

  // Handle module expansion toggle
  const toggleModuleExpansion = (moduleKey: string) => {
    setExpandedModules((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(moduleKey)) {
        newSet.delete(moduleKey);
      } else {
        newSet.add(moduleKey);
      }
      return newSet;
    });
  };

  // Check if form is valid
  const isFormValid = roleName.trim().length > 0;

  // Handle form submission
  const handleSubmit = () => {
    if (!isFormValid) return;

    const selectedPermissions = localGroups
      .flatMap((group) => group.modules)
      .flatMap((module) => module.permissions)
      .reduce(
        (acc, permission) => {
          acc[permission.id] = permission.checked;
          return acc;
        },
        {} as Record<string, boolean>
      );

    if (mode === "edit" && sourceRole) {
      // Update existing role
      updateRoleMutation.mutate({
        id: sourceRole.id,
        data: {
          name: roleName,
          note: note,
          permissions: selectedPermissions,
        },
      });
    } else {
      // Create new role (either from scratch or duplicated)
      createRoleMutation.mutate({
        name: roleName,
        note: note,
        permissions: selectedPermissions,
      });
    }
  };

  // Handle dialog close
  const handleClose = () => {
    setRoleName("");
    setNote("");
    setLocalGroups(groups);
    setExpandedModules(getDefaultExpandedModules(groups));
    setActiveTab(getDefaultActiveTab(groups));
    // setSourceRole(null); // This line is removed as per the new_code
    onOpenChange(false);
  };

  const isLoading = createRoleMutation.isPending || updateRoleMutation.isPending || isLoadingRole;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="flex max-h-[90vh] max-w-4xl flex-col overflow-y-hidden px-0 pb-0">
        {/* Header */}
        <DialogHeaderComponent
          title={
            mode === "edit"
              ? `${t("common.edit")} ${t("pages.employeePermission.role")}`
              : mode === "duplicate"
                ? `${t("common.duplicate")} ${t("pages.employeePermission.role")}`
                : `${t("common.add")} ${t("pages.employeePermission.role")}`
          }
        />

        {/* Content */}
        {isLoadingRole ? (
          <AddRoleDialogSkeleton />
        ) : (
          <div className="flex flex-auto flex-col gap-6 overflow-auto px-6 py-2">
            {/* Input Fields */}
            <RoleForm
              roleName={roleName}
              note={note}
              onRoleNameChange={setRoleName}
              onNoteChange={setNote}
            />

            <Separator />

            {/* Permissions Section */}
            <PermissionsSection
              isLoading={permissionsLoading || isLoadingRole}
              error={error || roleError}
              groups={localGroups}
              activeTab={activeTab}
              expandedModules={expandedModules}
              onTabChange={setActiveTab}
              onToggleModuleExpansion={toggleModuleExpansion}
              onToggleModulePermissions={handleModulePermissionToggle}
              onToggleGroup={handleGroupPermissionToggle}
              onTogglePermission={handlePermissionToggle}
            />
          </div>
        )}

        <DialogFooterComponent
          loading={isLoading}
          onCancel={handleClose}
          onSubmit={handleSubmit}
          isSubmitDisabled={!isFormValid || isLoading}
          submitText={mode === "edit" ? t("common.save") : t("common.create")}
        />
      </DialogContent>
    </Dialog>
  );
}
