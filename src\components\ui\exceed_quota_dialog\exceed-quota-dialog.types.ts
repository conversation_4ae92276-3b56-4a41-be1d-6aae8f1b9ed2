export interface ExceedQuotaDialogProps {
  /** Controls whether the dialog is open */
  open: boolean;
  /** Callback when dialog open state changes */
  onOpenChange: (open: boolean) => void;
  /** Current subscription plan name */
  currentPlan?: string;
  /** Expiration date of the current plan */
  expiresOn?: string;
  /** Current usage information */
  usage?: string | { used: string; max: string; quotaKey: string };
  quotaKey?: string;
  /** Callback when upgrade button is clicked */
  onUpgrade?: () => void;
  /** Callback when "see all plans" button is clicked */
  onSeeAllPlans?: () => void;
  /** Callback when dialog is closed programmatically */
  onClose?: () => void;
}

export interface UseExceedQuotaDialogProps {
  currentPlan?: string;
  expiresOn?: string;
  usage?: string | { used: string; max: string; quotaKey: string };
  onUpgrade?: () => void;
  onSeeAllPlans?: () => void;
  onClose?: () => void;
}

export interface UseExceedQuotaDialogReturn {
  isOpen: boolean;
  openDialog: () => void;
  closeDialog: () => void;
  dialogProps: ExceedQuotaDialogProps;
}
