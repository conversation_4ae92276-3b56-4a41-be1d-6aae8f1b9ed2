"use client";

import { useState } from "react";
import { ImageIcon } from "lucide-react";
import { useTranslation } from "react-i18next";

import { CustomImage } from "@/components/ui/image";
import { Property } from "@/lib/apis/types/property_assets/property";

interface PropertyImagesProps {
  property: Property;
}

export function PropertyImages({ property }: PropertyImagesProps) {
  const { t } = useTranslation();
  const images = property.images || [];
  const [activeIndex, setActiveIndex] = useState<number>(0);

  const hasImages = images.length > 0;
  const mainImage = hasImages ? images[Math.min(activeIndex, images.length - 1)] : null;
  return (
    <div>
      <div className="flex h-full gap-3 overflow-hidden">
        {/* Main Image */}
        <div className="relative aspect-video h-full w-0 flex-[5] rounded-lg bg-muted">
          {mainImage ? (
            <CustomImage
              src={mainImage?.url}
              alt={mainImage.name || t("pages.properties.components.propertyImages.propertyImage")}
              fill
              className="aspect-video rounded-lg object-cover"
              sizes="(max-width: 1024px) 100vw, 75vw"
            />
          ) : (
            <div className="flex size-full items-center justify-center">
              <ImageIcon className="size-16 text-muted-foreground" />
            </div>
          )}
        </div>

        {/* Thumbnails (click to set main) */}
        <div className="h-full w-0 flex-[1] space-y-1 overflow-auto px-1">
          {hasImages &&
            images.map((image, index) => (
              <button
                key={image.id}
                type="button"
                onClick={() => setActiveIndex(index)}
                className={`group relative aspect-video w-full overflow-hidden rounded-md bg-muted outline-none ring-offset-2 transition focus:ring-2 focus:ring-primary ${
                  index === activeIndex ? "ring-2 ring-primary" : "hover:ring-2 hover:ring-border"
                }`}
                aria-label={`Show image ${index + 1}`}
                aria-current={index === activeIndex}>
                <CustomImage
                  src={image.url}
                  alt={image.name}
                  fill
                  className="object-cover"
                  sizes="200px"
                />
              </button>
            ))}

          {!hasImages && (
            <div className="flex aspect-video w-full items-center justify-center rounded-lg bg-muted">
              <ImageIcon className="size-8 text-muted-foreground" />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
