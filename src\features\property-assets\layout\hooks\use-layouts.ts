import {
  InfiniteData,
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

import { layoutApi } from "@/lib/apis/property_assets/layout";
import { ResponseList } from "@/lib/apis/types/common";
import type {
  CreateLayout,
  Layout,
  LayoutStatistics,
  UpdateLayout,
  UpdateLayoutMapping,
} from "@/lib/apis/types/property_assets/layout";

import { IGetLayoutsParams, layoutKeys } from "./keys";

interface UseLayoutsOptions extends Partial<IGetLayoutsParams> {
  enabled?: boolean;
}

export function useLayouts(options: UseLayoutsOptions = {}) {
  const { limit = 20, enabled = true, ...restOptions } = options;
  const queryClient = useQueryClient();

  const query = useInfiniteQuery({
    queryKey: layoutKeys.list({ limit, ...restOptions }),
    queryFn: ({ pageParam = 0 }) =>
      layoutApi.list({
        page: pageParam as number,
        limit,
        ...restOptions,
      }),
    getNextPageParam: (lastPage) => {
      const totalPages = Math.ceil(lastPage.total / Number(lastPage.limit));
      if (lastPage.page < totalPages) {
        return lastPage.page + 1;
      }
      return undefined;
    },
    initialPageParam: 0,
    enabled,
  });

  const layouts = query.data?.pages.flatMap((page) => page.items) ?? [];
  const total = query.data?.pages[0]?.total ?? 0;

  const useDeleteLayoutMutation = useMutation({
    mutationFn: async (id: string) => {
      return layoutApi.delete(id);
    },
    onSuccess: (_, deletedId) => {
      queryClient.setQueryData<InfiniteData<ResponseList<Layout>>>(
        layoutKeys.list({ limit, ...restOptions }),
        (oldData) => {
          if (!oldData) return oldData;
          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.filter((item) => item.id !== deletedId),
            total: page.total - 1,
          }));
          return {
            ...oldData,
            pages: newPages,
          };
        }
      );
      toast.success("Layout deleted successfully");
    },
    onError: () => {
      toast.error("Layout deletion failed");
    },
  });

  const useDeleteListLayoutMutation = useMutation({
    mutationFn: async (objectData: any) => {
      // Note: deleteListLayouts method doesn't exist in the current API
      // This is a placeholder for future implementation
      throw new Error("Bulk delete not implemented yet");
    },
    onSuccess: (_, objectDeletedId) => {
      queryClient.setQueryData<InfiniteData<ResponseList<Layout>>>(
        layoutKeys.list({ limit, ...restOptions }),
        (oldData) => {
          if (!oldData) return oldData;
          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.filter((item) => !objectDeletedId.listId.includes(item.id)),
            total: page.total - objectDeletedId.listId.length,
          }));
          objectDeletedId?.handleRestRows();
          return {
            ...oldData,
            pages: newPages,
          };
        }
      );
      toast.success("Layouts deleted successfully");
    },
    onError: () => {
      toast.error("Layout deletion failed");
    },
  });
  const useAddLayoutMutation = useMutation<Layout, Error, CreateLayout>({
    mutationFn: async (data: CreateLayout) => {
      const response = await layoutApi.create(data);
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: layoutKeys.lists() });
      toast.success("Layout created successfully");
    },
    onError: () => {
      toast.error("Layout creation failed");
    },
  });

  return {
    ...query,
    layouts,
    total,
    useDeleteLayoutMutation,
    useDeleteListLayoutMutation,
    useAddLayoutMutation,
  };
}

interface UseAddLayoutOptions {
  onSuccess?: (data: Layout) => void;
  onError?: (error: Error) => void;
}

export function useAddLayout(options: UseAddLayoutOptions = {}) {
  const { onSuccess, onError } = options;
  const queryClient = useQueryClient();

  return useMutation<Layout, Error, CreateLayout>({
    mutationFn: async (data: CreateLayout) => {
      const response = await layoutApi.create(data);
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: layoutKeys.lists() });
      onSuccess?.(data);
    },
    onError,
  });
}

interface UpdateLayoutPayload {
  id: string;
  data: UpdateLayout;
}

interface UseUpdateLayoutOptions {
  onSuccess?: (data: Layout) => void;
  onError?: (error: Error) => void;
}

export function useUpdateLayout(options: UseUpdateLayoutOptions = {}) {
  const { onSuccess, onError } = options;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: UpdateLayoutPayload) => {
      const response = await layoutApi.update(id, data);
      return response.data;
    },
    onSuccess: (updatedLayout) => {
      queryClient.setQueryData<InfiniteData<ResponseList<Layout>>>(
        layoutKeys.lists(),
        (oldData) => {
          if (!oldData) return oldData;
          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.map((item) => (item.id === updatedLayout.id ? updatedLayout : item)),
          }));
          return { ...oldData, pages: newPages };
        }
      );
      queryClient.setQueryData(layoutKeys.detail(updatedLayout.id), updatedLayout);
      onSuccess?.(updatedLayout);
    },
    onError,
  });
}

export function useUpdateLayoutMapping() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateLayoutMapping }) => {
      const response = await layoutApi.updateMapping(id, data);
      return response.data;
    },
    onSuccess: (updatedLayout) => {
      queryClient.invalidateQueries({ queryKey: layoutKeys.detail(updatedLayout.id) });
      queryClient.invalidateQueries({ queryKey: layoutKeys.lists() });
      toast.success("Layout mapping updated successfully");
    },
    onError: () => {
      toast.error("Layout mapping update failed");
    },
  });
}

export function useLayout(id: string) {
  return useQuery<Layout>({
    queryKey: layoutKeys.detail(id),
    queryFn: async () => {
      const response = await layoutApi.getById(id);
      return response.data;
    },
    enabled: !!id,
  });
}

export function useLayoutStatistics(params?: {
  property_id?: string;
  start_date?: string;
  end_date?: string;
}) {
  return useQuery<LayoutStatistics>({
    queryKey: layoutKeys.statistics(),
    queryFn: async () => {
      const response = await layoutApi.getStatistics(params);
      return response.data;
    },
  });
}

export function useLayoutsByProperty(
  propertyId: string,
  params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
    floor_number?: number;
  }
) {
  return useQuery({
    queryKey: layoutKeys.byProperty(propertyId),
    queryFn: () => layoutApi.getByPropertyId(propertyId, params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!propertyId,
  });
}

export function useLayoutsByStatus(
  status: string,
  params?: {
    page?: number;
    limit?: number;
    search?: string;
    property_id?: string;
    floor_number?: number;
  }
) {
  return useQuery({
    queryKey: layoutKeys.byStatus(status),
    queryFn: () => layoutApi.getByStatus(status, params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!status,
  });
}

export function useLayoutsByFloor(
  floorNumber: number,
  params?: {
    page?: number;
    limit?: number;
    search?: string;
    property_id?: string;
    status?: string;
  }
) {
  return useQuery({
    queryKey: layoutKeys.byFloor(floorNumber),
    queryFn: () => layoutApi.getByFloorNumber(floorNumber, params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!floorNumber,
  });
}
