import { BusinessSector, PriceGroup } from "./types";

// Mock data for business sectors
export const BUSINESS_SECTORS: BusinessSector[] = [
  { id: "retail", name: "Retail", description: "General retail business" },
  { id: "ecommerce", name: "E-commerce", description: "Online retail business" },
  { id: "wholesale", name: "Wholesale", description: "Wholesale distribution" },
  { id: "manufacturing", name: "Manufacturing", description: "Product manufacturing" },
  { id: "services", name: "Services", description: "Service-based business" },
  { id: "food", name: "Food & Beverage", description: "Restaurant and food service" },
  { id: "fashion", name: "Fashion & Apparel", description: "Clothing and fashion" },
  { id: "electronics", name: "Electronics", description: "Electronic devices and accessories" },
];

// Mock data for price groups
export const PRICE_GROUPS: PriceGroup[] = [
  { id: "standard", name: "Standard", description: "Standard pricing" },
  { id: "premium", name: "Premium", description: "Premium pricing tier" },
  { id: "wholesale", name: "Wholesale", description: "Wholesale pricing" },
  { id: "discount", name: "Discount", description: "Discounted pricing" },
];
