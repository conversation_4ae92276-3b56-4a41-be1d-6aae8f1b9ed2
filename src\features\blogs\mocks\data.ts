import { Category } from "@/lib/apis/types/category";

export const mockCategories: Category[] = [
  {
    id: "1",
    name: "Finance",
    parent_id: null,
    has_children: false,
    product_count: 5,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
  {
    id: "2",
    name: "Education",
    parent_id: null,
    has_children: true,
    product_count: 3,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
  {
    id: "3",
    name: "Teaching",
    parent_id: "2",
    has_children: false,
    product_count: 2,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
  {
    id: "4",
    name: "Marketing",
    parent_id: null,
    has_children: true,
    product_count: 8,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
  {
    id: "5",
    name: "Content Marketing",
    parent_id: "4",
    has_children: false,
    product_count: 3,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
  {
    id: "6",
    name: "Social Media",
    parent_id: "4",
    has_children: false,
    product_count: 2,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
  {
    id: "7",
    name: "SEO",
    parent_id: "4",
    has_children: false,
    product_count: 4,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
  {
    id: "8",
    name: "Email Marketing",
    parent_id: "4",
    has_children: false,
    product_count: 1,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
  {
    id: "9",
    name: "Paid Ads",
    parent_id: "4",
    has_children: false,
    product_count: 6,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
];
