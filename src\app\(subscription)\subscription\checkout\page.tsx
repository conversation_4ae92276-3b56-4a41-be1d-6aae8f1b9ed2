"use client";

import { useEffect, useState } from "react";
import dynamic from "next/dynamic";
import { useSearchParams } from "next/navigation";

import { RegistrationTime, useCheckout } from "@/features/subscription/checkout/hooks/use-checkout";

import { useRightSidePanel } from "@/components/providers";
import { cn } from "@/lib/utils";

// Dynamically import components to prevent SSR issues
const OrderInformationCard = dynamic(
  () =>
    import("@/features/subscription/checkout/components").then((mod) => ({
      default: mod.OrderInformationCard,
    })),
  { ssr: false }
);

const QRPaymentCard = dynamic(
  () =>
    import("@/features/subscription/checkout/components").then((mod) => ({
      default: mod.QRPaymentCard,
    })),
  { ssr: false }
);

export default function CheckoutPage() {
  const [isClient, setIsClient] = useState(false);
  const searchParams = useSearchParams();
  const plan = searchParams.get("plan");
  const time = searchParams.get("time");
  const subId = searchParams.get("sub-id");
  const { openPanel, closePanel } = useRightSidePanel();

  const {
    checkoutData,
    formData,
    errors,
    isLoading,
    isLoadingPlan,
    isCompanyInfoCompleted,
    isPaymentMode,
    subscriptionDetails,
    handleUpdateCompanyInfo,
    handleRegistrationTimeChange,
    handleSubmit,
    handleConfirmCompanyInfo,
    handleCancelOrder,
    handleRemoveCompanyInfo,
  } = useCheckout({
    planId: plan!,
    initialRegistrationTime: time as RegistrationTime,
    subscriptionId: subId || undefined,
  });

  // Ensure this component only renders on client side
  useEffect(() => {
    setIsClient(true);
  }, []);

  // // Handle payment mode changes to show/hide right side panel
  useEffect(() => {
    if (!isClient || !isPaymentMode || !subscriptionDetails) return;

    const qrPaymentContent = (
      <QRPaymentCard
        qrUrl={subscriptionDetails?.qr_info?.qr_code}
        paymentStatus={subscriptionDetails?.status}
        accountName={subscriptionDetails?.qr_info?.account_name}
        accountNumber={subscriptionDetails?.qr_info?.account_number}
        bankName={subscriptionDetails?.qr_info?.bank_name}
        amount={subscriptionDetails?.qr_info?.amount}
        currency={checkoutData.currency}
        transferContent={subscriptionDetails?.qr_info?.description}
        isLoading={false}
        onCancelOrder={handleCancelOrder}
      />
    );
    openPanel(qrPaymentContent);
  }, [isClient, isPaymentMode, subscriptionDetails]);

  // Don't render anything until client-side hydration is complete
  if (!isClient) {
    return null;
  }

  return (
    <div className="flex flex-col gap-4 p-4 pt-0">
      <div className="flex flex-1 items-start justify-center overflow-hidden">
        {/* Order Information Card - now takes full width when payment panel is closed */}
        <div
          className={cn("hidden w-full justify-center", isPaymentMode ? "hidden md:flex" : "flex")}>
          <OrderInformationCard
            subscriptionPlan={checkoutData.subscriptionPlan}
            checkoutData={checkoutData}
            formData={formData}
            errors={errors}
            isLoading={isLoading}
            isLoadingPlan={isLoadingPlan}
            isPaymentMode={isPaymentMode}
            isCompanyInfoCompleted={isCompanyInfoCompleted}
            onConfirmCompanyInfo={handleConfirmCompanyInfo}
            onRemoveCompanyInfo={handleRemoveCompanyInfo}
            onUpdateCompanyInfo={handleUpdateCompanyInfo}
            onRegistrationTimeChange={handleRegistrationTimeChange}
            onSubmit={handleSubmit}
          />
        </div>
        {isPaymentMode && (
          <div className="fixed left-0 top-0 z-10 block size-full bg-transparent md:hidden">
            <QRPaymentCard
              qrUrl={subscriptionDetails?.qr_info?.qr_code}
              paymentStatus={subscriptionDetails?.status}
              accountName={subscriptionDetails?.qr_info?.account_name}
              accountNumber={subscriptionDetails?.qr_info?.account_number}
              bankName={subscriptionDetails?.qr_info?.bank_name}
              amount={subscriptionDetails?.qr_info?.amount}
              currency={checkoutData.currency}
              transferContent={subscriptionDetails?.qr_info?.description}
              // isLoading={isCancellingOrder}
              onCancelOrder={handleCancelOrder}
            />
          </div>
        )}
      </div>
    </div>
  );
}
