import { UseFormReturn } from "react-hook-form";
import { useTranslation } from "react-i18next";

import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

interface AmenitiesSectionProps {
  form: UseFormReturn<any>;
}

export function AmenitiesSection({ form }: AmenitiesSectionProps) {
  const { t } = useTranslation();

  const amenities = [
    "air-conditioning",
    "washer-dryer",
    "fireplace",
    "pool-access",
    "dishwasher",
    "balcony",
    "heating",
    "parking",
    "hardwood-floors",
    "gym-access",
    "pet-friendly",
    "elevator",
  ];

  return (
    <div className="rounded-lg border bg-card p-4">
      <h3 className="mb-4 text-sm font-medium text-card-foreground">
        {t("pages.units.amenitiesTitle") || "Amenities"}
      </h3>
      <div className="grid grid-cols-2 gap-3">
        {amenities.map((amenity) => (
          <div key={amenity} className="flex items-center space-x-2">
            <Checkbox
              id={amenity}
              checked={form.watch("amenities").includes(amenity)}
              onCheckedChange={(checked) => {
                const currentAmenities = form.watch("amenities");
                if (checked) {
                  form.setValue("amenities", [...currentAmenities, amenity]);
                } else {
                  form.setValue(
                    "amenities",
                    currentAmenities.filter((a: string) => a !== amenity)
                  );
                }
              }}
            />
            <Label htmlFor={amenity} className="text-sm">
              {t(`pages.units.amenities.${amenity}`) ||
                amenity.replace("-", " ").replace(/\b\w/g, (l) => l.toUpperCase())}
            </Label>
          </div>
        ))}
      </div>
    </div>
  );
}
