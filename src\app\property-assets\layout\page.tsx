"use client";

import { useMemo, useState } from "react";
import { Check, ChevronsUpDown, Plus, Settings } from "lucide-react";
import { useTranslation } from "react-i18next";

import CreateLayoutDialog from "@/features/property-assets/layout/components/create-layout-dialog";
import { LayoutGrid } from "@/features/property-assets/layout/components/LayoutGrid";
import { LayoutStatistics } from "@/features/property-assets/layout/components/LayoutStatistics";
// Components
import { useLayouts } from "@/features/property-assets/layout/hooks/use-layouts";
import { useProperties } from "@/features/property-assets/property/hooks/property";

import { CustomPagination } from "@/components/custom-pagination";
// UI Components
import GroupButton, { GroupButtonProps } from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { EFilterType, FilterTableProps, FilterType } from "@/components/data-table/types";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";

export default function LayoutManagementPage() {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch, handlePageChange, handlePageSizeChange } =
    useDatatable();
  const { data: propertiesData, isLoading: isPropertiesLoading } = useProperties({ limit: 100 });

  // Dialog state
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [selectedProperty, setSelectedProperty] = useState<string | undefined>(
    getInitialParams["property_id"] as string
  );

  // Use the real useLayouts hook to get data from API
  const options = useMemo(() => {
    return {
      limit: 20,
      enabled: !isPropertiesLoading,
      ...getInitialParams,
    };
  }, [getInitialParams, isPropertiesLoading]);
  const { layouts, total, isLoading, isFetching, useAddLayoutMutation } = useLayouts(options);

  // Get properties for the property selector

  // Mock data for statistics - replace with useLayoutStatistics hook when available
  const stats = {
    totalLayouts: total || 0,
    totalProperties: 5,
    mappingProgress: 180.0,
    mappedUnits: 9,
    totalUnits: 5,
    systemHealth: 100,
    weeklyProgress: 41.4,
    recentChanges: 3,
  };

  const filterConfig: FilterTableProps = {
    showSearch: true,
    filterType: "layout",
    searchPlaceHolder: "Type a command or search...",
    initialValues: getInitialParams,
    listFilter: [
      {
        id: "status",
        type: EFilterType.SELECT_BOX,
        title: "Status",
        defaultValue: getInitialParams["status"],
        dataOption: [
          { label: "All Status", value: "all" },
          { label: "Complete", value: "complete" },
          { label: "Pending", value: "pending" },
          { label: "In Progress", value: "in-progress" },
        ],
      },
      {
        id: "created_at",
        type: EFilterType.SELECT_BOX,
        title: "Created at",
        defaultValue: getInitialParams["created_at"],
        dataOption: [
          { label: "All Time", value: "all" },
          { label: "Today", value: "today" },
          { label: "This Week", value: "week" },
          { label: "This Month", value: "month" },
        ],
      },
    ] as FilterType[],
    handleParamSearch,
    listLoading: isLoading || isFetching,
  };

  const groupButtonConfig: GroupButtonProps = {
    buttons: [
      {
        type: "button" as const,
        title: "Bulk Actions",
        variant: "outline",
        icon: Settings,
        onClick: () => {},
      },
      {
        type: "button" as const,
        title: "Add layout",
        icon: Plus,
        onClick: () => setCreateDialogOpen(true),
        variant: "default",
        // className: "bg-orange-500 hover:bg-orange-600",
      },
    ],
    showRefresh: false,
  };

  return (
    <TableCard className="border-none p-4">
      {/* Table Header with Controls */}
      <TableHeader
        title={t("pages.layouts.title") || "Layout Management"}
        filterType="layout"
        data={layouts || []}
        filterProps={filterConfig}
        swapPositions
        rightComponent={<GroupButton {...groupButtonConfig} />}
        customRightFilter={
          <PropertySelector
            properties={propertiesData?.pages.flatMap((page) => page.items) || []}
            selectedProperty={getInitialParams["property_id"] as string}
            onPropertyChange={(propertyId) => {
              if (propertyId) {
                handleParamSearch({ property_id: propertyId });
              } else {
                handleParamSearch({ property_id: undefined });
              }
            }}
          />
        }
        isSaveFilters={false}
        isExportable={false}
      />

      <div className="flex flex-auto flex-col gap-4 overflow-y-auto">
        <LayoutStatistics {...stats} />
        <LayoutGrid layouts={layouts || []} loading={isLoading || isFetching} />
        {!isLoading && !isFetching && (
          <CustomPagination
            total={total}
            pageSize={Number(getInitialParams.limit)}
            currentPage={Number(getInitialParams.page)}
            onPageSizeChange={handlePageSizeChange}
            onPageChange={(page) => handlePageChange(page - 1)}
          />
        )}
      </div>

      {/* Create Layout Dialog */}
      <CreateLayoutDialog
        key={createDialogOpen ? "open" : "closed"}
        open={createDialogOpen}
        setOpen={setCreateDialogOpen}
        onSubmit={async (data) => {
          // TODO: Implement layout creation logic
          const layoutData = {
            ...data,
            property_id: selectedProperty || propertiesData?.pages[0]?.items[0]?.id,
            floor_number: data.floor_number || 1,
          };
          useAddLayoutMutation.mutate(layoutData);
          setCreateDialogOpen(false);
        }}
        isLoading={useAddLayoutMutation.isPending}
      />
    </TableCard>
  );
}

// Property Selector Component with Command search
function PropertySelector({
  properties,
  selectedProperty,
  onPropertyChange,
}: {
  properties: Array<{ id: string; name: string }>;
  selectedProperty?: string;
  onPropertyChange: (propertyId: string | undefined) => void;
}) {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState("");

  const selectedPropertyName = selectedProperty
    ? properties.find((p) => p.id === selectedProperty)?.name
    : undefined;

  const filteredProperties = properties.filter((property) =>
    property.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="flex items-center gap-2">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" role="combobox" className="justify-between">
            {selectedPropertyName || t("pages.layouts.allProperties", "All Properties")}
            <ChevronsUpDown className="ml-2 size-4 shrink-0 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="p-0">
          <Command>
            <CommandInput
              placeholder={t("pages.layouts.searchProperties", "Search properties...")}
              value={searchQuery}
              onValueChange={setSearchQuery}
            />
            <CommandList>
              <CommandEmpty>
                {t("pages.layouts.noPropertiesFound", "No properties found.")}
              </CommandEmpty>
              <CommandGroup>
                <CommandItem
                  value=""
                  onSelect={() => {
                    onPropertyChange(undefined);
                  }}>
                  <Check
                    className={cn("mr-2 h-4 w-4", !selectedProperty ? "opacity-100" : "opacity-0")}
                  />
                  {t("pages.layouts.allProperties", "All Properties")}
                </CommandItem>
                {filteredProperties.map((property) => (
                  <CommandItem
                    key={property.id}
                    value={property.name}
                    onSelect={() => {
                      onPropertyChange(property.id);
                    }}>
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        selectedProperty === property.id ? "opacity-100" : "opacity-0"
                      )}
                    />
                    {property.name}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
