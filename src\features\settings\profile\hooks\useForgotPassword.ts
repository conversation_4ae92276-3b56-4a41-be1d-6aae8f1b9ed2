import { useEffect, useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { authKeys } from "@/features/auth/hooks/keys";

import { authApi } from "@/lib/apis/auth";

import { useResendCode } from "./useResendCode";
import { useVerification } from "./useVerification";

export type ForgotPasswordStep = "email" | "verification" | "reset" | "success";

export const useForgotPassword = () => {
  const { t } = useTranslation();
  const [currentStep, setCurrentStep] = useState<ForgotPasswordStep>("email");
  const [email, setEmail] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [session, setSession] = useState("");

  // Use verification hook
  const { verifyCode, isVerifyingCode, verifyCodeError, resetVerifyCodeMutation } =
    useVerification();

  // Use resend code hook for countdown management
  const {
    onSubmit: sendCode,
    loading: isSendingCode,
    error: sendCodeError,
    countdown,
    isCheckingDelay,
    checkCountdown,
  } = useResendCode({
    onSuccess: () => setCurrentStep("verification"),
  });

  const resetState = () => {
    setCurrentStep("email");
    setEmail("");
    setVerificationCode("");
    setNewPassword("");
    setConfirmPassword("");
    setShowNewPassword(false);
    setShowConfirmPassword(false);
    setSession("");
  };

  // Handle verification success
  const handleVerificationSuccess = (data: any) => {
    toast.success(t("auth.verificationCodeSuccess"));
    // Store the session from the verification response
    if (data?.session) {
      setSession(data.session);
    }
    setCurrentStep("reset");
  };

  // Reset password mutation
  const {
    mutate: resetPassword,
    isPending: isResettingPassword,
    error: resetPasswordError,
    reset: resetPasswordMutation,
  } = useMutation({
    mutationKey: authKeys.resetPassword(""),
    mutationFn: async ({ session, password }: { session: string; password: string }) => {
      if (!session || !password) {
        throw new Error(t("auth.passwordRequired"));
      }
      return authApi.newPassword({ session, new_password: password });
    },
    onSuccess: () => {
      toast.success(t("auth.passwordResetSuccess"));
      setCurrentStep("success");
    },
    onError: (error) => {
      const message = error instanceof Error ? error.message : t(String(error));
      toast.error(t("common.error"), {
        description: message,
      });
    },
  });

  const handleSendCode = async (emailToSend: string) => {
    if (!emailToSend) {
      toast.error(t("common.error"), {
        description: t("pages.profile.forgotPasswordDialog.emailRequired"),
      });
      return;
    }

    // Update the email state
    setEmail(emailToSend);
    sendCode(emailToSend);
  };

  const handleVerifyCode = async (codeToVerify: string) => {
    console.log("codeToVerify", codeToVerify);
    if (!codeToVerify || codeToVerify.length !== 6) {
      toast.error(t("common.error"), {
        description: t("pages.profile.forgotPasswordDialog.codeRequired"),
      });
      return;
    }

    resetVerifyCodeMutation();
    verifyCode(
      { username: email, code: codeToVerify },
      {
        onSuccess: handleVerificationSuccess,
      }
    );
  };

  const handleResetPassword = async () => {
    if (!newPassword || !confirmPassword) {
      toast.error(t("common.error"), {
        description: t("pages.profile.forgotPasswordDialog.allFieldsRequired"),
      });
      return;
    }

    if (newPassword !== confirmPassword) {
      toast.error(t("common.error"), {
        description: t("pages.profile.forgotPasswordDialog.passwordsDoNotMatch"),
      });
      return;
    }

    if (newPassword.length < 8) {
      toast.error(t("common.error"), {
        description: t("pages.profile.forgotPasswordDialog.passwordTooShort"),
      });
      return;
    }

    if (!session) {
      toast.error(t("common.error"), {
        description: t("pages.profile.forgotPasswordDialog.sessionExpired"),
      });
      return;
    }

    resetPasswordMutation();
    // Use the session from the verification step
    resetPassword({ session, password: newPassword });
  };

  const handleResendCode = async () => {
    if (!email || countdown > 0 || isCheckingDelay) return;

    sendCode(email);
  };

  // Check countdown for username
  useEffect(() => {
    if (email) {
      checkCountdown(email);
    }
  }, [email, checkCountdown]);

  const isLoading = isSendingCode || isVerifyingCode || isResettingPassword;

  return {
    // State
    currentStep,
    email,
    verificationCode,
    newPassword,
    confirmPassword,
    showNewPassword,
    showConfirmPassword,
    isLoading,
    countdown,
    isCheckingDelay,
    session,

    // Errors
    sendCodeError,
    verifyCodeError,
    resetPasswordError,

    // Actions
    setEmail,
    setVerificationCode,
    setNewPassword,
    setConfirmPassword,
    setShowNewPassword,
    setShowConfirmPassword,
    setCurrentStep,
    resetState,
    handleSendCode,
    handleVerifyCode,
    handleResetPassword,
    handleResendCode,
    checkCountdown,
  };
};
