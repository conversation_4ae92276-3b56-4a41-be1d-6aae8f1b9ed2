import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { propertyUnitApi } from "@/lib/apis/property_assets/property_unit";
import type { UpdateUnit } from "@/lib/apis/types/property_assets/property_unit";

// Query keys
export const unitsKeys = {
  all: ["units"] as const,
  lists: () => [...unitsKeys.all, "list"] as const,
  list: (propertyId: string, params?: any) => [...unitsKeys.lists(), propertyId, params] as const,
  details: () => [...unitsKeys.all, "detail"] as const,
  detail: (id: string) => [...unitsKeys.details(), id] as const,
};

// Hooks
export const useUnits = (
  propertyId?: string,
  params?: Record<string, unknown>,
  enabled: boolean = true
) => {
  return useQuery({
    queryKey: unitsKeys.list(propertyId || "all", params),
    queryFn: () =>
      propertyId
        ? propertyUnitApi.getByPropertyId(propertyId, params)
        : propertyUnitApi.list(params),
    enabled: enabled,
    staleTime: 3 * 60 * 1000, // 3 minutes
  });
};

export const useUnit = (id: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: unitsKeys.detail(id),
    queryFn: () => propertyUnitApi.getById(id),
    enabled: enabled && !!id,
    staleTime: 3 * 60 * 1000,
  });
};

export const useCreateUnit = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: any) => {
      const response = await propertyUnitApi.create(data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: unitsKeys.lists() });
      toast.success("Unit created successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to create unit: ${error.message}`);
    },
  });
};

export const useUpdateUnit = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<UpdateUnit> }) => {
      const response = await propertyUnitApi.update(id, data);
      return response.data;
    },
    onSuccess: (updatedUnit) => {
      queryClient.invalidateQueries({ queryKey: unitsKeys.lists() });
      queryClient.setQueryData(unitsKeys.detail(updatedUnit.id), updatedUnit);
      toast.success("Unit updated successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to update unit: ${error.message}`);
    },
  });
};

export const useDeleteUnit = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      return propertyUnitApi.delete(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: unitsKeys.lists() });
      toast.success("Unit deleted successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete unit: ${error.message}`);
    },
  });
};

export const useBulkUpdateUnits = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (updates: Array<{ id: string; data: Partial<UpdateUnit> }>) => {
      // Since propertyUnitApi doesn't have bulk update, we'll do individual updates
      const results = await Promise.all(
        updates.map(({ id, data }) => propertyUnitApi.update(id, data))
      );
      return results.map((result) => result.data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: unitsKeys.lists() });
      toast.success("Units updated successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to update units: ${error.message}`);
    },
  });
};
