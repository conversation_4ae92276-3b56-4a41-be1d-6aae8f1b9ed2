import { EmployeeListParams } from "../types/employee";

export interface IGetEmployeesParams extends EmployeeListParams {
  limit?: number;
  nextPage?: string;
}

export const employeeKeys = {
  all: () => ["employee"] as const,
  lists: () => [...employeeKeys.all(), "list"] as const,
  list: (params: IGetEmployeesParams) => [...employeeKeys.lists(), params] as const,
  details: () => [...employeeKeys.all(), "detail"] as const,
  detail: (id: string) => [...employeeKeys.details(), id] as const,
  permissions: () => [...employeeKeys.all(), "permissions"] as const,
  roles: () => [...employeeKeys.all(), "roles"] as const,
};

export const QUERY_KEYS = {
  EMPLOYEES: ["employees"] as const,
  EMPLOYEE_PERMISSIONS: ["employeePermissions"] as const,
  EMPLOYEE_ROLES: ["employeeRoles"] as const,
  USERS: ["users"] as const,
} as const;
