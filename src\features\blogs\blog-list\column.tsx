import { UseMutationResult } from "@tanstack/react-query";
import { Row } from "@tanstack/react-table";

import {
  DateColumn,
  ImageColumn,
  TextColumn,
} from "@/components/custom-table/container/common-column";
import ActionGroup from "@/components/data-table/action-group";
import { CustomColumn } from "@/components/data-table/data-table";
import { Blog } from "@/lib/apis/types/blogs";

export const columns = (
  useDeleteBlogMutation: UseMutationResult<void, Error, string, unknown>,
  isDeleting: boolean,
  t: any
): CustomColumn<Blog>[] => [
  {
    id: "image",
    accessorKey: "image",
    header: t("pages.blogList.headers.image"),
    cell: ({ row }: { row: Row<Blog> }) => (
      <ImageColumn
        src={row.original.image || ""}
        alt={row.original.title}
        width={0}
        height={0}
        sizes="100vw"
        className="flex size-10 items-center justify-center overflow-hidden rounded bg-muted object-contain"
      />
    ),
  },
  {
    id: "title",
    accessorKey: "title",
    header: t("pages.blogList.headers.title"),
    sorter: true,
    sortKey: "title",
    cell: ({ row }: { row: Row<Blog> }) => (
      <TextColumn
        text={row.original.title}
        className="cursor-pointer font-medium text-primary hover:underline"
      />
    ),
  },
  {
    id: "category",
    accessorKey: "category",
    header: t("pages.blogList.headers.category"),
    sorter: true,
    sortKey: "category.id",
    cell: ({ row }: { row: Row<Blog> }) => <TextColumn text={row.original.category?.name} />,
  },
  {
    id: "updated_at",
    accessorKey: "updated_at",
    header: t("pages.blogList.headers.updatedAt"),
    sorter: true,
    sortKey: "updated_at",
    cell: ({ row }: { row: Row<Blog> }) => <DateColumn date={row.original.updated_at} />,
  },
  {
    id: "actions",
    header: t("pages.blogList.headers.actions"),
    cell: ({ row }: { row: Row<Blog> }) => (
      <ActionGroup
        actions={[
          {
            type: "viewOnWeb",
            onClick: () => {}, // Implement view logic
          },
          {
            type: "view",
            onClick: () => {}, // Implement view logic
          },
          {
            type: "edit",
            onClick: () => {}, // Implement edit logic
          },
          {
            type: "delete",
            onClick: () => useDeleteBlogMutation.mutateAsync(row.original.id),
            loading: isDeleting,
          },
        ]}
        separatorAfter={["viewOnWeb"]}
      />
    ),
  },
];
