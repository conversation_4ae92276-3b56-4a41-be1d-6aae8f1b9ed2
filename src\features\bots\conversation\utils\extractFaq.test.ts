import { extractRelatedQuestions, hasRelatedQuestions } from "./extractFaq";

describe("extractRelatedQuestions", () => {
  it("should extract questions from valid XML format", () => {
    const content = `Len nhung mèo có kích thước từ 3.5mm đến 4.0mm. 

<PERSON><PERSON><PERSON><PERSON> ra, len nhung mèo cũng có trọng lượng 50gr và chiều dài 60m.

<related_questions>
    <question>
        Khi nào nên sử dụng len nhung mèo cho các dự án đan móc?
    </question>
    <question>
        <PERSON><PERSON> thể làm những sản phẩm nào từ len nhung mèo?
    </question>
</related_questions>`;

    const result = extractRelatedQuestions(content);

    expect(result.cleanContent).toBe(`Len nhung mèo có kích thước từ 3.5mm đến 4.0mm. 

<PERSON><PERSON><PERSON><PERSON> ra, len nhung mèo cũng có trọng lượng 50gr và chiều dài 60m.`);

    expect(result.questions).toHaveLength(2);
    expect(result.questions[0]).toBe("Khi nào nên sử dụng len nhung mèo cho các dự án đan móc?");
    expect(result.questions[1]).toBe("Có thể làm những sản phẩm nào từ len nhung mèo?");
  });

  it("should return empty questions array when no related_questions tag", () => {
    const content = "This is a regular message without any FAQ";
    const result = extractRelatedQuestions(content);

    expect(result.cleanContent).toBe("This is a regular message without any FAQ");
    expect(result.questions).toEqual([]);
  });

  it("should handle empty content", () => {
    const result = extractRelatedQuestions("");

    expect(result.cleanContent).toBe("");
    expect(result.questions).toEqual([]);
  });

  it("should handle malformed XML gracefully", () => {
    const content = `Message with incomplete XML
<related_questions>
    <question>First question
    <question>Second question</question>
</related_questions>`;

    const result = extractRelatedQuestions(content);

    expect(result.cleanContent).toBe("Message with incomplete XML");
    // The regex will capture the first unclosed question tag content
    expect(result.questions.length).toBeGreaterThan(0);
    // The actual behavior captures the malformed content
    expect(
      result.questions.some((q) => q.includes("Second question") || q.includes("First question"))
    ).toBe(true);
  });

  it("should handle questions with whitespace correctly", () => {
    const content = `Main content
<related_questions>
    <question>
        
        Question with extra whitespace
        
    </question>
</related_questions>`;

    const result = extractRelatedQuestions(content);

    expect(result.cleanContent).toBe("Main content");
    expect(result.questions).toHaveLength(1);
    expect(result.questions[0]).toBe("Question with extra whitespace");
  });
});

describe("hasRelatedQuestions", () => {
  it("should return true when related_questions tag exists", () => {
    const content = "Message <related_questions><question>Test</question></related_questions>";
    expect(hasRelatedQuestions(content)).toBe(true);
  });

  it("should return false when no related_questions tag", () => {
    const content = "Regular message without FAQ";
    expect(hasRelatedQuestions(content)).toBe(false);
  });

  it("should return false for empty content", () => {
    expect(hasRelatedQuestions("")).toBe(false);
  });

  it("should return false for null/undefined content", () => {
    expect(hasRelatedQuestions(null as any)).toBe(false);
    expect(hasRelatedQuestions(undefined as any)).toBe(false);
  });
});
