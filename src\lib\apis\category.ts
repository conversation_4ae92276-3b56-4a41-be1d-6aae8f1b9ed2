import { CATEGORY_ENDPOINTS } from "@/constants/endpoints";

import { privateApi } from "../api_helper";
import { Category } from "./types/category";
import { ResponseAxiosDetail, ResponseList } from "./types/common";

export const categoryApi = {
  list: async (params?: Record<string, unknown>) => {
    return await privateApi.get<ResponseList<Category>>(CATEGORY_ENDPOINTS.LIST, {
      params,
    });
  },

  create: async (data: Partial<Category>) => {
    return await privateApi.post<ResponseAxiosDetail<Category>>(CATEGORY_ENDPOINTS.CREATE, data);
  },

  update: async (id: string, data: Partial<Category>) => {
    return await privateApi.put<ResponseAxiosDetail<Category>>(
      CATEGORY_ENDPOINTS.UPDATE.replace(":id", id),
      data
    );
  },

  delete: async (id: string) => {
    return await privateApi.delete<ResponseAxiosDetail<{ message: string }>>(
      CATEG<PERSON>Y_ENDPOINTS.DELETE.replace(":id", id)
    );
  },

  updateParent: async (id: string, parentId: string | null) => {
    return await privateApi.put<ResponseAxiosDetail<Category>>(
      CATEGORY_ENDPOINTS.UPDATE.replace(":id", id),
      { parent_id: parentId }
    );
  },
};
