import { Metadata } from "next";

import { PropertyDetailsComponent } from "@/features/property-assets/property/components/PropertyDetails";

export const metadata: Metadata = {
  title: "Property Details | OneX ERP",
  description: "View detailed property information, units, contracts, and performance metrics",
};

interface PropertyDetailPageProps {
  params: {
    id: string;
  };
}

export default function PropertyDetailPage({ params }: PropertyDetailPageProps) {
  return (
    <div className="container mx-auto py-6">
      <PropertyDetailsComponent propertyId={params.id} />
    </div>
  );
}
