"use client";

import { useTranslation } from "react-i18next";

import { Progress } from "@/components/ui/progress";
import { convertByte } from "@/lib/utils/convert_byte";

import { UsageStats } from "../hooks/use-subscription";

interface UsageMetricProps {
  label: string;
  used: number;
  total: number;
  formatValue?: (value: number) => string;
}

const UsageMetric = ({
  label,
  used,
  total,
  formatValue,
  isUnlimited,
}: UsageMetricProps & { isUnlimited?: boolean }) => {
  const { t } = useTranslation();
  const percentage = isUnlimited ? 0 : (used / total) * 100;
  const displayUsed = formatValue ? formatValue(used) : used.toLocaleString();
  const displayTotal = isUnlimited
    ? t("pages.subscription.usageStats.unlimited")
    : formatValue
      ? formatValue(total)
      : total.toLocaleString();

  return (
    <div className="flex flex-col gap-1">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-1">
          <span className="text-sm text-ring">{label}</span>
        </div>
        <span className="text-sm text-foreground">
          {displayUsed}/{displayTotal}
        </span>
      </div>
      <Progress
        value={isUnlimited ? 100 : percentage}
        className="h-2 bg-muted"
        primaryClassName={
          isUnlimited
            ? "bg-gradient-to-r from-sematic-warning to-primary"
            : percentage >= 80
              ? "bg-destructive"
              : ""
        }
      />
    </div>
  );
};

interface UsageStatsCardProps {
  usageStats: UsageStats;
}

export const UsageStatsCard = ({ usageStats }: UsageStatsCardProps) => {
  const { t } = useTranslation();

  // Helper function to format quota values
  const getFormatValue = (key: string) => {
    // Check if the key contains "capacity" or "storage" to apply byte conversion
    if (key.toLowerCase().includes("capacity") || key.toLowerCase().includes("storage")) {
      return (value: number) => convertByte(value);
    }
    return undefined; // Use default formatting for other keys
  };

  // Generate usage metrics dynamically from usageStats, including unlimited quotas
  const usageMetrics = Object.keys(usageStats).map((key) => {
    const stat = usageStats[key];
    const isUnlimited = Number(stat.total) === -1;

    const label = t(`quota.${key}`) || key;

    return {
      key,
      label,
      used: stat.used,
      total: stat.total,
      formatValue: getFormatValue(key),
      isUnlimited,
    };
  });

  // If no usage stats, show empty state
  if (usageMetrics.length === 0) {
    return (
      <div>
        <h3 className="text-base font-medium">{t("pages.subscription.usageStats.title")}</h3>
        <div className="mt-2 text-sm text-muted-foreground">
          {t("pages.subscription.usageStats.noData")}
        </div>
      </div>
    );
  }

  return (
    <div>
      <h3 className="text-base font-medium">{t("pages.subscription.usageStats.title")}</h3>

      <div className="mt-2 grid grid-cols-1 gap-2 gap-x-4 md:grid-cols-2">
        {usageMetrics.map((metric) => (
          <UsageMetric
            key={metric.key}
            label={metric.label}
            used={metric.used}
            total={metric.total}
            formatValue={metric.formatValue}
            isUnlimited={metric.isUnlimited}
          />
        ))}
      </div>
    </div>
  );
};
