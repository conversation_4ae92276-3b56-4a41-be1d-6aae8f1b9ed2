import { CRM_ENDPOINTS } from "../../../constants/endpoints";
import { privateApi } from "../../api_helper";
import { ResponseAxiosDetail, ResponseList } from "../types/common";
import { CreatePlanRequest, Plan, UpdatePlanRequest } from "./types";

export const planApi = {
  list: async (params?: Record<string, unknown>) => {
    return await privateApi.get<ResponseList<Plan>>(CRM_ENDPOINTS.PLANS);
  },
  create: async (plan: CreatePlanRequest) => {
    return await privateApi.post<ResponseAxiosDetail<Plan>>(CRM_ENDPOINTS.PLANS, plan);
  },
  get: async (id: string) => {
    return await privateApi.get<ResponseAxiosDetail<Plan>>(CRM_ENDPOINTS.PLAN(id));
  },
  update: async (id: string, plan: UpdatePlanRequest) => {
    return await privateApi.put<Plan>(CRM_ENDPOINTS.PLAN(id), plan);
  },
  delete: async (id: string) => {
    return await privateApi.delete(CRM_ENDPOINTS.PLAN(id));
  },
};
