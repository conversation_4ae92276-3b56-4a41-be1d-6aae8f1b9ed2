import { useCallback, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { clearAuth } from "@/lib/auth";

export const useLogout = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const queryClient = useQueryClient();

  const handleLogout = useCallback(() => {
    // Clear all TanStack Query cache to prevent data from previous sessions
    queryClient.clear();

    // Clear authentication data
    clearAuth();

    // Clear subscription data from localStorage
    localStorage.removeItem("activeSubscription");
    localStorage.removeItem("subscriptions");

    toast.success(t("auth.logoutSuccess"));
    router.push("/login");
  }, [t, router, queryClient]);

  useEffect(() => {
    handleLogout();
  }, [handleLogout]);
};
