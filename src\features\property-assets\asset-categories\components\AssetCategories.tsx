"use client";

import { useState } from "react";
import { Plus } from "lucide-react";

import { Button } from "@/components/ui/button";

import { mockCategories } from "../data/mockCategories";
import type { AssetCategory } from "../types";
import { getIconComponent } from "../utils/iconHelpers";
import { AnalyticsSidebar } from "./AnalyticsSidebar";
import { CategoriesList } from "./CategoriesList";
import { CreateCategoryDialog } from "./CreateCategoryDialog";
import { EditCategoryDialog } from "./EditCategoryDialog";
import { SummaryCard } from "./SummaryCard";

interface AssetCategoriesProps {
  className?: string;
}

export function AssetCategories({ className }: AssetCategoriesProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddCategoryOpen, setIsAddCategoryOpen] = useState(false);
  const [isEditCategoryOpen, setIsEditCategoryOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<AssetCategory | null>(null);

  const categories = mockCategories;

  const totalAssets = categories.reduce((sum, cat) => sum + cat.assetCount, 0);
  const totalValue = categories.reduce((sum, cat) => sum + cat.totalValue, 0);
  const avgPerformance =
    categories.reduce((sum, cat) => sum + cat.performanceScore, 0) / categories.length;
  const activeCategories = categories.filter((cat) => cat.isActive).length;

  const handleEdit = (category: AssetCategory) => {
    setSelectedCategory(category);
    setIsEditCategoryOpen(true);
  };

  const handleViewAssets = (category: AssetCategory) => {
    // TODO: Implement view assets functionality
    console.log("View assets for:", category.name);
  };

  const handleViewAnalytics = (category: AssetCategory) => {
    // TODO: Implement view analytics functionality
    console.log("View analytics for:", category.name);
  };

  const handleDelete = (category: AssetCategory) => {
    // TODO: Implement delete functionality
    console.log("Delete category:", category.name);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header Section */}
      <div className="rounded-lg border bg-card p-4">
        <div className="flex items-center justify-between">
          <h2 className="text-sm font-medium text-card-foreground">Categories</h2>
          <Button className="h-9 px-3 py-2" onClick={() => setIsAddCategoryOpen(true)}>
            <Plus className="mr-1 size-4" />
            Add category
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
        {/* Left Column - Main Content */}
        <div className="col-span-1 space-y-4 lg:col-span-2">
          {/* Categories List */}
          <CategoriesList
            categories={categories}
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            onEdit={handleEdit}
            onViewAssets={handleViewAssets}
            onViewAnalytics={handleViewAnalytics}
            onDelete={handleDelete}
            getIconComponent={getIconComponent}
          />
        </div>

        {/* Right Column - Sidebar */}
        <div className="col-span-1 space-y-4">
          {/* Summary Cards */}
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <SummaryCard
                title="Total Categories"
                value={categories.length}
                subtitle={`${activeCategories} active`}
                icon="messages-square"
                iconColor="text-yellow-600"
              />
              <SummaryCard
                title="Total Assets"
                value={totalAssets}
                icon="users"
                iconColor="text-green-600"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <SummaryCard
                title="Total Value"
                value={`$${(totalValue / 1000000).toFixed(1)}M`}
                icon="star"
                iconColor="text-blue-600"
              />
              <SummaryCard
                title="Avg Performance"
                value={`${avgPerformance.toFixed(0)}%`}
                icon="clock-10"
                iconColor="text-red-600"
              />
            </div>
          </div>

          {/* Analytics Sidebar */}
          <AnalyticsSidebar categories={categories} />
        </div>
      </div>

      {/* Create Category Dialog */}
      <CreateCategoryDialog open={isAddCategoryOpen} onOpenChange={setIsAddCategoryOpen} />

      {/* Edit Category Dialog */}
      <EditCategoryDialog
        open={isEditCategoryOpen}
        onOpenChange={setIsEditCategoryOpen}
        category={selectedCategory}
      />
    </div>
  );
}
