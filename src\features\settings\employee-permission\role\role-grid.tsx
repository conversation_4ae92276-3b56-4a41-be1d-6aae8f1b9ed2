"use client";

import { useState } from "react";
import { UseMutationResult } from "@tanstack/react-query";
import { <PERSON><PERSON>, Edit, Eye, MoreVertical, Trash2 } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { PermissionDropdownItem } from "@/components/ui/permission-dropdown";
import { StatusBadge } from "@/components/ui/status-badge";
import { Role } from "@/lib/apis/types/role";

import { DeleteRoleDialog } from "./components";

interface RoleGridProps {
  roles: Role[];
  onEditRole: (roleId: string) => void;
  onDuplicateRole: (roleId: string) => void;
  isLoading?: boolean;
  deleteRoleMutation: UseMutationResult<{ message: string }, Error, string>;
  setTab: (tab: "employee" | "role") => void;
}

export default function RoleGrid({
  roles,
  onEditRole,
  onDuplicateRole,
  isLoading = false,
  deleteRoleMutation,
  setTab,
}: RoleGridProps) {
  const { t } = useTranslation();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [roleToDelete, setRoleToDelete] = useState<Role | null>(null);

  const getRoleInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const handleDeleteRole = (role: Role) => {
    setRoleToDelete(role);
    setShowDeleteDialog(true);
  };

  const confirmDeleteRole = async () => {
    if (roleToDelete) {
      try {
        await deleteRoleMutation.mutateAsync(roleToDelete.id);
        // Only close on success
        setRoleToDelete(null);
        setShowDeleteDialog(false);
        toast.success(t("common.deleteSuccess"));
      } catch (error) {
        // Keep dialog open on error, let user see the error
        console.error("Failed to delete role:", error);
        toast.error(t("common.deleteError"));
      }
    }
  };

  if (isLoading) {
    return (
      <div className="mb-2 grid flex-auto grid-cols-1 gap-4 overflow-y-auto sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {Array.from({ length: 8 }).map((_, index) => (
          <div key={index} className="w-full animate-pulse rounded-lg border border-border p-4">
            <div className="h-20 rounded bg-muted"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <>
      <div className="grid flex-auto grid-cols-1 gap-4 overflow-y-auto sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {roles.map((role) => (
          <RoleCard
            key={role.id}
            role={role}
            getInitials={getRoleInitials}
            onDeleteRole={handleDeleteRole}
            onEditRole={onEditRole}
            onDuplicateRole={onDuplicateRole}
            setTab={setTab}
          />
        ))}
      </div>

      <DeleteRoleDialog
        open={showDeleteDialog}
        onOpenChange={(open) => !open && setShowDeleteDialog(false)}
        onConfirm={confirmDeleteRole}
        isLoading={deleteRoleMutation.isPending}
      />
    </>
  );
}

function RoleCard({
  role,
  getInitials,
  onDeleteRole,
  onEditRole,
  onDuplicateRole,
  setTab,
}: {
  role: Role;
  getInitials: (name: string) => string;
  onDeleteRole: (role: Role) => void;
  onEditRole: (roleId: string) => void;
  onDuplicateRole: (roleId: string) => void;
  setTab: (tab: "employee" | "role") => void;
}) {
  const { t } = useTranslation();

  const handleEditRole = () => {
    onEditRole(role.id);
  };

  const handleViewMembers = () => {
    // TODO: Implement view members functionality
    console.log("View members for role:", role.id);
    setTab("employee");
    window.history.replaceState(
      { role_list: role.id },
      "",
      `${window.location.pathname}?role_list.id=${role.id}`
    );
  };

  const handleDuplicateRole = () => {
    onDuplicateRole(role.id);
  };

  const handleDeleteRole = () => {
    onDeleteRole(role);
  };

  // Convert permissions object to display format
  const permissionsList = Object.entries(role.permissions || {})
    .filter(([_, enabled]) => enabled)
    .map(([permission]) => permission);

  return (
    <Card className="w-full border border-border p-4">
      {/* Header section with avatar, title, and action button */}
      <div className="flex h-full flex-col justify-between">
        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-2">
            <Avatar className="size-10 rounded-lg bg-muted text-muted-foreground">
              <AvatarFallback className="rounded-lg text-base font-normal">
                {getInitials(role.name)}
              </AvatarFallback>
            </Avatar>

            <div className="flex-1">
              <h3 className="text-sm font-semibold leading-[1.43] text-foreground">{role.name}</h3>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="size-6 p-1.5">
                  <MoreVertical className="size-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <PermissionDropdownItem permission="LIST_EMPLOYEE" onClick={handleViewMembers}>
                  <Eye className="mr-2 size-4" />
                  {t("pages.roleManagement.viewMembers")}
                </PermissionDropdownItem>
                <PermissionDropdownItem permission="CREATE_ROLE" onClick={handleDuplicateRole}>
                  <Copy className="mr-2 size-4" />
                  {t("pages.roleManagement.duplicateAndEdit")}
                </PermissionDropdownItem>
                <PermissionDropdownItem permission="EDIT_ROLE" onClick={handleEditRole}>
                  <Edit className="mr-2 size-4" />
                  {t("pages.roleManagement.editRole")}
                </PermissionDropdownItem>
                <DropdownMenuSeparator />
                <PermissionDropdownItem
                  permission="DELETE_ROLE"
                  onClick={handleDeleteRole}
                  className="text-destructive focus:text-destructive">
                  <Trash2 className="mr-2 size-4" />
                  {t("common.delete")}
                </PermissionDropdownItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <p className="pb-2 text-sm text-muted-foreground">{role.note}</p>
        </div>

        {/* Badge section */}
        <div className="flex gap-2">
          <StatusBadge status="default">
            {role.total_user || 0}{" "}
            {role.total_user && role.total_user > 1
              ? t("pages.roleManagement.members")
              : t("pages.roleManagement.member")}
          </StatusBadge>
          <StatusBadge status="inactive">
            {role.total_disabled_user || 0} {t("pages.roleManagement.deactivated")}
          </StatusBadge>
        </div>
      </div>
    </Card>
  );
}
