"use client";

import { useEffect, useRef, useState } from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Edit, GripVertical, MoreHorizontal, Trash2 } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Category } from "@/lib/apis/types/category";

import { useDeleteCategory, useUpdateCategory } from "../../hooks/blog-category";

interface CategoryItemProps {
  category: Category;
  onEdit: (category: Category) => void;
  onDelete: (id: string) => void;
  isEditing?: boolean;
  onCancelEdit?: () => void;
  level?: number;
}

export function CategoryItem({
  category,
  onEdit,
  onDelete,
  isEditing = false,
  onCancelEdit,
  level = 0,
}: CategoryItemProps) {
  const [editName, setEditName] = useState(category.name);
  const [showMenu, setShowMenu] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const updateCategory = useUpdateCategory();
  const deleteCategory = useDeleteCategory();

  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: category.id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  // Close menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowMenu(false);
      }
    }

    if (showMenu) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showMenu]);

  const handleSaveEdit = () => {
    if (editName.trim() && editName !== category.name) {
      updateCategory.mutate({
        id: category.id,
        data: { name: editName.trim() },
      });
    }
    onCancelEdit?.();
  };

  const handleDelete = () => {
    if (confirm("Are you sure you want to delete this category?")) {
      deleteCategory.mutate(category.id);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSaveEdit();
    } else if (e.key === "Escape") {
      onCancelEdit?.();
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`flex items-center gap-3 rounded-lg border bg-white p-3 shadow-sm transition-all hover:shadow-md ${
        isDragging ? "shadow-lg" : ""
      }`}>
      <div
        {...attributes}
        {...listeners}
        className="cursor-grab rounded p-1 hover:bg-gray-100 active:cursor-grabbing">
        <GripVertical className="size-4 text-gray-400" />
      </div>

      {/* Avatar placeholder */}
      <div className="flex size-8 items-center justify-center rounded bg-gray-200 text-xs font-medium text-gray-600">
        {category.name.substring(0, 2).toUpperCase()}
      </div>

      <div className="flex flex-1 items-center gap-2">
        {isEditing ? (
          <Input
            value={editName}
            onChange={(e) => setEditName(e.target.value)}
            onKeyDown={handleKeyPress}
            className="flex-1"
            autoFocus
          />
        ) : (
          <span className="flex-1 font-medium text-gray-900">{category.name}</span>
        )}
      </div>

      <div className="relative" ref={menuRef}>
        {isEditing ? (
          <div className="flex gap-1">
            <Button size="sm" onClick={handleSaveEdit} disabled={updateCategory.isPending}>
              Save
            </Button>
            <Button size="sm" variant="outline" onClick={onCancelEdit}>
              Cancel
            </Button>
          </div>
        ) : (
          <div className="relative">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setShowMenu(!showMenu)}
              className="p-1">
              <MoreHorizontal className="size-4" />
            </Button>

            {showMenu && (
              <div className="absolute right-0 top-full z-10 mt-1 min-w-[120px] rounded-lg border bg-white shadow-lg">
                <button
                  onClick={() => {
                    onEdit(category);
                    setShowMenu(false);
                  }}
                  className="flex w-full items-center gap-2 px-3 py-2 text-left text-sm hover:bg-gray-100">
                  <Edit className="size-4" />
                  Edit
                </button>
                <button
                  onClick={() => {
                    handleDelete();
                    setShowMenu(false);
                  }}
                  className="flex w-full items-center gap-2 px-3 py-2 text-left text-sm text-red-600 hover:bg-gray-100">
                  <Trash2 className="size-4" />
                  Delete
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
