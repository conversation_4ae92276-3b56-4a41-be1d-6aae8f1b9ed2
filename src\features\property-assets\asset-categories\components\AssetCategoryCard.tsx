"use client";

import { MoreHorizontal } from "lucide-react";

import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import type { AssetCategory } from "../types";

interface AssetCategoryCardProps {
  category: AssetCategory;
  onEdit: (category: AssetCategory) => void;
  onViewAssets: (category: AssetCategory) => void;
  onViewAnalytics: (category: AssetCategory) => void;
  onDelete: (category: AssetCategory) => void;
  getIconComponent: (iconName: string) => React.ReactNode;
}

export function AssetCategoryCard({
  category,
  onEdit,
  onViewAssets,
  onViewAnalytics,
  onDelete,
  getIconComponent,
}: AssetCategoryCardProps) {
  return (
    <Card className="border-border">
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          {/* Avatar with Icon */}
          <Avatar className="size-10 rounded-lg">
            <AvatarFallback
              className="rounded-lg"
              style={{
                backgroundColor: `${category.color}20`,
                color: category.color,
              }}>
              <div className="size-5">{getIconComponent(category.icon)}</div>
            </AvatarFallback>
          </Avatar>

          {/* Content */}
          <div className="flex-1 space-y-1">
            {/* Title and Status */}
            <div className="flex items-center gap-2">
              <h4 className="text-sm font-semibold text-foreground">{category.name}</h4>
              <Badge
                variant={category.isActive ? "default" : "secondary"}
                className="rounded-md"
                style={{
                  backgroundColor: category.isActive
                    ? "hsl(var(--success)/0.1)"
                    : "hsl(var(--muted))",
                  color: category.isActive ? "hsl(var(--success))" : "hsl(var(--muted-foreground))",
                }}>
                {category.isActive ? "Active" : "Inactive"}
              </Badge>
            </div>

            {/* Description */}
            <p className="text-xs text-muted-foreground">{category.description}</p>

            {/* Metrics */}
            <div className="flex items-center gap-4 pt-2 text-xs text-muted-foreground">
              <span>{category.assetCount} assets</span>
              <span>${(category.totalValue / 1000).toFixed(0)}k</span>
              <span>Avg: ${(category.avgValue / 1000).toFixed(0)}k</span>
            </div>
          </div>

          {/* Actions */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="size-6 p-0">
                <MoreHorizontal className="size-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit(category)}>Edit Category</DropdownMenuItem>
              <DropdownMenuItem onClick={() => onViewAssets(category)}>
                View Assets
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onViewAnalytics(category)}>
                View Analytics
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-destructive" onClick={() => onDelete(category)}>
                Delete Category
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Subcategories */}
        {category.subcategories.length > 0 && (
          <div className="mt-3 flex items-center gap-2">
            {category.subcategories.slice(0, 3).map((sub, index) => (
              <Badge key={index} variant="secondary" className="rounded-md text-xs">
                {sub}
              </Badge>
            ))}
            {category.subcategories.length > 3 && (
              <Badge variant="secondary" className="rounded-md text-xs">
                +{category.subcategories.length - 3} more
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
