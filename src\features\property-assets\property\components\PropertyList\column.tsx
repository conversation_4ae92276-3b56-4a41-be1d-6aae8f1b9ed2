"use client";

import { useCallback, useTransition } from "react";
import { useRouter } from "next/navigation";
import { Row } from "@tanstack/react-table";

import {
  DateColumn,
  ImageColumn,
  TextColumn,
} from "@/components/custom-table/container/common-column";
import ActionGroup from "@/components/data-table/action-group";
import { CustomColumn } from "@/components/data-table/data-table";
import StatusBadge from "@/components/ui/status-badge";
import { authProtectedPaths } from "@/constants/paths";
import type { Property } from "@/lib/apis/types/property_assets/property";

export const columns = (
  deletePropertyMutation: (id: string) => Promise<void>,
  isDeleting: boolean,
  t: any
): CustomColumn<Property>[] => [
  {
    id: "property",
    accessorKey: "property",
    header: t("pages.properties.headers.propertyInfo"),
    sorter: true,
    isMainColumn: true,
    sortKey: "name",
    cell: ({ row }: { row: Row<Property> }) => {
      const property = row.original;
      return (
        <div className="flex items-center gap-4 truncate">
          <ImageColumn
            src={property?.images?.[0]?.url || ""}
            alt={property?.name}
            width={0}
            iszoomable={true}
            height={0}
            sizes="100vw"
            className="flex size-10 items-center justify-center overflow-hidden rounded bg-muted object-contain"
          />
          <div className="flex flex-auto flex-col justify-between gap-1 truncate">
            <TextColumn text={property?.name} className="font-medium" />
            <TextColumn
              text={property?.owner?.name || "No owner"}
              className="text-xs text-muted-foreground"
            />
          </div>
        </div>
      );
    },
  },
  {
    id: "address",
    accessorKey: "address",
    sorter: true,
    sortKey: "address.city",
    header: t("pages.properties.headers.address"),
    cell: ({ row }: { row: Row<Property> }) => {
      const address = row?.original?.address;
      const fullAddress = address
        ? `${address.address1}, ${address.ward || ""}, ${address.province || ""} ${address.district || ""}`
        : "";
      return <TextColumn text={fullAddress} className="max-w-xs truncate" />;
    },
  },
  {
    id: "property_type",
    accessorKey: "type",
    sorter: true,
    sortKey: "type",
    header: t("pages.properties.headers.type"),
    cell: ({ row }: { row: Row<Property> }) => {
      const type = row?.original?.type;
      const typeLabel =
        type === "RESIDENTIAL"
          ? t("pages.properties.types.residential")
          : type === "COMMERCIAL"
            ? t("pages.properties.types.commercial")
            : type === "MIXED"
              ? t("pages.properties.types.mixed")
              : type;
      return <TextColumn text={typeLabel} />;
    },
  },
  // {
  //   id: "total_units",
  //   accessorKey: "total_units",
  //   sorter: true,
  //   sortKey: "total_units",
  //   header: t("pages.properties.headers.totalUnits"),
  //   cell: ({ row }: { row: Row<Property> }) => (
  //     <TextColumn text={row?.original?.total_units?.toString()} />
  //   ),
  // },
  {
    id: "status",
    accessorKey: "status",
    sorter: true,
    sortKey: "status",
    header: t("pages.properties.headers.status"),
    cell: ({ row }: { row: Row<Property> }) => {
      const status = row?.original?.status;
      return (
        <StatusBadge status={status}>
          {t(`pages.properties.status.${status?.replace(/\s/g, "")?.toLowerCase()}`) || status}
        </StatusBadge>
      );
    },
  },
  {
    id: "lastUpdated",
    accessorKey: "lastUpdated",
    sorter: true,
    sortKey: "updated_at",
    header: t("pages.properties.headers.updatedAt"),
    cell: ({ row }: { row: Row<Property> }) => <DateColumn date={row?.original?.updated_at} />,
  },
  {
    id: "actions",
    header: t("pages.properties.headers.actions"),
    cell: ({ row }: { row: Row<Property> }) => (
      <ActionCell
        deletePropertyMutation={deletePropertyMutation}
        row={row}
        isDeleting={isDeleting}
      />
    ),
  },
];

const ActionCell = ({
  deletePropertyMutation,
  row,
  isDeleting,
}: {
  deletePropertyMutation: (id: string) => Promise<void>;
  isDeleting: boolean;
  row: Row<Property>;
}) => {
  const router = useRouter();
  const property = row.original;
  const [isPending, startTransition] = useTransition();

  const handleView = useCallback(() => {
    startTransition(() => {
      router.push(authProtectedPaths.PROPERTIES_ID.replace(":id", property.id) as any);
    });
  }, [router, property?.id]);

  const handleEdit = useCallback(() => {
    router.push(authProtectedPaths.PROPERTIES_ID_EDIT.replace(":id", property?.id) as any);
  }, [router, property?.id]);

  const handleDelete = useCallback(async () => {
    return deletePropertyMutation(property?.id);
  }, [deletePropertyMutation, property?.id]);

  return (
    <ActionGroup
      actions={[
        {
          type: "view",
          onClick: handleView,
        },
        {
          type: "edit",
          onClick: handleEdit,
        },
        {
          type: "delete",
          onClick: handleDelete,
          loading: isDeleting,
        },
      ]}
    />
  );
};
