import { Metadata } from "next";

import { UnitForm } from "@/features/property-assets/unit";

export const metadata: Metadata = {
  title: "Edit Unit | OneX ERP",
  description: "Edit unit specifications, amenities, pricing, and availability settings",
};

interface EditUnitPageProps {
  params: {
    id: string;
  };
}

export default function EditUnitPage({ params }: EditUnitPageProps) {
  return <UnitForm isEditing={true} initialData={params.id} />;
}
