#!/usr/bin/env node

/**
 * Unit Form Validation Script
 *
 * This script validates the Unit CRUD forms to ensure they follow OneX ERP standards:
 * - Sticky footer pattern with proper button positioning
 * - Form validation and error handling
 * - Translation keys display properly (not raw keys)
 * - Navigation functionality works correctly
 * - Property selection dropdown loads data
 * - All form fields are accessible and functional
 */
import puppeteer from "puppeteer";

import { performLogin } from "./utils/auth.mjs";

const BASE_URL = "http://localhost:3000";

/**
 * Test unit form navigation and accessibility
 */
async function testUnitFormNavigation(page) {
  console.log("🔗 Testing Unit Form Navigation...");

  try {
    // Navigate to Units page
    await page.goto(`${BASE_URL}/property-assets/units`, { waitUntil: "networkidle0" });

    // Check if page loads correctly
    await page.waitForSelector("h1, h2", { timeout: 10000 });
    console.log("   ✅ Units page loaded successfully");

    // Check if we can navigate directly to the new unit form
    console.log("   ✅ Units page loaded, navigating to form...");

    // Navigate directly to new unit form
    await page.goto(`${BASE_URL}/property-assets/units/new`, { waitUntil: "networkidle0" });

    // Verify we're on the new unit page
    const currentUrl = page.url();
    if (!currentUrl.includes("/property-assets/units/new")) {
      throw new Error(`Expected to be on new unit page, but got: ${currentUrl}`);
    }

    console.log("   ✅ Navigation to new unit form successful");
  } catch (error) {
    console.error("   ❌ Unit form navigation test failed:", error.message);
    throw error;
  }
}

/**
 * Test unit form layout and structure
 */
async function testUnitFormLayout(page) {
  console.log("📐 Testing Unit Form Layout...");

  try {
    // Check for full-height layout
    const mainContainer = await page.$(".flex.h-screen.flex-col");
    if (!mainContainer) {
      throw new Error("Full-height layout container not found");
    }
    console.log("   ✅ Full-height layout structure correct");

    // Check for sticky footer
    const stickyFooter = await page.$(".sticky.bottom-0");
    if (!stickyFooter) {
      throw new Error("Sticky footer not found");
    }
    console.log("   ✅ Sticky footer present");

    // Check for form sections (cards)
    const formCards = await page.$$(".shadow-sm.border-gray-200");
    if (formCards.length < 4) {
      throw new Error(`Expected at least 4 form cards, found ${formCards.length}`);
    }
    console.log(`   ✅ Form cards present (${formCards.length} sections)`);

    // Check for card headers with colored indicators
    const cardHeaders = await page.$$(".w-2.h-2.rounded-full");
    if (cardHeaders.length < 4) {
      throw new Error(`Expected colored indicators, found ${cardHeaders.length}`);
    }
    console.log("   ✅ Card headers with colored indicators present");
  } catch (error) {
    console.error("   ❌ Unit form layout test failed:", error.message);
    throw error;
  }
}

/**
 * Test form fields and validation
 */
async function testUnitFormFields(page) {
  console.log("📝 Testing Unit Form Fields...");

  try {
    // Check for property selection dropdown
    const propertySelect =
      (await page.$('select[name="property_id"]')) ||
      (await page.$('[data-testid="property-select"]')) ||
      (await page.$('[role="combobox"]'));
    if (!propertySelect) {
      throw new Error("Property selection field not found");
    }
    console.log("   ✅ Property selection field present");

    // Check for unit number input
    const unitNumberInput = await page.$('input[name="unit_number"]');
    if (!unitNumberInput) {
      throw new Error("Unit number input not found");
    }
    console.log("   ✅ Unit number input present");

    // Check for unit type dropdown
    const unitTypeSelect =
      (await page.$('select[name="unit_type"]')) ||
      (await page.$('[data-testid="unit-type-select"]'));
    if (!unitTypeSelect) {
      throw new Error("Unit type selection field not found");
    }
    console.log("   ✅ Unit type selection field present");

    // Check for numeric fields
    const floorInput = await page.$('input[name="floor"]');
    const squareFootageInput = await page.$('input[name="square_footage"]');
    const rentInput = await page.$('input[name="rent_amount"]');
    const depositInput = await page.$('input[name="deposit_amount"]');

    if (!floorInput || !squareFootageInput || !rentInput || !depositInput) {
      throw new Error("Required numeric inputs missing");
    }
    console.log("   ✅ All numeric inputs present");

    // Check for amenities checkboxes
    const amenitiesSection = await page.$(".grid.grid-cols-1.md\\:grid-cols-3");
    if (!amenitiesSection) {
      throw new Error("Amenities section not found");
    }
    console.log("   ✅ Amenities section present");
  } catch (error) {
    console.error("   ❌ Unit form fields test failed:", error.message);
    throw error;
  }
}

/**
 * Test form buttons functionality
 */
async function testUnitFormButtons(page) {
  console.log("🔘 Testing Unit Form Buttons...");

  try {
    // Check for buttons in sticky footer
    const stickyFooter = await page.$(".sticky.bottom-0");
    if (!stickyFooter) {
      throw new Error("Sticky footer not found");
    }

    const buttonsInFooter = await stickyFooter.$$("button");
    if (buttonsInFooter.length < 2) {
      throw new Error("Expected at least 2 buttons in footer");
    }
    console.log(`   ✅ Found ${buttonsInFooter.length} buttons in sticky footer`);

    // Check for button styling
    const buttonClasses = await Promise.all(
      buttonsInFooter.map((btn) => btn.getAttribute("class"))
    );

    const hasOutlineButton = buttonClasses.some(
      (cls) => cls?.includes("border") || cls?.includes("outline")
    );
    const hasPrimaryButton = buttonClasses.some((cls) => cls?.includes("bg-primary"));

    if (!hasOutlineButton) {
      console.warn("   ⚠️  No outline/cancel button found");
    } else {
      console.log("   ✅ Cancel/outline button present");
    }

    if (!hasPrimaryButton) {
      console.warn("   ⚠️  No primary submit button found");
    } else {
      console.log("   ✅ Primary submit button present");
    }

    console.log("   ✅ Button styling appears correct");
  } catch (error) {
    console.error("   ❌ Unit form buttons test failed:", error.message);
    throw error;
  }
}

/**
 * Test translation keys (ensure no raw keys are displayed)
 */
async function testUnitTranslations(page) {
  console.log("🌐 Testing Unit Form Translations...");

  try {
    // Navigate back to form
    await page.goto(`${BASE_URL}/property-assets/units/new`, { waitUntil: "networkidle0" });

    // Check for translation key patterns (should not exist)
    const bodyText = await page.evaluate(() => document.body.textContent);

    const translationKeyPatterns = [
      /pages\.units\./,
      /\.title$/,
      /\.placeholder$/,
      /\.header\./,
      /{{.*}}/,
    ];

    const foundKeys = [];
    for (const pattern of translationKeyPatterns) {
      if (pattern.test(bodyText)) {
        foundKeys.push(pattern.toString());
      }
    }

    if (foundKeys.length > 0) {
      throw new Error(`Raw translation keys found: ${foundKeys.join(", ")}`);
    }

    console.log("   ✅ No raw translation keys detected");

    // Check for expected translated text
    const expectedTexts = [
      "Create Unit",
      "Basic Information",
      "Specifications",
      "Financial Information",
      "Property",
      "Unit Number",
      "Unit Type",
    ];

    const missingTexts = [];
    for (const text of expectedTexts) {
      if (!bodyText.includes(text)) {
        missingTexts.push(text);
      }
    }

    if (missingTexts.length > 0) {
      console.warn(`   ⚠️  Some expected texts not found: ${missingTexts.join(", ")}`);
    } else {
      console.log("   ✅ All expected translated texts present");
    }
  } catch (error) {
    console.error("   ❌ Unit form translations test failed:", error.message);
    throw error;
  }
}

/**
 * Test form validation behavior
 */
async function testUnitFormValidation(page) {
  console.log("✅ Testing Unit Form Validation...");

  try {
    // Try to submit empty form
    const submitButton =
      (await page.$('button[type="submit"]')) || (await page.$('button[form="unit-form"]'));
    if (!submitButton) {
      throw new Error("Submit button not found for validation test");
    }

    await submitButton.click();
    await page.waitForTimeout(1000);

    // Check for validation error messages
    const errorMessages = await page.$$(".text-red-600, .text-red-500");
    if (errorMessages.length === 0) {
      console.warn("   ⚠️  No validation errors shown for empty form");
    } else {
      console.log(`   ✅ Form validation working (${errorMessages.length} error messages shown)`);
    }

    // Check for required field indicators
    const requiredIndicators = await page.$$('.text-red-500:has-text("*")');
    if (requiredIndicators.length === 0) {
      console.warn("   ⚠️  No required field indicators found");
    } else {
      console.log(`   ✅ Required field indicators present (${requiredIndicators.length} found)`);
    }
  } catch (error) {
    console.error("   ❌ Unit form validation test failed:", error.message);
    throw error;
  }
}

/**
 * Main validation function
 */
async function validateUnitForm() {
  console.log("🏢 Starting Unit Form Validation");
  console.log("=".repeat(50));

  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: { width: 1200, height: 800 },
  });

  try {
    const page = await browser.newPage();

    // Authenticate user
    await performLogin(page);

    // Run all tests
    await testUnitFormNavigation(page);
    await testUnitFormLayout(page);
    await testUnitFormFields(page);
    await testUnitFormButtons(page);
    await testUnitTranslations(page);
    await testUnitFormValidation(page);

    console.log("\n🎉 Unit Form Validation Complete");
    console.log("=".repeat(50));
    console.log("✅ All tests passed successfully!");
    console.log("\n📋 Validated Features:");
    console.log("  • Navigation and button functionality");
    console.log("  • Sticky footer layout pattern");
    console.log("  • Form fields and validation");
    console.log("  • Translation system");
    console.log("  • OneX ERP design standards compliance");
  } catch (error) {
    console.error("\n💥 Unit Form Validation Failed");
    console.error("=".repeat(50));
    console.error("❌ Error:", error.message);
    console.error("\n🔧 Fix required before proceeding with Task 4.2");
    process.exit(1);
  } finally {
    await browser.close();
  }
}

// Run validation
validateUnitForm().catch(console.error);
