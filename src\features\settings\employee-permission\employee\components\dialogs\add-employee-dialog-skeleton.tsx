import { Skeleton } from "@/components/ui/skeleton";

export function AddEmployeeDialogSkeleton() {
  return (
    <div className="flex max-h-[95vh] max-w-[600px] flex-col overflow-y-hidden px-0 pb-0">
      {/* Form Content Skeleton */}
      <div className="flex flex-auto flex-col gap-2 overflow-auto">
        {/* Basic Info Section */}
        <div className="space-y-2">
          {/* Employee Name and Username */}
          <div className="grid grid-cols-2 gap-x-4 gap-y-2">
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-10 w-full" />
            </div>
          </div>

          {/* Email and Phone */}
          <div className="grid grid-cols-2 gap-x-4 gap-y-2">
            <div className="space-y-2">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-10 w-full" />
            </div>
          </div>

          {/* Birthday and Address */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-10 w-full" />
            </div>
          </div>
        </div>

        {/* Password Section Skeleton */}
        <div className="space-y-2">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-10 w-full" />
            </div>
          </div>
        </div>

        {/* Separator */}
        <div className="h-px bg-border" />

        {/* Role Assignments Section */}
        <div className="space-y-2">
          {/* Add Role Button Skeleton */}
          <Skeleton className="h-8 w-36" />

          {/* Role Assignment Items */}
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div className="flex items-end gap-2">
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <Skeleton className="size-8" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
