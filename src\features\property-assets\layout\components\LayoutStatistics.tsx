import { Activity, Building2, Hand, TrendingUp } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  caption?: string;
  valueColor?: string;
  icon?: React.ReactNode;
  badgeIcon?: React.ReactNode;
  badgeVariant?:
    | "sematic_default"
    | "sematic_primary"
    | "sematic_success"
    | "sematic_warning"
    | "sematic_error";
}

function StatCard({
  title,
  value,
  subtitle,
  caption,
  valueColor = "text-foreground",
  icon,
  badgeIcon,
  badgeVariant = "sematic_default",
}: StatCardProps) {
  return (
    <Card>
      <CardHeader className="px-4 pb-3 pt-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium text-foreground">{title}</CardTitle>
          {icon && <div className="opacity-50">{icon}</div>}
        </div>
      </CardHeader>
      <CardContent className="px-4 pb-4 pt-0">
        <div className="space-y-1">
          {/* Main value and badge row */}
          <div className="flex items-center gap-1">
            <div className={`text-2xl font-semibold leading-none tracking-tight ${valueColor}`}>
              {value}
            </div>
            {subtitle && (
              <Badge
                variant={badgeVariant}
                className="ml-1 flex items-center px-2 py-0.5 text-xs font-semibold">
                {badgeIcon && <span className="mr-1">{badgeIcon}</span>}
                {subtitle}
              </Badge>
            )}
          </div>

          {/* Caption */}
          {caption && (
            <div className="flex items-center gap-1 text-xs text-zinc-500">
              <span>0.00%</span>
              <span>from last month</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

interface LayoutStatisticsProps {
  totalLayouts: number;
  totalProperties: number;
  mappingProgress: number;
  mappedUnits: number;
  totalUnits: number;
  systemHealth: number;
  weeklyProgress: number;
  recentChanges: number;
}

export function LayoutStatistics({
  totalLayouts,
  totalProperties,
  mappingProgress,
  mappedUnits,
  totalUnits,
  systemHealth,
  weeklyProgress,
  recentChanges,
}: LayoutStatisticsProps) {
  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
      <StatCard
        title="Total Layouts"
        value={totalLayouts}
        subtitle={`${totalProperties} Properties`}
        caption="from last month"
        icon={<Hand className="size-6" />}
        badgeIcon={<Building2 className="size-4" />}
        badgeVariant="sematic_default"
      />

      <StatCard
        title="Mapping Progress"
        value={`${mappingProgress}%`}
        subtitle={`${mappedUnits}/${totalUnits}`}
        caption="from last month"
        valueColor="text-green-600"
        icon={<Activity className="size-6" />}
        badgeIcon={<Building2 className="size-4" />}
        badgeVariant="sematic_primary"
      />

      <StatCard
        title="System Health"
        value={`${systemHealth}%`}
        subtitle="Healthy"
        caption="from last month"
        valueColor="text-green-600"
        icon={<Activity className="size-6" />}
        badgeIcon={<Activity className="size-4" />}
        badgeVariant="sematic_success"
      />

      <StatCard
        title="Weekly Progress"
        value={`+${weeklyProgress}%`}
        subtitle={`${recentChanges} Changes`}
        caption="from last month"
        valueColor="text-blue-600"
        icon={<TrendingUp className="size-6" />}
        badgeIcon={<Activity className="size-4" />}
        badgeVariant="sematic_primary"
      />
    </div>
  );
}
