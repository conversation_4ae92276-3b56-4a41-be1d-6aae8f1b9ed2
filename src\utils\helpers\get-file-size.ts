/**
 * Calculates and formats file size from base64 data
 * @param value - Base64 string or null
 * @returns Formatted file size string (e.g., "2.5MB", "150KB") or null if invalid
 */
export const getFileSizeDisplay = (value: string | null): string | null => {
  if (!value || !value.startsWith("data:image")) return null;

  try {
    // Extract base64 data and calculate size
    const base64Data = value.split(",")[1];
    if (!base64Data) return null;

    // Calculate size in bytes (base64 is roughly 4/3 of original size)
    const sizeInBytes = Math.ceil((base64Data.length * 3) / 4);

    // Convert to KB or MB
    if (sizeInBytes >= 1024 * 1024) {
      return `${(sizeInBytes / (1024 * 1024)).toFixed(2)}MB`;
    } else if (sizeInBytes >= 1024) {
      return `${(sizeInBytes / 1024).toFixed(2)}KB`;
    } else {
      return `${sizeInBytes}B`;
    }
  } catch (error) {
    console.error("Error calculating file size:", error);
    return null;
  }
};
