"use client";

import * as React from "react";
import { useTranslation } from "react-i18next";

import { usePermission } from "@/components/provider/permission-provider";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

import { Button, ButtonProps } from "./button";

export interface PermissionButtonProps extends Omit<ButtonProps, "disabled"> {
  /** Permission key to check (e.g., 'create_user', 'edit_product') */
  permission?: string;
  /** Route to check permission for (e.g., '/users/create') */
  route?: string;
  /** Module name to check access for */
  module?: string;
  /** Custom message to show when permission is denied */
  permissionDeniedMessage?: string;
  /** Whether to show tooltip on hover when disabled */
  showTooltip?: boolean;
  /** Whether to disable the button when permission is denied */
  disableOnNoPermission?: boolean;
  /** Children to render when permission is denied (alternative to disabling) */
  fallbackChildren?: React.ReactNode;
}

const PermissionButton = React.forwardRef<HTMLButtonElement, PermissionButtonProps>(
  (
    {
      permission,
      route,
      module,
      permissionDeniedMessage,
      showTooltip = true,
      disableOnNoPermission = true,
      fallbackChildren,
      children,
      className,
      ...props
    },
    ref
  ) => {
    const { t } = useTranslation();
    const { hasPermission, hasRoutePermission, isModuleAccessible } = usePermission();
    // Determine if button should be disabled
    const isDisabled = React.useMemo(() => {
      // If no permission checks specified, button is enabled
      if (!permission && !route && !module) {
        return false;
      }

      // Check specific permission
      if (permission && !hasPermission(permission)) {
        return true;
      }

      // Check route permission
      if (route && !hasRoutePermission(route)) {
        return true;
      }

      // Check module access
      if (module && !isModuleAccessible(module)) {
        return true;
      }

      return false;
    }, [permission, route, module, hasPermission, hasRoutePermission, isModuleAccessible]);

    // Generate permission denied message
    const deniedMessage = React.useMemo(() => {
      if (permissionDeniedMessage) {
        return permissionDeniedMessage;
      }

      if (permission) {
        return t("common.noPermission", {
          permission: t(`permissions.${permission}`) || permission,
        });
      }

      if (route) {
        return t("common.noRoutePermission", { route });
      }

      if (module) {
        return t("common.noModuleAccess", { module: t(`modules.${module}`) || module });
      }

      return t("common.noPermission");
    }, [permissionDeniedMessage, permission, route, module, t]);

    // If not disabling and permission denied, show fallback content
    if (!disableOnNoPermission && isDisabled && fallbackChildren) {
      return <>{fallbackChildren}</>;
    }

    // If not disabling and permission denied, render nothing
    if (!disableOnNoPermission && isDisabled) {
      return null;
    }

    // Button content
    const buttonContent = (
      <Button ref={ref} className={className} disabled={isDisabled} {...props}>
        {children}
      </Button>
    );

    // Only show tooltip when button is disabled and showTooltip is true
    if (showTooltip && isDisabled) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="inline-block">{buttonContent}</div>
            </TooltipTrigger>
            <TooltipContent>
              <p>{deniedMessage}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }

    // Return button without tooltip when enabled
    return buttonContent;
  }
);

PermissionButton.displayName = "PermissionButton";

export { PermissionButton };
