import { authProtectedPaths, noSidebarPaths } from "@/constants/paths";

export const SETTINGS_PERMISSIONS = {
  // Dashboard
  [authProtectedPaths.DASHBOARD]: ["VIEW_DASHBOARD"],

  // General Settings
  [authProtectedPaths.SETTINGS]: ["VIEW_SETTINGS"],
  [authProtectedPaths.SETTINGS_THEME]: ["VIEW_SETTINGS"],
  [authProtectedPaths.SETTINGS_LANGUAGE_CURRENCY]: ["VIEW_SETTINGS"],
  [authProtectedPaths.SETTINGS_UNITS]: ["VIEW_SETTINGS"],
  [authProtectedPaths.SETTINGS_SHOP_INFO]: ["VIEW_SETTINGS"],
  [authProtectedPaths.SETTINGS_PROFILE_SETTINGS]: ["VIEW_SETTINGS"],

  // Property Assets
  [authProtectedPaths.PROPERTY_ASSETS_DASHBOARD]: ["VIEW_PROPERTY_ASSETS"],
  [authProtectedPaths.PROPERTIES]: ["VIEW_PROPERTY_ASSETS"],
  [authProtectedPaths.UNITS]: ["VIEW_PROPERTY_ASSETS"],
  [authProtectedPaths.CONTRACTS]: ["VIEW_PROPERTY_ASSETS"],
  [authProtectedPaths.TENANTS]: ["VIEW_PROPERTY_ASSETS"],
  [authProtectedPaths.MAINTENANCE]: ["VIEW_PROPERTY_ASSETS"],
  [authProtectedPaths.PAYMENTS]: ["VIEW_PROPERTY_ASSETS"],
  [authProtectedPaths.PROPERTY_REPORTS]: ["VIEW_PROPERTY_ASSETS"],
  [authProtectedPaths.ASSET_CATEGORIES]: ["VIEW_PROPERTY_ASSETS"],
  [authProtectedPaths.DOCUMENT_MANAGEMENT]: ["VIEW_PROPERTY_ASSETS"],
  [authProtectedPaths.PROPERTY_GALLERY]: ["VIEW_PROPERTY_ASSETS"],
  [authProtectedPaths.PROPERTY_VALUATION]: ["VIEW_PROPERTY_ASSETS"],
  [authProtectedPaths.LAYOUT_MANAGEMENT]: ["VIEW_PROPERTY_ASSETS"],
  [authProtectedPaths.FINANCIAL]: ["VIEW_PROPERTY_ASSETS"],

  // Integration
  [authProtectedPaths.CHANNELS]: ["VIEW_INTEGRATION"],
  [authProtectedPaths.PRODUCT_MAPPING]: ["VIEW_INTEGRATION"],
  [authProtectedPaths.SYNC_RECORDS]: ["VIEW_INTEGRATION"],
  [authProtectedPaths.FETCH_EVENTS]: ["VIEW_INTEGRATION"],

  // Subscription
  [authProtectedPaths.SUBSCRIPTION]: ["VIEW_SUBSCRIPTION"],

  // Installation & Onboarding
  [authProtectedPaths.INSTALLATION]: ["VIEW_INSTALLATION"],
  [noSidebarPaths.ONBOARDING]: ["VIEW_ONBOARDING"],
} as const;

export type SettingsPermissionKey = keyof typeof SETTINGS_PERMISSIONS;
export type SettingsPermissionValue = (typeof SETTINGS_PERMISSIONS)[SettingsPermissionKey];
