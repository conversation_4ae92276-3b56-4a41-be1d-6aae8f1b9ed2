import Image from "next/image";
import { useTranslation } from "react-i18next";

import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

import { AVAILABLE_CURRENCIES } from "../constants";

interface CurrencySelectorProps {
  selectedCurrencies: string[];
  displayedCurrencies: string[];
  onCurrencyToggle: (currencyCode: string) => void;
  onRemoveCurrency: (currencyCode: string) => void;
}

export const CurrencySelector = ({
  selectedCurrencies,
  displayedCurrencies,
  onCurrencyToggle,
  onRemoveCurrency,
}: CurrencySelectorProps) => {
  const { t } = useTranslation();

  return (
    <div className="space-y-0">
      <RadioGroup
        key={selectedCurrencies[0]} // Force re-render when selection changes
        value={selectedCurrencies[0]}
        onValueChange={onCurrencyToggle}
        className="space-y-0">
        {AVAILABLE_CURRENCIES.filter((curr) => displayedCurrencies.includes(curr.code)).map(
          (currency, index) => (
            <div
              key={currency.code}
              className={`flex items-center justify-between py-2 ${
                index < displayedCurrencies.length - 1 ? "border-b border-border" : ""
              }`}>
              <div className="flex items-center gap-6">
                <RadioGroupItem
                  value={currency.code}
                  id={`currency-${currency.code}`}
                  className="border-primary text-primary"
                />
                <div className="flex items-center gap-2">
                  <Image src={currency.flag} alt={currency.name} width={16} height={16} />
                  <div className="flex items-center gap-2">
                    <span className="text-sm leading-[1.43] text-foreground">{currency.code}</span>
                    <span className="text-sm leading-[1.43] text-muted-foreground">
                      {currency.symbol}
                    </span>
                  </div>
                </div>
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => onRemoveCurrency(currency.code)}
                className="px-3 py-2 text-sm font-medium leading-[1.71] text-muted-foreground hover:text-foreground"
                disabled={displayedCurrencies.length === 1}>
                {t("pages.settings.language.remove")}
              </Button>
            </div>
          )
        )}
      </RadioGroup>
    </div>
  );
};
