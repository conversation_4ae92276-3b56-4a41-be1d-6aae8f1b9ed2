"use client";

import { useTranslation } from "react-i18next";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

export const FAQSection = () => {
  const { t } = useTranslation();

  const faqs = [
    {
      question: t("pages.subscription.faq.q1"),
      answer: t("pages.subscription.faq.a1"),
    },
    {
      question: t("pages.subscription.faq.q2"),
      answer: t("pages.subscription.faq.a2"),
    },
    {
      question: t("pages.subscription.faq.q3"),
      answer: t("pages.subscription.faq.a3"),
    },
    {
      question: t("pages.subscription.faq.q4"),
      answer: t("pages.subscription.faq.a4"),
    },
    {
      question: t("pages.subscription.faq.q5"),
      answer: t("pages.subscription.faq.a5"),
    },
  ];

  return (
    <div className="flex flex-col items-center gap-4">
      <div className="flex flex-col gap-2 text-center">
        <h2 className="text-3xl font-bold leading-tight tracking-tight text-foreground">
          {t("pages.subscription.faq.title")}
        </h2>
        <div className="flex flex-col">
          <p className="text-sm text-muted-foreground">{t("pages.subscription.faq.description")}</p>
          <p className="text-sm text-muted-foreground">
            {t("pages.subscription.faq.description2")}
          </p>
        </div>
      </div>

      <Accordion type="single" collapsible className="w-full lg:max-w-2xl">
        {faqs.map((faq, index) => (
          <AccordionItem key={index} value={`item-${index}`}>
            <AccordionTrigger className="text-left text-base">{faq.question}</AccordionTrigger>
            <AccordionContent>{faq.answer}</AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
};
