"use client";

import { use<PERSON><PERSON>back, useEffect, use<PERSON>emo, useRef, useState } from "react";
import dynamic from "next/dynamic";
import type Konva from "konva";
import {
  AlertTriangle,
  AlignHorizontalJustifyCenter,
  AlignVerticalJustifyCenter,
  ArrowRight,
  Circle as CircleIcon,
  Copy,
  Eye,
  EyeOff,
  Grid3X3,
  Layers,
  Minus,
  MoreHorizontal,
  MousePointer,
  Move,
  RotateCw,
  Square,
  Trash2,
  Type,
  X,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import {
  Arrow,
  Circle,
  Ellipse,
  Group,
  Image as KonvaImage,
  Layer,
  Line,
  Rect,
  Stage,
  Text,
} from "react-konva";

import { Badge } from "@/components/ui/badge";
// UI Components
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

// Types
import type { Unit } from "../../types";
import type { LayoutData, UnitPosition } from "./LayoutViewer";

interface KonvaUnitShape extends UnitPosition {
  shape: "rectangle" | "circle" | "polygon" | "ellipse" | "line" | "arrow" | "text";
  fill: string;
  stroke: string;
  strokeWidth: number;
  opacity: number;
  label: string;
  fontSize: number;
  fontFamily: string;
  textColor: string;
  visible: boolean;
  rotation?: number;
  points?: number[]; // For line, arrow, and polygon shapes
  isComplete?: boolean; // For polygon shapes to track completion
}

interface LayoutOverlayProps {
  layoutImage: string;
  units: Unit[];
  initialShapes?: KonvaUnitShape[];
  onShapesChange?: (shapes: KonvaUnitShape[]) => void;
  onUnitAssign?: (shapeId: string, unitId: string) => void;
  isEditable?: boolean;
  width: number;
  height: number;
  className?: string;
}

type DrawingTool =
  | "select"
  | "rectangle"
  | "circle"
  | "polygon"
  | "ellipse"
  | "line"
  | "arrow"
  | "text";

export function LayoutOverlay({
  layoutImage,
  units,
  initialShapes = [],
  onShapesChange,
  onUnitAssign,
  isEditable = true,
  width,
  height,
  className = "",
}: LayoutOverlayProps) {
  const { t } = useTranslation();

  // Refs
  const stageRef = useRef<Konva.Stage>(null);
  const layerRef = useRef<Konva.Layer>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  // State
  const [image, setImage] = useState<HTMLImageElement | null>(null);
  const [shapes, setShapes] = useState<KonvaUnitShape[]>(initialShapes);
  const [selectedShapeId, setSelectedShapeId] = useState<string | null>(null);
  const [drawingTool, setDrawingTool] = useState<DrawingTool>("select");
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentPath, setCurrentPath] = useState<{ x: number; y: number }[]>([]);
  const [currentPolygonId, setCurrentPolygonId] = useState<string | null>(null);
  const [layersVisible, setLayersVisible] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [snapToGrid, setSnapToGrid] = useState(false);
  const [gridSize, setGridSize] = useState(20);
  const [showGrid, setShowGrid] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const [showOverlapWarning, setShowOverlapWarning] = useState(false);
  const overlapWarningTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Load image with error handling
  useEffect(() => {
    if (layoutImage) {
      setIsLoading(true);
      setError(null);

      const img = new window.Image();
      img.crossOrigin = "anonymous";

      img.onload = () => {
        setImage(img);
        setIsLoading(false);
      };

      img.onerror = () => {
        setError("Failed to load layout image");
        setIsLoading(false);
      };

      img.src = layoutImage;
      (imageRef as any).current = img;
    }
  }, [layoutImage]);

  // Notify parent of shape changes
  const onShapesChangeRef = useRef(onShapesChange);
  onShapesChangeRef.current = onShapesChange;

  useEffect(() => {
    onShapesChangeRef.current?.(shapes);
  }, [shapes]);

  // Get unit status color
  const getUnitStatusColor = useCallback(
    (unitId: string) => {
      const unit = units.find((u) => u.id === unitId);
      if (!unit) return "#6b7280"; // gray-500

      switch (unit.status) {
        case "available":
          return "#10b981"; // green-500
        case "occupied":
          return "#3b82f6"; // blue-500
        case "maintenance":
          return "#ef4444"; // red-500
        case "inactive":
          return "#6b7280"; // gray-500
        default:
          return "#6b7280";
      }
    },
    [units]
  );

  // Handle mouse events for drawing
  const handleMouseDown = useCallback(
    (e: Konva.KonvaEventObject<MouseEvent>) => {
      if (!isEditable || drawingTool === "select") return;

      const pos = e.target.getStage()?.getPointerPosition();
      if (!pos) return;

      if (drawingTool === "polygon") {
        if (!currentPolygonId) {
          // Start new polygon - create polygon shape
          const polygonId = `shape_${Date.now()}`;
          const newPolygon: KonvaUnitShape = {
            id: polygonId,
            unitId: "",
            x: pos.x,
            y: pos.y,
            width: 0,
            height: 0,
            shape: "polygon",
            fill: "#3b82f6",
            stroke: "#1e40af",
            strokeWidth: 2,
            opacity: 0.7,
            label: "New Unit",
            fontSize: 12,
            fontFamily: "Arial",
            textColor: "#ffffff",
            visible: true,
            points: [0, 0], // First point at origin
            isComplete: false,
          };

          setShapes((prev) => [...prev, newPolygon]);
          setCurrentPolygonId(polygonId);
          setCurrentPath([{ x: 0, y: 0 }]); // Start with origin point
          setSelectedShapeId(polygonId);
          // TODO: Add proper logging for polygon creation
        } else {
          // Add point to existing polygon
          // TODO: Add proper logging for polygon point addition
          setShapes((prev) => {
            return prev.map((shape) => {
              if (shape.id === currentPolygonId) {
                const relativeX = pos.x - shape.x;
                const relativeY = pos.y - shape.y;
                const newPoints = [...(shape.points || []), relativeX, relativeY];
                // TODO: Add proper logging for polygon point updates
                return {
                  ...shape,
                  points: newPoints,
                };
              }
              return shape;
            });
          });

          // Update current path for visual feedback - need to use functional update to get fresh polygon data
          setCurrentPath((prev) => {
            // We need to calculate relative position based on the polygon's origin
            // which is stored in the shapes array, so we'll use the stored origin
            const relativeX = pos.x - (shapes.find((s) => s.id === currentPolygonId)?.x || pos.x);
            const relativeY = pos.y - (shapes.find((s) => s.id === currentPolygonId)?.y || pos.y);
            return [...prev, { x: relativeX, y: relativeY }];
          });
        }
        return;
      }

      // For non-polygon tools, set drawing state
      setIsDrawing(true);

      // Create new shape based on drawing tool type
      let newShape: KonvaUnitShape;

      switch (drawingTool) {
        case "line":
          newShape = {
            id: `shape_${Date.now()}`,
            unitId: "",
            x: pos.x,
            y: pos.y,
            width: 0,
            height: 0,
            shape: "line",
            fill: "transparent",
            stroke: "#1e40af",
            strokeWidth: 3,
            opacity: 1,
            label: "",
            fontSize: 12,
            fontFamily: "Arial",
            textColor: "#1e40af",
            visible: true,
            points: [0, 0, 0, 0],
          };
          break;
        case "arrow":
          newShape = {
            id: `shape_${Date.now()}`,
            unitId: "",
            x: pos.x,
            y: pos.y,
            width: 0,
            height: 0,
            shape: "arrow",
            fill: "#1e40af",
            stroke: "#1e40af",
            strokeWidth: 3,
            opacity: 1,
            label: "",
            fontSize: 12,
            fontFamily: "Arial",
            textColor: "#1e40af",
            visible: true,
            points: [0, 0, 0, 0],
          };
          break;
        case "text":
          newShape = {
            id: `shape_${Date.now()}`,
            unitId: "",
            x: Math.max(0, Math.min(pos.x, width - 100)),
            y: Math.max(0, Math.min(pos.y, height - 30)),
            width: 100,
            height: 30,
            shape: "text",
            fill: "transparent",
            stroke: "transparent",
            strokeWidth: 0,
            opacity: 1,
            label: "Text",
            fontSize: 16,
            fontFamily: "Arial",
            textColor: "#000000",
            visible: true,
          };
          break;
        default:
          // Rectangle, circle, ellipse
          newShape = {
            id: `shape_${Date.now()}`,
            unitId: "",
            x: Math.max(0, Math.min(pos.x, width - 50)),
            y: Math.max(0, Math.min(pos.y, height - 50)),
            width: ["rectangle", "ellipse"].includes(drawingTool) ? 0 : 50,
            height: ["rectangle", "ellipse"].includes(drawingTool) ? 0 : 50,
            shape: drawingTool as "rectangle" | "circle" | "ellipse",
            fill: "#3b82f6",
            stroke: "#1e40af",
            strokeWidth: 2,
            opacity: 0.7,
            label: "New Unit",
            fontSize: 12,
            fontFamily: "Arial",
            textColor: "#ffffff",
            visible: true,
          };
      }

      setShapes((prev) => [...prev, newShape]);
      setSelectedShapeId(newShape.id);
    },
    [isEditable, drawingTool, width, height, currentPolygonId, shapes]
  );

  const handleMouseMove = useCallback(
    (e: Konva.KonvaEventObject<MouseEvent>) => {
      if (!isDrawing || drawingTool === "select" || drawingTool === "polygon") return;

      const pos = e.target.getStage()?.getPointerPosition();
      if (!pos || !selectedShapeId) return;

      setShapes((prev) =>
        prev.map((shape) => {
          if (shape.id === selectedShapeId) {
            if (shape.shape === "line" || shape.shape === "arrow") {
              // Update line/arrow points
              const endX = pos.x - shape.x;
              const endY = pos.y - shape.y;
              return {
                ...shape,
                points: [0, 0, endX, endY],
                width: Math.abs(endX),
                height: Math.abs(endY),
              };
            } else if (shape.shape === "text") {
              // Text shapes don't resize during drawing
              return shape;
            } else {
              // Rectangle, circle, ellipse
              const newWidth = Math.abs(pos.x - shape.x);
              const newHeight = Math.abs(pos.y - shape.y);

              return {
                ...shape,
                width: Math.min(newWidth, width - shape.x),
                height: Math.min(newHeight, height - shape.y),
              };
            }
          }
          return shape;
        })
      );
    },
    [isDrawing, drawingTool, selectedShapeId, width, height]
  );

  const handleMouseUp = useCallback(() => {
    setIsDrawing(false);
  }, []);

  // Handle double click to finish polygon or right-click
  const handleDoubleClick = useCallback(() => {
    if (drawingTool === "polygon" && currentPolygonId && currentPath.length >= 3) {
      // Complete the polygon by marking it as complete
      setShapes((prev) =>
        prev.map((shape) => {
          if (shape.id === currentPolygonId) {
            // Calculate bounding box for the completed polygon
            const points = shape.points || [];
            if (points.length >= 6) {
              // At least 3 points (x,y pairs)
              const xs = points.filter((_, i) => i % 2 === 0);
              const ys = points.filter((_, i) => i % 2 === 1);
              const minX = Math.min(...xs);
              const minY = Math.min(...ys);
              const maxX = Math.max(...xs);
              const maxY = Math.max(...ys);

              // Calculate center offset to center the polygon within its bounding box
              const centerX = (minX + maxX) / 2;
              const centerY = (minY + maxY) / 2;
              const width = maxX - minX;
              const height = maxY - minY;

              // Adjust points to be centered within the bounding box
              const centeredPoints = points.map((point, index) => {
                if (index % 2 === 0) {
                  // X coordinate - center it
                  return point - centerX + width / 2;
                } else {
                  // Y coordinate - center it
                  return point - centerY + height / 2;
                }
              });

              return {
                ...shape,
                width,
                height,
                points: centeredPoints,
                isComplete: true,
              };
            }
          }
          return shape;
        })
      );

      // Reset polygon drawing state
      setCurrentPolygonId(null);
      setCurrentPath([]);
      setIsDrawing(false);
    }
  }, [drawingTool, currentPolygonId, currentPath]);

  // Handle right-click to complete polygon
  const handleRightClick = useCallback(
    (e: Konva.KonvaEventObject<MouseEvent>) => {
      e.evt.preventDefault();
      if (drawingTool === "polygon" && currentPolygonId) {
        handleDoubleClick();
      }
    },
    [drawingTool, currentPolygonId, handleDoubleClick]
  );

  // Handle escape key to cancel polygon drawing
  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (e.key === "Escape" && drawingTool === "polygon" && currentPolygonId) {
        // Cancel polygon drawing
        setShapes((prev) => prev.filter((shape) => shape.id !== currentPolygonId));
        setCurrentPolygonId(null);
        setCurrentPath([]);
        setIsDrawing(false);
        setSelectedShapeId(null);
      }
    },
    [drawingTool, currentPolygonId]
  );

  // Add keyboard event listener
  useEffect(() => {
    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [handleKeyDown]);

  // Handle shape click
  const handleShapeClick = useCallback(
    (shapeId: string) => {
      if (drawingTool === "select") {
        setSelectedShapeId(selectedShapeId === shapeId ? null : shapeId);
      }
    },
    [drawingTool, selectedShapeId]
  );

  // Check if shape is within bounds
  const isWithinBounds = useCallback(
    (shape: KonvaUnitShape) => {
      return (
        shape.x >= 0 &&
        shape.y >= 0 &&
        shape.x + shape.width <= width &&
        shape.y + shape.height <= height
      );
    },
    [width, height]
  );

  // Snap to grid helper
  const snapToGridHelper = useCallback(
    (value: number) => {
      if (!snapToGrid) return value;
      return Math.round(value / gridSize) * gridSize;
    },
    [snapToGrid, gridSize]
  );

  // Handle shape transform with boundary checking and grid snapping
  const handleShapeTransform = useCallback(
    (shapeId: string, newAttrs: Partial<KonvaUnitShape>) => {
      setShapes((prev) =>
        prev.map((shape) => {
          if (shape.id === shapeId) {
            const updatedShape = { ...shape, ...newAttrs };

            // Apply grid snapping to position
            if (newAttrs.x !== undefined || newAttrs.y !== undefined) {
              updatedShape.x = snapToGridHelper(
                Math.max(0, Math.min(updatedShape.x, width - updatedShape.width))
              );
              updatedShape.y = snapToGridHelper(
                Math.max(0, Math.min(updatedShape.y, height - updatedShape.height))
              );
            }

            // Apply grid snapping to size
            if (newAttrs.width !== undefined) {
              updatedShape.width = Math.max(
                gridSize,
                Math.min(snapToGridHelper(updatedShape.width), width - updatedShape.x)
              );
            }
            if (newAttrs.height !== undefined) {
              updatedShape.height = Math.max(
                gridSize,
                Math.min(snapToGridHelper(updatedShape.height), height - updatedShape.y)
              );
            }

            return updatedShape;
          }
          return shape;
        })
      );
    },
    [width, height, snapToGridHelper, gridSize]
  );

  // Handle shape deletion
  const handleDeleteShape = useCallback((shapeId: string) => {
    setShapes((prev) => prev.filter((shape) => shape.id !== shapeId));
    setSelectedShapeId(null);
  }, []);

  // Handle shape duplication
  const handleDuplicateShape = useCallback(
    (shapeId: string) => {
      const shape = shapes.find((s) => s.id === shapeId);
      if (shape) {
        const newShape = {
          ...shape,
          id: `shape_${Date.now()}`,
          x: shape.x + 20,
          y: shape.y + 20,
          label: `${shape.label} Copy`,
        };
        setShapes((prev) => [...prev, newShape]);
        setSelectedShapeId(newShape.id);
      }
    },
    [shapes]
  );

  // Check for overlapping shapes
  const checkOverlaps = useCallback((shapes: KonvaUnitShape[]) => {
    const overlaps: { shape1: string; shape2: string }[] = [];

    for (let i = 0; i < shapes.length; i++) {
      for (let j = i + 1; j < shapes.length; j++) {
        const shape1 = shapes[i];
        const shape2 = shapes[j];

        // Simple bounding box overlap detection
        const overlap = !(
          shape1.x + shape1.width < shape2.x ||
          shape2.x + shape2.width < shape1.x ||
          shape1.y + shape1.height < shape2.y ||
          shape2.y + shape2.height < shape1.y
        );

        if (overlap) {
          overlaps.push({ shape1: shape1.id, shape2: shape2.id });
        }
      }
    }

    return overlaps;
  }, []);

  // Get overlapping shapes
  const overlappingShapes = useMemo(() => {
    return checkOverlaps(shapes);
  }, [shapes, checkOverlaps]);

  // Handle overlap warning visibility
  useEffect(() => {
    // Clear existing timeout
    if (overlapWarningTimeoutRef.current) {
      clearTimeout(overlapWarningTimeoutRef.current);
      overlapWarningTimeoutRef.current = null;
    }

    if (overlappingShapes.length > 0) {
      setShowOverlapWarning(true);

      // Auto-hide after 5 seconds
      overlapWarningTimeoutRef.current = setTimeout(() => {
        setShowOverlapWarning(false);
        overlapWarningTimeoutRef.current = null;
      }, 5000);
    } else {
      setShowOverlapWarning(false);
    }

    // Cleanup function
    return () => {
      if (overlapWarningTimeoutRef.current) {
        clearTimeout(overlapWarningTimeoutRef.current);
        overlapWarningTimeoutRef.current = null;
      }
    };
  }, [overlappingShapes.length]);

  // Handle dismiss overlap warning
  const handleDismissOverlapWarning = useCallback(() => {
    setShowOverlapWarning(false);
    if (overlapWarningTimeoutRef.current) {
      clearTimeout(overlapWarningTimeoutRef.current);
      overlapWarningTimeoutRef.current = null;
    }
  }, []);

  // Handle unit assignment
  const handleUnitAssignment = useCallback(
    (shapeId: string, unitId: string) => {
      const unit = units.find((u) => u.id === unitId);
      if (unit) {
        const color = getUnitStatusColor(unitId);
        setShapes((prev) =>
          prev.map((shape) =>
            shape.id === shapeId
              ? {
                  ...shape,
                  unitId,
                  label: unit.unit_number,
                  fill: color,
                  stroke: color,
                }
              : shape
          )
        );
        onUnitAssign?.(shapeId, unitId);
      }
    },
    [units, getUnitStatusColor, onUnitAssign]
  );

  // Drag and drop handlers for external units
  const handleDragOver = useCallback(
    (e: React.DragEvent) => {
      if (!isEditable) return;
      e.preventDefault();
      e.dataTransfer.dropEffect = "copy";
      setIsDragOver(true);
    },
    [isEditable]
  );

  const handleDragLeave = useCallback(
    (e: React.DragEvent) => {
      if (!isEditable) return;
      // Check if we're leaving the container, not just entering a child
      const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
      const { clientX, clientY } = e;
      if (
        clientX < rect.left ||
        clientX > rect.right ||
        clientY < rect.top ||
        clientY > rect.bottom
      ) {
        setIsDragOver(false);
      }
    },
    [isEditable]
  );

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      if (!isEditable) return;
      e.preventDefault();
      setIsDragOver(false);

      try {
        const data = JSON.parse(e.dataTransfer.getData("text/plain"));
        const { unitId, unitNumber, unitType, status } = data;

        // Get drop position relative to the stage
        const stage = stageRef.current;
        if (!stage) return;

        const stageBox = stage.container().getBoundingClientRect();
        const x = e.clientX - stageBox.left;
        const y = e.clientY - stageBox.top;

        // Get unit color based on status
        const unit = units.find((u) => u.id === unitId);
        const color = getUnitStatusColor(unitId);

        // Create new shape for the dropped unit
        const newShape: KonvaUnitShape = {
          id: `shape_${Date.now()}_${unitId}`,
          unitId,
          x: snapToGridHelper(Math.max(0, Math.min(x - 50, width - 100))),
          y: snapToGridHelper(Math.max(0, Math.min(y - 30, height - 60))),
          width: 100,
          height: 60,
          shape: "rectangle",
          fill: color,
          stroke: color,
          strokeWidth: 2,
          opacity: 0.8,
          label: unitNumber,
          fontSize: 12,
          fontFamily: "Arial",
          textColor: "#ffffff",
          visible: true,
        };

        setShapes((prev) => [...prev, newShape]);
        setSelectedShapeId(newShape.id);
        onUnitAssign?.(newShape.id, unitId);
      } catch (error) {
        // TODO: Implement proper error logging for unit drop operations
      }
    },
    [isEditable, units, getUnitStatusColor, snapToGridHelper, width, height, onUnitAssign]
  );

  // Alignment functions
  const alignShapesHorizontally = useCallback(() => {
    if (!selectedShapeId) return;

    const selectedShape = shapes.find((s) => s.id === selectedShapeId);
    if (!selectedShape) return;

    const centerY = selectedShape.y + selectedShape.height / 2;

    setShapes((prev) =>
      prev.map((shape) =>
        shape.id !== selectedShapeId ? { ...shape, y: centerY - shape.height / 2 } : shape
      )
    );
  }, [selectedShapeId, shapes]);

  const alignShapesVertically = useCallback(() => {
    if (!selectedShapeId) return;

    const selectedShape = shapes.find((s) => s.id === selectedShapeId);
    if (!selectedShape) return;

    const centerX = selectedShape.x + selectedShape.width / 2;

    setShapes((prev) =>
      prev.map((shape) =>
        shape.id !== selectedShapeId ? { ...shape, x: centerX - shape.width / 2 } : shape
      )
    );
  }, [selectedShapeId, shapes]);

  // Get selected shape
  const selectedShape = shapes.find((s) => s.id === selectedShapeId);

  // Render shape based on type
  const renderShape = useCallback(
    (shape: KonvaUnitShape) => {
      const isSelected = shape.id === selectedShapeId;
      const isOverlapping = overlappingShapes.some(
        (overlap) => overlap.shape1 === shape.id || overlap.shape2 === shape.id
      );

      const commonProps = {
        x: shape.x,
        y: shape.y,
        fill: isOverlapping ? "#ef4444" : shape.fill, // Red for overlapping shapes
        stroke: isOverlapping ? "#dc2626" : shape.stroke,
        strokeWidth: isOverlapping ? 3 : shape.strokeWidth,
        opacity: shape.opacity,
        visible: shape.visible,
        draggable: isEditable && drawingTool === "select",
        onClick: () => handleShapeClick(shape.id),
        onDragEnd: (e: Konva.KonvaEventObject<DragEvent>) => {
          handleShapeTransform(shape.id, {
            x: e.target.x(),
            y: e.target.y(),
          });
        },
      };

      const shapeElement = (() => {
        switch (shape.shape) {
          case "rectangle":
            return (
              <Rect
                {...commonProps}
                width={shape.width}
                height={shape.height}
                rotation={shape.rotation || 0}
              />
            );
          case "circle":
            return (
              <Circle
                fill={isOverlapping ? "#ef4444" : shape.fill}
                stroke={isOverlapping ? "#dc2626" : shape.stroke}
                strokeWidth={isOverlapping ? 3 : shape.strokeWidth}
                opacity={shape.opacity}
                visible={shape.visible}
                draggable={isEditable && drawingTool === "select"}
                onClick={() => handleShapeClick(shape.id)}
                onDragEnd={(e: Konva.KonvaEventObject<DragEvent>) => {
                  handleShapeTransform(shape.id, {
                    x: e.target.x() - shape.width / 2,
                    y: e.target.y() - shape.height / 2,
                  });
                }}
                x={shape.x + shape.width / 2}
                y={shape.y + shape.height / 2}
                radius={Math.min(shape.width, shape.height) / 2}
              />
            );
          case "ellipse":
            return (
              <Ellipse
                fill={isOverlapping ? "#ef4444" : shape.fill}
                stroke={isOverlapping ? "#dc2626" : shape.stroke}
                strokeWidth={isOverlapping ? 3 : shape.strokeWidth}
                opacity={shape.opacity}
                visible={shape.visible}
                draggable={isEditable && drawingTool === "select"}
                onClick={() => handleShapeClick(shape.id)}
                onDragEnd={(e: Konva.KonvaEventObject<DragEvent>) => {
                  handleShapeTransform(shape.id, {
                    x: e.target.x() - shape.width / 2,
                    y: e.target.y() - shape.height / 2,
                  });
                }}
                x={shape.x + shape.width / 2}
                y={shape.y + shape.height / 2}
                radiusX={shape.width / 2}
                radiusY={shape.height / 2}
                rotation={shape.rotation || 0}
              />
            );
          case "line":
            return (
              <Line
                {...commonProps}
                points={shape.points || [0, 0, shape.width, shape.height]}
                lineCap="round"
                lineJoin="round"
              />
            );
          case "arrow":
            return (
              <Arrow
                {...commonProps}
                points={shape.points || [0, 0, shape.width, shape.height]}
                pointerLength={10}
                pointerWidth={10}
                lineCap="round"
                lineJoin="round"
              />
            );
          case "polygon":
            return (
              <Line
                {...commonProps}
                points={shape.points || []}
                closed={shape.isComplete}
                lineCap="round"
                lineJoin="round"
              />
            );
          case "text":
            return null; // Text is handled separately below
          default:
            return <Rect {...commonProps} width={shape.width} height={shape.height} />;
        }
      })();

      return (
        <Group key={shape.id}>
          {shapeElement}
          {shape.shape === "text" ? (
            <Text
              x={shape.x}
              y={shape.y}
              text={shape.label}
              fontSize={shape.fontSize}
              fontFamily={shape.fontFamily}
              fill={shape.textColor}
              align="left"
              verticalAlign="top"
              visible={shape.visible}
              draggable={isEditable && drawingTool === "select"}
              onClick={() => handleShapeClick(shape.id)}
              onDragEnd={(e: Konva.KonvaEventObject<DragEvent>) => {
                handleShapeTransform(shape.id, {
                  x: e.target.x(),
                  y: e.target.y(),
                });
              }}
            />
          ) : shape.label && shape.shape !== "line" && shape.shape !== "arrow" ? (
            <Text
              x={
                shape.shape === "circle" || shape.shape === "ellipse" || shape.shape === "polygon"
                  ? shape.x + shape.width / 2 // For centered shapes, position text at their visual center
                  : shape.x + shape.width / 2 // For rectangles, same calculation
              }
              y={
                shape.shape === "circle" || shape.shape === "ellipse" || shape.shape === "polygon"
                  ? shape.y + shape.height / 2 // For centered shapes, position text at their visual center
                  : shape.y + shape.height / 2 // For rectangles, same calculation
              }
              text={shape.label}
              fontSize={shape.fontSize}
              fontFamily={shape.fontFamily}
              fill={shape.textColor}
              align="center"
              verticalAlign="middle"
              offsetX={(shape.label.length * shape.fontSize) / 4}
              offsetY={shape.fontSize / 2}
              visible={shape.visible}
              listening={false} // Prevent text from blocking drag interactions
            />
          ) : null}
          {isSelected && (
            <>
              {/* Selection border */}
              <Rect
                x={
                  shape.shape === "circle" || shape.shape === "ellipse"
                    ? shape.x - 2 // For centered shapes, the selection box should match the bounding box
                    : shape.x - 2 // For rectangles, same calculation
                }
                y={
                  shape.shape === "circle" || shape.shape === "ellipse"
                    ? shape.y - 2 // For centered shapes, the selection box should match the bounding box
                    : shape.y - 2 // For rectangles, same calculation
                }
                width={shape.width + 4}
                height={shape.height + 4}
                stroke="#3b82f6"
                strokeWidth={2}
                dash={[4, 4]}
                fill="transparent"
                listening={false} // Don't block interactions
              />
              {/* Resize handles */}
              {[
                // Top-left
                { x: shape.x - 4, y: shape.y - 4, cursor: "nw-resize" },
                // Top-right
                { x: shape.x + shape.width - 4, y: shape.y - 4, cursor: "ne-resize" },
                // Bottom-left
                { x: shape.x - 4, y: shape.y + shape.height - 4, cursor: "sw-resize" },
                // Bottom-right
                {
                  x: shape.x + shape.width - 4,
                  y: shape.y + shape.height - 4,
                  cursor: "se-resize",
                },
                // Top-center
                { x: shape.x + shape.width / 2 - 4, y: shape.y - 4, cursor: "n-resize" },
                // Bottom-center
                {
                  x: shape.x + shape.width / 2 - 4,
                  y: shape.y + shape.height - 4,
                  cursor: "s-resize",
                },
                // Left-center
                { x: shape.x - 4, y: shape.y + shape.height / 2 - 4, cursor: "w-resize" },
                // Right-center
                {
                  x: shape.x + shape.width - 4,
                  y: shape.y + shape.height / 2 - 4,
                  cursor: "e-resize",
                },
              ].map((handle, index) => (
                <Rect
                  key={`handle-${index}`}
                  x={handle.x}
                  y={handle.y}
                  width={8}
                  height={8}
                  fill="#3b82f6"
                  stroke="#1e40af"
                  strokeWidth={1}
                  draggable={isEditable}
                  onDragMove={(e) => {
                    // Handle resize logic based on which handle is being dragged
                    const handleType = index;
                    const newX = e.target.x() + 4; // Adjust for handle offset
                    const newY = e.target.y() + 4;

                    let newAttrs: Partial<KonvaUnitShape> = {};

                    switch (handleType) {
                      case 0: // Top-left
                        newAttrs = {
                          x: newX,
                          y: newY,
                          width: shape.width + (shape.x - newX),
                          height: shape.height + (shape.y - newY),
                        };
                        break;
                      case 1: // Top-right
                        newAttrs = {
                          y: newY,
                          width: newX - shape.x,
                          height: shape.height + (shape.y - newY),
                        };
                        break;
                      case 2: // Bottom-left
                        newAttrs = {
                          x: newX,
                          width: shape.width + (shape.x - newX),
                          height: newY - shape.y,
                        };
                        break;
                      case 3: // Bottom-right
                        newAttrs = {
                          width: newX - shape.x,
                          height: newY - shape.y,
                        };
                        break;
                      case 4: // Top-center
                        newAttrs = {
                          y: newY,
                          height: shape.height + (shape.y - newY),
                        };
                        break;
                      case 5: // Bottom-center
                        newAttrs = {
                          height: newY - shape.y,
                        };
                        break;
                      case 6: // Left-center
                        newAttrs = {
                          x: newX,
                          width: shape.width + (shape.x - newX),
                        };
                        break;
                      case 7: // Right-center
                        newAttrs = {
                          width: newX - shape.x,
                        };
                        break;
                    }

                    // Apply minimum size constraints
                    if (newAttrs.width !== undefined) {
                      newAttrs.width = Math.max(20, newAttrs.width);
                    }
                    if (newAttrs.height !== undefined) {
                      newAttrs.height = Math.max(20, newAttrs.height);
                    }

                    handleShapeTransform(shape.id, newAttrs);
                  }}
                />
              ))}
            </>
          )}
        </Group>
      );
    },
    [
      selectedShapeId,
      overlappingShapes,
      isEditable,
      drawingTool,
      handleShapeClick,
      handleShapeTransform,
    ]
  );

  return (
    <>
      <div
        className={`relative size-full ${className} ${isDragOver ? "border-2 border-dashed border-primary/30 bg-primary/5" : ""}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}>
        {/* Drawing Tools */}
        {isEditable && (
          <Card className="absolute left-4 top-4 z-10 shadow-lg">
            <CardContent className="p-3">
              <div className="flex items-center gap-2">
                <div className="flex flex-wrap items-center gap-1">
                  {/* Basic Tools */}
                  {[{ tool: "select", icon: MousePointer, label: t("common.select") }].map(
                    ({ tool, icon: Icon, label }) => (
                      <Tooltip key={tool}>
                        <TooltipTrigger asChild>
                          <Button
                            variant={drawingTool === tool ? "default" : "outline"}
                            size="sm"
                            onClick={() => {
                              // Reset polygon state when switching tools
                              if (currentPolygonId && tool !== "polygon") {
                                setCurrentPolygonId(null);
                                setCurrentPath([]);
                                setIsDrawing(false);
                              }
                              setDrawingTool(tool as DrawingTool);
                            }}>
                            <Icon className="size-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>{label}</TooltipContent>
                      </Tooltip>
                    )
                  )}

                  <Separator orientation="vertical" className="mx-1 h-6" />

                  {/* Shape Tools */}
                  {[
                    { tool: "rectangle", icon: Square, label: t("shapes.rectangle") },
                    { tool: "circle", icon: CircleIcon, label: t("shapes.circle") },
                    { tool: "ellipse", icon: MoreHorizontal, label: "Ellipse" }, // Using MoreHorizontal as ellipse placeholder
                    { tool: "polygon", icon: Square, label: t("shapes.polygon") },
                  ].map(({ tool, icon: Icon, label }) => (
                    <Tooltip key={tool}>
                      <TooltipTrigger asChild>
                        <Button
                          variant={drawingTool === tool ? "default" : "outline"}
                          size="sm"
                          onClick={() => {
                            // Reset polygon state when switching tools
                            if (currentPolygonId && tool !== "polygon") {
                              setCurrentPolygonId(null);
                              setCurrentPath([]);
                              setIsDrawing(false);
                            }
                            setDrawingTool(tool as DrawingTool);
                          }}>
                          <Icon className="size-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>{label}</TooltipContent>
                    </Tooltip>
                  ))}

                  <Separator orientation="vertical" className="mx-1 h-6" />

                  {/* Line Tools */}
                  {[
                    { tool: "line", icon: Minus, label: "Line" },
                    { tool: "arrow", icon: ArrowRight, label: "Arrow" },
                    { tool: "text", icon: Type, label: "Text" },
                  ].map(({ tool, icon: Icon, label }) => (
                    <Tooltip key={tool}>
                      <TooltipTrigger asChild>
                        <Button
                          variant={drawingTool === tool ? "default" : "outline"}
                          size="sm"
                          onClick={() => {
                            // Reset polygon state when switching tools
                            if (currentPolygonId && tool !== "polygon") {
                              setCurrentPolygonId(null);
                              setCurrentPath([]);
                              setIsDrawing(false);
                            }
                            setDrawingTool(tool as DrawingTool);
                          }}>
                          <Icon className="size-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>{label}</TooltipContent>
                    </Tooltip>
                  ))}
                </div>

                <Separator orientation="vertical" className="h-6" />

                {/* Polygon Drawing Status */}
                {currentPolygonId && (
                  <div className="flex items-center gap-2 rounded border border-primary/20 bg-primary/10 px-2 py-1 text-xs text-primary">
                    <div className="size-2 animate-pulse rounded-full bg-primary"></div>
                    Drawing polygon ({currentPath.length} points)
                  </div>
                )}

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setLayersVisible(!layersVisible)}>
                  {layersVisible ? <Eye className="size-4" /> : <EyeOff className="size-4" />}
                </Button>

                <Separator orientation="vertical" className="h-6" />

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={showGrid ? "default" : "outline"}
                      size="sm"
                      onClick={() => setShowGrid(!showGrid)}>
                      <Grid3X3 className="size-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>{t("common.toggleGrid")}</TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={snapToGrid ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSnapToGrid(!snapToGrid)}>
                      📐
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>{t("common.snapToGrid")}</TooltipContent>
                </Tooltip>

                {selectedShapeId && (
                  <>
                    <Separator orientation="vertical" className="h-6" />

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="outline" size="sm" onClick={alignShapesHorizontally}>
                          <AlignHorizontalJustifyCenter className="size-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>{t("common.alignHorizontally")}</TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="outline" size="sm" onClick={alignShapesVertically}>
                          <AlignVerticalJustifyCenter className="size-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>{t("common.alignVertically")}</TooltipContent>
                    </Tooltip>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Overlap Warning Panel - Bottom Right with Auto-hide */}
        {overlappingShapes.length > 0 && isEditable && showOverlapWarning && (
          <Card
            className={`absolute bottom-4 right-4 z-20 w-72 border-destructive/20 bg-destructive/10 shadow-lg transition-all duration-300 ease-in-out${showOverlapWarning ? "translate-x-0 opacity-100" : "translate-x-full opacity-0"} `}>
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center justify-between text-sm text-destructive">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="size-4" />
                  {t("pages.layouts.overlapWarning")}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDismissOverlapWarning}
                  className="size-6 p-0 text-destructive hover:bg-destructive/10 hover:text-destructive/80">
                  <X className="size-3" />
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <p className="text-xs text-destructive">
                {t("pages.layouts.overlapDetected", { count: overlappingShapes.length })}
              </p>
              <div className="max-h-24 space-y-1 overflow-y-auto">
                {overlappingShapes.slice(0, 5).map((overlap, index) => {
                  const shape1 = shapes.find((s) => s.id === overlap.shape1);
                  const shape2 = shapes.find((s) => s.id === overlap.shape2);
                  return (
                    <div
                      key={index}
                      className="truncate rounded bg-destructive/10 p-1 text-xs text-destructive">
                      {shape1?.label || "Unit"} ↔ {shape2?.label || "Unit"}
                    </div>
                  );
                })}
                {overlappingShapes.length > 5 && (
                  <div className="text-xs font-medium text-destructive">
                    +{overlappingShapes.length - 5} more overlaps
                  </div>
                )}
              </div>
              <div className="flex items-center justify-between border-t border-destructive/20 pt-1">
                <span className="text-xs text-destructive">Auto-hides in 5s</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDismissOverlapWarning}
                  className="h-6 border-destructive/30 px-2 text-xs text-destructive hover:bg-destructive/10">
                  Dismiss
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Shape Properties Panel */}
        {selectedShape && isEditable && (
          <Card className="absolute right-4 top-4 z-10 w-64 shadow-lg">
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center justify-between text-sm">
                {t("common.properties")}
                <div className="flex gap-1">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDuplicateShape(selectedShape.id)}>
                        <Copy className="size-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>{t("common.duplicate")}</TooltipContent>
                  </Tooltip>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteShape(selectedShape.id)}>
                        <Trash2 className="size-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>{t("common.delete")}</TooltipContent>
                  </Tooltip>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {/* Unit Assignment */}
              <div className="space-y-2">
                <Label className="text-xs">{t("pages.units.assignUnit")}</Label>
                <Select
                  value={selectedShape.unitId}
                  onValueChange={(value) => handleUnitAssignment(selectedShape.id, value)}>
                  <SelectTrigger>
                    <SelectValue placeholder={t("pages.units.selectUnit")} />
                  </SelectTrigger>
                  <SelectContent>
                    {units.map((unit) => (
                      <SelectItem key={unit.id} value={unit.id}>
                        <div className="flex items-center gap-2">
                          <div
                            className="size-3 rounded-full"
                            style={{ backgroundColor: getUnitStatusColor(unit.id) }}
                          />
                          <span>
                            {unit.unit_number} - {unit.unit_type}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Label */}
              <div className="space-y-2">
                <Label className="text-xs">{t("common.label")}</Label>
                <Input
                  value={selectedShape.label}
                  onChange={(e) =>
                    handleShapeTransform(selectedShape.id, { label: e.target.value })
                  }
                  className="h-8 text-xs"
                />
              </div>

              {/* Colors */}
              <div className="grid grid-cols-2 gap-2">
                <div className="space-y-2">
                  <Label className="text-xs">Fill Color</Label>
                  <Input
                    type="color"
                    value={selectedShape.fill}
                    onChange={(e) =>
                      handleShapeTransform(selectedShape.id, {
                        fill: e.target.value,
                      })
                    }
                    className="h-8 w-full"
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-xs">Stroke Color</Label>
                  <Input
                    type="color"
                    value={selectedShape.stroke}
                    onChange={(e) =>
                      handleShapeTransform(selectedShape.id, {
                        stroke: e.target.value,
                      })
                    }
                    className="h-8 w-full"
                  />
                </div>
              </div>

              {/* Stroke Width */}
              <div className="space-y-2">
                <Label className="text-xs">Stroke Width</Label>
                <Input
                  type="range"
                  min="0"
                  max="10"
                  step="1"
                  value={selectedShape.strokeWidth}
                  onChange={(e) =>
                    handleShapeTransform(selectedShape.id, {
                      strokeWidth: parseInt(e.target.value),
                    })
                  }
                  className="h-8"
                />
              </div>

              {/* Opacity */}
              <div className="space-y-2">
                <Label className="text-xs">{t("common.opacity")}</Label>
                <Input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={selectedShape.opacity}
                  onChange={(e) =>
                    handleShapeTransform(selectedShape.id, {
                      opacity: parseFloat(e.target.value),
                    })
                  }
                  className="h-8"
                />
              </div>

              {/* Rotation (for rectangle and ellipse) */}
              {(selectedShape.shape === "rectangle" || selectedShape.shape === "ellipse") && (
                <div className="space-y-2">
                  <Label className="text-xs">Rotation (degrees)</Label>
                  <Input
                    type="range"
                    min="0"
                    max="360"
                    step="1"
                    value={selectedShape.rotation || 0}
                    onChange={(e) =>
                      handleShapeTransform(selectedShape.id, {
                        rotation: parseInt(e.target.value),
                      })
                    }
                    className="h-8"
                  />
                </div>
              )}

              {/* Font Size (for text) */}
              {selectedShape.shape === "text" && (
                <div className="space-y-2">
                  <Label className="text-xs">Font Size</Label>
                  <Input
                    type="range"
                    min="8"
                    max="72"
                    step="1"
                    value={selectedShape.fontSize}
                    onChange={(e) =>
                      handleShapeTransform(selectedShape.id, {
                        fontSize: parseInt(e.target.value),
                      })
                    }
                    className="h-8"
                  />
                </div>
              )}

              {/* Visibility */}
              <div className="flex items-center justify-between">
                <Label className="text-xs">{t("common.visible")}</Label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    handleShapeTransform(selectedShape.id, {
                      visible: !selectedShape.visible,
                    })
                  }>
                  {selectedShape.visible ? (
                    <Eye className="size-3" />
                  ) : (
                    <EyeOff className="size-3" />
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Konva Stage */}
        <div className="relative">
          <Stage
            width={width}
            height={height}
            ref={stageRef}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onDblClick={handleDoubleClick}
            onContextMenu={handleRightClick}>
            <Layer ref={layerRef}>
              {/* Background Image */}
              {image && (
                <KonvaImage image={image} width={width} height={height} listening={false} />
              )}

              {/* Grid overlay */}
              {showGrid && (
                <Group>
                  {/* Vertical grid lines */}
                  {Array.from({ length: Math.ceil(width / gridSize) }, (_, i) => (
                    <Rect
                      key={`v-${i}`}
                      x={i * gridSize}
                      y={0}
                      width={1}
                      height={height}
                      fill="#e5e5e5"
                      opacity={0.5}
                      listening={false}
                    />
                  ))}
                  {/* Horizontal grid lines */}
                  {Array.from({ length: Math.ceil(height / gridSize) }, (_, i) => (
                    <Rect
                      key={`h-${i}`}
                      x={0}
                      y={i * gridSize}
                      width={width}
                      height={1}
                      fill="#e5e5e5"
                      opacity={0.5}
                      listening={false}
                    />
                  ))}
                </Group>
              )}

              {/* Unit Shapes */}
              {layersVisible && shapes.map(renderShape)}

              {/* Current drawing path for polygon */}
              {drawingTool === "polygon" && currentPolygonId && (
                <Group>
                  {/* Show the polygon in progress */}
                  {currentPath.length > 1 && (
                    <Line
                      points={currentPath.flatMap((point) => [point.x, point.y])}
                      stroke="#3b82f6"
                      strokeWidth={2}
                      dash={[5, 5]}
                      lineCap="round"
                      lineJoin="round"
                      x={shapes.find((s) => s.id === currentPolygonId)?.x || 0}
                      y={shapes.find((s) => s.id === currentPolygonId)?.y || 0}
                    />
                  )}

                  {/* Show points as circles */}
                  {currentPath.map((point, index) => (
                    <Circle
                      key={index}
                      x={(shapes.find((s) => s.id === currentPolygonId)?.x || 0) + point.x}
                      y={(shapes.find((s) => s.id === currentPolygonId)?.y || 0) + point.y}
                      radius={4}
                      fill="#3b82f6"
                      stroke="#ffffff"
                      strokeWidth={2}
                    />
                  ))}

                  {/* Show instruction text */}
                  {currentPath.length >= 1 && (
                    <Text
                      x={width - 250}
                      y={30}
                      text="Click to add points. Double-click or right-click to finish polygon."
                      fontSize={12}
                      fill="#3b82f6"
                      fontFamily="Arial"
                      align="left"
                      wrap="word"
                      width={240}
                    />
                  )}
                </Group>
              )}
            </Layer>
          </Stage>
        </div>
      </div>
    </>
  );
}
