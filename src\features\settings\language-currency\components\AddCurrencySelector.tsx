import { useEffect, useState } from "react";
import Image from "next/image";
import { useTranslation } from "react-i18next";

import { PortalCombobox } from "@/components/ui/portal-combobox";

interface AddCurrencySelectorProps {
  selectedCurrencies: string[];
  onAddCurrency: (currencyCode: string) => void;
  getAllCurrenciesForSelection: () => Array<{
    code: string;
    name: string;
    symbol: string;
    flag: any;
    isDisabled: boolean;
    isDirty?: boolean;
  }>;
}

export const AddCurrencySelector = ({
  selectedCurrencies,
  onAddCurrency,
  getAllCurrenciesForSelection,
}: AddCurrencySelectorProps) => {
  const { t } = useTranslation();
  const [currencyItems, setCurrencyItems] = useState<any[]>([]);

  // Update currency items when selectedCurrencies changes
  useEffect(() => {
    const allCurrencies = getAllCurrenciesForSelection();
    const items = allCurrencies.map((currency) => ({
      id: currency.code,
      name: currency.code,
      displayValue: `${currency.code} ${currency.symbol}`,
      flag: currency.flag,
      symbol: currency.symbol,
      disabled: currency.isDisabled,
      isDirty: currency.isDirty,
    }));
    setCurrencyItems(items);
  }, [selectedCurrencies, getAllCurrenciesForSelection]);

  const handleCurrencySelect = (value: string | string[]) => {
    // Handle both single and multiple selection
    if (Array.isArray(value)) {
      // Multiple selection - add all non-disabled currencies
      value.forEach((currencyCode) => {
        const selectedCurrency = currencyItems.find((curr) => curr.id === currencyCode);
        if (selectedCurrency && !selectedCurrency.disabled) {
          onAddCurrency(currencyCode);
        }
      });
    } else {
      // Single selection
      const selectedCurrency = currencyItems.find((curr) => curr.id === value);
      if (selectedCurrency && !selectedCurrency.disabled) {
        onAddCurrency(value);
      }
    }
  };

  // Custom icon display for currencies
  const iconDisplay = (item: any) => (
    <div className="mr-2 flex items-center gap-2">
      <Image src={item.flag} alt={item.name} width={16} height={16} />
    </div>
  );

  return (
    <PortalCombobox
      value=""
      onValueChange={handleCurrencySelect}
      items={currencyItems}
      placeholder={t("pages.settings.language.addCurrency")}
      searchPlaceholder={t("pages.settings.language.searchCurrency")}
      emptyText="No currencies available"
      iconDisplay={iconDisplay}
      showCheck={true}
      variantButton="outline"
      multiple={true}
      showSelectedCount={true}
      showPlusForAvailable={true}
      popoverWidth="w-[200px]"
      align="center"
    />
  );
};
