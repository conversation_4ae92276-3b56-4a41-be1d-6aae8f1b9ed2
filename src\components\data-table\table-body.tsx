import { Fragment } from "react";
import { flexRender, Row } from "@tanstack/react-table";
import { motion } from "framer-motion";

import { TableBody, TableCell, TableRow } from "@/components/ui/table";
import { cn } from "@/lib/utils";

import Empty from "../ui/empty";
import styles from "./style.module.scss";

interface TableBodyProps<TData> {
  table: any; // ReactTable<TData>
  columns: CustomColumn<TData>[];
  selectedRows: Record<string, boolean>;
  expandedRows: string[];
  expandable?: {
    enabled?: boolean;
    content: (row: Row<TData>) => React.ReactNode;
  };
  onRowClick?: (rowId: string) => void;
  expandedRowRef: React.RefObject<HTMLTableRowElement>;
}

export type CustomColumn<TData> = any; // ColumnDef<TData> & { ... }

export function TableBodyComponent<TData>({
  table,
  columns,
  selectedRows,
  expandedRows,
  expandable,
  onRowClick,
  expandedRowRef,
}: TableBodyProps<TData>) {
  return (
    <TableBody>
      {table.getRowModel().rows?.length ? (
        table.getRowModel().rows.map((row: any) => (
          <Fragment key={row.id}>
            <TableRow
              data-state={selectedRows[row.id] && "selected"}
              className={expandable?.enabled ? "hover:bg-muted/50" : ""}
              onClick={() => onRowClick?.(row.id)}>
              {row.getVisibleCells().map((cell: any) => {
                return (
                  <TableCell
                    key={cell.id}
                    className={cn(
                      cell.column.id === "select"
                        ? "sticky left-0 z-20 w-[48px] bg-card transition-colors group-hover:bg-table-hover group-data-[state=selected]:bg-muted"
                        : cell.column.id === "actions"
                          ? "sticky right-0 z-20 max-w-[60px] bg-card text-end transition-colors group-hover:bg-table-hover group-data-[state=selected]:bg-muted"
                          : (cell.column.columnDef as CustomColumn<TData>).isMainColumn
                            ? "block max-w-[200px] truncate sm:max-w-[250px] md:max-w-[300px] lg:max-w-[400px] 2xl:max-w-[500px]"
                            : ""
                    )}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                );
              })}
            </TableRow>
            {expandable?.enabled && expandedRows.includes(row.id) && (
              <motion.tr
                ref={expandedRowRef}
                className={`bg-muted/50 ${styles.expandedRow}`}
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}>
                <TableCell colSpan={columns.length}>{expandable.content(row)}</TableCell>
              </motion.tr>
            )}
          </Fragment>
        ))
      ) : (
        <TableRow>
          <TableCell colSpan={columns.length} className="h-24 text-center">
            <Empty />
          </TableCell>
        </TableRow>
      )}
    </TableBody>
  );
}
