"use client";

import React, { createContext, useContext } from "react";

import Loading from "../ui/loading";
import { PermissionContextType, usePermissionProvider } from "./use-permission-provider";

// Create permission context
const PermissionContext = createContext<PermissionContextType>({
  permissions: {},
  isLoading: false,
  isAccessForbidden: false,
  hasPermission: () => false,
  isPermissionBlocked: () => false,
  hasRoutePermission: () => false,
  checkAccess: () => false,
  refreshPermissions: () => {},
  isModuleAccessible: () => false,
  getModulePermissions: () => [],
  filterSidebarItems: () => [],
});

// Hook to use permission context
export const usePermission = () => useContext(PermissionContext);

// Permission Provider Component
export function PermissionProvider({
  children,
  disableAdminBypass = false,
}: {
  children: React.ReactNode;
  disableAdminBypass?: boolean;
}) {
  const permissionData = usePermissionProvider(disableAdminBypass);

  if (permissionData.isLoading) {
    return <Loading />;
  }

  return <PermissionContext.Provider value={permissionData}>{children}</PermissionContext.Provider>;
}
