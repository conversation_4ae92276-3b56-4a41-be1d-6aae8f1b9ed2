import { z } from "zod";

const addressSchema = z.object({
  address1: z.string().min(1, "validation.streetRequired"),
  zip: z.string().optional(),
  province: z.string().optional(),
  district: z.string().optional(),
  ward: z.string().optional(),
});

const ownerSchema = z.object({
  name: z.string().min(1, "validation.ownerNameRequired"),
  email: z.string().email("validation.invalidEmail"),
  phone: z.string().min(1, "validation.ownerPhoneRequired"),
});

const purchaseInformationSchema = z.object({
  price: z.number().positive("validation.purchasePriceMustBePositive"),
  purchase_date: z.string().min(1, "validation.purchaseDateRequired"),
});

export const createPropertySchema = z.object({
  name: z.string().min(1, "validation.propertyNameRequired"),
  address: addressSchema,
  type: z.enum(["RESIDENTIAL", "COMMERCIAL", "MIXED_USE"], {
    errorMap: () => ({ message: "validation.propertyTypeRequired" }),
  }),
  description: z.string().optional(),
  status: z.enum(["ACTIVE", "INACTIVE"], {
    errorMap: () => ({ message: "validation.statusRequired" }),
  }),
  owner: ownerSchema,
  purchase_information: purchaseInformationSchema.optional(),
  images: z
    .array(
      z.object({
        name: z.string(),
        image: z.string(),
      })
    )
    .optional(),
});

export const updatePropertySchema = createPropertySchema.partial().extend({
  id: z.string().min(1, "validation.idRequired"),
});

export const propertyFilterSchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  query: z.string().optional(),
  type: z.enum(["RESIDENTIAL", "COMMERCIAL", "MIXED_USE"]).optional(),
  status: z.enum(["ACTIVE", "INACTIVE"]).optional(),
});

export type CreatePropertyFormValues = z.infer<typeof createPropertySchema>;
export type UpdatePropertyFormValues = z.infer<typeof updatePropertySchema>;
export type PropertyFilterValues = z.infer<typeof propertyFilterSchema>;
