"use client";

import { usePathname } from "next/navigation";
import { AnimatePresence, motion } from "framer-motion";

import { settingsPaths } from "@/constants/paths";

import { SettingsSidebar } from "./components/settings-sidebar";
import { useSidebarContext } from "./context/sidebar-context";
import { AppSidebar } from "./Sidebar";

interface SidebarWrapperProps {
  defaultCollapsed?: boolean;
}

export function SidebarWrapper({ defaultCollapsed }: SidebarWrapperProps) {
  const { currentSidebar, closeSettingsSidebar } = useSidebarContext();
  const pathname = usePathname();

  // Check if current path is a settings path
  const isSettingsPath = Object.values(settingsPaths).some((path) => {
    const routePattern = path.split(":")[0].replace(/\/$/, "");
    return pathname.startsWith(routePattern);
  });

  // Determine which sidebar to show
  const shouldShowSettingsSidebar = currentSidebar === "settings" || isSettingsPath;

  return (
    <AnimatePresence mode="wait">
      {!shouldShowSettingsSidebar ? (
        <motion.div
          key="main-sidebar"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          className="shrink-0">
          <AppSidebar defaultCollapsed={defaultCollapsed} />
        </motion.div>
      ) : (
        <motion.div
          key="settings-sidebar"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          className="shrink-0">
          <SettingsSidebar onBack={closeSettingsSidebar} />
        </motion.div>
      )}
    </AnimatePresence>
  );
}
