"use client";

import Link from "next/link";
import { useTranslation } from "react-i18next";

import { But<PERSON> } from "@/components/ui";
import { authProtectedPaths } from "@/constants/paths";
import { useSubscriptionExpired } from "@/hooks/use-subscription-expired";
import { cn } from "@/lib/utils";
import { dateFormatter } from "@/utils/helpers/date-formatter";

import { CurrentPlan } from "../hooks/use-subscription";

interface CurrentPlanCardProps {
  currentPlan: CurrentPlan;
  onCancelSubscription: () => void;
  isAnnualBilling: boolean;
}

export const CurrentPlanCard = ({
  currentPlan,
  onCancelSubscription,
  isAnnualBilling,
}: CurrentPlanCardProps) => {
  const { t } = useTranslation();

  // Check if subscription is expired using reusable hook
  const { isExpired } = useSubscriptionExpired({
    status: currentPlan.status,
    expires_date: currentPlan.expires_date || "",
  });
  return (
    <div>
      <h3 className="text-base font-medium text-foreground">
        {t("pages.subscription.currentPlan.title")}
      </h3>

      <div className="mt-4 flex flex-col gap-4 md:flex-row md:items-end md:justify-between">
        <div className="flex flex-col gap-1">
          <h4 className="text-2xl font-medium text-foreground">{currentPlan.name}</h4>
          <p className={cn("text-sm", isExpired && "text-destructive")}>
            {t("pages.subscription.currentPlan.expiresOn")}:{" "}
            {dateFormatter(currentPlan.expires_date || "", "date")}
          </p>
        </div>

        <div className="flex w-full items-center gap-2 md:w-fit">
          {/* <Button
            variant="ghost"
            size="md"
            onClick={onCancelSubscription}
            className="flex-1 text-muted-foreground hover:text-foreground">
            {t("pages.subscription.currentPlan.cancelSubscription")}
          </Button> */}
          <Link
            className="flex-1"
            href={{
              pathname: authProtectedPaths.SUBSCRIPTION,
              hash: "#pricing-section",
            }}>
            <Button size="md" className="w-full">
              {t("pages.subscription.currentPlan.upgrade")}
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};
