import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { settingApi } from "@/lib/apis/setting";
import { useVersion, versionApi } from "@/lib/apis/version";

import { AVAILABLE_CURRENCIES } from "../constants";

// Hook for managing currency settings
export const useCurrency = () => {
  const { getSettingValue } = useVersion();
  const [selectedCurrencies, setSelectedCurrencies] = useState<string[]>(["USD"]);
  const [displayedCurrencies, setDisplayedCurrencies] = useState<string[]>(["USD"]);
  const [isLoading, setIsLoading] = useState(false);
  const isInitialized = useRef(false);
  const originalCurrencies = useRef<string[]>(["USD"]);
  const { t } = useTranslation();
  // Use React Query to fetch shop info data directly from the version query
  const { data: versionData, isLoading: isVersionLoading } = useQuery({
    queryKey: ["version"],
    queryFn: versionApi.getVersion,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  // Get currency settings from shop_info
  const savedCurrencies = useMemo(() => {
    const shopInfo = versionData?.data?.dict?.shop_info?.setting_value as Record<string, any>;
    return (shopInfo?.currency as string[]) || [];
  }, [versionData]);

  // Initialize currencies from saved currency settings
  useEffect(() => {
    if (savedCurrencies.length > 0 && !isVersionLoading) {
      // Only update if the values are actually different to prevent infinite loops
      setDisplayedCurrencies((prev) => {
        if (JSON.stringify(prev) !== JSON.stringify(savedCurrencies)) {
          return savedCurrencies;
        }
        return prev;
      });

      // Don't automatically change selected currency when language changes
      // Let user control currency independently
      // Only set initial currency if none is selected
      if (selectedCurrencies.length === 0 || selectedCurrencies[0] === "USD") {
        setSelectedCurrencies([savedCurrencies[0] || "USD"]);
      }

      // Mark as initialized to prevent further updates
      isInitialized.current = true;

      // Store original currencies for change detection
      originalCurrencies.current = [...savedCurrencies];
    }
  }, [savedCurrencies, isVersionLoading]); // Add isVersionLoading dependency

  // Handle savedCurrencies changes after initialization
  useEffect(() => {
    if (!isInitialized.current || !savedCurrencies.length || isVersionLoading) return;

    // Update displayed currencies if they're different
    setDisplayedCurrencies((prev) => {
      if (JSON.stringify(prev) !== JSON.stringify(savedCurrencies)) {
        return savedCurrencies;
      }
      return prev;
    });
  }, [savedCurrencies, isVersionLoading]);

  const handleCurrencyToggle = useCallback((currencyCode: string) => {
    // For currency, we only allow one selection at a time (like radio buttons)
    setSelectedCurrencies([currencyCode]);
  }, []);

  const handleAddCurrency = useCallback(
    (currencyCode: string | string[]) => {
      if (Array.isArray(currencyCode)) {
        // Add multiple currencies
        const newCurrencies = currencyCode.filter((code) => !displayedCurrencies.includes(code));
        if (newCurrencies.length > 0) {
          setDisplayedCurrencies((prev) => [...prev, ...newCurrencies]);
        }
      } else {
        // Add single currency
        if (!displayedCurrencies.includes(currencyCode)) {
          setDisplayedCurrencies((prev) => [...prev, currencyCode]);
        }
      }
    },
    [displayedCurrencies]
  );

  const handleRemoveCurrency = useCallback(
    (currencyCode: string) => {
      if (displayedCurrencies.length > 1) {
        setDisplayedCurrencies((prev) => prev.filter((code) => code !== currencyCode));

        // If the removed currency was selected, select another available currency
        if (selectedCurrencies.includes(currencyCode)) {
          const remainingCurrencies = displayedCurrencies.filter((code) => code !== currencyCode);
          setSelectedCurrencies([remainingCurrencies[0] || "USD"]);
        }
      }
    },
    [displayedCurrencies, selectedCurrencies]
  );

  const handleRemoveCurrencyFromDisplay = useCallback(
    (currencyCode: string) => {
      // Remove from displayed currencies
      if (displayedCurrencies.length > 1) {
        setDisplayedCurrencies((prev) => prev.filter((code) => code !== currencyCode));

        // If the removed currency was selected, select another available currency
        if (selectedCurrencies.includes(currencyCode)) {
          const remainingCurrencies = displayedCurrencies.filter((code) => code !== currencyCode);
          setSelectedCurrencies([remainingCurrencies[0] || "USD"]);
        }
      }
    },
    [displayedCurrencies, selectedCurrencies]
  );

  const queryClient = useQueryClient();

  const updateCurrencyMutation = useMutation({
    mutationFn: async () => {
      // Get current shop_info settings
      const currentShopInfo = getSettingValue<Record<string, any>>("shop_info") || {};

      // Update only currency-related fields while preserving other fields
      const updatedShopInfo = {
        ...currentShopInfo,
        currency: displayedCurrencies,
      };

      await settingApi.updateLanguageSetting("shop_info", updatedShopInfo);
      const version = await versionApi.getVersion();

      return {
        ...version,
        data: {
          ...version.data,
          dict: {
            ...version.data.dict,
            shop_info: { ...version.data.dict.shop_info, setting_value: updatedShopInfo },
          },
        },
      };
    },
    onSuccess: (updatedVersion) => {
      queryClient.setQueryData(["version"], updatedVersion);

      // Invalidate the version query to refetch fresh data
      // queryClient.invalidateQueries({ queryKey: ["version"] });

      // Update the original currencies ref so future changes are detected correctly
      originalCurrencies.current = [...displayedCurrencies];

      toast.success(t("pages.languageCurrency.currencySettingsUpdatedSuccessfully"));
    },
    onError: (error: Error) => {
      console.error("Error updating currency settings:", error);
      toast.error(t("pages.languageCurrency.currencySettingsUpdatedFailed"));
    },
  });

  const handleUpdateCurrency = useCallback(async () => {
    setIsLoading(true);
    try {
      await updateCurrencyMutation.mutateAsync();
    } catch (error) {
      console.error("Failed to update currency settings:", error);
    } finally {
      setIsLoading(false);
    }
  }, [updateCurrencyMutation]);

  const hasCurrencyChanges = useCallback(() => {
    // Check if the displayed currencies are different from what was originally loaded from shop_info
    if (originalCurrencies.current.length > 0) {
      const currentDisplayed = displayedCurrencies;

      // Check if the currently displayed currencies are different from what's saved
      if (originalCurrencies.current.length !== currentDisplayed.length) {
        return true;
      }

      // Check if any currency codes are different
      return (
        originalCurrencies.current.some((code: string) => !currentDisplayed.includes(code)) ||
        currentDisplayed.some((code: string) => !originalCurrencies.current.includes(code))
      );
    }

    // Fallback: check if selected currency is different from default
    const defaultCurrency = AVAILABLE_CURRENCIES.find((curr) => curr.isDefault)?.code || "USD";
    return selectedCurrencies[0] !== defaultCurrency;
  }, [selectedCurrencies, displayedCurrencies]);

  // Get all currencies for the add selector (displayed ones will be disabled)
  const getAllCurrenciesForSelection = useCallback(() => {
    return AVAILABLE_CURRENCIES.map((curr) => ({
      ...curr,
      isDisabled: displayedCurrencies.includes(curr.code),
    }));
  }, [displayedCurrencies]);

  // Get available currencies that are not currently selected
  const getAvailableCurrenciesForSelection = useCallback(() => {
    return AVAILABLE_CURRENCIES.filter((curr) => !displayedCurrencies.includes(curr.code));
  }, [displayedCurrencies]);

  // Get all currencies for the add selector (displayed ones will be disabled)
  const getAvailableCurrenciesForAdd = useCallback(() => {
    return AVAILABLE_CURRENCIES.map((curr) => ({
      ...curr,
      isDisabled: displayedCurrencies.includes(curr.code), // Disable if already in the list
      isDirty: displayedCurrencies.includes(curr.code) && !savedCurrencies.includes(curr.code), // Mark as dirty if newly added
    }));
  }, [displayedCurrencies, savedCurrencies]);

  return {
    selectedCurrencies,
    displayedCurrencies,
    isLoading,
    isVersionLoading,
    handleCurrencyToggle,
    handleAddCurrency,
    handleRemoveCurrency,
    handleRemoveCurrencyFromDisplay,
    handleUpdateCurrency,
    hasCurrencyChanges,
    getAvailableCurrenciesForSelection,
    getAllCurrenciesForSelection,
    getAvailableCurrenciesForAdd,
  };
};
