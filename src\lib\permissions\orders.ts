import { authProtectedPaths } from "@/constants/paths";

export const ORDER_PERMISSIONS = {
  // Order Management
  [authProtectedPaths.ORDERS]: ["LIST_ORDER", "LIST_ORDER_IN_CHARGE"],
  [authProtectedPaths.ORDERS_NEW]: ["CREATE_ORDER"],
  [authProtectedPaths.ORDERS_ID]: ["GET_ORDER"],
  [authProtectedPaths.ORDERS_ID_EDIT]: ["UPDATE_ORDER"],
  [`${authProtectedPaths.ORDERS}/:id/delete`]: ["DELETE_ORDER"],
  [`${authProtectedPaths.ORDERS}/:id/note`]: ["UPDATE_ORDER_NOTE"],
  [`${authProtectedPaths.ORDERS}/:id/confirm`]: ["ORDER_CONFIRM_STATUS"],
  [`${authProtectedPaths.ORDERS}/:id/draft`]: ["SAVE_DRAFT_ORDER"],
  [`${authProtectedPaths.ORDERS}/:id/paid`]: ["ORDER_CONFIRM_PAID_STATUS"],
  [`${authProtectedPaths.ORDERS}/:id/completed`]: ["ORDER_CONFIRM_COMPLETED_STATUS"],
  [`${authProtectedPaths.ORDERS}/:id/canceled`]: ["ORDER_CONFIRM_CANCELED_STATUS"],
  [`${authProtectedPaths.ORDERS}/:id/packing`]: ["ORDER_CONFIRM_PACKING_STATUS"],
  [`${authProtectedPaths.ORDERS}/import`]: ["ORDER_IMPORT"],
  [`${authProtectedPaths.ORDERS}/export`]: ["ORDER_LIST_EXPORT_FILE"],
} as const;

export type OrderPermissionKey = keyof typeof ORDER_PERMISSIONS;
export type OrderPermissionValue = (typeof ORDER_PERMISSIONS)[OrderPermissionKey];
