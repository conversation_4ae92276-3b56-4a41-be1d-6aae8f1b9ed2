"use client";

import { useCallback, useEffect, useState } from "react";
import {
  AlertCircle,
  Building2,
  Download,
  Edit,
  Eye,
  Filter,
  Grid3X3,
  List,
  Loader2,
  MapPin,
  Plus,
  RefreshCw,
  Save,
  Search,
  Trash2,
  Upload,
  Users,
} from "lucide-react";
import { useTranslation } from "react-i18next";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
// UI Components
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

// Types
import type { Property, Unit } from "../../types";
import { LayoutOverlay } from "./LayoutOverlay";
// Components
import { LayoutData, LayoutViewer, UnitPosition } from "./LayoutViewer";
import { StatsCardSkeleton, UnitListSkeleton } from "./LoadingSkeleton";

interface UnitMappingInterfaceProps {
  property: Property;
  units: Unit[];
  layouts: LayoutData[];
  onLayoutSave?: (layout: LayoutData) => void;
  onLayoutDelete?: (layoutId: string) => void;
  onUnitUpdate?: (unit: Unit) => void;
  className?: string;
}

type ViewMode = "grid" | "list";
type FilterStatus = "all" | "mapped" | "unmapped";

interface UnitMapping {
  unitId: string;
  layoutId: string;
  positionId: string;
  x: number;
  y: number;
  width: number;
  height: number;
}

export function UnitMappingInterface({
  property,
  units,
  layouts,
  onLayoutSave,
  onLayoutDelete,
  onUnitUpdate,
  className = "",
}: UnitMappingInterfaceProps) {
  const { t } = useTranslation();

  // State
  const [selectedLayoutId, setSelectedLayoutId] = useState<string>(layouts[0]?.id || "");
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState<FilterStatus>("all");
  const [selectedUnitId, setSelectedUnitId] = useState<string | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [layoutToDelete, setLayoutToDelete] = useState<string | null>(null);
  const [unitMappings, setUnitMappings] = useState<UnitMapping[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get current layout
  const currentLayout = layouts.find((l) => l.id === selectedLayoutId);

  // Filter units based on search and status
  const filteredUnits = units.filter((unit) => {
    const matchesSearch =
      unit.unit_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      unit.unit_type.toLowerCase().includes(searchTerm.toLowerCase());

    if (!matchesSearch) return false;

    if (filterStatus === "all") return true;

    const isMapped = unitMappings.some(
      (mapping) => mapping.unitId === unit.id && mapping.layoutId === selectedLayoutId
    );

    return filterStatus === "mapped" ? isMapped : !isMapped;
  });

  // Calculate stats
  const totalUnits = units.length;
  const mappedUnits = unitMappings.filter((m) => m.layoutId === selectedLayoutId).length;
  const unmappedUnits = totalUnits - mappedUnits;
  const mappingProgress = totalUnits > 0 ? (mappedUnits / totalUnits) * 100 : 0;

  // Handle layout selection
  const handleLayoutSelect = useCallback((layoutId: string) => {
    setSelectedLayoutId(layoutId);
    setSelectedUnitId(null);
  }, []);

  // Handle unit selection
  const handleUnitSelect = useCallback((unit: Unit) => {
    setSelectedUnitId(unit.id);
  }, []);

  // Handle unit position change
  const handleUnitPositionChange = useCallback(
    (unitPosition: UnitPosition) => {
      setUnitMappings((prev) => {
        const existing = prev.find(
          (m) => m.unitId === unitPosition.unitId && m.layoutId === selectedLayoutId
        );

        if (existing) {
          return prev.map((m) =>
            m.unitId === unitPosition.unitId && m.layoutId === selectedLayoutId
              ? {
                  ...m,
                  x: unitPosition.x,
                  y: unitPosition.y,
                  width: unitPosition.width,
                  height: unitPosition.height,
                }
              : m
          );
        } else {
          return [
            ...prev,
            {
              unitId: unitPosition.unitId,
              layoutId: selectedLayoutId,
              positionId: unitPosition.id,
              x: unitPosition.x,
              y: unitPosition.y,
              width: unitPosition.width,
              height: unitPosition.height,
            },
          ];
        }
      });
    },
    [selectedLayoutId]
  );

  // Handle save layout
  const handleSaveLayout = useCallback(async () => {
    if (!currentLayout) return;

    setIsLoading(true);
    setError(null);

    try {
      const updatedLayout: LayoutData = {
        ...currentLayout,
        unitPositions: unitMappings
          .filter((m) => m.layoutId === selectedLayoutId)
          .map((m) => {
            const unit = units.find((u) => u.id === m.unitId);
            return {
              id: m.positionId,
              unitId: m.unitId,
              x: m.x,
              y: m.y,
              width: m.width,
              height: m.height,
            };
          }),
      };

      await onLayoutSave?.(updatedLayout);
      setIsEditing(false);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to save layout";
      setError(errorMessage);
      // TODO: Implement proper error logging for layout save operations
    } finally {
      setIsLoading(false);
    }
  }, [currentLayout, unitMappings, selectedLayoutId, units, onLayoutSave]);

  // Handle delete layout
  const handleDeleteLayout = useCallback(() => {
    if (layoutToDelete) {
      onLayoutDelete?.(layoutToDelete);
      setLayoutToDelete(null);
      setShowDeleteDialog(false);

      // Select first available layout
      const remainingLayouts = layouts.filter((l) => l.id !== layoutToDelete);
      if (remainingLayouts.length > 0) {
        setSelectedLayoutId(remainingLayouts[0].id);
      }
    }
  }, [layoutToDelete, onLayoutDelete, layouts]);

  // Get unit status color
  const getUnitStatusColor = useCallback((status: Unit["status"]) => {
    switch (status) {
      case "available":
        return "bg-success";
      case "occupied":
        return "bg-primary";
      case "maintenance":
        return "bg-destructive";
      case "inactive":
        return "bg-muted-foreground";
      default:
        return "bg-muted-foreground";
    }
  }, []);

  // Get unit status badge variant
  const getUnitStatusVariant = useCallback((status: Unit["status"]) => {
    switch (status) {
      case "available":
        return "secondary" as const;
      case "occupied":
        return "default" as const;
      case "maintenance":
        return "destructive" as const;
      case "inactive":
        return "outline" as const;
      default:
        return "outline" as const;
    }
  }, []);

  // Handle shapes change callback (must be outside conditional rendering)
  const handleShapesChange = useCallback(
    (shapes: any) => {
      const newMappings = shapes.map((shape: any) => ({
        unitId: shape.unitId,
        layoutId: selectedLayoutId,
        positionId: shape.id,
        x: shape.x,
        y: shape.y,
        width: shape.width,
        height: shape.height,
      }));
      setUnitMappings((prev) => [
        ...prev.filter((m) => m.layoutId !== selectedLayoutId),
        ...newMappings,
      ]);
    },
    [selectedLayoutId]
  );

  return (
    <TooltipProvider>
      <div className={`flex h-full gap-6 ${className}`}>
        {/* Left Sidebar - Unit List */}
        <div className="flex w-80 flex-col space-y-4">
          {/* Stats Card */}
          {isLoading ? (
            <StatsCardSkeleton />
          ) : (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Building2 className="size-4" />
                  {t("pages.layouts.mappingStats")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-3 gap-2 text-center">
                  <div>
                    <div className="text-lg font-bold text-primary">{mappedUnits}</div>
                    <div className="text-xs text-muted-foreground">{t("pages.layouts.mapped")}</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-warning">{unmappedUnits}</div>
                    <div className="text-xs text-muted-foreground">
                      {t("pages.layouts.unmapped")}
                    </div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-success">
                      {mappingProgress.toFixed(0)}%
                    </div>
                    <div className="text-xs text-muted-foreground">{t("common.progress")}</div>
                  </div>
                </div>
                <div className="h-2 w-full rounded-full bg-muted">
                  <div
                    className="h-2 rounded-full bg-success transition-all duration-300"
                    style={{ width: `${mappingProgress}%` }}
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Controls */}
          <Card>
            <CardContent className="space-y-3 p-4">
              {/* Layout Selector */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">{t("pages.layouts.selectLayout")}</label>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Grid3X3 className="size-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => setIsEditing(!isEditing)}>
                        <Edit className="mr-2 size-4" />
                        {isEditing ? t("common.viewMode") : t("common.editMode")}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={handleSaveLayout}
                        disabled={!isEditing || isLoading}>
                        {isLoading ? (
                          <Loader2 className="mr-2 size-4 animate-spin" />
                        ) : (
                          <Save className="mr-2 size-4" />
                        )}
                        {t("common.save")}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => {
                          setLayoutToDelete(selectedLayoutId);
                          setShowDeleteDialog(true);
                        }}
                        className="text-destructive">
                        <Trash2 className="mr-2 size-4" />
                        {t("common.delete")}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <Select value={selectedLayoutId} onValueChange={handleLayoutSelect}>
                  <SelectTrigger>
                    <SelectValue placeholder={t("pages.layouts.selectLayout")} />
                  </SelectTrigger>
                  <SelectContent>
                    {layouts.map((layout) => (
                      <SelectItem key={layout.id} value={layout.id}>
                        {layout.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Search */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      placeholder={t("pages.units.searchUnits")}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-9"
                    />
                  </div>
                  <div className="flex gap-1">
                    <Button
                      variant={viewMode === "list" ? "default" : "outline"}
                      size="sm"
                      onClick={() => setViewMode("list")}>
                      <List className="size-4" />
                    </Button>
                    <Button
                      variant={viewMode === "grid" ? "default" : "outline"}
                      size="sm"
                      onClick={() => setViewMode("grid")}>
                      <Grid3X3 className="size-4" />
                    </Button>
                  </div>
                </div>
              </div>

              {/* Filter */}
              <Select
                value={filterStatus}
                onValueChange={(value: FilterStatus) => setFilterStatus(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    {t("common.all")} ({totalUnits})
                  </SelectItem>
                  <SelectItem value="mapped">
                    {t("pages.layouts.mapped")} ({mappedUnits})
                  </SelectItem>
                  <SelectItem value="unmapped">
                    {t("pages.layouts.unmapped")} ({unmappedUnits})
                  </SelectItem>
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          {/* Unit List */}
          <Card className="flex-1">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center justify-between text-sm">
                <span className="flex items-center gap-2">
                  <Users className="size-4" />
                  {t("pages.units.unitList")} ({filteredUnits.length})
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <ScrollArea className="h-[400px]">
                <div className="space-y-2 p-4">
                  {isLoading ? (
                    <UnitListSkeleton />
                  ) : (
                    filteredUnits.map((unit) => {
                      const isMapped = unitMappings.some(
                        (m) => m.unitId === unit.id && m.layoutId === selectedLayoutId
                      );
                      const isSelected = selectedUnitId === unit.id;

                      return (
                        <div
                          key={unit.id}
                          draggable={!isMapped}
                          className={`cursor-pointer rounded-lg border p-3 transition-all duration-200 ${
                            isSelected
                              ? "border-primary bg-primary/5"
                              : "border-border hover:border-primary/50 hover:bg-muted/50"
                          } ${!isMapped ? "cursor-grab active:cursor-grabbing" : "cursor-default opacity-60"}`}
                          onClick={() => handleUnitSelect(unit)}
                          onDragStart={(e) => {
                            if (!isMapped) {
                              e.dataTransfer.setData(
                                "text/plain",
                                JSON.stringify({
                                  unitId: unit.id,
                                  unitNumber: unit.unit_number,
                                  unitType: unit.unit_type,
                                  status: unit.status,
                                })
                              );
                              e.dataTransfer.effectAllowed = "copy";
                              // Add drag image
                              const dragImage = document.createElement("div");
                              dragImage.className =
                                "p-2 bg-primary text-primary-foreground rounded shadow-lg";
                              dragImage.textContent = unit.unit_number;
                              dragImage.style.position = "absolute";
                              dragImage.style.top = "-1000px";
                              document.body.appendChild(dragImage);
                              e.dataTransfer.setDragImage(dragImage, 50, 25);
                              setTimeout(() => document.body.removeChild(dragImage), 0);
                            } else {
                              e.preventDefault();
                            }
                          }}>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div
                                className={`size-3 rounded-full ${getUnitStatusColor(unit.status)}`}
                              />
                              <div>
                                <div className="text-sm font-medium">{unit.unit_number}</div>
                                <div className="text-xs text-muted-foreground">
                                  {unit.unit_type} • {unit.square_footage} sq ft
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge
                                variant={getUnitStatusVariant(unit.status)}
                                className="text-xs">
                                {t(`pages.units.status.${unit.status}`)}
                              </Badge>
                              {isMapped && (
                                <Tooltip>
                                  <TooltipTrigger>
                                    <MapPin className="size-4 text-success" />
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    {t("pages.layouts.mappedToLayout")}
                                  </TooltipContent>
                                </Tooltip>
                              )}
                            </div>
                          </div>

                          {unit.rent_amount && (
                            <div className="mt-2 text-xs text-muted-foreground">
                              ${unit.rent_amount.toLocaleString()}/month
                            </div>
                          )}
                        </div>
                      );
                    })
                  )}

                  {filteredUnits.length === 0 && (
                    <div className="py-8 text-center text-muted-foreground">
                      <Users className="mx-auto mb-2 size-12 opacity-50" />
                      <p className="text-sm">{t("pages.units.noUnitsFound")}</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>

        {/* Main Layout Area */}
        <div className="flex-1">
          <Card className="h-full">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <MapPin className="size-5" />
                  {currentLayout?.name || t("pages.layouts.noLayoutSelected")}
                </span>
                {isEditing && (
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">{t("common.editMode")}</Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleSaveLayout}
                      disabled={isLoading}>
                      {isLoading ? (
                        <Loader2 className="mr-2 size-4 animate-spin" />
                      ) : (
                        <Save className="mr-2 size-4" />
                      )}
                      {t("common.save")}
                    </Button>
                  </div>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="h-[calc(100%-5rem)]">
              {currentLayout ? (
                isEditing ? (
                  <LayoutOverlay
                    layoutImage={currentLayout.imageUrl}
                    units={units}
                    initialShapes={currentLayout.unitPositions.map((pos) => ({
                      ...pos,
                      shape: "rectangle" as const,
                      fill: "#3b82f6",
                      stroke: "#1e40af",
                      strokeWidth: 2,
                      opacity: 0.7,
                      label: units.find((u) => u.id === pos.unitId)?.unit_number || "Unit",
                      fontSize: 12,
                      fontFamily: "Arial",
                      textColor: "#ffffff",
                      visible: true,
                    }))}
                    onShapesChange={handleShapesChange}
                    width={800}
                    height={600}
                  />
                ) : (
                  <LayoutViewer
                    layoutData={currentLayout}
                    units={units}
                    onUnitClick={handleUnitSelect}
                    onUnitPositionChange={handleUnitPositionChange}
                    isEditable={false}
                    showControls={true}
                  />
                )
              ) : (
                <div className="flex h-full items-center justify-center">
                  <div className="text-center">
                    <MapPin className="mx-auto mb-4 size-16 text-muted-foreground" />
                    <h3 className="mb-2 text-lg font-semibold text-foreground">
                      {t("pages.layouts.noLayoutSelected")}
                    </h3>
                    <p className="mb-4 text-sm text-muted-foreground">
                      {t("pages.layouts.selectLayoutToStart")}
                    </p>
                    <Button>
                      <Plus className="mr-2 size-4" />
                      {t("pages.layouts.createLayout")}
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Delete Layout Dialog */}
        <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t("pages.layouts.deleteLayout")}</DialogTitle>
              <DialogDescription>{t("pages.layouts.deleteLayoutConfirmation")}</DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
                {t("common.cancel")}
              </Button>
              <Button variant="destructive" onClick={handleDeleteLayout}>
                {t("common.delete")}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </TooltipProvider>
  );
}
