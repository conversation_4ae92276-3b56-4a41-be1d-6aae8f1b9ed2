"use client";

import { useEffect, useState } from "react";
import { MailCheck } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

interface CustomPlanSuccessDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export function CustomPlanSuccessDialog({ isOpen, onClose }: CustomPlanSuccessDialogProps) {
  const [countdown, setCountdown] = useState(5);
  const { t } = useTranslation();

  useEffect(() => {
    if (isOpen && countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);

      return () => clearTimeout(timer);
    } else if (countdown === 0) {
      onClose();
    }
  }, [isOpen, countdown, onClose]);

  useEffect(() => {
    if (isOpen) {
      setCountdown(5);
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="border-0 bg-card p-6 shadow-lg">
        <DialogHeader className="flex flex-col items-center justify-center text-center">
          {/* Success Icon */}
          <div className="flex size-[140px] items-center justify-center rounded-full bg-success/10 text-center">
            <div className="flex size-32 items-center justify-center rounded-full">
              <MailCheck size={100} className="text-success" />
            </div>
          </div>

          {/* Title */}
          <DialogTitle className="text-lg font-bold text-foreground">
            {t("pages.subscription.customPlan.dialog.title")}
          </DialogTitle>
        </DialogHeader>

        {/* Message */}
        <div className="text-center">
          <p className="mb-4 text-sm text-muted-foreground">
            {t("pages.subscription.customPlan.dialog.message")}
          </p>

          {/* Timer */}
          <p className="text-xs text-neutral-400">
            {t("pages.subscription.customPlan.dialog.countdown", { countdown })}
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
}
