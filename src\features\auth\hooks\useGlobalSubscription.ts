"use client";

import { useCallback, useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";

import { useCurrentSubscription } from "@/hooks/use-subscription";
import { Subscription } from "@/lib/apis/crm_plan/types";

export interface SubscriptionData {
  id: string;
  expires_date: string;
  plan: any;
  plan_quota: any;
  used_quota: any;
  status: string;
  billing_cycle: string;
  activation_date: string;
  company_id: string;
  service: any;
}

export const useGlobalSubscription = () => {
  const router = useRouter();
  const pathname = usePathname();
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [activeSubscription, setActiveSubscription] = useState<Subscription | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasCheckedSubscription, setHasCheckedSubscription] = useState(false);

  // Move hook call to top level
  const { subscription } = useCurrentSubscription();

  const checkSubscriptions = useCallback(async () => {
    try {
      setIsLoading(true);
      if (subscription && (subscription.status === "ACTIVE" || subscription.status === "TRIAL")) {
        setActiveSubscription(subscription);
        setSubscriptions([subscription]);
        // Store specific subscription data in localStorage
        const subscriptionData: SubscriptionData = {
          id: subscription.id,
          expires_date: subscription.expires_date,
          plan: subscription.plan,
          plan_quota: subscription.plan.quota,
          used_quota: subscription.used_quota,
          status: subscription.status,
          billing_cycle: subscription.billing_cycle,
          activation_date: subscription.activation_date,
          company_id: subscription.company_id,
          service: subscription.service,
        };

        if (!hasCheckedSubscription && pathname === "/") {
          router.replace(`/subscription?sub-id=${subscription.id}`);
          setHasCheckedSubscription(true);
        }
      } else {
        setActiveSubscription(null);
        setSubscriptions([]);

        // Only redirect on first load and if we're on the home page
        if (!hasCheckedSubscription && pathname === "/") {
          router.replace("/dashboard");
          setHasCheckedSubscription(true);
        }
      }
    } catch (error) {
      console.error("Failed to fetch subscriptions:", error);
      setActiveSubscription(null);
      setSubscriptions([]);

      // On error, redirect to dashboard as fallback (only on first load)
      if (!hasCheckedSubscription && pathname === "/") {
        router.replace("/dashboard");
        setHasCheckedSubscription(true);
      }
    } finally {
      setIsLoading(false);
    }
  }, [hasCheckedSubscription, pathname, router, subscription]);

  const refreshSubscriptions = useCallback(() => {
    checkSubscriptions();
  }, [checkSubscriptions]);

  // Check if user has active subscription

  useEffect(() => {
    // Only check subscriptions once on mount
    if (!hasCheckedSubscription) {
      checkSubscriptions();
    }
  }, [checkSubscriptions, hasCheckedSubscription]);

  return {
    // State
    subscriptions,
    activeSubscription,
    isLoading,
    hasCheckedSubscription,

    // Actions
    checkSubscriptions,
    refreshSubscriptions,
  };
};
