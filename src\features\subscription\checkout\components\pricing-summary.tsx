"use client";

import { HelpCircle } from "lucide-react";
import { useTranslation } from "react-i18next";

interface PricingSummaryProps {
  totalAmount: number;
  currency: string;
  isCompanyInfoCompleted: boolean;
}

export const PricingSummary = ({
  totalAmount,
  currency,
  isCompanyInfoCompleted,
}: PricingSummaryProps) => {
  const { t } = useTranslation();

  const formatPrice = (amount: number) => {
    return new Intl.NumberFormat("vi-VN").format(amount);
  };

  return (
    <div className="flex flex-col gap-2">
      {isCompanyInfoCompleted && (
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">
            {t("pages.checkout.orderInformation.vat")}
          </span>
          <div className="flex items-center gap-1">
            <span className="text-base text-card-foreground">0</span>
            <span className="text-base text-muted-foreground">{currency}</span>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-1">
          <span className="text-sm text-muted-foreground">
            {t("pages.checkout.orderInformation.total")}
          </span>
          <HelpCircle className="size-4 text-muted-foreground" />
        </div>
        <div className="flex items-center gap-1">
          <span className="text-2xl font-medium text-card-foreground">
            {formatPrice(totalAmount)}
          </span>
          <span className="text-base text-muted-foreground">{currency}</span>
        </div>
      </div>
    </div>
  );
};
