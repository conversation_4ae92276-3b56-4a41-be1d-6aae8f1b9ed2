import { useTranslation } from "react-i18next";

import { useBranches } from "../../hook/use-branch";
import { useRoles } from "../../role/hooks/use-role";

interface Branch {
  id: string;
  name: string;
  code?: string;
  address?: string;
}

interface UseEmployeeDataReturn {
  branches: Branch[];
  roles: any[];
  isLoadingBranches: boolean;
  isLoadingRoles: boolean;
  errorBranches: string | null;
  errorRoles: string | null;
}

export function useEmployeeData(): UseEmployeeDataReturn {
  const { t } = useTranslation();
  // Use the actual branches API instead of mock data
  const {
    data: branchesData,
    isLoading: isLoadingBranches,
    error: errorBranches,
  } = useBranches({
    limit: 999,
  });

  // Use the actual roles API instead of mock data
  const {
    roles,
    isLoading: isLoadingRoles,
    error: errorRoles,
  } = useRoles({
    limit: 999,
    enabled: true,
  });

  const branches: Branch[] = [
    // Add "All" option at the beginning
    {
      id: "all",
      name: t("pages.employeePermission.branchAll", "All Branches"),
      code: "ALL",
      address: "",
    },
    // Map actual branches
    ...(branchesData?.items?.map((branch: any) => ({
      id: branch.id,
      name: branch.name,
      code: branch.code,
      address: branch.address,
    })) || []),
  ];

  return {
    branches,
    roles: roles || [],
    isLoadingBranches,
    isLoadingRoles,
    errorBranches: errorBranches ? "Failed to load branches" : null,
    errorRoles: errorRoles ? "Failed to load roles" : null,
  };
}
