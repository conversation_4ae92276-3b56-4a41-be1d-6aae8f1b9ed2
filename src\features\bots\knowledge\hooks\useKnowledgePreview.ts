import { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";

import { Knowledge } from "@/features/bots/knowledge/types";

interface DetailedKnowledge {
  raw_data?: string;
  presigned_url?: string;
  content_type?: string;
}

export function useKnowledgePreview(
  knowledge: Knowledge | null,
  detailedKnowledge?: DetailedKnowledge
) {
  const { t } = useTranslation();
  const [content, setContent] = useState<string>("");
  const [presignedUrl, setPresignedUrl] = useState<string>("");
  const [contentType, setContentType] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const isUrlInRawData = useCallback((rawData: string): boolean => {
    if (!rawData) return false;
    const cleanData = rawData.replace(/^b['"]?/, "").replace(/['"]?$/, "");
    try {
      new URL(cleanData);
      return true;
    } catch {
      return false;
    }
  }, []);

  const getContentBySource = useCallback(
    (source: string, url?: string): string => {
      const contentMap: Record<string, string> = {
        FILE: url
          ? t("pages.knowledge.preview.filePreviewNotAvailable")
          : t("pages.knowledge.preview.noFileContent"),
        URL: url
          ? `URL: ${url}\n\n${t("pages.knowledge.preview.urlDescription")}`
          : t("pages.knowledge.preview.urlNotAvailable"),
        DIRECT_TEXT: t("pages.knowledge.preview.textPreviewNotAvailable"),
      };

      return contentMap[source] || t("pages.knowledge.preview.unknownSourceType");
    },
    [t]
  );

  const loadContent = useCallback(async () => {
    if (!knowledge) return;

    setIsLoading(true);
    setError(null);

    try {
      if (detailedKnowledge?.raw_data) {
        setContent(detailedKnowledge.raw_data);
      }

      if (detailedKnowledge?.presigned_url) {
        setPresignedUrl(detailedKnowledge.presigned_url);
        if (detailedKnowledge?.content_type) {
          setContentType(detailedKnowledge.content_type);
        }
        return;
      }

      const fallbackContent = getContentBySource(knowledge.source, knowledge.url);
      setContent(fallbackContent);
    } catch (err) {
      setError(t("pages.knowledge.preview.loadError"));
      console.error("Error loading knowledge content:", err);
    } finally {
      setIsLoading(false);
    }
  }, [knowledge, detailedKnowledge, getContentBySource]);

  const isUrlContent = detailedKnowledge?.raw_data && isUrlInRawData(detailedKnowledge.raw_data);

  return {
    content,
    presignedUrl,
    contentType,
    isLoading,
    error,
    isUrlContent,
    loadContent,
  };
}
