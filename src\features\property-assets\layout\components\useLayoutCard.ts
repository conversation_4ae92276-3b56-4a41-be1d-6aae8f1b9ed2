import { toast } from "sonner";

import type { Layout } from "@/lib/apis/types/property_assets/layout";

interface UseLayoutCardReturn {
  mappingProgress: number;
  totalUnits: number;
  progressPercentage: number;
  handleActionsClick: () => void;
}

export function useLayoutCard(layout: Layout): UseLayoutCardReturn {
  const mappingProgress = layout.unit_layout_positions?.length || 0;
  const totalUnits = 5;
  const progressPercentage = (mappingProgress / totalUnits) * 100;

  const handleActionsClick = () => {
    // TODO: Implement actions menu (edit, delete, view details, etc.)
    toast.info(`Actions for layout: ${layout.name}`);
  };

  return {
    mappingProgress,
    totalUnits,
    progressPercentage,
    handleActionsClick,
  };
}
