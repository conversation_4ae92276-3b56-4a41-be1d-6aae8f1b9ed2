"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";

import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Combobox } from "@/components/ui/combobox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cleanPhoneNumber, formatPhoneNumber } from "@/utils/helpers/number-formatter";

import { BUSINESS_SECTORS } from "../constants";
import { StoreInformation } from "../types";

interface BasicInformationFormProps {
  formData: Pick<StoreInformation, "storeName" | "store_phone" | "store_email" | "businessSector">;
  isLoading: boolean;
  onInputChange: (
    field: keyof StoreInformation,
    value: string | { name: string; id: string }
  ) => void;
  onSubmit: () => Promise<void>;
  isFormDirty: boolean;
  onReset?: () => void;
}

export const BasicInformationForm = ({
  formData,
  isLoading,
  onInputChange,
  onSubmit,
  isFormDirty,
  onReset,
}: BasicInformationFormProps) => {
  const { t } = useTranslation();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      await onSubmit();
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReset = () => {
    if (onReset) {
      onReset();
    }
  };

  return (
    <Card className="border border-border">
      <CardHeader>
        <CardTitle className="text-sm font-medium text-foreground">
          {t("storeInformation.basicInformation")}
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          {t("storeInformation.basicInformationDescription")}
        </p>
      </CardHeader>

      <CardContent className="px-6 py-3">
        <div className="space-y-2">
          {/* Store Name */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-foreground">
              {t("storeInformation.storeName")}
            </Label>
            <Input
              value={formData.storeName}
              onChange={(e) => onInputChange("storeName", e.target.value)}
              placeholder={t("storeInformation.storeNamePlaceholder")}
              className="border-border"
            />
          </div>

          {/* Phone */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-foreground">
              {t("storeInformation.phone")}
            </Label>
            <Input
              type="tel"
              value={formData.store_phone ? formatPhoneNumber(formData.store_phone) : ""}
              onChange={(e) => {
                // Clean and limit to 10 digits
                const cleanValue = cleanPhoneNumber(e.target.value);
                onInputChange("store_phone", cleanValue);
              }}
              placeholder={t("storeInformation.phonePlaceholder")}
              className="border-border"
              onBlur={(e) => {
                // Format on blur for better UX
                if (formData.store_phone) {
                  e.target.value = formatPhoneNumber(formData.store_phone);
                }
              }}
            />
          </div>

          {/* Email */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-foreground">
              {t("storeInformation.email")}
            </Label>
            <Input
              value={formData.store_email}
              onChange={(e) => onInputChange("store_email", e.target.value)}
              placeholder={t("storeInformation.emailPlaceholder")}
              className="border-border"
            />
          </div>

          {/* Business Sector */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-foreground">
              {t("storeInformation.businessSector")}
            </Label>
            <Combobox
              items={BUSINESS_SECTORS}
              value={formData.businessSector}
              onValueChange={(value) => onInputChange("businessSector", value)}
              placeholder={t("storeInformation.selectBusinessSector")}
            />
          </div>
        </div>
      </CardContent>

      {/* Footer Actions */}
      <CardFooter className="flex items-center justify-end py-3">
        <Button
          onClick={handleSubmit}
          disabled={!isFormDirty || isLoading || isSubmitting}
          loading={isSubmitting}
          className="bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50">
          {t("common.update")}
        </Button>
      </CardFooter>
    </Card>
  );
};
