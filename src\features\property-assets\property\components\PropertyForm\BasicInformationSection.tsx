"use client";

import { useTranslation } from "react-i18next";

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { ImageUpload } from "@/components/ui/image-upload";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

interface BasicInformationSectionProps {
  form: any;
}

export function BasicInformationSection({ form }: BasicInformationSectionProps) {
  const { t } = useTranslation();

  // Property type options
  const propertyTypeOptions = [
    { value: "RESIDENTIAL", label: t("pages.properties.types.residential") },
    { value: "COMMERCIAL", label: t("pages.properties.types.commercial") },
    { value: "MIXED_USE", label: t("pages.properties.types.mixed") },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-base font-medium text-foreground">
          {t("pages.properties.basicInformation")}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Property Image */}
        <FormField
          control={form.control}
          name="images"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-foreground">
                {t("pages.properties.headers.image")} <span className="text-destructive">*</span>
              </FormLabel>
              <FormControl>
                <ImageUpload
                  value={
                    field.value.length > 0
                      ? JSON.stringify(field.value.map((img: { image: string }) => img.image))
                      : null
                  }
                  onChange={(base64) => {
                    if (!base64) {
                      field.onChange([]);
                      return;
                    }

                    try {
                      const images = JSON.parse(base64) as string[];
                      const processedImages = images.map((image) => {
                        const nameMatch = image.match(/;name=(.*?)(;|$)/);
                        const filename = nameMatch ? decodeURIComponent(nameMatch[1]) : "image.jpg";

                        return {
                          name: filename,
                          image: image,
                        };
                      });

                      field.onChange(processedImages);
                    } catch (error) {
                      console.error("Error processing images:", error);
                    }
                  }}
                  multiple
                />
              </FormControl>
              <FormMessage className="text-destructive" />
            </FormItem>
          )}
        />

        {/* Property Name */}
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-foreground">
                {t("pages.properties.headers.name")} <span className="text-destructive">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder={t("pages.properties.placeholders.name")}
                  className="h-9"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Property Type */}
        <FormField
          control={form.control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-foreground">
                {t("pages.properties.headers.type")} <span className="text-destructive">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="h-9 border-input focus:border-primary focus:ring-ring">
                    <SelectValue placeholder={t("pages.properties.placeholders.type")} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {propertyTypeOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage className="text-destructive" />
            </FormItem>
          )}
        />

        {/* Description */}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-foreground">
                {t("pages.properties.headers.description")}
              </FormLabel>
              <FormControl>
                <Textarea
                  placeholder={t("pages.properties.placeholders.description")}
                  rows={3}
                  className="resize-none border-input focus:border-primary focus:ring-ring"
                  {...field}
                />
              </FormControl>
              <FormMessage className="text-destructive" />
            </FormItem>
          )}
        />

        {/* Property Status */}
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-foreground">
                {t("pages.properties.headers.status")} <span className="text-destructive">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="h-9 border-input focus:border-primary focus:ring-ring">
                    <SelectValue placeholder={t("pages.properties.placeholders.status")} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="ACTIVE">{t("common.status.active")}</SelectItem>
                  <SelectItem value="INACTIVE">{t("common.status.inactive")}</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage className="text-destructive" />
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  );
}
