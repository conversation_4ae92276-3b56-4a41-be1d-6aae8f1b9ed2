import { Modu<PERSON> } from "@/lib/apis/permission";

import { PermissionGroup } from "./permission-group";

interface ModuleContentProps {
  module: Module;
  groupId: string;
  isExpanded: boolean;
  onToggleExpansion: (moduleId: string) => void;
  onToggleModulePermissions: (groupId: string, moduleId: string) => void;
  onToggleGroup: (groupId: string, moduleId: string, typeName: "CRUD" | "ACTION") => void;
  onTogglePermission: (groupId: string, moduleId: string, permissionId: string) => void;
}

export function ModuleContent({
  module,
  groupId,
  isExpanded,
  onToggleExpansion,
  onToggleModulePermissions,
  onToggleGroup,
  onTogglePermission,
}: ModuleContentProps) {
  // Group permissions by their group
  const crudPermissions = module.permissions.filter((p) => p.group === "CRUD");
  const actionPermissions = module.permissions.filter((p) => p.group === "ACTION");

  return (
    <div className="">
      <PermissionGroup
        moduleId={module.id}
        groupId={groupId}
        groupName="CRUD"
        permissions={crudPermissions}
        onToggleGroup={onToggleGroup}
        onTogglePermission={onTogglePermission}
      />

      <PermissionGroup
        moduleId={module.id}
        groupId={groupId}
        groupName="ACTION"
        permissions={actionPermissions}
        onToggleGroup={onToggleGroup}
        onTogglePermission={onTogglePermission}
      />
    </div>
  );
}
