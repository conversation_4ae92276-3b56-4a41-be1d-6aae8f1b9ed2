"use client";

import { useRouter } from "next/navigation";
import { ArrowLeft } from "lucide-react";
import { useTranslation } from "react-i18next";

import { But<PERSON> } from "@/components/ui/button";

export default function ForbiddenPage() {
  const { t } = useTranslation();
  const router = useRouter();

  return (
    <div className="flex min-h-screen w-full items-center bg-background px-4 py-12 sm:px-6 md:px-8 lg:px-12 xl:px-16">
      <div className="w-full space-y-6 text-center">
        <div className="space-y-3">
          <h1 className="animate-bounce text-4xl font-bold tracking-tighter sm:text-5xl">403</h1>
          <p className="text-gray-500">
            <span>{t("error.forbidden.title")}</span>
          </p>
        </div>
        <div className="flex flex-col justify-center space-y-2 sm:flex-row sm:space-x-2 sm:space-y-0">
          <Button onClick={() => router.push("/dashboard")}>
            <ArrowLeft />
            {t("error.backToHome")}
          </Button>
        </div>
      </div>
    </div>
  );
}
