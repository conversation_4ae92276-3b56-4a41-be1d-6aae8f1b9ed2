import { useCallback, useState } from "react";

import { useQuotaCheck } from "@/hooks/use-quota-check";

interface UseExceedQuotaDialogProps {
  quotaKey?: string;
  subscription: any;
  onUpgrade?: () => void;
  onSeeAllPlans?: () => void;
  onClose?: () => void;
}

export const useExceedQuotaDialog = ({
  quotaKey = "staffs",
  subscription,
  onUpgrade,
  onSeeAllPlans,
  onClose,
}: UseExceedQuotaDialogProps) => {
  const [open, setOpen] = useState(false);

  // Check quota status for the specified quota key
  const { isExceeded, used, max } = useQuotaCheck(subscription, quotaKey);

  const openDialog = useCallback(() => {
    // if (isExceeded) {
    //   setOpen(true);
    // }
    setOpen(true);
  }, []);

  const closeDialog = useCallback(() => {
    setOpen(false);
    onClose?.();
  }, [onClose]);

  const handleOpenChange = useCallback((newOpen: boolean) => {
    setOpen(newOpen);
  }, []);

  const dialogProps = {
    open,
    onOpenChange: (newOpen: boolean) => {
      setOpen(newOpen);
    },
    currentPlan: subscription?.plan?.name || "Advance",
    expiresOn: subscription?.expires_date
      ? new Date(subscription.expires_date).toLocaleDateString()
      : "N/A",
    usage: { used, max, quotaKey },
    quotaKey,
    onUpgrade,
    onSeeAllPlans,
    onClose: closeDialog,
  };

  return {
    dialogProps,
    openDialog,
    closeDialog,
    isExceeded,
    used,
    max,
  };
};
