import { fireEvent, render, screen } from "@testing-library/react";

import { RelatedQuestions } from "./RelatedQuestions";

// Mock useTranslation
jest.mock("react-i18next", () => ({
  useTranslation: () => ({
    t: (key: string, defaultValue?: string) => defaultValue || key,
  }),
}));

describe("RelatedQuestions Component", () => {
  const mockQuestions = [
    "Khi nào nên sử dụng len nhung mèo cho các dự án đan móc?",
    "<PERSON><PERSON> thể làm những sản phẩm nào từ len nhung mèo?",
    "Len nhung mèo có phù hợp cho người mới bắt đầu không?",
  ];

  const mockOnQuestionClick = jest.fn();

  beforeEach(() => {
    mockOnQuestionClick.mockClear();
  });

  it("should render all questions correctly", () => {
    render(<RelatedQuestions questions={mockQuestions} onQuestionClick={mockOnQuestionClick} />);

    // Check that all questions are rendered
    mockQuestions.forEach((question) => {
      expect(screen.getByText(question)).toBeInTheDocument();
    });

    // Check that the header is rendered
    expect(screen.getByText("Related questions")).toBeInTheDocument();
  });

  it("should call onQuestionClick when a question is clicked", () => {
    render(<RelatedQuestions questions={mockQuestions} onQuestionClick={mockOnQuestionClick} />);

    // Click on the first question
    const firstQuestion = screen.getByText(mockQuestions[0]);
    fireEvent.click(firstQuestion);

    // Verify the callback was called with the correct question
    expect(mockOnQuestionClick).toHaveBeenCalledWith(mockQuestions[0]);
    expect(mockOnQuestionClick).toHaveBeenCalledTimes(1);
  });

  it("should allow multiple questions to be clicked", () => {
    render(<RelatedQuestions questions={mockQuestions} onQuestionClick={mockOnQuestionClick} />);

    // Click on multiple questions
    const firstQuestion = screen.getByText(mockQuestions[0]);
    const secondQuestion = screen.getByText(mockQuestions[1]);

    fireEvent.click(firstQuestion);
    fireEvent.click(secondQuestion);

    // Verify both callbacks were called
    expect(mockOnQuestionClick).toHaveBeenCalledWith(mockQuestions[0]);
    expect(mockOnQuestionClick).toHaveBeenCalledWith(mockQuestions[1]);
    expect(mockOnQuestionClick).toHaveBeenCalledTimes(2);
  });

  it("should not render when questions array is empty", () => {
    const { container } = render(
      <RelatedQuestions questions={[]} onQuestionClick={mockOnQuestionClick} />
    );

    expect(container.firstChild).toBeNull();
  });

  it("should not render when questions is undefined", () => {
    const { container } = render(
      <RelatedQuestions questions={undefined as any} onQuestionClick={mockOnQuestionClick} />
    );

    expect(container.firstChild).toBeNull();
  });

  it("should have proper accessibility attributes", () => {
    render(<RelatedQuestions questions={mockQuestions} onQuestionClick={mockOnQuestionClick} />);

    // Check that buttons have proper focus styles and are focusable
    const questionButtons = screen.getAllByRole("button");

    expect(questionButtons).toHaveLength(mockQuestions.length);

    questionButtons.forEach((button) => {
      expect(button).toBeInTheDocument();
      expect(button).not.toBeDisabled();
    });
  });

  it("should handle keyboard interactions", () => {
    render(<RelatedQuestions questions={mockQuestions} onQuestionClick={mockOnQuestionClick} />);

    const firstButton = screen.getAllByRole("button")[0];

    // Test Enter key
    fireEvent.keyDown(firstButton, { key: "Enter" });
    fireEvent.keyUp(firstButton, { key: "Enter" });

    // The click should happen when Enter is pressed on a button
    fireEvent.click(firstButton);
    expect(mockOnQuestionClick).toHaveBeenCalledWith(mockQuestions[0]);
  });
});
