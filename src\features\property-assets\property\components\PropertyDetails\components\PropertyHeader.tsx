"use client";

import { Edit, MapPin, Trash2 } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Button } from "@/components/ui/button";
import { Property } from "@/lib/apis/types/property_assets/property";

interface PropertyHeaderProps {
  property: Property;
  onEdit: () => void;
  onDelete: () => void;
  getStatusBadge: (status: string) => React.ReactNode;
}

export function PropertyHeader({
  property,
  onEdit,
  onDelete,
  getStatusBadge,
}: PropertyHeaderProps) {
  const { t } = useTranslation();

  return (
    <div className="flex items-start justify-between">
      <div className="space-y-2">
        <div className="flex items-center gap-3">
          <h1 className="text-3xl font-bold text-foreground">{property.name}</h1>
          {getStatusBadge(property.status)}
        </div>
        <div className="flex items-center text-muted-foreground">
          <MapPin className="mr-2 size-4" />
          <span className="text-sm">
            {property.address.address1}
            {property.address.zip && `, ${property.address.zip}`}
            {property.address.province && `, ${property.address.province}`}
            {property.address.district && `, ${property.address.district}`}
            {property.address.ward && `, ${property.address.ward}`}
          </span>
        </div>
      </div>

      <div className="flex items-center gap-3">
        <Button variant="outline" onClick={onEdit} className="gap-2">
          <Edit className="size-4" />
          {t("pages.properties.components.propertyHeader.edit")}
        </Button>
        <Button
          variant="outline"
          onClick={onDelete}
          className="gap-2 text-destructive hover:bg-destructive/5 hover:text-destructive">
          <Trash2 className="size-4" />
          {t("pages.properties.components.propertyHeader.delete")}
        </Button>
      </div>
    </div>
  );
}
