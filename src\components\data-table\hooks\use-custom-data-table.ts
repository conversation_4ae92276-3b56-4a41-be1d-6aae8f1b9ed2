import { useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";

import { usePermission } from "@/components/provider/permission-provider";

interface UseCustomDataTableProps<TData> {
  tableKey?: string;
  selectedRows: Record<string, boolean>;
  expandedRows: string[];
  onDelete: (selectedRows: number[]) => Promise<void> | void;
  onRowClick?: (rowId: string) => void;
  allowDelete?: boolean;
}

export function useCustomDataTable<TData>({
  tableKey,
  selectedRows,
  expandedRows,
  onDelete,
  onRowClick,
  allowDelete = true,
}: UseCustomDataTableProps<TData>) {
  const { t } = useTranslation();
  const { hasPermission } = usePermission();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const expandedRowRef = useRef<HTMLTableRowElement>(null);

  // Check if user has permission to view the table data
  const hasDataPermission = useMemo(() => {
    // If no tableKey specified, allow access
    if (!tableKey) {
      return true;
    }

    // Generate permission as "LIST_<TABLEKEY>"
    const permission = `LIST_${tableKey.toUpperCase()}`;
    return hasPermission(permission);
  }, [tableKey, hasPermission]);

  // Generate permission denied message
  const deniedMessage = useMemo(() => {
    if (!tableKey) {
      return t("common.noPermission");
    }

    const permission = `LIST_${tableKey.toUpperCase()}`;
    return t("common.noPermission", {
      permission: t(`permissions.${permission}`) || permission,
    });
  }, [tableKey, t]);

  // Format selected rows for deletion
  const formattedSelectedRows: number[] = useMemo(() => {
    return Object.keys(selectedRows)
      .filter((key) => selectedRows[key]) // Filter valid keys
      .map((key) => Number(key));
  }, [selectedRows]);

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    await onDelete(formattedSelectedRows);
    setIsDeleteDialogOpen(false);
  };

  // Handle row click
  const handleRowClick = (rowId: string) => {
    onRowClick?.(rowId);
  };

  // Handle view dialog toggle
  const handleViewDialogToggle = (open: boolean) => {
    setIsViewDialogOpen(open);
  };

  // Handle delete dialog toggle
  const handleDeleteDialogToggle = (open: boolean) => {
    setIsDeleteDialogOpen(open);
  };

  // Scroll to expanded row effect
  useEffect(() => {
    if (expandedRowRef.current) {
      if (expandedRowRef.current.parentElement) {
        expandedRowRef.current.style.scrollMarginTop = "100px";
      }
      expandedRowRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }, [expandedRows]);

  return {
    // State
    isDeleteDialogOpen,
    isViewDialogOpen,
    expandedRowRef,

    // Computed values
    hasDataPermission,
    deniedMessage,
    formattedSelectedRows,

    // Handlers
    handleDeleteConfirm,
    handleRowClick,
    handleViewDialogToggle,
    handleDeleteDialogToggle,
  };
}
