{"nav": {"blog": "Blog", "operations": "Operations", "overview": "Overview", "patientManagement": "Patient Management", "doctorManagement": "Doctor Management", "medicalSupplies": "Medical Supplies", "invoicesPayments": "Invoices & Payments", "report": "Report", "administration": "Administration", "product": "Product", "productList": "Product List", "newProduct": "Add product", "editProduct": "Edit product", "variantsList": "Variant List", "brandList": "Brand List", "categoryList": "Category List", "order": "Order", "orderList": "Order List", "orderDetail": "Order Detail", "orderEdit": "Edit Order", "orderProcess": "Process Order ", "returnOrderList": "Return Order List", "packageList": "Package List", "integration": "Integration", "fetchEvent": "Fetch Event", "detailFetchEvent": "Fetch Event Detail", "syncRecords": "Sync data", "channel": "Channel", "supportedChannels": "Channel list", "installChannel": "Install new channel", "logistics": "Logistics", "shippingProviderList": "Shipping Provider List", "purchaseOrder": "Purchase Order", "purchaseOrderList": "Purchase Order List", "supplierList": "Supplier List", "customers": "Customers", "customerDashboard": "Dashboard", "customerList": "Customer List", "customerDetail": "Customer Detail", "customerGroupList": "Customer Group List", "loyaltyProgram": "Loyalty Program", "rewardProgram": "Reward Program", "finance": "Finance", "account": "Account", "paymentMethod": "Payment Method", "transaction": "Transaction", "inventory": "Inventory", "locationList": "Location List", "inventoryList": "Inventory List", "stockAdjustmentList": "Stock adjustment list", "stockRelocateList": "Stock Relocate List", "promotion": "Promotion", "discountList": "Discount Products List", "voucherList": "Voucher List", "import": "Import", "importList": "Import List", "recordList": "Record List", "website": "Website", "blogCategory": "Blog Category", "blogList": "Blog List", "notification": "Notification", "notificationList": "Notification List", "loyaltyApp": "Loyalty App", "pos": "Sales POS", "terminalList": "Terminal list", "shiftList": "Shift List", "posFnB": "POS F&B", "settings": "Settings", "dashboard": "Dashboard", "productReport": "Product Report", "productDetail": "Product Detail", "orderManual": "Add Order", "productMapping": "Product Mapping", "productMappingDetail": "Mapping Detail", "productMappingAttribute": "Product Mapping Attribute", "staff": "Staff", "staffList": "Staff List", "department": "Department", "knowledge": "Knowledge", "task": "Task", "conversation": "Conversation", "interact": "Interact", "editStaff": "Edit Staff", "activities": "Activities", "opportunities": "Opportunities", "crm": "CRM", "opportunityDetail": "Opportunity Detail", "pipelines": "Pipeline", "subscription": "Subscription", "checkout": "Checkout", "propertyAssets": "Property Assets", "properties": "Properties", "layoutManagement": "Layout Management", "units": "Units", "contracts": "Contracts", "tenants": "Tenants", "financial": "Financial", "maintenance": "Maintenance", "reports": "Reports", "propertyAssetsDashboard": "Property Assets Dashboard", "propertyDetail": "Property Details", "newProperty": "New Property", "editProperty": "Edit Property", "unitDetail": "Unit Details", "newUnit": "New Unit", "editUnit": "Edit Unit", "contractDetail": "Contract Details", "newContract": "New Contract", "editContract": "Edit Contract", "tenantDetail": "Tenant Details", "newTenant": "New Tenant", "editTenant": "Edit Tenant", "payments": "Payments", "paymentDetail": "Payment Details", "newPayment": "New Payment", "editPayment": "Edit Payment", "maintenanceDetail": "Maintenance Details", "newMaintenance": "New Maintenance", "editMaintenance": "Edit Maintenance", "assetCategories": "Asset Categories", "assetCategoryDetail": "Asset Category Details", "newAssetCategory": "New Asset Category", "editAssetCategory": "Edit Asset Category", "documentManagement": "Document Management", "documentDetail": "Document Details", "newDocument": "New Document", "editDocument": "Edit Document", "propertyGallery": "Property Gallery", "propertyGalleryDetail": "Gallery Details", "newPropertyGallery": "New Gallery Item", "editPropertyGallery": "Edit Gallery Item", "propertyValuation": "Property Valuation", "propertyValuationDetail": "Valuation Details", "newPropertyValuation": "New Valuation", "editPropertyValuation": "Edit Valuation", "employeePermission": "Employee & Permission"}, "error": {"backToHome": "Back to home", "notFound": "Page not found", "forbidden": {"title": "You don't have permission to access this page", "description": "Contact your administrator if you believe this is an error or need access to this resource."}}, "quota": {"storage": "Storage", "products": "Products", "orders": "Orders", "messages": "Messages", "staffs": "Staffs", "knowledge": "Knowledge", "capacity": "Storage", "knowledge_capacity": "Knowledge capacity"}, "product": {"image": "Product image", "title": "Product name", "description": "Description", "price": "Price", "sku": "SKU code", "brand": "Brand", "category": "Category", "inventory": "Inventory", "notMapped": "Not mapped to destination"}, "productMapping": {"lastSynced": "Last synced", "errorLoading": "Error loading product mapping details", "manualRetry": "Manual retry", "cancelledMessage": "Product mapping cancelled", "mappingStatus": "Mapping status", "variant": "Variants"}, "groups": {"crm": "CRM", "operations": "Operations", "virtual_staff": "Virtual Staff", "product": "Product", "property_assets": "Property Assets"}, "branch": {"Ho Chi Minh": "<PERSON>", "Ha Noi": "<PERSON>", "Da Nang": "<PERSON>", "Hai Phong": "<PERSON>", "Can Tho": "<PERSON>", "Binh Duong": "<PERSON><PERSON> Duong", "Binh Phuoc": "<PERSON><PERSON> Ph<PERSON>", "All": "All", "title": "Select location", "branch": "Branch", "all": "All branches", "addBranch": "Add new branch", "shortcuts": {"alt": "Alt", "plus": "+"}, "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "yearly": "Yearly", "annually": "Annually", "refresh": "Refresh"}, "profile": {"free": "Free", "profile": "Profile", "settings": "Settings", "darkMode": "Dark mode", "on": "On", "off": "Off", "language": "Language", "english": "English", "vietnamese": "Vietnamese", "japanese": "Japanese", "updateSuccess": "Language settings updated successfully", "updateError": "Failed to update language settings", "selectLanguage": "Select Language", "searchLanguage": "Search language...", "noLanguagesFound": "No languages found.", "current": "Current", "logout": "Log out", "founder": "Founder", "usedSpace": "Used space", "upgrade": "Upgrade", "message": "Message", "documents": "Documents", "staff": "Staff", "storage": "Storage", "profileUpdateSuccess": "Profile updated successfully", "profileUpdateError": "Failed to update profile", "avatarUploadSuccess": "Avatar uploaded successfully", "avatarUploadError": "Failed to upload avatar"}, "storeInformation": {"storeInformation": "Store information", "basicInformation": "Basic information", "basicInformationDescription": "Information used for customers to contact you.", "advancedInformation": "Advanced information", "advancedInformationDescription": "Configure advanced settings for URL, pricing, and store address.", "storeName": "Store name", "storeNamePlaceholder": "E.g. ABC store", "phone": "Phone", "phonePlaceholder": "**********", "email": "Email", "emailPlaceholder": "<EMAIL>", "businessSector": "Business sector", "selectBusinessSector": "Select...", "storeUrl": "Store URL", "storeUrlPlaceholder": "E.g. ABC store", "defaultPriceGroup": "Default price group", "selectPriceGroup": "Select...", "address": "Address", "addressPlaceholder": "Enter store address", "province": "Province", "selectProvince": "Select...", "district": "District", "selectDistrict": "Select...", "ward": "Ward", "selectWard": "Select..."}, "auth": {"brandSection": {"title": "Enjoy 14 days free trial"}, "passwordResetSuccess": "Password reset successfully", "minPasswordLength": "Password must be at least 8 characters", "passwordsDontMatch": "Passwords do not match", "changePasswordTitle": "Change password", "changePasswordSubtitle": "Enter your current password and new password", "changePasswordButton": "Change password", "changePasswordSuccess": "Password changed successfully", "changePasswordError": "Failed to change password", "changePasswordLoading": "Changing password...", "currentPasswordRequired": "Current password is required", "currentPasswordPlaceholder": "Enter current password", "currentPassword": "Current password", "incorrectUsernameOrPassword": "Incorrect username or password.", "userExist": "User already exist!", "logoutSuccess": "Logged out successfully", "logoutError": "Error logging out", "gender": "Gender", "genderPlaceholder": "Select your gender", "male": "Male", "female": "Female", "other": "Other", "preferNotToSay": "Prefer not to say", "dob": "Date of birth", "dobPlaceholder": "Pick a date", "username": "Username", "usernamePlaceholder": "Enter username", "login": "Log in", "register": "Register", "forgotPasswordDescription": "Enter your email and the instructions will be sent to you!", "forgotPasswordTitle": "Forgot password?", "forgotPasswordSubtitle": "Enter your email to receive a password reset instructions", "resetPassword": "Reset password", "resetPasswordTitle": "Reset password", "resetPasswordDescription": "Enter the verification code and new password", "resetPasswordSubtitle": "Enter the verification code and new password", "resetPasswordButton": "Reset password", "resetPasswordSuccess": "Password reset successfully", "resetPasswordSuccessDescription": "Now you can log in with your new password", "resetPasswordError": "Failed to reset password", "resetPasswordLoading": "Resetting...", "confirmPassword": "Confirm password", "confirmPasswordPlaceholder": "Confirm password", "backToLogin": "Back to the login page", "backToForgotPassword": "Back to the login", "loginTitle": "<PERSON><PERSON>", "loginSubtitle": "Enter your username or email to log in", "email": "Email", "emailPlaceholder": "m@example", "emailPlaceholderSignUp": "Enter email", "verifyEmail": "Verify email", "verifyEmailButton": "Verify email", "verifyEmailSuccess": "Email has been verified successfully", "verifyEmailError": "Failed to verify email", "verifyEmailLoading": "Verifying email...", "verifyEmailCode": "Enter the code sent to your email", "verifyEmailCodePlaceholder": "Enter the code", "verifyEmailCodeButton": "Verify code", "verifyEmailCodeSuccess": "Code has been verified successfully", "verifyEmailCodeError": "Failed to verify code", "verifyEmailCodeLoading": "Verifying code...", "newPassword": "New password", "newPasswordPlaceholder": "Enter new password", "verificationCode": "Verification code", "verificationCodePlaceholder": "Enter the verification code", "verificationCodeButton": "Verify", "verificationCodeSuccess": "Verification successful", "verificationCodeError": "Verification failed", "verificationCodeDescription": "We have sent a code to {{username}}. Enter it below.", "sendInstructions": "Send instructions", "sending": "Sending...", "resetting": "Resetting...", "password": "Password", "passwordPlaceholder": "Enter password", "rememberMe": "Remember me", "loginButton": "Log in", "loginWithGoogle": "Login with Google", "loginWithGithub": "Login with <PERSON><PERSON><PERSON>", "noAccount": "Don't have an account?", "signUp": "Sign up", "signUpTitle": "Sign up", "signUpSubtitle": "Sign up to log in to your admin panel", "signUpButton": "Sign up", "signUpSuccess": "Sign up successful! Please verify your email", "signUpError": "Sign up failed", "signUpLoading": "Signing up...", "alreadyHaveAccount": "Already have an account?", "sendNewCode": "Resend", "resendCodeSuccess": "New verification code has been sent", "resendCodeError": "Failed to send verification code", "usernameOrEmail": "Username or email", "usernameOrEmailPlaceholder": "Enter username or email", "forgot": "Forgot?", "or": "Or", "loginSuccess": "Login successful", "loginError": "<PERSON><PERSON> failed", "loginLoading": "Logging in...", "usernameRequired": "Please enter username", "emailRequired": "Please enter email", "passwordRequired": "Please enter password", "confirmPasswordRequired": "Please confirm password", "invalidPassword": "Password must be at least 8 characters", "forgotPasswordSuccess": "Password reset instructions have been sent to your email", "forgotPasswordError": "Failed to send password reset instructions", "newPasswordRequired": "New password is required", "pleaseChangePassword": "Please change your password", "passwordsDoNotMatch": "Wrong password", "passwordMustBeAtLeast8Characters": "Password must be at least 8 characters", "codeRequired": "Please confirm the verification code", "resendCodeCountdown": "Resend in {{seconds}}s", "userExistAndVerified": "This email is already registered and verified. Please log in or use a different email."}, "onboarding": {"step1": {"title": "What brings you here?", "options": {"facebook": "Facebook", "zalo": "<PERSON><PERSON>", "youtube": "Youtube", "instagram": "Instagram", "tiktok": "TikTok", "google": "Google", "linkedin": "LinkedIn", "referral": "Via referral", "other": "Other"}, "otherPlaceholder": "X, Threads, Whatsapp, etc."}, "step2": {"title": "Have you ever used an AI chat software?", "options": {"never": "Never", "tried": "Tried it before", "regularly": "Use it regularly"}}, "step3": {"title": "What best describes the industry you work in?", "options": {"ecommerce": "E-commerce", "travel": "Travel", "real_estate": "Real estate", "health": "Healthcare & Beauty", "education": "Education", "other": "Other"}, "otherPlaceholder": "Information technology, Logistics, etc."}, "step3_part2": {"title": "How large is your company?", "options": {"just_me": "Just me", "2-9": "2-9", "10-49": "10-49", "50-199": "50-199", "200-499": "200-499", "500+": "500+"}, "inputLabel": "Website URL (Optional)", "inputPlaceholder": "www.example.com"}, "step4": {"title": "What type of company do you have?", "options": {"specialty_clinic": "General Hospital", "aesthetic_clinic": "Aesthetic Clinic/ Spa", "cosmetic_surgery": "Cosmetic surgery", "nutrition_clinic": "Nutrition Clinic & Sports Medicine", "telemedicine": "Telemedicine & HealthTech", "pharma": "Pharmaceutical & Cosmetic retail chain", "other": "Other"}, "otherPlaceholder": "Wellness center, Biotechnology startup, etc."}, "step5": {"title": "Which specialist do you need support from?", "options": {"consulting": "Consulting", "customer_care": "Customer care", "accounting": "Accounting", "marketing": "Marketing", "other": "Other"}, "otherPlaceholder": "Legal advisor, IT support, etc."}, "step6": {"title": "What is your goal in using the OnexBots platform?", "options": {"feedback": "Collect and analyze feedback", "pressure": "Reduce peak hour pressure", "channels": "Handle multiple channels simultaneously", "quality": "Improve interaction quality", "responses": "Automate quick responses", "monitor": "Train and monitor agents", "other": "Other"}, "otherPlaceholder": "Lead generation, Customer onboarding, etc."}, "buttons": {"back": "Back", "skip": "<PERSON><PERSON>", "continue": "Continue", "done": "Done"}, "otherInput": {"pleaseSpecify": "Please specify", "optional": "(Optional)"}}, "common": {"submit": "Submit", "dataCreatedBySystem": "Root data", "exceedTotalSize": "Total file size must not exceed 5MB", "tryingToAdd": "Trying to add", "description": "Description", "fileSizeMustNotExceed": "File size must not exceed", "onlyImageFilesAllowed": "Only image files are allowed", "resetToDefault": "Reset to default", "settings": "Settings", "viewOnWeb": "View on web", "confirmCancel": "This action cannot be undone. Can<PERSON> changes?", "pickADateRange": "Pick a date range", "selected": "selected", "other": "Other", "escTo": "Esc to", "at": "at", "deactivate": "Deactivate", "activate": "Activate", "updatedAt": "Updated at", "resetPassword": "Reset password", "areYouAbsolutelySure": "Are you absolutely sure?", "areYouAbsolutelySureDescription": "This action cannot be undone. Are you sure you want to proceed?", "canNotDeleteStage": "Can not delete this stage", "canNotDeleteStageDescription": "You must move all opportunities out of this stage before it can be deleted.", "selectCountry": "Select a country", "displayCustomizer": "Display Customizer", "customizeTheDisplayOfContentColumnsAccordingToYourPreferences": "Customize the display of content columns according to your preferences.", "noDataAvailable": "No data available", "start": "Start", "back": "Back", "words": "words", "confirm": "Confirm", "apply": "Apply", "totalSize": "Total size", "aspectRatio": "Aspect ratio", "formats": "Formats", "recommendedSize": "Recommended size", "search": "Search", "searchPlaceholder": "Search...", "filter": "Filter", "reset": "Reset", "setAsDefault": "Set as default", "saveFilters": "Save filters", "sort": "Sort", "view": "View", "add": "Add", "update": "Update", "edit": "Edit", "delete": "Delete", "cancel": "Cancel", "save": "Save", "saving": "Saving...", "close": "Close", "clear": "Clear", "loading": "Loading...", "loadingMore": "Loading more...", "deleting": "Deleting...", "toggleGrid": "Toggle Grid", "snapToGrid": "Snap to Grid", "alignHorizontally": "Align Horizontally", "alignVertically": "Align Vertically", "noData": "No data available", "success": "Success", "uploadImage": "Drag and drop an image or", "upload": "Upload", "uploading": "Uploading...", "fileSizeError": "File size must be less than 5MB", "uploadError": "Failed to upload image", "imageUploadError": "Failed to upload staff image", "areYouSure": "Are you sure?", "leaveDesc": "Any unsaved changes will be lost.", "deleteTaskConfirmation": "This action cannot be undone. This task will be permanently deleted.", "deleteProductConfirmation": "This action cannot be undo. This product will be permanently deleted.", "deleteListProductConfirmation": "This action cannot be undo.This {{count}} products will be permanently deleted.", "deleteOpportunityConfirmation": "This action cannot be undone. This opportunity will be permanently deleted.", "deleteEmployeeConfirmation": "This action cannot be undone. This {{count}} employees will be permanently deleted.", "install": "Cài đặt", "configure": "Configure", "deleteSuccess": "Product deleted successfully", "deleteError": "Failed to delete product", "deleteSuccessDescription": "Product has been deleted successfully", "actions": "Action", "bulkActions": "Bulk Actions", "all": "All", "sortBy": "Sort by", "select": "Select", "selectType": "Select type", "label": "Label", "issues": "Issues", "status": {"active": "Active", "available": "Available", "occupied": "Occupied", "disabled": "Disabled", "enabled": "Enabled", "suspended": "Suspended", "maintenance": "Maintenance", "inactive": "Inactive"}, "empty": {"description": "No data found."}, "healthy": "Healthy", "complete": "Complete", "inProgress": "In Progress", "processing": "Processing", "error": "Error", "ready": "Ready", "pending": "Pending", "online": "Online", "offline": "Offline", "confirmed": "Confirmed", "unconfirmed": "Unconfirmed", "unknown": "Unknown", "resetRequired": "Reset required", "forceChangePassword": "Force change password", "externalProvider": "External provider"}, "confirmationStatus": "Confirmation status", "errorFetchingPermissions": "Failed to fetch user permissions", "accessDenied": "Access denied", "requestAccess": "Request access", "saveChanges": "Save changes", "unsavedChanges": "Unsaved changes", "unsavedChangesDescription": "You have unsaved changes. Are you sure you want to close without saving?", "discard": "Discard", "keepEditing": "Keep editing", "week": "Week", "month": "Month", "quarter": "Quarter", "year": "Year", "units": "units", "leaveWithoutSavingDescription": "You have unsaved changes. Are you sure you want to leave without saving?", "leaveWithoutSaving": "Leave without saving", "leave": "Leave", "restore": "Rest<PERSON>", "stay": "Stay", "knowledgeUpdated": "Knowledge updated successfully", "knowledgeDeleted": "Knowledge deleted successfully", "areYouSureDescription": "Are you sure you want to delete this knowledge?", "staffUpdated": "Staff updated successfully", "updateStaffError": "Failed to update staff", "areYouSureConfirm": "Confirm", "areYouSureCancel": "Cancel", "updateAttribute": "Update attribute", "time": {"month": "Month", "timeAgo": {"seconds": "{{count}} second ago", "seconds_plural": "{{count}} seconds ago", "minutes": "{{count}} minute ago", "minutes_plural": "{{count}} minutes ago", "hours": "{{count}} hour ago", "hours_plural": "{{count}} hours ago", "days": "{{count}} day ago", "days_plural": "{{count}} days ago", "months": "{{count}} month ago", "months_plural": "{{count}} months ago", "years": "{{count}} year ago", "years_plural": "{{count}} years ago", "invalidDate": "Invalid date"}}, "empty": {"title": "There's nothing here!", "description": "No matching result found."}, "create": "Create", "noFileSelected": "No file selected", "uploadSuccess": "Upload successful", "fileTooLarge": "File exceeds maximum size of {{max}}MB", "lastUpdated": "Last updated", "name": "Name", "loadMore": "Load more", "markAsDone": "<PERSON> as <PERSON>", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "fitToScreen": "Fit to Screen", "backToOverview": "Back to Overview", "progress": "Progress", "opacity": "Opacity", "visible": "Visible", "properties": "Properties", "duplicate": "Duplicate", "quickActions": "Quick Actions", "imageLoadError": "Failed to load image", "uploadNew": "Upload New", "viewMode": "View Mode", "editMode": "Edit Mode", "total": "Total", "allow": "Allow", "enterNote": "Enter note", "noPermission": "You don't have permission to perform this action", "noRoutePermission": "You don't have access to this page", "noModuleAccess": "You don't have access to this module", "tenants": {"title": "Tenants", "addTenant": "Add Tenant", "editTenant": "Edit Tenant", "tenant": "Tenant", "tenantDetail": "Tenant Detail", "tenantDetails": "Tenant Details", "tenantId": "Tenant ID", "tenantName": "Tenant Name", "sections": {"personalInfo": "Personal Information", "contactInfo": "Contact Information", "emergencyContact": "Emergency Contact", "leaseInfo": "Lease Information", "documents": "Documents", "paymentInfo": "Payment Information", "notes": "Notes", "identification": "Identification", "employment": "Employment"}, "fields": {"firstName": "First Name", "lastName": "Last Name", "fullName": "Full Name", "email": "Email", "phone": "Phone", "phoneNumber": "Phone Number", "address": "Address", "dateOfBirth": "Date of Birth", "occupation": "Occupation", "emergencyContactName": "Emergency Contact Name", "emergencyContactPhone": "Emergency Contact Phone", "relationship": "Relationship", "leaseStart": "Lease Start", "leaseEnd": "Lease End", "rentAmount": "Rent Amount", "securityDeposit": "Security Deposit", "unit": "Unit", "status": "Status", "notes": "Notes", "documents": "Documents", "identificationType": "Identification Type", "identificationNumber": "Identification Number", "employmentStatus": "Employment Status", "employerName": "Employer Name", "monthlyIncome": "Monthly Income"}, "placeholders": {"enterFirstName": "Enter first name", "enterLastName": "Enter last name", "enterEmail": "Enter email address", "enterPhone": "Enter phone number", "enterOccupation": "Enter occupation", "selectUnit": "Select unit", "selectStatus": "Select status", "enterNotes": "Enter notes", "firstName": "Enter first name", "lastName": "Enter last name", "email": "Enter email address", "phone": "Enter phone number", "identificationType": "Select identification type", "identificationNumber": "Enter identification number", "employmentStatus": "Select employment status", "employerName": "Enter employer name", "monthlyIncome": "Enter monthly income", "emergencyContactName": "Enter emergency contact name", "emergencyContactPhone": "Enter emergency contact phone"}, "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "terminated": "Terminated"}, "actions": {"save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "view": "View Details"}, "messages": {"saveSuccess": "Tenant saved successfully", "saveError": "Failed to save tenant", "deleteSuccess": "Tenant deleted successfully", "deleteError": "Failed to delete tenant", "deleteConfirm": "Are you sure you want to delete this tenant?", "required": "This field is required", "createSuccess": "Tenant created successfully", "updateSuccess": "Tenant updated successfully", "createError": "Failed to create tenant", "updateError": "Failed to update tenant"}, "contracts": {"title": "Contracts", "contract": "Contract", "contractId": "Contract ID", "startDate": "Start Date", "endDate": "End Date", "monthlyRent": "Monthly Rent", "status": "Status", "viewContract": "View Contract"}, "quickStats": "Quick Stats", "activeContracts": "Active Contracts", "documents": "Documents", "stats": {"totalContracts": "Total Contracts", "activeContracts": "Active Contracts", "joinDate": "Join Date"}, "employmentStatus": {"employed": "Employed", "unemployed": "Unemployed", "self_employed": "Self Employed", "student": "Student", "retired": "Retired"}, "identificationTypes": {"passport": "Passport", "national_id": "National ID", "driver_license": "Driver License"}, "errors": {"notFound": "Tenant Not Found", "notFoundDescription": "The tenant you are looking for does not exist or has been deleted."}, "deleteConfirmation": {"title": "Delete Tenant", "description": "Are you sure you want to delete this tenant? This action cannot be undone.", "hasActiveContracts": "This tenant has active contracts. Are you sure you want to delete?"}, "createTenant": "Create Tenant", "createDescription": "Fill in the details to create a new tenant", "editDescription": "Update the tenant information"}, "maintenance": {"form": {"requestId": "Request ID", "status": "Status", "priority": "Priority", "category": "Category", "reportedDate": "Reported Date", "description": "Description", "estimatedCost": "Estimated Cost", "contractorName": "Contractor Name", "contractorPhone": "Contractor Phone", "notes": "Notes", "actualCost": "Actual Cost"}, "details": {"overview": "Overview", "timeline": "Timeline", "daysSinceReported": "Days Since Reported", "cost": "Cost", "related": "Related", "contractor": "Contractor"}, "status": {"in_progress": "In Progress", "pending": "Pending", "completed": "Completed", "cancelled": "Cancelled", "open": "Open"}, "priority": {"low": "Low", "medium": "Medium", "high": "High", "urgent": "<PERSON><PERSON>"}, "category": {"plumbing": "Plumbing", "electrical": "Electrical", "hvac": "HVAC", "structural": "Structural", "appliance": "Appliance", "cosmetic": "Cosmetic", "cleaning": "Cleaning", "security": "Security", "general": "General", "other": "Other"}, "messages": {"deleteSuccess": "Maintenance request deleted successfully", "deleteError": "Failed to delete maintenance request", "statusUpdateSuccess": "Status updated successfully", "statusUpdateError": "Failed to update status"}, "errors": {"notFound": "Maintenance Request Not Found", "notFoundDescription": "The maintenance request you are looking for does not exist or has been deleted."}, "dialog": {"deleteTitle": "Delete Maintenance Request", "deleteDescription": "Are you sure you want to delete this maintenance request? This action cannot be undone."}}, "shapes": {"rectangle": "Rectangle", "circle": "Circle", "polygon": "Polygon"}, "pages": {"editor": {"placeholder": "Press / for commands...", "characters": "characters", "words": "words"}, "blogList": {"title": "Blog List", "headers": {"image": "Image", "title": "Title", "category": "Category", "updatedAt": "Updated At", "actions": "Actions"}, "filters": {"search": {"placeholder": "Search blogs..."}, "category": "Category", "createdAt": "Created At", "updatedAt": "Updated At"}, "actions": {"import": "Import", "addBlog": "Add Blog"}}, "checkout": {"title": "Checkout", "orderInformation": {"title": "Order Information", "confirm": "Confirm", "cancel": "Cancel", "subscribeTo": "Subscribe to", "registrationTime": "Registration time", "price": "Price", "creationDate": "Creation date", "paymentMethod": "Payment method", "issueInvoice": "Issue invoice", "companyName": "Company name", "taxId": "Tax identification number", "address": "Address", "email": "Company email", "vat": "VAT (0%)", "total": "Total amount", "pay": "Pay", "companyNamePlaceholder": "E.g. Company name", "taxIdPlaceholder": "Enter tax identification number", "addressPlaceholder": "Enter Address", "emailPlaceholder": "<EMAIL>"}, "qrPayment": {"title": "QR code for transfer", "accountName": "Account name", "accountNumber": "Account number", "bankName": "Bank", "amount": "Amount", "content": "Content", "cancelOrder": "Cancel order", "success": "Payment successful !", "redirectingIn": "Redirecting to subscription page in {{countdown}} seconds..."}}, "opportunities": {"noOrderYet": "No order yet", "clickTabToAddOrder": "Click tab to add order", "orders": "Order", "opportunities": "Opportunity(s)", "deleteSuccess": "Opportunity deleted successfully", "deleteError": "Failed to delete opportunity", "title": "Opportunities", "add": "Add", "search": "Search...", "addOpportunity": "New Opportunity", "contact": "Contact", "salesperson": "Salesperson", "expectedClosing": "Expected closing", "tags": "Tags", "phone": "Phone", "enterContactName": "Enter contact name...", "enterEmailAddress": "Enter email address...", "enterTags": "Enter tags...", "enterPhoneNumber": "Enter phone number...", "opportunityDetail": "Opportunity detail", "enterOpportunityTitle": "Enter opportunity title...", "enterProbability": "Enter probability...", "expectedRevenue": "Expected revenue", "probability": "Probability", "customerInfo": "Customers Information", "notes": "Notes", "enterRevenue": "Enter revenue...", "selectAssignee": "Select an assignee", "enterExpectedClosingDate": "Select expected closing date...", "status": {"won": "Won", "lost": "Lost", "ongoing": "Ongoing"}, "lostReason": {"title": "Lost Reason", "description": "Please select a reason why this opportunity was lost or provide a custom reason.", "selectReason": "Select Reason", "selectPlaceholder": "Choose a reason...", "searchPlaceholder": "Search reasons...", "noResults": "No reasons found.", "customReason": "Custom Reason", "customPlaceholder": "Please specify the reason...", "priceTooHigh": "Price too high", "competitorWon": "Competitor won", "noBudget": "No budget", "timingIssues": "Timing issues", "technicalRequirements": "Technical requirements not met", "decisionMakerChanged": "Decision maker changed", "projectCancelled": "Project cancelled", "poorFit": "Poor fit for needs", "noLongerInterested": "No longer interested", "stopResponding": "Stop responding", "foundBetterAlternative": "Found better alternative", "productDidNotMeetExpectations": "Product did not meet expectations"}}, "stages": {"egDiscuss": "e.g. <PERSON><PERSON> proposal", "createOpportunity": "Create Opportunity", "opportunityNamePlaceholder": "e.g. <PERSON><PERSON><PERSON> for Clinnic", "contactPlaceholder": "e.g. <PERSON>", "editColumn": "Edit column", "columnName": "Column name", "isWonStage": "Is Won Stage?", "deleted": "Stage deleted successfully", "added": "Stage added successfully", "failedToCreateStage": "Failed to create stage", "yesterday": "Yesterday", "overdue": "Overdue", "today": "Today", "upcoming": "Upcoming", "noDueDate": "No Due Date", "noActivity": "No Activity", "inDays": "In {{count}} days", "tomorrow": "Tomorrow", "daysAgo": "{{count}} days ago", "filters": {"createdAt": "Created At", "updatedAt": "Updated At", "outcome": "Outcome"}, "newColumn": "New column", "newColumnPlaceholder": "Enter column name and enter", "unassigned": "Unassigned", "editActivity": "Edit activity", "fold": "Fold", "edit": "Edit", "delete": "Delete", "view": "View", "addColumnAfter": "Add column after", "addColumnBefore": "Add column before", "deleteColumn": "Delete column", "title": "Pipeline", "add": "Add", "search": "Search...", "addOpportunity": "New Opportunity", "status": "Status", "opportunity": "Opportunity name", "contact": "Contact name", "phone": "Phone", "expectedRevenue": "Expected revenue", "priority": "Priority", "assignee": "Assignee", "selectAssignee": "Select an assignee", "noScheduledActivities": "No scheduled activities", "noActivitiesForStatus": "No activities for {{status}}", "scheduleActivity": "Schedule Activity", "activityType": "Activity type", "selectActivityType": "Select activity type", "searchActivityTypes": "Search activity types...", "dueDate": "Due date", "summary": "Summary", "notes": "Notes", "typeSomething": "Type something...", "searchAssignee": "Search assignee...", "noAssigneeFound": "No assignee found.", "pickADate": "Pick a date", "schedule": "Schedule", "actionName": "Action name", "newActivityType": "New activity type", "salesperson": "Salesperson", "expectedClosing": "Expected closing", "tags": "Tags", "headers": {"salesPerson": "Sales Person", "contact": "Contact Name", "email": "Email", "opportunity": "Opportunity", "stage": "Stage", "status": "Status", "assignee": "Assignee", "createdAt": "Created At", "updatedAt": "Updated At", "customer": "Customer", "amount": "Amount", "closeDate": "Close Date", "probability": "Probability", "expectedClosingDate": "Expected closing date", "expectedRevenue": "Expected Revenue", "activities": "Activities"}, "sort": {"title": "Sort by", "order": "Order", "closingDate": "Closing date", "dealValue": "Deal value", "created": "Created", "lastActivity": "Last activity", "winProbability": "Win probability", "rating": "Rating", "lowestToHighest": "Lowest to highest", "highestToLowest": "Highest to lowest"}}, "onexbotsDashboard": {"title": "OnexBot dashboard", "filters": {"period": "Time period", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "yearly": "Yearly", "staff": "Staff"}, "stats": {"conversations": "Conversations", "users": "Users", "accuracyRate": "Accuracy rate", "averageResponseTime": "Average response time", "viewMore": "View more", "fromLastPeriod": "from last period", "noComparisonData": "No comparison data"}, "accuracyRateChart": {"title": "Accuracy rate", "description": "The accuracy of answers compared to the knowledge base", "tooltip": {"rate": "Rate", "resolved": "Resolved", "total": "Total"}}, "responseTimeChart": {"title": "Average response time", "description": "Average response time", "fastest": "Fastest", "slowest": "Slowest", "tooltip": {"average": "Average", "min": "Min", "max": "Max"}}}, "onboarding": {"welcomeTo": "Welcome to", "skip": "<PERSON><PERSON>", "done": "Done"}, "overview": {"title": "Overview", "filters": {"period": "Time period", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "yearly": "Yearly", "selectLocation": "Select location", "refresh": "Refresh"}, "stats": {"totalFacilities": "Total facilities", "totalPatients": "Total patients", "averageOccupancy": "Occupancy rate", "totalRevenue": "Total revenue", "viewMore": "View more", "fromLastMonth": "from last month"}, "patientStats": {"title": "Patient Statistics", "outpatient": "Outpatient", "inpatient": "Inpatient"}, "topTwenty": {"title": "Top 20", "icdDiagnoses": "ICD diagnoses", "prescribedMedications": "Prescribed medications"}, "costs": {"averageTreatmentCosts": "Average treatment costs", "insurancePayments": "Insurance payments", "insurance": "Insurance", "service": "Service", "specialCare": "Special care"}, "treatmentOutcomes": {"title": "Treatment outcomes", "recovered": "Recovered", "improved": "Improved", "unchanged": "Unchanged", "deteriorated": "Deteriorated", "deceased": "Deceased", "left": "Left"}}, "customers": {"title": "Customer List", "filters": {"search": {"placeholder": "Search by name"}, "group": "Group", "createdAt": "Created at", "updatedAt": "Updated at"}, "orderHistory": "Order History", "name": "Name", "phone": "Phone", "email": "Email", "address": "Address", "group": "Group", "createdAt": "Created at", "updatedAt": "Updated at"}, "orders": {"filters": {"shift": "Shift", "status": "Order status", "paymentStatus": "Payment status", "createdAt": "Created date", "updatedAt": "Updated date"}, "orderStatus": {"draft": "Draft", "shipping": "Shipping", "await_packing": "Awaiting packing", "delivered": "Delivered", "pending": "Pending", "confirmed": "Confirmed", "cancelled": "Cancelled", "completed": "Completed", "partial": "Partial", "returned": "Returned"}, "orderPaymentStatus": {"unpaid": "Unpaid", "pending": "Pending", "paid": "Paid", "partiallyPaid": "Partially paid", "cancelled": "Cancelled"}, "order": "Order", "printInvoice": "Print invoice", "printOrder": "Print order", "printReceipt": "Print receipt", "productInformation": "Product information", "qty": "Qty", "totalAmount": "Total amount", "notes": "Notes", "hasBeenCreatedSuccessfully": "has been created successfully", "orderHistory": "Order history", "amount": "Amount", "redeemPoints": "Redeem Points", "loyalPoints": "Loyalty Points", "updatedAt": "Updated at", "title": "Order list", "searchPriceGroup": "Search price group", "noPriceGroupsFound": "No price groups found", "searchBranch": "Search branch", "noBranchFound": "No branches found", "emptyServiceName": "Please fill in all service names", "updateOrder": "Update", "confirm": "Are you sure?", "confirmCancel": "Confirm", "confirmDelete": "Delete", "cancelWarning": "This action cannot be undo. Can<PERSON> changes?", "cancelDelete": "This action cannot be undo. Delete item?", "selectGender": "Select gender", "tax": "Tax", "shipping": "Shipping fee", "addCustomer": "Add customer", "save": "Save", "defaultShippingAddress": "Default shipping address", "defaultBillingAddress": "Default billing address", "noAddressesFound": "No addresses found", "edit": "Edit", "removeRecipientInfo": "Remove recipient information", "addRecipientInfo": "Add recipient information", "address": "Address", "province": "Province", "district": "District", "ward": "Ward", "enterName": "Enter name", "enterAddress": "Enter address", "searchWard": "Search ward", "searchDistrict": "Search district", "selectWard": "Select ward", "selectDistrict": "Select district", "selectProvince": "Select province", "searchProvince": "Search province", "enterCompanyName": "Enter company name", "enterPhoneNumber": "Enter phone number", "noProvincesFound": "No provinces found", "noDistrictsFound": "No districts found", "noWardsFound": "No wards found", "shippingDefault": "Default shipping address", "billingDefault": "Default billing address", "addAddress": "Add address", "editCustomer": "Edit customer", "Name": "Full name", "phoneNumber": "Phone number", "email": "Email", "gender": "Gender", "enterEmail": "Enter email", "birthday": "Birthday", "pickADate": "Pick a date", "customerGroup": "Customer group", "selectCustomerGroup": "Select customer group", "companyName": "Company name", "addresses": "Addresses", "submit": "Submit", "accumulatedPoints": "Accumulated points", "groupName": "Group name", "placeholder": "Search variant by name, SKU, barcode...", "quantity": "Quantity", "price": "Price", "total": "Total", "noProductsFound": "No products found", "addService": "Add service (F9)", "loadingMore": "Loading more...", "addProduct": "Add product", "available": "Available", "onHand": "In stock", "note": "Note", "maximumAvailableQuantity": "Maximum available quantity", "branch": "Branch", "loadingCustomerDetails": "Loading customer details...", "customer": "Customer", "shippingAddress": "Shipping address", "billingAddress": "Billing address", "noCustomersFound": "No customers found", "loading": "Loading...", "searchCustomer": "Search customer", "payment": "Payment", "addPromotion": "Add promotion", "products": "Products", "subtotal": "Subtotal", "discount": "Discount", "voucher": "Voucher", "fees": "Fees", "promotions": "Promotions", "notePlaceholder": "Enter note", "tags": "Tags", "tagsPlaceholder": "Enter tags", "noProductsInOrder": "No products in the order.", "cancel": "Cancel", "addOrder": "Add order", "success": "Order created successfully!", "error": "Order could not be processed", "adjustPrice": "Adjust price", "adjustPriceSuccess": "Price adjusted successfully!", "adjustPriceError": "Failed to adjust price", "adjustPriceDescription": "Adjust the price of the selected product", "adjustPricePlaceholder": "Enter new price", "adjustPriceButton": "Adjust price", "adjustPriceCancel": "Cancel", "setNewPrice": "Set new price", "value": "Value", "percent": "Percent", "addProductToOrderWarning": "Please add products to the order", "selectCustomer": "Please select a customer", "addVoucher": "Add voucher", "voucherCode": "Voucher", "voucherCodePlaceholder": "Enter voucher code", "voucherCodeButton": "Add voucher", "voucherCodeSuccess": "Voucher added successfully", "voucherCodeError": "Failed to add voucher"}, "variants": {"title": "Variants", "filters": {"search": {"placeholder": "Search by name, SKU, barcode"}}}, "products": {"title": "Products", "addBulk": {"title": "Add Bulk Product", "notice": "Notice:", "templateInstructions": "Please import the file according to the template to avoid data discrepancies.", "simpleTemplate": "Download our simple template", "advancedTemplate": "Download our advanced template", "here": "here", "supportedFiles": "Supported file formats: .xlsx, .xls, .csv", "dragAndDrop": "Drag files here or", "uploadButton": "Upload", "fileUpload": "File Upload", "cancel": "Cancel", "import": "Import", "importing": "Importing...", "selectFileError": "Please select a file to upload", "importSuccess": "Products imported successfully", "importError": "An error occurred while importing products"}, "filters": {"search": {"placeholder": "Search by name, SKU, barcode", "placeholderBrand": "Search brand..."}, "product": "Product", "source": "Source", "category": "Category", "brand": "Brand", "createdAt": "Created at", "updatedAt": "Updated at", "otherFilters": {"title": "Other filters", "all": "All", "description": "The first two filters will be prioritized for display on the main page. Customize them based on your needs."}, "deletionFailed": "Failed to delete variant", "deletedSuccessfully": "<PERSON><PERSON><PERSON> deleted successfully", "dateOptions": {"allTime": "All time", "today": "Today", "yesterday": "Yesterday", "lastWeek": "Last week", "thisWeek": "This week", "lastMonth": "Last month", "thisMonth": "This month", "customize": "Customize"}}, "headers": {"productInfo": "Product information", "category": "Category", "brand": "Brand", "updatedAt": "Updated at", "createdAt": "Created at", "available": "Available", "variant": "<PERSON><PERSON><PERSON>"}, "actions": {"addProduct": "Add product", "addManual": {"title": "Add product", "onThisPage": "On this page", "sections": {"basicInfo": "Basic information", "options": "Product options", "units": "Packing units", "prices": "Product prices", "measurements": "Measurements"}}, "addQuick": "Quick add product", "addBulk": "Bulk add products", "refresh": "Refresh", "saveFilters": "Save filters", "reset": "Reset", "filter": "Filter"}, "descriptionDeleteOption": "This action cannot be undo. Data related to Package unit, Product prices and Measurements will be permanently deleted.", "descriptionDeleteValueOption": "This action cannot be undo. Data related to Product Prices and Measurements will be permanently deleted.", "name": "Name", "sku": "SKU code", "barcode": "Barcode", "option1": "Option 1", "option2": "Option 2", "option3": "Option 3", "unit": "Unit", "weight": "Weight", "height": "Height", "width": "<PERSON><PERSON><PERSON>", "length": "Length", "source": "Source", "category": "Category", "brand": "Brand", "createdAt": "Created at", "description": "Description", "viewLess": "View less", "viewMore": "View more", "variantDetails": "Variant details", "variants": "Variants", "noPricesAvailable": "No prices available", "prices": "Prices", "tags": "Tags", "inventory": {"noMatchResult": "No matching result found", "title": "Inventory", "branch": "Branch", "history": "History", "allBranches": "All branches", "inventory": "Inventory", "packing": "Packing", "shipping": "Shipping", "minValue": "Min value", "maxValue": "Max value", "staff": "Staff", "transactionType": "Transaction type", "change": "Change", "quantity": "Quantity", "reference": "Reference", "available": "Available", "incoming": "Incoming", "onHand": "In stock"}, "addManual": {"title": "Add manual product", "onThisPage": "On this page", "publish": "Publish", "sections": {"addVariant": "Create variants when there is more than one option, such as different sizes or colors.", "variant": "<PERSON><PERSON><PERSON>", "all": "All", "apply": "Apply", "unitPlaceholder": "Select unit", "unitSearchPlaceholder": "Search unit", "variantSearchPlaceholder": "Search variant", "unitEmptyText": "No units found", "variantPlaceholder": "Select variant", "usedByAllVariants": " Used by all variants", "usedByThis": " Used by this", "addType": "Add type", "addUnit": "Add unit", "basicInfo": "Basic information", "option": "option", "options": "Product options", "units": "Packing units", "prices": "Product prices", "measurements": "Measurements", "selectImages": "Select images", "submit": "Submit", "cancel": "Cancel", "add": "Add", "edit": "Edit", "save": "Save", "stay": "Stay", "leave": "Leave", "values": "Values", "valuesPlaceholder": "Value 1, Value 2, Value 3", "optionsPlaceholder": "Enter option name", "addOption": "Add option", "optionName": "Option name", "addValue": "Add value", "remove": "Remove", "valuesPlaceholderInput": "Enter value", "duplicateValue": "This value is duplicated", "createVariant": "Create variants when there is more than one option, such as different sizes or colors."}, "basicInfo": {"brandPlaceholder": "Select brand", "brandSearchPlaceholder": "Search brand", "brandEmptyText": "No brands found", "categoryPlaceholder": "Select category", "categorySearchPlaceholder": "Search category", "categoryEmptyText": "No categories found", "tagsPlaceholder": "Enter tags", "images": "Images", "name": "Name", "description": "Description", "shortDescription": "Short Description", "brand": "Brand", "category": "Category", "sku": "SKU code", "tags": "Tags", "price": "Price", "uploadImage": "Upload image", "optimize": "Optimize", "required": "This field is required", "imageRequired": "At least one image is required", "nameRequired": "Product name is required", "nameWarning": "Product name is required for optimization", "descriptionWarning": "Description is required for optimization", "priceRequired": "Price is required", "skuRequired": "SKU code is required"}, "options": {"addOption": "Add option", "optionName": "Option name", "values": "Values", "addValue": "Add value", "remove": "Remove"}, "units": {"title": "Packing units", "addUnit": "Add unit", "unitName": "Unit name", "ratio": "<PERSON><PERSON>", "remove": "Remove"}, "prices": {"title": "Product prices", "addGroup": "Add new price group", "groupName": "Group name", "price": "Price", "apply": "Apply", "applyAll": "Apply to all"}, "measurements": {"weight": "Weight", "height": "Height", "width": "<PERSON><PERSON><PERSON>", "length": "Length", "apply": "Apply", "applyAll": "Apply to all"}, "buttons": {"cancel": "Cancel", "add": "Add", "edit": "Update", "save": "Save", "stay": "Stay", "leave": "Leave"}, "dialogs": {"leaveTitle": "Are you sure you want to leave?", "leaveDesc": "Any unsaved changes will be lost."}, "validation": {"hasErrors": "Validation error", "checkFields": "Please check all required fields and try again"}, "success": "Product created", "successUpdate": "Product updated", "successDescription": "Product has been created successfully", "successDescriptionUpdate": "Product has been updated successfully", "error": "Error", "errorDescription": "Failed to create product. Please try again.", "errorDescriptionUpdate": "Failed to update product. Please try again."}, "deletedSuccessfully": "<PERSON><PERSON><PERSON> deleted successfully", "deletionFailed": "Failed to delete variant"}, "choosePlan": {"forIndividuals": "for Individuals", "forCompanies": "for Companies", "monthly": "Monthly", "annually": "Annually", "chooseAPlan": "Choose a plan", "planDescription": {"firstSection": "Choose a plan that best fits your business needs.", "middleSection": "You can always upgrade or downgrade later.", "secondSection": "All plans include basic features."}, "planData": {"Up to 50 variants": "Up to 50 variants, overall statistics.", "Real-time inventory syncing": "Real-time inventory syncing", "Ideal for startups (1,000 items)": "Ideal for startups (1,000 items)", "Analytics dashboard": "Analytics dashboard", "User-friendly interface": "User-friendly interface.", "Support for multiple currencies and languages": "Support for multiple currencies and languages", "Real time inventory": "Real-time inventory"}, "planNames": {"Free": "Free", "Starter": "Starter", "Pro": "Pro", "Agency": "Agency"}, "mostPopular": "Most popular", "numberIntegrations": "Number of integrations", "explainNoIntegrations": "No integrations available", "getStarted": "Get Started"}, "synchronization": {"platforms": {"source": "Source", "destination": "Destination"}, "title": {"success": "Connect {{source}} to {{destination}}", "error": "Synchronize configuration"}, "error": {"missingConnection": "No connection found", "connectionError": "Connection error", "sourceNotFound": "Source not found", "destinationNotFound": "Destination not found"}, "success": {"completeTitle": "Connection completed!", "gotoDashboard": "Go to dashboard"}, "description": "Choose a niche that matches your interests and target audience.", "syncSetting": {"title": "Sync settings", "product": {"title": "Products", "description": "Sync products from {{source}} to {{destination}}"}, "inventory": {"title": "Inventory", "description": "Keep inventory levels synchronized"}, "order": {"title": "Orders", "description": "Import {{destination}} orders to {{source}}"}, "buttonTitle": "Connect to {{destination}}"}}, "syncRecords": {"title": "Sync records list", "filters": {"search": {"placeholder": "Search return orders..."}, "status": "Status", "recordType": "Record type", "channel": "Channel", "connectionId": "Connection ID", "fetchEventId": "Fetch Event ID"}, "columns": {"channel": "Channel", "header": "Record type", "fetchEventId": "Fetch Event ID", "connectionId": "Connection ID", "lastUpdated": "Last updated", "fetchedAt": "Fetched at", "finishedAt": "Finished at", "publishedAt": "Published at", "transformedAt": "Transformed at"}}, "fetchEvents": {"title": "Fetch event", "filters": {"search": {"placeholder": "Search Fetch event..."}, "status": "Status", "actionType": "Action type", "actionGroup": "Action group", "eventTime": "Event time", "eventSource": "Event source", "fetchEventId": "Fetch event ID"}, "headers": {"channel": "Channel", "actionType": "Action type", "actionGroup": "Action group", "eventSource": "Event source", "eventTime": "Event time", "status": "Status", "actions": "Actions"}, "columns": {"channel": "Channel", "header": "Record type", "fetchEventId": "Fetch event ID", "connectionId": "Connection ID", "lastUpdated": "Last updated", "fetchedAt": "Fetched at", "finishedAt": "Finished at", "publishedAt": "Published at", "transformedAt": "Transformed at"}}, "fetchEventDetail": {"title": "Fetch event details of", "actionGroup": "Action group", "connectionId": "Connection ID", "actionType": "Action type", "eventSource": "Event source", "retryCount": "Retry count", "status": "Status", "continuationToken": "Continuation Token", "objectId": "Object ID", "eventTime": "Event time", "createdAt": "Created at", "updatedAt": "Updated at", "eventNumber": "{{number}}. No ID"}, "channel": {"title": "Connection List", "filters": {"search": {"placeholder": "Search connections..."}, "status": "Status"}, "headers": {"channel": "Channel", "status": "Status", "url": "URL", "createdAt": "Created at", "lastUpdated": "Updated at", "actions": "Actions"}, "actions": {"install": "Install new channel", "configure": "Configure", "activate": "Activate", "deactivate": "Deactivate"}}, "supportedChannels": {"title": "Channel list", "filters": {"search": {"placeholder": "Search channel..."}}}, "settings": {"passwordsDontMatch": "Passwords don't match", "eachRoleAssignmentMustHaveABranchAndAtLeastOneRoleSelected": "Each role assignment must have a branch and at least one role selected", "cannotMixAllBranchesWithIndividualBranchSelections": "Cannot mix 'All Branches' with individual branch selections", "emailInvalid": "Invalid email format", "emailRequired": "Email is required", "passwordRequired": "Password is required", "confirmPasswordRequired": "Confirm password is required", "employeeNameRequired": "Employee name is required", "usernameMinLength": "Username must be at least 3 characters", "usernameInvalid": "Username can only contain letters, numbers, and underscores", "branchRequired": "Branch is required", "atLeastOneRoleRequired": "At least one role is required", "atLeastOneRoleAssignmentRequired": "At least one role assignment is required", "shopInfo": "Store information", "profileSettings": "Profile", "profileSettingsDescription": "Avatar, name, password", "storeInformation": "Store information", "storeInformationDescription": "Contact information, URL", "appearance": "Appearance", "appearanceDescription": "Logo, color, themes", "languageCurrency": "Language & Currency", "languageCurrencyDescription": "Language, currency support", "employeesPermissions": "Employees & Permissions", "employeesPermissionsDescription": "Assign roles, role manager", "employeeAccountAlreadyExists": "User account already exists", "employeeAccountExpired": "User account is expired. Please call with action RESEND to reset user account", "employeeAccountDoesNotExist": "Incorrect username or password.", "accessDenied": "Access denied", "employeeUpdatedSuccess": "Employee updated successfully", "employeeCreatedSuccess": "Employee created successfully", "employeeDeletedSuccess": "Employee deleted successfully", "employeeDeletedError": "Failed to delete employee", "employeeUpdatedError": "Failed to update employee", "employeeCreatedError": "Failed to create employee", "emailAlreadyInUse": "Email already in use", "phoneNumberAlreadyInUse": "Phone number already in use", "phone": "Phone", "birthday": "Birthday", "address": "Address", "employeeInfo": "Employee information", "username": "Username", "addEmployee": "Add Employee", "employeeName": "Employee name", "employeeNamePlaceholder": "E.g. <PERSON>", "usernamePlaceholder": "johnroger123", "emailPlaceholder": "<EMAIL>", "phonePlaceholder": "**********", "birthdayPlaceholder": "Pick a date", "addressPlaceholder": "123 Main Street", "password": "Password", "confirmPassword": "Confirm Password", "roleAssignments": "Role Assignments", "assignNewRole": "Assign new role", "branch": "Branch", "selectBranch": "Select branch", "selectRole": "Select role", "role": {"permissionsDescription": "Permissions allow you to control what actions each role can perform across different modules in the system.", "roleName": "Role name", "roleNameRequired": "Role name *"}, "add": "Add", "themeSetting": "Appearance", "saveSuccess": "Colors have been saved", "saveError": "Failed to save colors", "colorSetting": "Color Setting", "logoSetting": "Logo setting", "lightMode": "Light mode", "darkMode": "Dark mode", "language": {"language": "Language", "searchLanguage": "Search language", "searchCurrency": "Search currency", "addLanguage": "Add Language", "remove": "Remove", "addCurrency": "Add <PERSON>cy", "update": "Update", "currency": "<PERSON><PERSON><PERSON><PERSON>", "confirmRemoveTitle": "Are you absolutely sure?", "confirmRemoveDescription": "This action cannot be undone. This language will be permanently removed from the system."}, "theme": {"title": "Theme", "description": "Customize the theme of your store.", "lightMode": "Light mode", "darkMode": "Dark mode"}, "logo": {"title": "Logo", "lightTheme": "(Light theme)", "darkTheme": "(Dark theme)", "description": "Customize the logo shown on your store.", "lightModeLogo": "Light mode logo", "darkModeLogo": "Dark mode logo", "lightModeIcon": "Light mode icon", "darkModeIcon": "Dark mode icon", "favicon": "Favicon", "lightModeLogoDescription": "Upload logo for light mode (recommended size: 150x48px)", "darkModeLogoDescription": "Upload logo for dark mode (recommended size: 150x48px)", "lightModeIconDescription": "Upload icon for light mode (recommended size: 48x48px)", "darkModeIconDescription": "Upload icon for dark mode (recommended size: 48x48px)", "faviconDescription": "Upload favicon for the website (recommended size: 32x32px)", "saveSuccess": "Logo has been saved", "saveError": "Failed to save logo", "noChangesToSave": "No changes to save", "resetSuccess": "Logo has been reset", "resetError": "Failed to reset logo"}, "colors": {"title": "Custom brand colors", "description": "Customize your own brand color into your system.", "brandColor": "Brand color", "lightMode": "(Light theme)", "darkMode": "(Dark theme)"}, "color": {"saveSuccess": "Theme color has been saved", "saveError": "Unable to save theme color", "resetSuccess": "Theme color has been reset", "resetError": "Unable to reset theme color"}}, "profile": {"title": "Profile", "contactInformation": "Contact information", "contactInformationDescription": "Information used for customer to contact", "changePassword": "Change password", "aspectRatio": "Aspect ratio", "formats": "Formats", "name": "Name", "username": "Username", "email": "Email", "phone": "Phone", "update": "Update", "avatar": "Avatar", "changePasswordDiaglog": {"title": "Change password", "oldPassword": "Old password", "newPassword": "New password", "confirmPassword": "Confirm password", "enterOldPassword": "Enter old password", "enterNewPassword": "Enter new password", "enterConfirmPassword": "Enter confirm password", "passwordRequirements": "Password must be at least 8 characters long", "passwordChangedSuccessfully": "Password changed successfully", "passwordChangeFailed": "Password change failed", "passwordsDoNotMatch": "Passwords do not match", "passwordTooShort": "Password must be at least 8 characters long", "forgotPassword": "Forgot password?", "allFieldsRequired": "All fields are required", "currentPasswordRequired": "Current password is required", "confirmPasswordRequired": "Confirm password is required", "passwordComplexity": "Password must contain at least one lowercase letter, one uppercase letter, and one number", "passwordAttemptsExceeded": "Password attempts exceeded", "passwordDoesNotMatch": "Incorrect password."}, "forgotPasswordDialog": {"emailRequired": "Please enter your email", "codeRequired": "Please enter the verification code", "codeSentSuccessfully": "Verification code sent successfully", "codeSendFailed": "Failed to send verification code", "codeVerifiedSuccessfully": "Code verified successfully", "codeVerificationFailed": "Code verification failed", "codeResentSuccessfully": "Code resent successfully", "codeResendFailed": "Failed to resend code", "passwordChangedSuccessfully": "Password changed successfully", "passwordChangeFailed": "Password change failed", "passwordsDoNotMatch": "Passwords do not match", "passwordTooShort": "Password must be at least 8 characters long", "allFieldsRequired": "All fields are required", "emailStep": {"title": "Send Verification Code", "description": "Confirm your email below and click 'Send Code' to receive your password reset code.", "emailLabel": "Email", "emailPlaceholder": "Enter your email", "sending": "Sending...", "sendCode": "Send Code"}, "verificationStep": {"title": "Enter Verification Code", "description": "Enter the verification code we just sent to {{email}}", "codeLabel": "Verification Code", "codePlaceholder": "Code", "yourEmail": "your email", "resend": "Resend", "verifying": "Verifying...", "continue": "Continue"}, "resetPasswordStep": {"title": "Change password", "description": "Enter a new password below to change your password", "newPasswordLabel": "New password", "newPasswordPlaceholder": "Enter new password", "confirmPasswordLabel": "Confirm password", "confirmPasswordPlaceholder": "Confirm new password", "changing": "Changing...", "continue": "Continue"}, "successStep": {"title": "Password changed", "description": "Now you can log in with your new password.", "loginButton": "Log in"}}}, "storeInformation": {"success": "Store information updated successfully", "error": "Failed to update store information", "title": "Store information", "contactInformation": "Contact information", "contactInformationDescription": "Manage your contact information", "changePassword": "Change password", "aspectRatio": "Aspect ratio", "formats": "Formats", "name": "Name", "username": "Username", "email": "Email", "phone": "Phone", "update": "Update", "storeInformationDescription": "Manage your store's contact information and settings"}, "languageCurrency": {"title": "Language & Currency", "description": "Manage your language and currency settings", "language": "Language", "currency": "<PERSON><PERSON><PERSON><PERSON>", "addLanguage": "Add language", "addCurrency": "Add currency", "remove": "Remove", "update": "Update", "back": "Back", "languageDescription": "Select the languages you want to support", "currencyDescription": "Select the currencies you want to support", "updating": "Updating...", "confirmRemoveTitle": "Are you absolutely sure?", "confirmRemoveDescription": "This action cannot be undone. This currency will be permanently removed from the system.", "currencySettingsUpdatedSuccessfully": "Currency settings updated successfully", "currencySettingsUpdatedFailed": "Failed to update currency settings"}, "employeePermission": {"branchAll": "All Branches", "title": "Employee & Permission", "description": "Manage employee and permission", "employee": "Employee", "role": "Role", "deactivateEmployee": "Deactivate this employee account?", "activateEmployee": "Activate this employee account?", "deactivateEmployeeDescription": "The employee will no longer be able to log in or access the system after deactivation. Are you sure you want to continue?", "activateEmployeeDescription": "The account will be activated, and the employee will be able to log in and use the system as normal. Are you sure you want to continue?", "deactivate": "Deactivate", "activate": "Activate", "deleteEmployeeTitle": "Are you absolutely sure?", "deleteEmployeeDescription": "This action cannot be undone. This employee will be permanently deleted.", "resetPassword": "Reset password", "resetPasswordDescription": "Generates a 24-hour temporary password sent via email. The user must log in and change it before expiry, or the account will be deactivated.", "temporaryPassword": "Temporary password", "passwordResetSuccess": "Password has been reset successfully", "passwordResetError": "Failed to reset password", "statusUpdateSuccess": "Employee status has been updated successfully", "statusUpdateError": "Failed to update employee status", "employeeDeleteSuccess": "Employee has been deleted successfully", "employeeDeleteError": "Failed to delete employee", "toggleStatusError": "Failed to update employee status", "employeeDeletedSuccess": "Employee has been deleted successfully", "employeeDeletedError": "Failed to delete employee", "employeeActivatedSuccess": "Employee has been activated successfully", "employeeDeactivatedSuccess": "Employee has been deactivated successfully", "selectRoles": "Select roles", "employeeCreatedSuccess": "Employee created successfully", "employeeCreatedError": "Failed to create employee", "employeeUpdatedSuccess": "Employee updated successfully", "employeeUpdatedError": "Failed to update employee", "passwordsDoNotMatch": "Passwords do not match", "creating": "Creating...", "updating": "Updating...", "update": "Update", "roleCreatedSuccess": "Role created successfully", "roleUpdatedSuccess": "Role updated successfully", "editEmployee": "Edit Employee", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "employeeAccountAlreadyExists": "User account already exists", "employeeAccountExpired": "User account is expired. Please call with action RESEND to reset user account", "employeeAccountDoesNotExist": "User account does not exist"}, "roleManagement": {"title": "Role Management", "description": "Manage roles and permissions for employees", "addRole": "Add Role", "editRole": "Edit Role", "viewMembers": "View Members", "duplicate": "Duplicate", "duplicateAndEdit": "Duplicate and Edit", "deactivated": "deactivated", "member": "member", "members": "members", "roleName": "Role Name", "roleNameRequired": "Role name is required", "roleNamePlaceholder": "E.g. <PERSON>", "note": "Note", "notePlaceholder": "Enter note", "permissions": "Permissions", "permissionsDescription": "Permissions allow you to control what actions each role can perform across different modules in the system.", "deleteRoleDescription": "This action cannot be undone. This role will be permanently deleted.", "crud": "CRUD", "action": "Action", "view": "View", "create": "Create", "edit": "Edit", "delete": "Delete", "groups": {"e_commerce": "E-commerce", "crm": "CRM", "virtual_staff": "Virtual Staff", "integration": "Integration"}, "modules": {"product": "Product", "order": "Order", "branch": "Branch", "variant": "<PERSON><PERSON><PERSON>", "brand": "Brand", "customer": "Customer", "stage": "Stage", "opportunity": "Opportunity", "activity": "Activity", "department": "Department", "staff": "Staff", "skill": "Skill", "task": "Task", "conversation": "Conversation", "knowledge": "Knowledge", "account": "Account", "role": "Role", "virtual_staff": "Virtual Staff", "crm": "CRM", "operations": "Operations", "e_commerce": "E-commerce", "finance": "Finance", "settings": "Settings", "reports": "Reports", "integration": "Integration"}, "permissionDetails": {"product": {"view": "View", "viewPublished": "View published product list", "viewAll": "View all product list", "viewDetail": "View product details", "add": "Add product", "edit": "Edit product information", "delete": "Delete product", "quickAdd": "Quick add product", "viewPriceGroup": "View product price group", "addFile": "Add product file", "export": "Export product list file", "import": "Import products", "bulkEdit": "Bulk edit", "variantManage": "Manage product variants", "categoryManage": "Manage product categories", "brandManage": "Manage brands"}, "order": {"print": "Print", "sendEmail": "Send email", "enterServiceName": "Enter service name", "serviceNameCannotBeEmpty": "Service name cannot be empty", "view": "View", "viewAssigned": "View assigned order list", "viewAll": "View all order list", "viewDetail": "View order details", "add": "Add order", "edit": "Edit order information", "editNote": "Edit order notes", "delete": "Delete order", "confirmStatus": "Confirm order status", "saveDraft": "Save draft order", "confirmPayment": "Confirm order payment status", "confirmComplete": "Confirm order completion status", "confirmCancel": "Confirm order cancellation status", "confirmPackage": "Confirm order packaging status and create package", "import": "Import orders", "export": "Export order list file", "refund": "Process refunds", "return": "Process returns", "shipping": "Manage shipping", "tracking": "Track orders"}, "virtual_staff": {"view": "View", "create": "Create", "edit": "Edit", "delete": "Delete", "assign": "Assign", "performance": "Performance", "training": "Training", "schedule": "Schedule"}, "crm": {"view": "View", "customerView": "Customer View", "customerCreate": "Customer Create", "customerEdit": "Customer Edit", "customerDelete": "Customer Delete", "leadView": "Lead View", "leadCreate": "Lead Create", "leadEdit": "Lead Edit", "leadDelete": "Lead Delete", "opportunityView": "Opportunity View", "opportunityCreate": "Opportunity Create", "opportunityEdit": "Opportunity Edit", "opportunityDelete": "Opportunity Delete", "activityView": "Activity View", "activityCreate": "Activity Create", "activityEdit": "Activity Edit", "activityDelete": "Activity Delete", "reportView": "Report View", "reportExport": "Report Export"}, "operations": {"view": "View", "inventoryView": "Inventory View", "inventoryManage": "Inventory Manage", "inventoryAdjust": "Inventory Adjust", "inventoryTransfer": "Inventory Transfer", "supplierView": "Supplier View", "supplierCreate": "Supplier Create", "supplierEdit": "Supplier Edit", "supplierDelete": "Supplier Delete", "purchaseView": "Purchase View", "purchaseCreate": "Purchase Create", "purchaseEdit": "Purchase Edit", "purchaseDelete": "Purchase Delete", "warehouseView": "Warehouse View", "warehouseManage": "Warehouse Manage", "logisticsView": "Logistics View", "logisticsManage": "Logistics Manage", "reportView": "Report View", "reportExport": "Report Export"}, "finance": {"view": "View", "accountView": "Account View", "accountCreate": "Account Create", "accountEdit": "Account Edit", "accountDelete": "Account Delete", "transactionView": "Transaction View", "transactionCreate": "Transaction Create", "transactionEdit": "Transaction Edit", "transactionDelete": "Transaction Delete", "invoiceView": "Invoice View", "invoiceCreate": "Invoice Create", "invoiceEdit": "Invoice Edit", "invoiceDelete": "Invoice Delete", "paymentView": "Payment View", "paymentProcess": "Payment Process", "reportView": "Report View", "reportExport": "Report Export"}, "settings": {"view": "View", "generalEdit": "General Edit", "userManage": "User Manage", "roleManage": "Role Manage", "systemManage": "System Manage", "backupManage": "Backup Manage", "logView": "Log View", "apiManage": "API Manage"}, "reports": {"view": "View", "salesView": "Sales Report View", "salesExport": "Sales Report Export", "inventoryView": "Inventory Report View", "inventoryExport": "Inventory Report Export", "financialView": "Financial Report View", "financialExport": "Financial Report Export", "customerView": "Customer Report View", "customerExport": "Customer Report Export", "customCreate": "Custom Report Create", "scheduleManage": "Report Schedule Manage"}}}, "installChannel": {"title": "Install channel"}, "productMappingList": {"syncSuccess": "Receive process mapping request successful", "syncFail": "Receive process mapping request failed", "noConnection": "No connection", "title": "Product Mapping", "description": "Map your Shopify products to TikTok Shop.", "filters": {"search": {"placeholder": "Search"}, "status": {"all": "All products", "synced": "Synced", "mapped": "Mapped", "unMapped": "Unmaped", "errors": "Errors"}}, "alert": {"title": "Confirm product synchronization?", "description": "You are about to synchronize all products between the two platforms. This process may take some time to complete.", "note": "Note:", "noteDescription": "If you only want to sync specific products, please select them from the table before proceeding.", "confirm": "Sync", "cancel": "Cancel", "areYouSure": "Are you sure you want to continue?", "unmapSuccess": "Product unmapped successfully", "unmapFail": "Fail to unmap product"}, "groupButton": {"settingButton": "Setting", "syncButton": "Sync product"}, "status": {"synced": "Synced", "mapped": "Mapped", "unmapped": "Unmaped", "error": "Errors"}, "actions": {"unmap": "Unmap", "map": "Map", "fix": "Fix attributes"}, "headers": {"product": "{{product}} Product", "price": "Price", "last_synced": "Last synced", "status": "Status", "actions": "Actions"}, "nomap": "Not mapped to {{destination}}"}, "productMapping": {"advancedMapping": {"title": "Advanced Mapping", "description": "Configure advanced mapping rules for your products.", "sourceField": "Source field", "transformationType": "Transformation type", "addTransformation": "Add transformation", "removeTransformation": "Remove transformation", "ruleConfiguration": "Rule configuration", "outputPreview": "Output preview", "finalOutput": "Final output", "applyTransformations": "Apply transformations", "transformationChain": "Transformation chain", "sampleData": "Sample data", "preview": "Preview", "output": "Output", "singleValue": "Single value", "transformationForm": "Transformation", "exampleUsage": "Example usage", "selectFieldsPlaceholder": "Select a field", "searchFieldsPlaceholder": "Search fields...", "source": "Source", "searchTransformationTypes": "Search transformation types...", "selectTransformationTypes": "Select transformation types..."}, "cancelledMessage": "Product sync canceled", "errorLoading": "Failed to load product sync details", "lastSynced": "Last synchronized", "manualRetry": "Retry manually", "mappingStatus": "Sync status"}, "staff": {"title": "Staff list", "name": "Name", "phoneNumber": "Phone number", "howCanICallYou": "How can I call you?", "enterYourPhoneNumber": "Enter your phone number...", "filters": {"department": "Department", "role": "Role", "search": {"placeholder": "Search staff..."}}, "actionButton": {"create": "Create staff"}, "columns": {"staff": "Staff", "role": "Role", "skills": "Skills", "task": "Task", "conversations": "Conversations", "actions": "Actions"}, "actions": {"view": "View", "edit": "Edit", "delete": "Delete"}, "maxCharactersReached": "Maximum 50 characters reached", "online": "Online", "noStaff": "No staff found.", "loading": "Loading...", "interact": "Interact", "createStaff": "Create staff", "staffName": "Staff name", "enterStaffName": "Enter staff name", "staffNameRequired": "Staff name is required", "maxCharacters": "Maximum 250 characters", "selectDepartment": "Select a department", "searchDepartments": "Search departments...", "selectRole": "Select a role", "searchRoles": "Search roles...", "creating": "Creating", "update": "Update", "role": "Role", "department": "Department", "expertise": "Expertise", "score": "Score", "knowledgeWarning": "The current knowledge may be too limited for accurate answers. Please consider adding more details to improve performance.", "avatar": {"title": "Avatar", "xbotAvatar": "XBot avatar", "image": "Image", "selectedAvatar": "Selected avatar", "avatar": "Avatar"}, "knowledge": {"tab": "Knowledge", "baby": "Baby", "warning": "The current knowledge may be too limited for accurate answers. Please consider adding more details to improve performance."}, "interactionStyle": {"tab": "Interaction style", "description": "Configure how the staff interacts with users", "communicationTone": "Communication tone", "languagePreferences": "Language preferences", "responseLength": "Response length", "temper": "Temper", "personalityTraits": "Personality traits", "formal": "Formal", "casual": "Casual", "detailed": "Detailed", "concise": "Concise", "creative": "Creative", "analytical": "Analytical", "ethicalConstraints": "Ethical constraints", "contentFiltering": "Enable content filtering and safety checks", "instruction": "Instruction", "instructionPlaceholder": "Enter custom instructions for this staff member (optional)", "greeting": "Greeting Message", "greetingPlaceholder": "Hi! I'm your AI staff from OneXBots. Let me know how I can help!", "welcomeMessage": "Hi there! I'm {{botName}}. How can I help you today?"}, "skills": {"tab": "Data access configuration", "description": "Configure which data sources the virtual staff can access", "products": "Products", "orders": "Orders", "inventory": "Inventory"}, "staffInfo": {"tab": "Staff information", "description": "Manage staff basic information"}, "task": {"tab": "Task", "description": "Manage staff tasks and assignments", "noTasks": "No tasks available"}, "editStaff": {"validation": {"nameRequired": "Name is required", "roleRequired": "Role is required", "departmentRequired": "Department is required", "greetingMaxLength": "Greeting message must be less than 100 characters"}, "tabs": {"staffInfo": "Staff Information", "interactionStyle": "Interaction Style", "knowledge": "Knowledge", "skills": "Skills", "task": "Task"}, "integration": "Integration", "embedCodeInstructions": "Follow the instructions below to embed the virtual staff widget on your website", "embedCodeTitle": "Embed Virtual Staff Widget", "embedVirtualStaffWidget": "Embed Virtual Staff Widget", "themesColor": "Themes color", "embedCode": "Embed code", "greeting": "OneXBots welcomes you", "title": "Edit staff", "staffName": "Staff name", "rolePurpose": "Role/Purpose", "department": "Department", "domainExpertise": "Domain expertise", "customExpertisePlaceholder": "Enter custom domain expertise and press Enter", "noRoleFound": "No role found", "noDepartmentFound": "No department found", "selectRole": "Select role", "selectDepartment": "Select department", "searchRoles": "Search roles...", "searchDepartments": "Search departments...", "namePhoneRequirement": "Name, phone number requirement", "roles": {"contentWriter": "Content writer", "seoSpecialist": "SEO Specialist", "socialMediaManager": "Social media manager", "marketingSpecialist": "Marketing Specialist"}, "knowledge": {"knowledgeWarning": "The current knowledge may be too limited for accurate answers. Please consider adding more details to improve performance."}, "departments": {"sales": "Sales", "marketing": "Marketing", "engineering": "Engineering", "support": "Support"}, "expertise": {"contentWriting": "Content writing", "seo": "SEO", "socialMedia": "Social media management", "emailMarketing": "Email marketing", "graphicDesign": "Graphic design", "dataAnalysis": "Data analysis", "projectManagement": "Project management", "customerSupport": "Customer support"}}, "embed": {"instructions": {"title": "Embedding Instructions", "step1Title": "Step 1: Add the Script", "step1Description": "Copy the script tag and paste it in your website's HTML, preferably in the <head> section or just before the closing </body> tag.", "step2Title": "Step 2: Add the Widget Container", "step2Description": "Copy the div element and paste it where you want the virtual staff widget to appear on your page. The widget will automatically initialize in this location.", "step3Title": "Step 3: Customize the Widget (Optional)", "step3Description": "You can customize the widget's appearance by adding CSS to your website. The widget container has the ID xbot-container.", "step4Title": "Step 4: Test the integration", "step4Description": "After adding the code, refresh your page and verify that the virtual staff widget appears correctly. The widget should show the staff member's information and allow visitors to interact with them.", "troubleshootingTitle": "Troubleshooting", "troubleshooting1": "Make sure the script URL is accessible from your website.", "troubleshooting2": "Check your browser's console for any error messages.", "troubleshooting3": "Verify that the staff ID and name are correct.", "troubleshooting4": "Ensure your website allows loading external scripts."}, "script": {"title": "Embed Code", "copy": "Copy", "copied": "Copied!", "containerInstructions": "1. Add this container where you want the widget to appear", "scriptInstructions": "2. Add this script to your HTML"}}, "promptExperiment": {"title": "Test Prompt Experiment", "datasetName": "Dataset Name", "datasetNamePlaceholder": "Enter dataset name", "datasetNameRequired": "Dataset name is required", "runName": "Run Name", "runNamePlaceholder": "Enter run name", "runNameRequired": "Run name is required", "cancel": "Cancel", "runExperiment": "Run Experiment", "success": "Prompt experiment run successfully", "error": "Failed to run prompt experiment"}}, "knowledge": {"title": "Knowledge", "headers": {"file": "File", "status": "Status", "size": "Size", "updatedAt": "Last updated"}, "filters": {"search": {"placeholder": "Search file..."}, "fileType": "File type", "status": "Status", "url": "URL", "file": "File", "text": "Text"}, "status": {"pending": "Pending", "processing": "Processing", "ready": "Ready", "error": "Error"}, "actions": {"upload": "Upload new file"}, "upload": {"dragAndDrop": "Drag and drop file here or", "uploadButton": "Upload", "supportedFiles": "PDF, DOCX, TXT, or CSV files", "totalSize": "Total size", "noFileSelected": "No file selected", "uploadSuccess": "Upload successful", "fileTooLarge": "File exceeds maximum size of {{max}}MB", "file": "File", "url": "URL import", "text": "Enter text", "title": "Upload knowledge", "uploaded": "Knowledge uploaded", "invalidUrl": "Invalid URL", "urlAlreadyAdded": "URL already added", "noUrlsToUpload": "Please enter at least one URL", "uploadError": "Upload error", "knowledgeNameRequired": "Knowledge name is required", "knowledgeNameTooLong": "Knowledge name must be less than 250 characters", "textRequired": "Text is required", "textTooLong": "Text must be less than 20000 characters", "search": "Search knowledge", "textTitle": "Knowledge name", "fileTitle": "Knowledge from file", "urlTitle": "Knowledge from URL", "allTitle": "All knowledge", "deleteTitle": "Delete knowledge", "deleteDescription": "This action cannot be undone. This knowledge will be permanently deleted.", "deleteSuccess": "Knowledge deleted successfully", "deleteError": "Failed to delete knowledge", "deleteConfirm": "Delete", "deleteCancel": "Cancel", "leaveTitle": "Leave without saving?", "leaveDescription": "Any unsaved changes will be lost.", "leaveConfirm": "Leave", "leaveCancel": "Stay", "textInput": "Enter text", "textInputPlaceholder": "Enter your knowledge text here...", "textInputTitle": "Knowledge from text", "textTitlePlaceholder": "Enter knowledge title", "pleaseSelectAtLeastOneFile": "Please select at least one file", "pleaseEnterAtLeastOneURL": "Please enter at least one URL", "pleaseEnterAtLeastOneTextFile": "Please enter at least one text file", "pleaseEnterAllFields": "Please enter all fields", "newest": "Newest", "oldest": "Oldest", "noKnowledge": "No knowledge found", "urlImport": "URL import", "urlImportDescription": "Import knowledge from web pages", "deleteKnowledge": "Delete knowledge", "deleteKnowledgeDescription": "This action cannot be undone. This knowledge will be permanently deleted.", "deleteKnowledgeSuccess": "Knowledge deleted successfully", "deleteKnowledgeError": "Failed to delete knowledge", "deleteKnowledgeConfirm": "Delete", "deleteKnowledgeCancel": "Cancel", "deleteKnowledgeTitle": "Delete Knowledge", "fileImport": "Import from file", "fileImportCancel": "Cancel", "fileImportConfirm": "Upload", "fileImportDescription": "Import knowledge from file", "fileImportError": "Cannot upload knowledge", "fileImportSuccess": "Knowledge has been successfully uploaded", "textImport": "Import from text", "textImportCancel": "Cancel", "textImportConfirm": "Upload", "textImportDescription": "Import knowledge from text", "textImportError": "Cannot upload knowledge", "textImportSuccess": "Knowledge has been successfully uploaded", "urlImportCancel": "Cancel", "urlImportConfirm": "Upload", "urlImportError": "Cannot upload knowledge", "urlImportSuccess": "Knowledge has been successfully uploaded"}, "preview": {"errorTitle": "Failed to load preview", "errorDescription": "Unable to load the preview. Please try again later.", "filePreviewNotAvailable": "File content preview not available. Please download the file to view content.", "noFileContent": "No file content available.", "urlDescription": "This knowledge source is a web URL. Click the link below to view the content.", "urlNotAvailable": "URL content not available.", "textPreviewNotAvailable": "Text content preview not available. Please edit the knowledge to view content.", "unknownSourceType": "Unknown knowledge source type.", "loadError": "Failed to load knowledge content."}}, "department": {"subscriptionNotFound": "You don't have any plan to create department", "deleteDepartmentTitle": "Are you absolutely sure?", "deleteDepartmentDescription": "This action cannot be undone. This Department will be permanently deleted.", "updateDepartmentSuccess": "Department updated successfully", "updateDepartmentError": "Failed to update department", "deleteDepartmentSuccess": "Department deleted successfully", "deleteDepartmentError": "Failed to delete department", "createDepartmentSuccess": "Department created successfully", "title": "Departments", "departmentNameAlreadyExists": "Department name already exists", "createDepartmentFail": "Failed to create department.", "createDepartment": "Create department", "createStaff": "Create staff", "departmentName": "Department name", "enterDepartmentName": "Enter department name", "editDepartment": "Edit department", "description": "Description", "enterDescription": "Enter description...", "departmentNameRequired": "Department name is required", "viewStaff": "View staff", "staffCount": "{{count}} staff", "additionalStaff": "{{count}} additional staff", "interact": "Interact", "upload": "Upload", "knowledgeWarning": "The current knowledge may be too limited for accurate answers. Please consider adding more details to improve performance.", "deleteKnowledge": "Delete knowledge", "deleteKnowledgeDescription": "Are you sure you want to delete this knowledge? This action cannot be undo.", "deleteKnowledgeSuccess": "Knowledge deleted successfully", "deleteKnowledgeError": "Failed to delete knowledge", "deleteKnowledgeConfirm": "Delete", "deleteKnowledgeCancel": "Cancel", "knowledge": {"tab": "Knowledge", "baby": "Baby", "warning": "The current knowledge may be too limited for accurate answers. Please consider adding more details to improve performance.", "status": {"error": "Error", "pending": "Pending", "success": "Success"}}}, "customer": {"details": {"customerDetails": "Customer details", "name": "Name", "birthday": "Birthday", "gender": "Gender", "phone": "Phone", "email": "Email", "shippingAddress": "Shipping Address", "billingAddress": "Shipping address", "groupName": "Group name", "totalLoyalPoints": "Total loyal points", "totalRedeemPoints": "Total redeem points", "tags": "Tags", "noTags": "---"}, "purchase": {"purchaseInfo": "Purchase information", "totalSpent": "Total spent", "totalProductsPurchased": "Total products purchased", "purchasedOrder": "Purchased order", "totalProductsReturned": "Total products returned", "lastOrderAt": "Last order at"}, "sales": {"suggestionInfo": "Sales suggestion information", "defaultPriceGroup": "Default price group", "defaultPaymentMethod": "Default payment method", "discountPercent": "Discount percent"}, "order": {"orderHistory": "Order history"}}, "conversation": {"saySomething": "Say something...", "title": "Conversation", "whatCanIHelpWith": "What can I help with?", "filters": {"search": {"placeholder": "Search..."}, "source": "Source", "unread": "Unread", "read": "Read", "assignee": "Assignee"}}, "tasks": {"deleteDescription": "This action cannot be undone. This task will be permanently deleted.", "promptContent": "Prompt content", "shortDescription": "Short description", "shortDescriptionPlaceholder": "Paste your knowledge base text here...", "namePlaceholder": "Enter task name", "promptContentPlaceholder": "Summarize the article in [language], focusing on the main points. The summary should be no longer than [word count] words.", "editTask": "Edit task", "addTask": "Add task", "save": "Save", "add": "Add", "promptHelper": "Use square brackets like [variable] to insert placeholders into your prompt. Each placeholder should have a unique name. When the same variable appears multiple times, the user will only need to input it once."}, "activities": {"title": "Activities", "unknown": "Unknown", "opportunity_created": "Opportunity created", "field_labels": {"order_changed": "Order changed", "title": "Title", "status": "Status", "stage_changed": "Stage changed", "assignee": "Assignee", "assignee_changed": "Assignee changed", "customer_id": "Customer", "customer_changed": "Customer changed", "expected_revenue": "Expected Revenue", "probability": "Probability", "priority": "Priority", "expected_closing": "Expected Closing", "position": "Position", "position_changed": "Position changed", "color": "Color", "tags": "Tags", "user": "User", "note": "Note", "note_changed": "Note changed", "schedule_activities": "Schedule Activities", "schedule_activities_changed": "Schedule Activities changed", "schedule_activities_updated_at": "Schedule Activities updated at", "schedule_activities_created_at": "Schedule Activities created at", "schedule_activities_due_date": "Schedule Activities due date", "schedule_activities_status": "Schedule Activities status", "schedule_activities_summary": "Schedule Activities summary", "schedule_activities_note": "Schedule Activities note", "schedule_activities_assignee": "Schedule Activities assignee", "schedule_activities_user": "Schedule Activities user", "schedule_activities_count": "Schedule Activities Count", "representative_phone": "Phone", "representative_contact": "Contact", "representative_email": "Email", "colors": {"neutral": "Neutral", "blue": "Blue", "green": "Green", "yellow": "Yellow", "red": "Red", "teal": "<PERSON><PERSON>", "pink": "Pink", "orange": "Orange", "purple": "Purple", "sky": "Sky"}, "priorities": {"very_low": "Very Low", "low": "Low", "medium": "Medium", "high": "High"}, "schedule_activity_status": {"status": "Schedule Activity Status", "pending": "Pending", "completed": "Completed", "cancelled": "Cancelled", "done": "Done"}, "opportunity_status": {"status": "Status", "ongoing": "Ongoing", "won": "Won", "lost": "Lost"}}, "scheduleActivity": {"groupButton": {"markAsDone": "Mark as done", "edit": "Edit", "cancel": "Cancel"}}, "history": {"tabs": {"history": "History", "comments": "Comments", "placeholder": "Comment..."}}, "headers": {"activity": "Activity", "assignedTo": "Assigned To", "summary": "Summary", "dueDate": "Due Date", "status": "Status", "updatedAt": "Updated At"}, "filters": {"search": {"placeholder": "Search..."}, "status": "Status", "assignee": "Assignee", "activity": "Activity"}, "status": {"overdue": "Overdue", "today": "Today", "upcoming": "Upcoming", "noduedate": "No Due Date", "no_activity": "No Activity"}, "updateSuccess": "Activity updated successfully", "updateError": "Failed to update activity", "deleteSuccess": "Activity deleted successfully", "deleteError": "Failed to delete activity", "error": "Error creating activity type", "errorActivityTypeExists": "Activity type with this name already exists", "createSuccess": "Activity type created successfully", "createError": "Failed to create activity"}, "opportunityDetail": {"updateStatusSuccess": {"title": "Success", "description": "Opportunity status updated successfully."}, "updateStatusError": {"title": "Error", "description": "Failed to update opportunity status. Please try again."}, "customerInfo": {"name": "Name", "company": "Company", "country": "Country", "address": "Address", "billingAddress": "Billing Address", "province": "Province", "ward": "Ward", "phone": "Phone", "job": "Job Position", "website": "Website", "district": "District", "placeholder": "Select customer"}, "updateError": {"title": "Error", "description": "Failed to update opportunity. Please try again.", "probability": "Probability must be greater than or equal to 0 and less than or equal to 100.", "representativeEmail": "Invalid email format", "customer": "Customer is required"}, "updateSuccess": {"title": "Success", "description": "Opportunity updated successfully."}, "updateCustomerError": {"title": "Error", "description": "Failed to update customer. Please try again."}, "updateCustomerSuccess": {"title": "Success", "description": "Customer updated successfully."}, "createSuccess": {"title": "Success", "description": "Opportunity created successfully."}}, "subscription": {"currentPlan": {"title": "Your Current Plan", "expiresOn": "Expires on", "cancelSubscription": "Cancel Subscription", "upgrade": "Upgrade", "expired": "Expired"}, "expired": {"title": "You've reached your limit", "description": "You've exceeded the number of AI Staff allowed in your current plan. Upgrade now to continue creating more.", "yourUsage": "Your usage:", "upgradeButton": "Upgrade your plan", "seeAllPlans": "See all plans", "planTitle": "Expired plan", "planDescription": "Your current plan has expired. To continue using our service, please upgrade the plan.", "expiredPlan": "Expired plan:", "upgrade": "Upgrade", "logout": "Log out"}, "exceedQuota": {"title": "You've reached your limit", "description": "You've exceeded the number of {{quotaType}} allowed in your current plan. Upgrade now to continue creating more.", "yourUsage": "Your usage:", "upgradeButton": "Upgrade your plan", "seeAllPlans": "See all plans", "quotaTypes": {"staffs": "Staff", "knowledge_capacity": "Knowledge Capacity", "knowledge": "Knowledge", "messages": "Messages", "products": "Products", "orders": "Orders"}}, "usageStats": {"title": "Used space", "messages": "Message", "staff": "Staff", "storage": "Storage", "noData": "No data", "unlimited": "Unlimited"}, "pricing": {"save": "Save", "annually": "annually", "title": "Pricing", "description": "Check out our flexible pricing packages for individuals and businesses.", "mostPopular": "Most Popular", "upgrade": "Upgrade", "more": "More", "showLess": "Show Less"}, "billing": {"annualPlan": "Annual plan", "savings": "Save 20%"}, "customPlan": {"title": "Custom Plan", "description": "Customize according to your needs. Flexible in features, capacity, and cost, suitable for businesses with special requirements.", "contactInfo": "Contact Information", "companyName": "Company Name", "companyNamePlaceholder": "Enter your company name", "contactEmail": "Contact Email", "emailPlaceholder": "Enter your contact email", "requirements": "Your Requirements", "messages": "Messages per month", "staff": "Virtual assistants", "storage": "Knowledge capacity (MB)", "currentMessages": "Current", "currentStaff": "Current", "currentStorage": "Current", "assistants": "assistants", "additionalRequirements": "Additional Requirements", "additionalRequirementsPlaceholder": "Tell us about any specific requirements or features you need...", "included": "Always Included", "multilingualSupport": "Multilingual support", "prioritySupport": "Priority support", "customIntegrations": "Custom integrations", "submit": "Get Custom Quote", "contact": "We'll contact you within 24 hours with a personalized quote", "success": "Custom plan request submitted successfully!", "error": "Failed to submit custom plan request. Please try again.", "emailRequired": "Please provide your contact email", "companyRequired": "Please provide your company name", "features": {"productManagement": "Product Management", "orderManagement": "Order Management", "knowledgeManagement": "Knowledge Management", "departmentManagement": "Department Management", "employeeManagement": "Employee Management", "crm": "CRM"}, "configuration": {"virtualAssistants": "Virtual assistants", "messages": "Messages", "knowledgeCapacity": "Knowledge capacity (MB)", "aiModel": "AI Model", "multilingual": "Multilingual", "scheduleCustomerCare": "Schedule customer care", "customIntegration": "Custom integration"}, "button": "Contact us", "dialog": {"title": "Custom Package Request Sent Successfully", "message": "We've received your request and will contact you soon with more details. Thank you for choosing our service.", "countdown": "Closing in {{countdown}} seconds..."}}, "faq": {"title": "FAQs", "description": "Can't find the answer you're looking for?", "description2": "Please contact our customer support team.", "q1": "Is it accessible?", "a1": "Yes. It adheres to the WAI-ARIA design pattern.", "q2": "Is it styled?", "a2": "Yes. It comes with default styles that matches the other components' aesthetic.", "q3": "Is it animated?", "a3": "Yes. It's animated by default, but you can disable it if you prefer.", "q4": "Can I use it on my website?", "a4": "Yes. This component is designed to be used in any React application.", "q5": "How do I get started?", "a5": "Simply import the component and use it in your JSX."}, "createError": {"title": "Error", "description": "Failed to create opportunity. Please try again."}}, "layouts": {"title": "Layout Management", "description": "Manage property floor plans and unit mappings", "totalLayouts": "Total Layouts", "mappedUnits": "Mapped Units", "mapped": "Mapped", "unmapped": "Unmapped", "mappingStats": "Mapping Statistics", "selectLayout": "Select Layout", "openMapping": "Open Mapping", "floorPlans": "Floor Plans", "addLayout": "Add Layout", "createLayout": "Create Layout", "createLayoutDescription": "Create a new floor plan layout for this property", "layoutNamePlaceholder": "e.g., Floor 1, Ground Level", "layoutDescriptionPlaceholder": "Optional description for this layout", "searchLayouts": "Search layouts...", "noLayouts": "No Layouts Found", "noLayoutsFound": "No layouts match your search", "createFirstLayout": "Create your first floor plan layout to get started", "tryDifferentSearch": "Try a different search term", "selectPropertyFirst": "Select a property to view and manage its floor plan layouts", "deleteLayout": "Delete Layout", "deleteLayoutConfirmation": "Are you sure you want to delete this layout? This action cannot be undone.", "saveSuccess": "Layout saved successfully", "deleteSuccess": "Layout deleted successfully", "createSuccess": "Layout created successfully", "dimensions": "Dimensions", "uploadImage": "Upload Floor Plan", "uploadImageDescription": "Upload an image file of your floor plan to get started with unit mapping", "noLayoutSelected": "No Layout Selected", "selectLayoutToStart": "Select a layout from the dropdown to start mapping units", "mappedToLayout": "Mapped to layout", "assignUnit": "Assign Unit", "selectUnit": "Select Unit", "mappingProgress": "Mapping Progress", "systemHealth": "System Health", "weeklyProgress": "Weekly Progress", "recentChanges": "Recent Changes", "complete": "Complete", "incomplete": "Incomplete", "withIssues": "With Issues", "duplicateAll": "Duplicate All", "exportAll": "Export All", "importAll": "Import All", "shareAll": "Share All", "generateReport": "Generate Report", "saveAsTemplate": "Save as Template", "viewReport": "View Report", "lastActivity": "Last Activity", "totalUnits": "Total Units", "exportSuccess": "Export completed successfully", "exportError": "Failed to export layouts", "importSuccess": "Successfully imported {{count}} layouts", "importError": "Failed to import layouts", "importParseError": "Invalid file format. Please select a valid layout export file.", "saveError": "Failed to save layout: {{error}}", "deleteError": "Failed to delete layout: {{error}}", "createError": "Failed to create layout: {{error}}", "templateCreated": "Template created successfully", "templateCreateError": "Failed to create template", "createTemplate": "Create Template", "createTemplateDescription": "Save this layout as a reusable template for future properties", "templateName": "Template Name", "templateNamePlaceholder": "Enter template name", "templateDescription": "Description", "templateDescriptionPlaceholder": "Optional description for this template", "templateCategory": "Category", "categories": {"custom": "Custom", "residential": "Residential", "commercial": "Commercial", "mixed": "Mixed Use"}, "overlapWarning": "Overlap Warning", "overlapDetected": "{{count}} overlapping shapes detected", "floorNumber": "Floor Number", "floorNumberDescription": "Optional floor number for this layout", "dropImageHere": "Drop your floor plan image here or click to browse", "supportedFormats": "Supports JPG, PNG, SVG, WebP (max 10MB)", "chooseFile": "Choose <PERSON>", "imageUploaded": "Image uploaded successfully", "uploading": "Uploading", "invalidFileType": "Please select a valid image file", "fileTooLarge": "File size too large. Maximum 10MB allowed", "uploadFailed": "Failed to upload image", "dropUnitHere": "Drop unit here to place on layout"}, "properties": {"title": "Properties", "add": "Add Property", "selectProperty": "Select Property", "createProperty": "Create Property", "editProperty": "Edit Property", "basicInformation": "Basic Information", "addressInformation": "Address Information", "ownerInformation": "Owner Information", "purchaseInformation": "Purchase Information", "images": "Property Images", "updateSuccess": "Property updated successfully", "createSuccess": "Property created successfully", "updateError": "Unable to update property", "createError": "Unable to create property", "headers": {"propertyInfo": "Property Info", "name": "Property Name", "address": "Address", "type": "Type", "description": "Description", "totalUnits": "Total Units", "status": "Status", "updatedAt": "Updated At", "actions": "Actions", "image": "Property Images"}, "address": {"street": "Street Address", "city": "City", "state": "State/Province", "zipCode": "Zip Code", "country": "Country", "province": "Province/State", "district": "District/County", "ward": "Ward/City", "zip": "ZIP/Postal Code"}, "owner": {"name": "Owner Name", "email": "Owner <PERSON><PERSON>", "phone": "Owner Phone"}, "purchase": {"price": "Purchase Price", "date": "Purchase Date"}, "types": {"residential": "Residential", "commercial": "Commercial", "mixed": "Mixed Use"}, "status": {"active": "Active", "inactive": "Inactive", "maintenance": "Maintenance", "pending": "Pending"}, "placeholders": {"name": "Enter property name", "type": "Select property type", "description": "Enter property description", "street": "Enter street address", "city": "Enter city", "state": "Enter state/province", "zipCode": "Enter zip code", "country": "Enter country", "ownerName": "Enter owner name", "ownerEmail": "Enter owner email", "ownerPhone": "Enter owner phone number", "purchasePrice": "Enter purchase price", "uploadImages": "Click to upload images or drag and drop", "selectProvince": "Select province", "selectDistrict": "Select district", "selectWard": "Select ward", "zip": "Enter ZIP/postal code", "status": "Select property status", "selectDate": "Select date"}, "filters": {"search": {"placeholder": "Search properties..."}, "type": "Property Type", "status": "Status", "createdAt": "Created Date", "updatedAt": "Updated Date"}, "actions": {"addManual": "Add Property Manually", "view": "View", "edit": "Edit", "delete": "Delete"}, "deleteSuccess": "Property deleted successfully", "deleteError": "Unable to delete property", "loadError": "Unable to load property information", "deleteProperty": "Delete Property", "deleteConfirmation": "Are you sure you want to delete {{name}}? This action cannot be undone.", "noImages": "No images available", "unitStatus": "Unit Status Overview", "stats": {"totalUnits": "Total Units", "occupancyRate": "Occupancy Rate", "occupiedUnits": "Occupied Units", "availableUnits": "Available Units"}, "components": {"propertyBasicInfo": {"type": "Type", "totalUnits": "Total Units", "description": "Description"}, "propertyImages": {"noImages": "No images available", "propertyImage": "Property Image"}, "propertyOwnerInfo": {"ownerInformation": "Owner Information", "ownerName": "Owner Name", "ownerEmail": "Owner <PERSON><PERSON>", "ownerPhone": "Owner Phone", "purchasePrice": "Purchase Price", "purchaseDate": "Purchase Date"}, "propertyHeader": {"edit": "Edit", "delete": "Delete"}, "propertyStatsCards": {"totalUnits": "Total Units", "occupiedUnits": "Occupied Units", "availableUnits": "Available Units", "maintenance": "Maintenance", "occupancyRate": "Occupancy Rate"}}}, "units": {"title": "Units", "add": "Add Unit", "addUnit": "Add Unit", "viewUnits": "View Units", "search": "Search units...", "searchUnits": "Search units...", "unitList": "Unit List", "noUnitsFound": "No units found", "unitNumber": "Unit Number", "unitType": "Unit Type", "property": "Property", "description": "Description", "floor": "Floor", "squareFootage": "Square Footage", "bedrooms": "Bedrooms", "bathrooms": "Bathrooms", "rentAmount": "Rent Amount", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "assignUnit": "Assign Unit", "selectUnit": "Select Unit", "createUnit": "Create Unit", "editUnit": "Edit Unit", "basicInformation": "Basic Information", "specifications": "Specifications", "financialInformation": "Financial Information", "amenitiesTitle": "Amenities", "images": "Unit Images", "noImages": "No images uploaded", "dragImageHere": "Drag an image here or", "totalSize": "Total size: 0.00 /5MB", "unitNumberPlaceholder": "Enter unit (e.g. A-101)", "descriptionPlaceholder": "Type your message here.", "sampleProperty1": "Property 1", "sampleProperty2": "Property 2", "createSuccess": "Unit created successfully", "updateSuccess": "Unit updated successfully", "createError": "Failed to create unit", "updateError": "Failed to update unit", "createDescription": "Fill in the details to create a new unit", "editDescription": "Update unit information", "noPropertiesAvailable": "No properties available", "headers": {"unitInfo": "Unit Info", "property": "Property", "unitNumber": "Unit Number", "type": "Unit Type", "floor": "Floor", "squareFootage": "Square Footage", "bedrooms": "Bedrooms", "bathrooms": "Bathrooms", "description": "Description", "rentAmount": "Rent Amount", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "size": "Size", "rent": "Rent", "status": "Status", "tenant": "Current Tenant", "updatedAt": "Updated At", "actions": "Actions"}, "placeholders": {"property": "Select property", "unitNumber": "Enter unit number (e.g., 101, A-1)", "type": "Select unit type", "floor": "Enter floor number", "squareFootage": "Enter square footage", "bedrooms": "Enter number of bedrooms", "bathrooms": "Enter number of bathrooms", "description": "Enter unit description", "rentAmount": "Enter monthly rent amount", "depositAmount": "Enter security deposit amount", "uploadImages": "Click to upload images or drag and drop"}, "types": {"studio": "Studio", "1br": "1 Bedroom", "2br": "2 Bedrooms", "3br": "3 Bedrooms", "commercial": "Commercial", "office": "Office", "retail": "Retail"}, "amenities": {"air-conditioning": "Air Conditioning", "washer-dryer": "Washer/Dryer", "fireplace": "Fireplace", "pool-access": "Pool Access", "dishwasher": "Dishwasher", "balcony": "Balcony", "heating": "Heating", "parking": "Parking", "hardwood-floors": "Hardwood Floors", "gym-access": "Gym Access", "pet-friendly": "Pet Friendly", "elevator": "Elevator"}, "status": {"available": "Available", "occupied": "Occupied", "maintenance": "Under Maintenance", "inactive": "Inactive"}, "filters": {"search": {"placeholder": "Search by unit number"}, "type": "Unit Type", "status": "Status", "property": "Property", "rentRange": "Rent Range", "createdAt": "Created At", "updatedAt": "Updated At"}, "actions": {"addManual": "Add Unit", "view": "View", "edit": "Edit", "delete": "Delete"}}, "tenants": {"title": "Tenants", "add": "Add Tenant", "search": "Search tenants...", "searchPlaceholder": "Search by tenant name", "export": "Export", "confirmDelete": "Are you sure you want to delete this tenant?", "headers": {"tenantInfo": "Tenant Info", "contact": "Contact", "currentUnit": "Current Unit", "leaseStatus": "Lease Status", "employmentStatus": "Employment Status", "status": "Status", "emergencyContact": "Emergency Contact", "joinDate": "Join Date", "updatedAt": "Updated At", "actions": "Actions"}, "filters": {"allStatus": "All Status", "leaseStatus": "Lease Status", "property": "Property", "clear": "Clear Filters"}, "status": {"active": "Active", "inactive": "Inactive", "expired": "Expired", "pending": "Pending"}, "employmentStatus": {"employed": "Employed", "unemployed": "Unemployed", "self_employed": "Self Employed", "student": "Student", "retired": "Retired"}, "actions": {"addManual": "Add Tenant", "view": "View", "edit": "Edit", "delete": "Delete"}, "sections": {"personalInfo": "Personal Information", "identification": "Identification", "employment": "Employment", "emergencyContact": "Emergency Contact"}, "fields": {"fullName": "Full Name", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "dateOfBirth": "Date of Birth", "status": "Status", "identificationType": "Identification Type", "identificationNumber": "Identification Number", "employmentStatus": "Employment Status", "employerName": "Employer Name", "monthlyIncome": "Monthly Income", "emergencyContactName": "Emergency Contact Name", "emergencyContactPhone": "Emergency Contact Phone"}, "createTenant": "Create Tenant", "editTenant": "Edit Tenant", "createDescription": "Create a new tenant", "editDescription": "Update tenant information", "viewTenant": "View Tenant", "noTenant": "No Tenant", "stats": {"totalContracts": "Total Contracts", "activeContracts": "Active Contracts", "joinDate": "Join Date"}, "quickStats": "Quick Stats", "activeContracts": "Active Contracts", "identificationTypes": {"passport": "Passport", "national_id": "National ID/Card", "driver_license": "Driver License"}, "errors": {"notFound": "Tenant Not Found", "notFoundDescription": "The tenant you are looking for does not exist or has been deleted."}, "documents": "Documents", "deleteConfirmation": {"title": "Delete Tenant", "description": "Are you sure you want to delete this tenant? This action cannot be undone.", "hasActiveContracts": "This tenant has active contracts. Are you sure you want to delete?"}, "messages": {"deleteSuccess": "Tenant deleted successfully", "deleteError": "Failed to delete tenant"}}, "contracts": {"title": "Contracts", "add": "Add Contract", "search": "Search contracts...", "searchPlaceholder": "Search by contract number", "export": "Export", "confirmDelete": "Are you sure you want to delete this contract?", "headers": {"contractInfo": "Contract Info", "tenant": "Tenant", "property": "Property", "duration": "Duration", "rentAmount": "Rent Amount", "status": "Status", "createdAt": "Created At", "updatedAt": "Updated At", "actions": "Actions"}, "filters": {"status": "Status", "contractType": "Contract Type", "property": "Property", "startDate": "Start Date", "endDate": "End Date", "clear": "Clear Filters"}, "status": {"draft": "Draft", "active": "Active", "expired": "Expired", "terminated": "Terminated", "pending": "Pending"}, "types": {"monthly": "Monthly", "annual": "Annual", "profitSharing": "Profit Sharing", "revenueSharing": "Revenue Sharing"}, "actions": {"view": "View", "edit": "Edit", "delete": "Delete"}, "createContract": "Create Contract", "editContract": "Edit Contract", "createDescription": "Create a new property contract with flexible terms and revenue models", "editDescription": "Update contract details and terms", "sections": {"propertyUnit": "Property & Unit Selection", "contractDetails": "Contract Details", "financialTerms": "Financial Terms", "termsConditions": "Terms & Conditions"}, "fields": {"property": "Property", "unit": "Unit", "tenant": "Tenant", "contractType": "Contract Type", "startDate": "Start Date", "endDate": "End Date", "rentDueDay": "Rent Due Day", "rentAmount": "Rent Amount", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "lateFeeAmount": "Late <PERSON>e Amount", "noticePeriodDays": "Notice Period (Days)", "autoRenewal": "Auto Renewal", "profitSharingPercentage": "Profit Sharing Percentage", "revenueSharingPercentage": "Revenue Sharing Percentage", "termsConditions": "Terms & Conditions"}, "placeholders": {"property": "Select a property", "unit": "Select a unit", "tenant": "Select a tenant", "contractType": "Select contract type", "rentDueDay": "Enter rent due day (1-31)", "rentAmount": "Enter monthly rent amount", "depositAmount": "Enter security deposit", "lateFeeAmount": "Enter late fee amount (optional)", "noticePeriodDays": "Enter notice period in days", "profitSharingPercentage": "Enter percentage (0-100)", "revenueSharingPercentage": "Enter percentage (0-100)", "termsConditions": "Enter contract terms and conditions"}, "contractTypes": {"monthly": "Monthly Rental", "annual": "Annual Lease", "profitSharing": "Profit Sharing", "revenueSharing": "Revenue Sharing"}, "descriptions": {"autoRenewal": "Automatically renew contract when it expires"}, "messages": {"createSuccess": "Contract created successfully", "createError": "Failed to create contract", "updateSuccess": "Contract updated successfully", "updateError": "Failed to update contract"}, "contractDetails": "Contract Details", "overview": "Overview", "paymentHistory": "Payment History"}, "maintenance": {"title": "Maintenance", "add": "New Request", "search": "Search maintenance...", "searchPlaceholder": "Search by request title", "export": "Export", "confirmDelete": "Are you sure you want to delete this maintenance request?", "headers": {"requestInfo": "Request Info", "property": "Property", "requester": "Requester", "category": "Category", "priority": "Priority", "status": "Status", "assignedTo": "Assigned To", "estimatedCost": "Estimated Cost", "dueDate": "Due Date", "createdAt": "Created At", "updatedAt": "Updated At", "actions": "Actions"}, "filters": {"status": "Status", "priority": "Priority", "category": "Category", "property": "Property"}, "priorities": {"low": "Low", "medium": "Medium", "high": "High", "urgent": "<PERSON><PERSON>"}, "categories": {"plumbing": "Plumbing", "electrical": "Electrical", "hvac": "HVAC", "structural": "Structural", "appliance": "Appliance", "cosmetic": "Cosmetic", "cleaning": "Cleaning", "security": "Security", "general": "General", "other": "Other"}, "actions": {"addManual": "Add Maintenance Request", "view": "View", "edit": "Edit", "delete": "Delete"}, "createRequest": "Create Maintenance Request", "editRequest": "Edit Maintenance Request", "createDescription": "Submit a new maintenance request for your property", "editDescription": "Update the maintenance request details", "sections": {"propertyUnit": "Property & Unit Selection", "requestDetails": "Request Details", "scheduling": "Scheduling & Contractor", "costInfo": "Cost Information", "additional": "Additional Information"}, "fields": {"property": "Property", "unit": "Unit", "tenant": "Tenant", "title": "Request Title", "description": "Description", "priority": "Priority", "category": "Category", "status": "Status", "scheduledDate": "Scheduled Date", "completedDate": "Completed Date", "contractorName": "Contractor Name", "contractorPhone": "Contractor Phone", "estimatedCost": "Estimated Cost ($)", "actualCost": "Actual Cost ($)", "notes": "Additional Notes", "images": "Attachments"}, "placeholders": {"property": "Select a property", "unit": "Select a unit", "tenant": "Select a tenant", "title": "Brief description of the issue", "description": "Detailed description of the maintenance issue", "priority": "Select priority level", "category": "Select category", "status": "Select status", "contractorName": "Enter contractor name", "contractorPhone": "Enter contractor phone", "estimatedCost": "0.00", "actualCost": "0.00", "notes": "Any additional notes or instructions"}, "options": {"propertyWide": "Property-wide maintenance", "noTenant": "No specific tenant"}, "status": {"open": "Open", "in_progress": "In Progress", "completed": "Completed", "cancelled": "Cancelled"}, "messages": {"createSuccess": "Maintenance request created successfully", "createError": "Failed to create maintenance request", "updateSuccess": "Maintenance request updated successfully", "updateError": "Failed to update maintenance request"}, "viewRequests": "View Requests"}, "reports": {"title": "Property Reports", "description": "Generate comprehensive reports and analytics for property performance", "generateReport": "Generate Report", "downloadReport": "Download Report", "generating": "Generating...", "categories": {"financial": {"title": "Financial Reports", "description": "Revenue, expenses, and profit analysis", "generate": "Generate"}, "occupancy": {"title": "Occupancy Reports", "description": "Vacancy rates and tenant retention", "generate": "Generate"}, "performance": {"title": "Performance Analytics", "description": "Property and unit performance metrics", "generate": "Generate"}, "maintenance": {"title": "Maintenance Reports", "description": "Maintenance costs and trends", "generate": "Generate"}, "tenant": {"title": "Tenant Analysis", "description": "Tenant demographics and behavior", "generate": "Generate"}, "custom": {"title": "Custom Reports", "description": "Build custom reports with filters", "create": "Create"}}, "comingSoon": {"title": "Advanced Reporting System - Coming Soon", "description": "Comprehensive analytics, custom reports, and data export features will be available here."}}, "dashboard": {"propertyAssets": {"title": "Property Assets Dashboard", "description": "Overview of your property portfolio performance and key metrics"}, "metrics": {"totalProperties": "Total Properties", "totalUnits": "Total Units", "activeContracts": "Active Contracts", "monthlyRevenue": "Monthly Revenue"}, "charts": {"occupancyOverview": "Occupancy Overview"}, "occupancy": {"occupied": "Occupied", "vacant": "Vacant", "rate": "Occupancy Rate", "tenants": "Total Tenants"}, "quickActions": {"title": "Quick Actions", "addProperty": "Add Property", "addUnit": "Add Unit", "createContract": "Create Contract", "addTenant": "Add Tenant"}, "recentActivities": {"title": "Recent Activities", "viewAll": "View All Activities"}, "portfolio": {"title": "Portfolio Summary", "properties": "Properties", "units": "Units", "contracts": "Contracts", "revenue": "Revenue", "performance": "Overall Performance"}}, "payments": {"headers": {"paymentInfo": "Payment Info", "amount": "Amount", "contract": "Contract", "property": "Property", "status": "Status", "paymentDate": "Payment Date", "createdAt": "Created", "updatedAt": "Updated", "actions": "Actions"}, "status": {"pending": "Pending", "completed": "Completed", "failed": "Failed", "refunded": "Refunded"}}, "pipelines": {"headers": {"activities": "Activities", "amount": "Amount", "assignee": "Assignee", "closeDate": "Close date", "createdAt": "Created at", "customer": "Customer", "expectedClosingDate": "Expected Closing Date", "expectedRevenue": "Expected Revenue", "opportunity": "Opportunity", "pipeline": "Pipeline", "probability": "Probability", "stage": "Stage", "status": "Status", "updatedAt": "Updated at"}}}, "table": {"pagination": {"rowsPerPage": "Rows per page", "description": "{{start}} to {{end}} row(s) of a total of {{total}}", "next": "Next", "previous": "Previous"}, "selected": {"title": "Selected", "delete": "Delete {{count}} selected"}, "export": {"title": "Export", "description": "Export data", "confirm": "Export", "cancel": "Cancel"}, "filter": {"clearFilter": "Clear filter", "loadMore": "Load more"}, "savedFilters": {"FilterTypeRequired": "Filter type is required", "NoFiltersToSave": "No filters to save", "CreateFilterSuccess": "Filter saved successfully", "CreateFilterFail": "Failed to save filter", "UpdateFilterSuccess": "Filter updated successfully", "DeleteFilterSuccess": "Filter deleted successfully", "DeleteFilterFail": "Failed to delete filter", "UpdateFilterFail": "Failed to update filter", "UpdateFilter": "Filter updated", "settings": {"settings": "Settings", "title": "Tabs settings", "description": "The first six tabs will be prioritized for display on the main page. Customize them based on your needs.", "placeholder": "Enter tab name", "delete": {"title": "Delete Saved Filter", "description": "Are you sure you want to delete this saved filter? This action cannot be undone.", "confirm": "Delete"}}}}, "validation": {"titleRequired": "Title is required", "required": "This field is required", "invalidEmail": "Please enter a valid email address", "invalidUsername": "Username must be at least 3 characters", "minLength": "Must be at least {{count}} characters", "maxLength": "Must be less than {{count}} characters", "passwordMismatch": "Passwords do not match", "emailRequired": "Please enter email", "usernameRequired": "Please enter username", "passwordRequired": "Please enter password", "confirmPasswordRequired": "Please confirm password", "invalidPassword": "Password must be at least 8 characters", "passwordsDoNotMatch": "Passwords do not match", "verificationCodeRequired": "Please enter verification code", "verificationCodeLength": "Verification code must be 6 characters", "sessionRequired": "Session is required", "usernameSpecialCharacters": "Username can only contain letters, numbers, and underscores", "skuFormat": "The SKU can only contain letters, numbers, and underscores", "skuRequired": "SKU code is required", "nameRequired": "Product name is required", "nameTooLong": "Product name must be less than 250 characters", "titleTooLong": "Title must be less than 100 characters", "priceRequired": "Price required", "imageRequired": "At least one image is required", "imageFormat": "Invalid image format", "priceMustBePositive": "Please enter a valid price", "invalidPrice": "Invalid price", "wrongUsernameOrPassword": "Incorrect username or password", "phoneRequired": "Phone is required", "phoneNumberAlreadyExists": "Phone number already exists", "contactRequired": "Contact is required", "customerRequired": "Customer is required", "emailOrPhoneRequired": "Email or phone is required", "activityTypeRequired": "Activity type is required", "summaryRequired": "Summary is required", "propertyRequired": "Property is required", "unitRequired": "Unit is required", "tenantRequired": "Tenant is required", "contractTypeRequired": "Contract type is required", "startDateRequired": "Start date is required", "rentAmountMustBePositive": "Rent amount must be positive", "depositAmountMustBePositive": "Deposit amount must be positive", "lateFeeAmountMustBePositive": "Late fee amount must be positive", "rentDueDayMin": "Rent due day must be at least 1", "rentDueDayMax": "Rent due day must be at most 31", "percentageMax": "Percentage must be at most 100", "noticePeriodMustBePositive": "Notice period must be positive", "sharingPercentageRequired": "Sharing percentage is required for this contract type", "idRequired": "ID is required"}, "footer": {"crafted": "Crafted with", "by": "by", "team": "OneXAPIs team", "heart": "heart"}, "install": {"installing": "Installing...", "pleaseWait": "Please wait a moment", "error": {"backToHome": "Back to home", "notFound": "The page you are looking for does not exist", "installationFailed": "Installation Failed", "missingSourceChannel": "Source channel not found", "authorizeDestination": "Please authorize the {channel_name}"}}, "socialIntegration": {"authorize": "Authorize", "reAuthorize": "Re-authorize", "newAuthorize": "New authorize", "authorized": "Authorized", "sessionExpired": "Session expired", "failedToSwitchStatus": "Failed to switch connection status", "failedToSetup": "Failed to setup connection", "facebookSetup": "Facebook integration setup", "zaloSetup": "Zalo OA integration setup", "defaultSetup": "Integration setup"}, "filter": {"allTime": "All time", "today": "Today", "yesterday": "Yesterday", "lastWeek": "Last week", "lastMonth": "Last month", "last7Days": "Last 7 days", "last30Days": "Last 30 days", "last90Days": "Last 90 days", "thisWeek": "This week", "thisMonth": "This month", "thisYear": "This year", "customize": "Customize", "reset": "Reset", "apply": "Apply"}, "financial": {"dashboard": {"title": "Financial Dashboard", "description": "Overview of property financial performance and metrics"}, "timeRange": {"month": "Month", "quarter": "Quarter", "year": "Year"}, "exportReport": "Export Report", "metrics": {"totalRevenue": "Total Revenue", "netIncome": "Net Income", "occupancyRate": "Occupancy Rate", "totalExpenses": "Total Expenses"}, "charts": {"revenueTrend": "Revenue Trend", "expenseBreakdown": "Expense Breakdown", "occupancyTrend": "Occupancy Trend", "revenueAnalysis": "Revenue Analysis"}, "revenue": "Revenue", "netIncome": "Net Income", "performance": "Performance", "profitMargin": "<PERSON><PERSON>", "totalProperties": "Total Properties", "totalUnits": "Total Units", "occupiedUnits": "Occupied Units", "vacancyRate": "Vacancy Rate", "ytdPerformance": "Year-to-Date Performance", "topProperties": "Top Performing Properties", "monthlyRevenue": "monthly revenue", "expenses": "Expenses", "alerts": {"title": "Financial Alerts", "rentDue": "Rent Due Soon", "highExpenses": "High Expenses Alert", "leaseExpiring": "Lease Expiring Soon"}, "revenueAnalytics": {"title": "Revenue Analytics", "description": "Detailed revenue analysis with interactive charts and trends"}, "expenseAnalysis": {"title": "Expense Analysis", "description": "Comprehensive expense breakdown and cost optimization insights"}, "profitAnalysis": {"title": "Profit Analysis", "description": "Profit trends, margins, and ROI performance tracking"}}, "contracts": {"contractDetails": "Contract Details", "contractId": "Contract ID", "overview": "Overview", "duration": "Duration", "progress": "Progress", "daysRemaining": "days remaining", "openEnded": "Open-ended", "autoRenewalEnabled": "Auto-renewal enabled", "perMonth": "per month", "dayOfMonth": "th of each month", "sharingTerms": "Sharing Terms", "paymentSummary": "Payment Summary", "revenueProjections": "Revenue Projections", "info": "Contract Information", "days": "days", "quickActions": "Quick Actions", "fields": {"property": "Property", "unit": "Unit", "tenant": "Tenant", "startDate": "Start Date", "endDate": "End Date", "rentAmount": "Rent Amount", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "lateFeeAmount": "Late <PERSON>e Amount", "rentDueDay": "Rent Due Day", "profitSharingPercentage": "Profit Sharing Percentage", "revenueSharingPercentage": "Revenue Sharing Percentage", "noticePeriod": "Notice Period"}, "sections": {"financialTerms": "Financial Terms", "termsConditions": "Terms & Conditions"}, "stats": {"totalPayments": "Total Payments", "completedPayments": "Completed Payments", "pendingPayments": "Pending Payments", "totalCollected": "Total Collected"}, "projections": {"expectedMonthly": "Expected Monthly Revenue", "expectedAnnual": "Expected Annual Revenue"}, "actions": {"recordPayment": "Record Payment", "viewPayments": "View Payment History"}, "errors": {"notFound": "Contract Not Found", "notFoundDescription": "The contract you are looking for does not exist or has been deleted."}, "deleteConfirmation": {"title": "Delete Contract", "description": "Are you sure you want to delete this contract? This action cannot be undone.", "activeContract": "This is an active contract. Deleting it may affect ongoing payments and records."}, "messages": {"deleteSuccess": "Contract deleted successfully", "deleteError": "Failed to delete contract"}}, "unit": {"title": "Unit", "type": {"apartment": "Apartment", "studio": "Studio", "1br": "1 Bedroom", "2br": "2 Bedroom", "3br": "3 Bedroom", "4br": "4 Bedroom", "penthouse": "Penthouse", "duplex": "Duplex", "commercial": "Commercial", "office": "Office", "retail": "Retail", "warehouse": "Warehouse"}}, "numbers": {"abbreviations": {"thousand": "K", "million": "M", "billion": "B", "trillion": "T"}, "currency": {"symbol": "$", "code": "USD"}}, "payments": {"createPayment": "Create Payment", "editPayment": "Edit Payment", "createDescription": "Create a new payment record for rent, deposits, and other fees", "editDescription": "Update payment information and status", "sections": {"contract": "Contract Information", "paymentDetails": "Payment Details"}, "fields": {"contract": "Contract", "amount": "Amount", "paymentDate": "Payment Date", "paymentType": "Payment Type", "paymentMethod": "Payment Method", "referenceNumber": "Reference Number", "status": "Status", "description": "Description"}, "placeholders": {"contract": "Select a contract", "amount": "0.00", "paymentType": "Select payment type", "paymentMethod": "Select payment method", "referenceNumber": "Enter reference number", "status": "Select status", "description": "Enter description or notes"}, "paymentTypes": {"rent": "Rent", "deposit": "Security Deposit", "lateFee": "Late Fee", "maintenance": "Maintenance", "other": "Other"}, "paymentMethods": {"cash": "Cash", "bankTransfer": "Bank Transfer", "creditCard": "Credit Card", "check": "Check"}, "status": {"pending": "Pending", "completed": "Completed", "failed": "Failed", "refunded": "Refunded"}, "contractInfo": {"rent": "Monthly Rent", "deposit": "Security Deposit", "dueDay": "Due Day"}, "quickFill": {"rent": "Fill Rent Amount", "deposit": "<PERSON>ll <PERSON><PERSON><PERSON><PERSON>"}, "messages": {"createSuccess": "Payment created successfully", "updateSuccess": "Payment updated successfully", "createError": "Failed to create payment", "updateError": "Failed to update payment", "deleteSuccess": "Payment deleted successfully", "deleteError": "Failed to delete payment"}}, "permissions": {"staff": {"CREATE_STAFF": "Create Staff", "CREATE_STAFF_TASK": "Create Staff Task", "CREATE_STAFF_KNOWLEDGE": "Create Staff Knowledge", "GET_STAFF": "Get Staff", "LIST_STAFF": "List Staff", "UPDATE_STAFF": "Update Staff", "UPDATE_STAFF_ROLE": "Update Staff Role", "UPDATE_STAFF_INTERACT_INFO": "Update Staff Interact Info", "DELETE_STAFF_KNOWLEDGE": "Delete Staff Knowledge", "DELETE_STAFF_TASK": "Delete Staff Task", "INTEGRATE_STAFF_TO_SOCIAL_PLATFORM": "Integrate Staff to Social Platform"}, "account": {"CREATE_STAFF_ACCOUNT": "Create Staff Account", "GET_STAFF_ACCOUNT": "Get Staff Account", "LIST_STAFFS_ACCOUNT": "List Staffs Account", "UPDATE_STAFF_ACCOUNT": "Update Staff Account", "UPDATE_STAFF_ACCOUNT_AUTHORIZATION": "Update Staff Account Authorization", "DISABLE_STAFF_ACCOUNT": "Disable Staff Account", "DELETE_STAFF_ACCOUNT": "Delete Staff Account"}, "activity": {"CREATE_ACTIVITIES": "Create Activities", "LIST_ACTIVITIES": "List Activities", "LIST_ACTIVITIES_IN_CHARGE": "List Activities In Charge", "UPDATE_ACTIVITIES": "Update Activities", "DELETE_ACTIVITIES": "Delete Activities", "ASSIGN_STAFF_TO_ACTIVITIES": "Assign Staff to Activities"}, "branch": {"CREATE_BRAND": "Create Brand", "GET_BRAND": "Get Brand", "LIST_BRAND": "List Brand", "UPDATE_BRAND": "Update Brand", "DELETE_BRAND": "Delete Brand"}, "brand": {"BRAND_LIST_EXPORT_FILE": "Brand List Export File"}, "conversation": {"LIST_MESSAGE": "List Message", "LIST_MESSAGE_IN_CHARGE": "List Message In Charge", "ASSIGN_STAFF_TO_CONVERSATION": "Assign Staff to Conversation", "REPLY_MESSAGE": "Reply Message"}, "customer": {"CREATE_CUSTOMER": "Create Customer", "GET_CUSTOMER": "Get Customer", "LIST_CUSTOMER": "List Customer", "LIST_CUSTOMER_IN_CHARGE": "List Customer In Charge", "UPDATE_CUSTOMER": "Update Customer", "DELETE_CUSTOMER": "Delete Customer", "SHOW_CUSTOMER_PHONE": "Show Customer Phone", "CUSTOMER_LIST_EXPORT_FILE": "Customer List Export File", "SHOW_CUSTOMER_GROUP": "Show Customer Group"}, "department": {"CREATE_DEPARTMENT": "Create Department", "GET_DEPARTMENT": "Get Department", "UPDATE_DEPARTMENT": "Update Department", "UPDATE_DEPARTMENT_DESCRIPTION": "Update Department Description", "DELETE_DEPARTMENT": "Delete Department"}, "knowledge": {"CREATE_KNOWLEDGE": "Create Knowledge", "GET_KNOWLEDGE": "Get Knowledge", "UPDATE_KNOWLEDGE": "Update Knowledge", "DELETE_KNOWLEDGE": "Delete Knowledge"}, "opportunity": {"CREATE_OPPORTUNITY": "Create Opportunity", "GET_OPPORTUNITY": "Get Opportunity", "LIST_OPPORTUNITY": "List Opportunity", "LIST_OPPORTUNITY_IN_CHARGE": "List Opportunity In Charge", "LIST_OPPORTUNITY_HISTORY": "List Opportunity History", "LIST_OPPORTUNITY_ORDER_HISTORY": "List Opportunity Order History", "UPDATE_OPPORTUNITY": "Update Opportunity", "UPDATE_OPPORTUNITY_EXPECTED_CLOSING_DATE": "Update Opportunity Expected Closing Date", "UPDATE_OPPORTUNITY_PRIORITY": "Update Opportunity Priority", "UPDATE_OPPORTUNITY_NOTE": "Update Opportunity Note", "UPDATE_OPPORTUNITY_EXPECTED_REVENUE": "Update Opportunity Expected Revenue", "DELETE_OPPORTUNITY": "Delete Opportunity", "ASSIGN_STAFF_TO_OPPORTUNITY": "Assign Staff to Opportunity", "MARK_OPPORTUNITY_WON_LOST": "<PERSON> Won/Lost"}, "order": {"CREATE_ORDER": "Create Order", "GET_ORDER": "Get Order", "LIST_ORDER": "List Order", "LIST_ORDER_IN_CHARGE": "List Order In Charge", "UPDATE_ORDER": "Update Order", "UPDATE_ORDER_NOTE": "Update Order Note", "DELETE_ORDER": "Delete Order", "ORDER_CONFIRM_STATUS": "Order Confirm Status", "SAVE_DRAFT_ORDER": "Save Draft Order", "ORDER_CONFIRM_PAID_STATUS": "Order Confirm Paid Status", "ORDER_CONFIRM_COMPLETED_STATUS": "Order Confirm Completed Status", "ORDER_CONFIRM_CANCELED_STATUS": "Order Confirm Canceled Status", "ORDER_CONFIRM_PACKING_STATUS": "Order Confirm Packing Status", "ORDER_IMPORT": "Order Import", "ORDER_LIST_EXPORT_FILE": "Order List Export File"}, "product": {"CREATE_PRODUCT": "Create Product", "CREATE_PRODUCT_QUICKLY": "Create Product Quickly", "GET_PRODUCT": "Get Product", "GET_PRODUCT_PRICE_GROUP": "Get Product Price Group", "LIST_PRODUCT": "List Product", "LIST_PUBLISHED_PRODUCT": "List Published Product", "UPDATE_PRODUCT": "Update Product", "UPDATE_PRODUCT_FILES": "Update Product Files", "DELETE_PRODUCT": "Delete Product", "PRODUCT_LIST_EXPORT_FILE": "Product List Export File"}, "role": {"CREATE_ROLE": "Create Role", "LIST_ROLE": "List Role", "UPDATE_ROLE": "Update Role", "DELETE_ROLE": "Delete Role"}, "stage": {"CREATE_STAGE": "Create Stage", "GET_STAGE_TOTAL_EXPECTED_REVENUE": "Get Stage Total Expected Revenue", "UPDATE_STAGE": "Update Stage", "DELETE_STAGE": "Delete Stage"}, "task": {"CREATE_TASK": "Create Task", "GET_TASK": "Get Task", "LIST_TASK": "List Task", "UPDATE_TASK": "Update Task", "UPDATE_TASK_PROMPT": "Update Task Prompt", "DELETE_TASK": "Delete Task"}, "variant": {"VARIANT_LIST_EXPORT_FILE": "Variant List Export File"}}}