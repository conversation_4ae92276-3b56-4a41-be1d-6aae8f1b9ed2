import { BaseEntity } from "../base";

export enum UnitType {
  STUDIO = "studio",
  ONE_BR = "1br",
  TWO_BR = "2br",
  THREE_BR = "3br",
  FOUR_BR = "4br",
  FIVE_BR = "5br",
}

export enum UnitStatus {
  AVAILABLE = "AVAILABLE",
  OCCUPIED = "OCCUPIED",
  UNDER_MAINTENANCE = "UNDER_MAINTENANCE",
  INACTIVE = "INACTIVE",
}

export interface Amount {
  rent: number;
  deposit: number;
}

export interface Unit extends BaseEntity {
  property_id: string;
  unit_number: string;
  asset_category_id: string;
  type: string;
  description?: string;
  floor_number: number;
  square_footage: number;
  bedrooms: number;
  bathroom: number;
  amount: Amount;
  status: string;
  amenities: string[];
  images: string[];
}

export interface CreateUnit {
  property_id: string;
  unit_number: string;
  asset_category_id: string;
  type: string;
  description?: string;
  floor_number: number;
  square_footage: number;
  bedrooms: number;
  bathroom: number;
  amount: Amount;
  status: string;
  amenities: string[];
  images: string[];
}

export interface UpdateUnit {
  unit_number?: string;
  asset_category_id?: string;
  type?: string;
  description?: string;
  floor_number?: number;
  square_footage?: number;
  bedrooms?: number;
  bathroom?: number;
  amount?: Amount;
  status?: string;
  amenities?: string[];
  images?: string[];
}
