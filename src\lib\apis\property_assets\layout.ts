import { ENDPOINTS } from "@/constants/endpoints";

import { privateApi } from "../../api_helper";
import { ResponseAxiosDetail, ResponseList } from "../types/common";
import type {
  CreateLayout,
  Layout,
  LayoutStatistics,
  UpdateLayout,
  UpdateLayoutMapping,
} from "../types/property_assets/layout";

// Layout APIs
export const layoutApi = {
  // Get all layouts with optional filtering and pagination
  list: async (params?: Record<string, unknown>) => {
    return await privateApi.get<ResponseList<Layout>>(ENDPOINTS.PROPERTY_ASSETS.LAYOUTS, {
      params,
    });
  },

  // Create a new layout
  create: async (data: CreateLayout) => {
    return await privateApi.post<ResponseAxiosDetail<Layout>>(
      ENDPOINTS.PROPERTY_ASSETS.CREATE_LAYOUT,
      data
    );
  },

  // Update an existing layout
  update: async (id: string, data: UpdateLayout) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.UPDATE_LAYOUT.replace(":id", id);
    return await privateApi.put<ResponseAxiosDetail<Layout>>(url, data);
  },

  // Delete a layout
  delete: async (id: string) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.DELETE_LAYOUT.replace(":id", id);
    return await privateApi.delete<{ success: boolean; message?: string }>(url);
  },

  // Get layout by ID
  getById: async (id: string) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.GET_LAYOUT.replace(":id", id);
    return await privateApi.get<ResponseAxiosDetail<Layout>>(url);
  },

  // Update layout mapping (unit positions and styling)
  updateMapping: async (id: string, data: UpdateLayoutMapping) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.UPDATE_LAYOUT_MAPPING.replace(":id", id);
    return await privateApi.put<ResponseAxiosDetail<Layout>>(url, data);
  },

  // Get layout statistics
  getStatistics: async (params?: {
    property_id?: string;
    start_date?: string;
    end_date?: string;
  }) => {
    return await privateApi.get<ResponseAxiosDetail<LayoutStatistics>>(
      ENDPOINTS.PROPERTY_ASSETS.LAYOUT_STATISTICS,
      {
        params,
      }
    );
  },

  // Get layouts by property ID
  getByPropertyId: async (
    propertyId: string,
    params?: {
      page?: number;
      limit?: number;
      search?: string;
      status?: string;
      floor_number?: number;
    }
  ) => {
    return await privateApi.get<ResponseList<Layout>>(ENDPOINTS.PROPERTY_ASSETS.LAYOUTS, {
      params: {
        ...params,
        property_id: propertyId,
      },
    });
  },

  // Get layouts by status
  getByStatus: async (
    status: string,
    params?: {
      page?: number;
      limit?: number;
      search?: string;
      property_id?: string;
    }
  ) => {
    return await privateApi.get<ResponseList<Layout>>(ENDPOINTS.PROPERTY_ASSETS.LAYOUTS, {
      params: {
        ...params,
        status,
      },
    });
  },

  // Get layouts by floor number
  getByFloorNumber: async (
    floorNumber: number,
    params?: {
      page?: number;
      limit?: number;
      search?: string;
      property_id?: string;
      status?: string;
    }
  ) => {
    return await privateApi.get<ResponseList<Layout>>(ENDPOINTS.PROPERTY_ASSETS.LAYOUTS, {
      params: {
        ...params,
        floor_number: floorNumber,
      },
    });
  },
};
