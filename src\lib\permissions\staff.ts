import { authProtectedPaths } from "@/constants/paths";

export const STAFF_PERMISSIONS = {
  // Virtual Staff Management
  [authProtectedPaths.STAFF]: ["LIST_STAFF"],
  [`${authProtectedPaths.STAFF}/new`]: ["CREATE_STAFF"],
  [authProtectedPaths.DETAIL_STAFF]: ["GET_STAFF"],
  [`${authProtectedPaths.STAFF}/:id/edit`]: ["UPDATE_STAFF"],
  [authProtectedPaths.STAFF_EDIT]: ["UPDATE_STAFF_INTERACT_INFO"],
  [authProtectedPaths.INTERACT]: ["UPDATE_STAFF_INTERACT_INFO"],
  [`${authProtectedPaths.STAFF}/:id/role`]: ["UPDATE_STAFF_ROLE"],
  [`${authProtectedPaths.STAFF}/:id/social-platform`]: ["INTEGRATE_STAFF_TO_SOCIAL_PLATFORM"],

  // Knowledge Management
  [authProtectedPaths.KNOWLEDGE]: ["LIST_KNOWLEDGE"],
  [`${authProtectedPaths.KNOWLEDGE}/new`]: ["CREATE_KNOWLEDGE"],
  [`${authProtectedPaths.KNOWLEDGE}/:id`]: ["GET_KNOWLEDGE"],
  [`${authProtectedPaths.KNOWLEDGE}/:id/edit`]: ["UPDATE_KNOWLEDGE"],
  [`${authProtectedPaths.KNOWLEDGE}/:id/delete`]: ["DELETE_STAFF_KNOWLEDGE"],

  // Task Management
  [authProtectedPaths.TASK]: ["LIST_TASK"],
  [`${authProtectedPaths.TASK}/new`]: ["CREATE_TASK"],
  [`${authProtectedPaths.TASK}/:id`]: ["GET_TASK"],
  [`${authProtectedPaths.TASK}/:id/edit`]: ["UPDATE_TASK"],
  [`${authProtectedPaths.TASK}/:id/prompt`]: ["UPDATE_TASK_PROMPT"],
  [`${authProtectedPaths.TASK}/:id/delete`]: ["DELETE_STAFF_TASK"],

  // Conversation Management
  [authProtectedPaths.CONVERSATION]: ["LIST_MESSAGE", "LIST_MESSAGE_IN_CHARGE"],
  [`${authProtectedPaths.CONVERSATION}/:id/assign`]: ["ASSIGN_STAFF_TO_CONVERSATION"],
  [`${authProtectedPaths.CONVERSATION}/:id/reply`]: ["REPLY_MESSAGE"],
} as const;

export type StaffPermissionKey = keyof typeof STAFF_PERMISSIONS;
export type StaffPermissionValue = (typeof STAFF_PERMISSIONS)[StaffPermissionKey];
