"use client";

import { X } from "lucide-react";
import { useTranslation } from "react-i18next";
import { z } from "zod";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useZodForm,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

import { Employee } from "../../types/employee";

// Form validation schema
const resetPasswordSchema = z.object({
  username: z.string().min(1, "Username is required"),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(
      /[0-9!@#$%^&*(),.?":{}|<>]/,
      "Password must contain at least one number or special character"
    ),
});

type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;

interface ResetPasswordDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  employee: Employee | null;
  onSubmit: (data: ResetPasswordFormData) => Promise<void>;
  loading?: boolean;
}

export function ResetPasswordDialog({
  open,
  onOpenChange,
  employee,
  onSubmit,
  loading = false,
}: ResetPasswordDialogProps) {
  const { t } = useTranslation();

  const form = useZodForm({
    schema: resetPasswordSchema,
    defaultValues: {
      username: employee?.username || "",
      password: "",
    },
  });

  const {
    handleSubmit,
    formState: { isValid },
    reset,
  } = form;
  const handleFormSubmit = async (data: ResetPasswordFormData) => {
    try {
      await onSubmit(data);
      reset();
      onOpenChange(false);
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  const handleCancel = () => {
    reset();
    onOpenChange(false);
  };

  if (!employee) return null;
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[600px] p-0">
        {/* Close Button */}
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => onOpenChange(false)}
          className="absolute right-2 top-2 size-8 p-0">
          <X className="size-4" />
        </Button>

        {/* Header */}
        <DialogHeader className="p-6">
          <DialogTitle className="text-lg font-semibold leading-[1.56] tracking-[-0.025]">
            {t("pages.employeePermission.resetPassword")}
          </DialogTitle>
          <DialogDescription className="text-sm leading-[1.43] text-muted-foreground">
            {t("pages.employeePermission.resetPasswordDescription")}
          </DialogDescription>
        </DialogHeader>

        {/* Content */}
        <div className="px-6 pb-6">
          <Form {...form}>
            <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
              {/* Employee Info - Disabled Fields */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">
                    {t("pages.settings.employeeName")}
                  </label>
                  <Input
                    value={employee.name || ""}
                    disabled
                    placeholder={t("pages.settings.employeeNamePlaceholder")}
                    className="opacity-50"
                  />
                </div>
                <FormField
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-foreground">
                        {t("pages.settings.username")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          value={employee.username || ""}
                          disabled
                          placeholder={t("pages.settings.usernamePlaceholder")}
                          className="opacity-50"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Temporary Password Field */}
              <FormField
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium text-foreground">
                      {t("pages.employeePermission.temporaryPassword")}{" "}
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          {...field}
                          type={"password"}
                          placeholder="••••••••••"
                          className="pr-10"
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </form>
          </Form>
        </div>

        {/* Footer */}
        <DialogFooter className="border-t border-border px-6 py-3">
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={loading}
              className="px-3 py-2 text-sm font-medium">
              {t("common.cancel")}
            </Button>
            <Button
              type="submit"
              onClick={handleSubmit(handleFormSubmit)}
              disabled={!isValid}
              loading={loading}
              className="px-3 py-2 text-sm font-medium">
              {t("common.confirm")}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
