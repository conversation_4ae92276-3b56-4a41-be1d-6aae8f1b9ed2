"use client";

import { use<PERSON>allback, useState } from "react";
import Image from "next/image";
import { zodResolver } from "@hookform/resolvers/zod";
import { AlertCircle, CheckCircle, FileImage, Info, X } from "lucide-react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { z } from "zod";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { ImageUpload } from "@/components/ui/image-upload";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { Textarea } from "@/components/ui/textarea";

// Types
const createLayoutSchema = z.object({
  name: z.string().min(1, "Layout name is required").max(100, "Name too long"),
  description: z.string().max(500, "Description too long").optional(),
  floor_number: z.number().min(0).max(100),
  image: z
    .object({
      name: z.string(),
      image: z.string(),
    })
    .optional(),
});

type CreateLayoutFormData = z.infer<typeof createLayoutSchema>;

interface CreateLayoutDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  onSubmit: (data: CreateLayoutFormData) => Promise<void>;
  isLoading?: boolean;
}

// Basic Information Form Fields Component
function BasicInfoFields({ form }: { form: any }) {
  const { t } = useTranslation();

  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
      <FormField
        control={form.control}
        name="name"
        render={({ field }) => (
          <FormItem>
            <FormLabel>{t("common.name") || "Name"} *</FormLabel>
            <FormControl>
              <Input
                placeholder={t("pages.layouts.layoutNamePlaceholder") || "Enter layout name"}
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="floor_number"
        render={({ field }) => (
          <FormItem>
            <FormLabel>{t("pages.layouts.floorNumber") || "Floor Number"} *</FormLabel>
            <FormControl>
              <Input
                type="number"
                min="0"
                max="100"
                placeholder="1"
                {...field}
                onChange={(e) =>
                  field.onChange(e.target.value ? Number(e.target.value) : undefined)
                }
              />
            </FormControl>
            <FormDescription>
              {t("pages.layouts.floorNumberDescription") || "Floor number for this layout"}
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}

// Description Field Component
function DescriptionField({ form }: { form: any }) {
  const { t } = useTranslation();

  return (
    <FormField
      control={form.control}
      name="description"
      render={({ field }) => (
        <FormItem>
          <FormLabel>{t("common.description") || "Description"}</FormLabel>
          <FormControl>
            <Textarea
              placeholder={
                t("pages.layouts.layoutDescriptionPlaceholder") || "Enter layout description"
              }
              className="resize-none"
              rows={3}
              {...field}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

// Image Upload Component
function ImageUploadSection({
  form,
  uploadProgress,
  setUploadProgress,
}: {
  form: any;
  uploadProgress: number;
  setUploadProgress: (progress: number) => void;
}) {
  const { t } = useTranslation();
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [imageDimensions, setImageDimensions] = useState<{ width: number; height: number } | null>(
    null
  );
  const [uploadError, setUploadError] = useState<string | null>(null);

  // Handle image upload
  const handleImageUpload = useCallback(
    async (base64: string | null) => {
      if (!base64) {
        return;
      }

      setUploadError(null);
      setUploadProgress(0);

      try {
        // Check if base64 is already a data URL (starts with data:image)
        if (base64.startsWith("data:image")) {
          // Single image upload
          const image = base64;

          // Start upload progress simulation
          let currentProgress = 0;
          const progressInterval = setInterval(() => {
            currentProgress += 10;
            if (currentProgress >= 90) {
              clearInterval(progressInterval);
              setUploadProgress(90);
            } else {
              setUploadProgress(currentProgress);
            }
          }, 100);

          // Generate filename from timestamp
          const filename = `layout_${Date.now()}.jpg`;

          // Create image object
          const imageObj = {
            name: filename,
            image: image,
          };

          // Update form
          form.setValue("image", imageObj);

          // Set preview and dimensions (simulated for now)
          setImagePreview(image);
          setImageDimensions({ width: 800, height: 600 }); // Default dimensions
          setUploadProgress(100);

          // Clear interval
          clearInterval(progressInterval);
        } else {
          // Try to parse as JSON (for multiple images)
          const images = JSON.parse(base64) as string[];
          if (images.length === 0) return;

          // Take the first image for layout
          const image = images[0];

          // Start upload progress simulation
          let currentProgress = 0;
          const progressInterval = setInterval(() => {
            currentProgress += 10;
            if (currentProgress >= 90) {
              clearInterval(progressInterval);
              setUploadProgress(90);
            } else {
              setUploadProgress(currentProgress);
            }
          }, 100);

          // Process image data
          const nameMatch = image.match(/;name=(.*?)(;|$)/);
          const filename = nameMatch ? decodeURIComponent(nameMatch[1]) : "layout.jpg";

          // Create image object
          const imageObj = {
            name: filename,
            image: image,
          };

          // Update form
          form.setValue("image", imageObj);

          // Set preview and dimensions (simulated for now)
          setImagePreview(image);
          setImageDimensions({ width: 800, height: 600 }); // Default dimensions
          setUploadProgress(100);

          // Clear interval
          clearInterval(progressInterval);
        }
      } catch (error) {
        setUploadProgress(0);
        const errorMessage = error instanceof Error ? error.message : "Failed to process image";
        setUploadError(errorMessage);
      }
    },
    [form, setUploadProgress, setUploadError, setImagePreview, setImageDimensions]
  );

  // Remove image
  const handleRemoveImage = useCallback(() => {
    setImagePreview(null);
    setImageDimensions(null);
    setUploadProgress(0);
    setUploadError(null);
    form.setValue("image", undefined);
  }, [form, setImagePreview, setImageDimensions, setUploadProgress, setUploadError]);

  return (
    <div className="space-y-4">
      <Label className="text-sm font-medium">
        {t("pages.layouts.uploadImage") || "Upload Image"}
      </Label>

      {!imagePreview ? (
        <FormField
          control={form.control}
          name="image"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <ImageUpload
                  value={field.value ? field.value.image : null}
                  onChange={handleImageUpload}
                  multiple={false}
                  className="w-full"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      ) : (
        <ImagePreviewCard
          imagePreview={imagePreview}
          imageDimensions={imageDimensions}
          uploadProgress={uploadProgress}
          filename={form.watch("image")?.name}
          onRemove={handleRemoveImage}
        />
      )}

      {uploadError && (
        <Alert variant="destructive">
          <AlertCircle className="size-4" />
          <AlertDescription>{uploadError}</AlertDescription>
        </Alert>
      )}

      <Alert>
        <Info className="size-4" />
        <AlertDescription>
          {t("pages.layouts.uploadImageDescription") ||
            "Upload a floor plan or layout image for this property"}
        </AlertDescription>
      </Alert>
    </div>
  );
}

// Image Preview Card Component
function ImagePreviewCard({
  imagePreview,
  imageDimensions,
  uploadProgress,
  filename,
  onRemove,
}: {
  imagePreview: string;
  imageDimensions: { width: number; height: number } | null;
  uploadProgress: number;
  filename?: string;
  onRemove: () => void;
}) {
  const { t } = useTranslation();

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-start gap-4">
          <div className="relative">
            <Image
              src={imagePreview}
              alt="Layout preview"
              width={128}
              height={96}
              className="h-24 w-32 rounded-lg border object-cover"
            />
            <Button
              type="button"
              variant="destructive"
              size="sm"
              className="absolute -right-2 -top-2 size-6 rounded-full p-0"
              onClick={onRemove}>
              <X className="size-3" />
            </Button>
          </div>

          <div className="flex-1 space-y-2">
            <div className="flex items-center gap-2">
              <CheckCircle className="size-4 text-success" />
              <span className="text-sm font-medium">
                {t("pages.layouts.imageUploaded") || "Image uploaded"}
              </span>
            </div>

            {imageDimensions && (
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <Badge variant="outline">
                  {imageDimensions.width} × {imageDimensions.height}px
                </Badge>
                <span>{filename}</span>
              </div>
            )}

            {uploadProgress < 100 && (
              <div className="space-y-1">
                <Progress value={uploadProgress} className="h-2" />
                <p className="text-xs text-muted-foreground">
                  {t("pages.layouts.uploading") || "Uploading"} {uploadProgress}%
                </p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Footer Component
function DialogFooter({
  form,
  isLoading,
  onSubmit,
  onCancel,
}: {
  form: any;
  isLoading: boolean;
  onSubmit: () => void;
  onCancel: () => void;
}) {
  const { t } = useTranslation();

  return (
    <div className="flex flex-none justify-end gap-3 border-t pt-4">
      <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
        {t("common.cancel") || "Cancel"}
      </Button>
      <Button
        type="submit"
        onClick={onSubmit}
        loading={isLoading}
        disabled={isLoading || !form.watch("name") || !form.watch("floor_number")}>
        {t("common.create", "Create")}
      </Button>
    </div>
  );
}

export default function CreateLayoutDialog({
  open,
  setOpen,
  onSubmit,
  isLoading = false,
}: CreateLayoutDialogProps) {
  const { t } = useTranslation();

  // Note: This component is designed to be reset when the 'open' prop changes
  // The parent component should use a key prop (e.g., key={open ? 'open' : 'closed'})
  // to force a complete remount and reset all internal state when the dialog opens

  // Form
  const form = useForm<CreateLayoutFormData>({
    resolver: zodResolver(createLayoutSchema),
    defaultValues: {
      name: "",
      description: "",
      floor_number: 1,
      image: undefined,
    },
  });

  // Upload progress state
  const [uploadProgress, setUploadProgress] = useState(0);

  // Form submission
  const handleSubmit = useCallback(
    async (data: CreateLayoutFormData) => {
      try {
        await onSubmit(data);
        setOpen(false);
      } catch (error) {
        // TODO: Implement proper error logging
      }
    },
    [onSubmit, setOpen]
  );

  const handleCancel = () => {
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="flex max-h-[90vh] max-w-2xl flex-col overflow-y-hidden">
        <DialogHeader className="flex-none px-1">
          <DialogTitle className="flex items-center gap-2">
            <FileImage className="size-5" />
            {t("pages.layouts.createLayout") || "Create Layout"}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-auto space-y-4 overflow-y-auto px-1">
          <p className="text-sm text-muted-foreground">
            {t("pages.layouts.createLayoutDescription") || "Create a new layout"}
          </p>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              <BasicInfoFields form={form} />
              <DescriptionField form={form} />
              <ImageUploadSection
                form={form}
                uploadProgress={uploadProgress}
                setUploadProgress={setUploadProgress}
              />
            </form>
          </Form>
        </div>

        <DialogFooter
          form={form}
          isLoading={isLoading}
          onSubmit={() => form.handleSubmit(handleSubmit)()}
          onCancel={handleCancel}
        />
      </DialogContent>
    </Dialog>
  );
}
