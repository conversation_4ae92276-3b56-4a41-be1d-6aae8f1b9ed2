"use client";

import { ReactNode } from "react";
import { Clock, Home, Star, Users } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Card, CardContent } from "@/components/ui/card";

import { type PropertyStats } from "../PropertyDetailsComponent.types";

// Reusable StatCard component
interface StatCardProps {
  title: string;
  value: ReactNode;
  icon: ReactNode;
  iconColor: string;
}

function StatCard({ title, value, icon, iconColor }: StatCardProps) {
  return (
    <Card className="border-0 bg-card shadow-sm">
      <CardContent className="p-0">
        <div className="flex items-center justify-between p-4 pb-3">
          <span className="text-sm font-medium text-card-foreground">{title}</span>
          <div className={`size-6 ${iconColor}`}>{icon}</div>
        </div>
        <div className="px-4 pb-4">
          <p className="text-2xl font-semibold text-card-foreground">{value}</p>
        </div>
      </CardContent>
    </Card>
  );
}

interface PropertyStatsCardsProps {
  stats: PropertyStats;
}

export function PropertyStatsCards({ stats }: PropertyStatsCardsProps) {
  const { t } = useTranslation();
  return (
    <div className="space-y-4">
      {/* Top row - 2x2 grid */}
      <div className="grid grid-cols-2 gap-4">
        <StatCard
          title={t("pages.properties.components.propertyStatsCards.totalUnits")}
          value={stats.totalUnits}
          icon={<Home className="size-5 text-warning" />}
          iconColor="text-warning"
        />

        <StatCard
          title={t("pages.properties.components.propertyStatsCards.occupiedUnits")}
          value={stats.occupiedUnits}
          icon={<Star className="text-info size-5" />}
          iconColor="text-info"
        />

        <StatCard
          title={t("pages.properties.components.propertyStatsCards.availableUnits")}
          value={stats.availableUnits}
          icon={<Clock className="size-5 text-destructive" />}
          iconColor="text-destructive"
        />

        <StatCard
          title={t("pages.properties.components.propertyStatsCards.maintenance")}
          value={stats.maintenanceUnits}
          icon={<Clock className="size-5 text-destructive" />}
          iconColor="text-destructive"
        />
      </div>

      {/* Bottom row - Full width Occupancy Rate */}
      <StatCard
        title={t("pages.properties.components.propertyStatsCards.occupancyRate")}
        value={`${stats.occupancyRate}%`}
        icon={<Users className="size-5 text-success" />}
        iconColor="text-success"
      />
    </div>
  );
}
