import puppeteer from "puppeteer";

import { navigateWithAuth } from "./utils/auth.mjs";

async function validateTranslations() {
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: { width: 1920, height: 1080 },
  });

  try {
    const page = await browser.newPage();

    console.log("🔐 Authenticating and navigating to property-assets-dashboard...");
    await navigateWithAuth(page, "http://localhost:3000/property-assets-dashboard");

    // Wait for page to load
    await new Promise((resolve) => setTimeout(resolve, 3000));

    console.log("📸 Taking English version screenshot...");
    await page.screenshot({
      path: "/Users/<USER>/Desktop/Projects/onex-erp/screenshots/translations-en-overview.png",
      fullPage: true,
    });

    // Capture text content for analysis
    const textContent = await page.evaluate(() => {
      const walker = document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT, null, false);

      const texts = [];
      let node;
      while ((node = walker.nextNode())) {
        const text = node.textContent.trim();
        if (text && text.length > 1 && !text.match(/^\d+$/) && !text.match(/^[\s\n\r]+$/)) {
          texts.push(text);
        }
      }
      return texts;
    });

    console.log("📝 Found text content:", textContent.slice(0, 20));

    // Test all tabs for translations
    const tabs = ["Overview", "Financial", "Properties", "Units", "Analytics", "Operations"];
    const translationIssues = [];

    for (let tabName of tabs) {
      console.log(`🔍 Testing ${tabName} tab...`);

      try {
        // Find and click tab
        const tabButtons = await page.$$(
          '[role="tablist"] button, .tabs button, button[data-state]'
        );
        let targetTab = null;

        for (let tab of tabButtons) {
          const text = await tab.evaluate((el) => el.textContent?.trim());
          if (text && text.toLowerCase().includes(tabName.toLowerCase())) {
            targetTab = tab;
            break;
          }
        }

        if (targetTab) {
          await targetTab.click();
          await new Promise((resolve) => setTimeout(resolve, 2000));

          // Take screenshot
          await page.screenshot({
            path: `/Users/<USER>/Desktop/Projects/onex-erp/screenshots/translations-en-${tabName.toLowerCase()}.png`,
            fullPage: true,
          });

          // Check for translation keys (indicators of missing translations)
          const untranslatedContent = await page.evaluate(() => {
            const text = document.body.innerText;
            const issues = [];

            // Look for translation key patterns
            const keyPatterns = [/common\.\w+/g, /pages\.\w+/g, /nav\.\w+/g, /\w+\.\w+\.\w+/g];

            keyPatterns.forEach((pattern) => {
              const matches = text.match(pattern);
              if (matches) {
                matches.forEach((match) => {
                  if (!issues.includes(match)) {
                    issues.push(match);
                  }
                });
              }
            });

            return issues;
          });

          if (untranslatedContent.length > 0) {
            translationIssues.push({
              tab: tabName,
              issues: untranslatedContent,
            });
          }
        } else {
          console.log(`⚠️ Could not find ${tabName} tab`);
        }
      } catch (error) {
        console.log(`❌ Error testing ${tabName} tab: ${error.message}`);
      }
    }

    // Change to Vietnamese language
    console.log("🇻🇳 Switching to Vietnamese language...");

    // Look for language selector
    const languageSelector = await page.$(
      '[class*="language"], [data-testid*="language"], button[class*="lang"]'
    );
    if (languageSelector) {
      await languageSelector.click();
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Try to find Vietnamese option
      const vietnameseOption = await page.$x(
        "//button[contains(text(), 'Tiếng Việt')] | //option[contains(text(), 'Tiếng Việt')] | //a[contains(text(), 'VI')]"
      );
      if (vietnameseOption.length > 0) {
        await vietnameseOption[0].click();
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }
    }

    // Take Vietnamese screenshots
    console.log("📸 Taking Vietnamese version screenshots...");
    await page.screenshot({
      path: "/Users/<USER>/Desktop/Projects/onex-erp/screenshots/translations-vi-overview.png",
      fullPage: true,
    });

    // Test Vietnamese tabs
    for (let tabName of tabs) {
      console.log(`🔍 Testing ${tabName} tab in Vietnamese...`);

      try {
        const tabButtons = await page.$$(
          '[role="tablist"] button, .tabs button, button[data-state]'
        );
        let targetTab = null;

        // Try English names first, then Vietnamese
        const vietnameseTabNames = {
          Overview: "Tổng quan",
          Financial: "Tài chính",
          Properties: "Bất động sản",
          Units: "Đơn vị",
          Analytics: "Phân tích",
          Operations: "Vận hành",
        };

        for (let tab of tabButtons) {
          const text = await tab.evaluate((el) => el.textContent?.trim());
          if (
            text &&
            (text.toLowerCase().includes(tabName.toLowerCase()) ||
              text.includes(vietnameseTabNames[tabName]))
          ) {
            targetTab = tab;
            break;
          }
        }

        if (targetTab) {
          await targetTab.click();
          await new Promise((resolve) => setTimeout(resolve, 2000));

          await page.screenshot({
            path: `/Users/<USER>/Desktop/Projects/onex-erp/screenshots/translations-vi-${tabName.toLowerCase()}.png`,
            fullPage: true,
          });
        }
      } catch (error) {
        console.log(`❌ Error testing ${tabName} tab in Vietnamese: ${error.message}`);
      }
    }

    // Mobile responsive test
    console.log("📱 Testing mobile translations...");
    await page.setViewport({ width: 375, height: 667 });
    await new Promise((resolve) => setTimeout(resolve, 1000));
    await page.screenshot({
      path: "/Users/<USER>/Desktop/Projects/onex-erp/screenshots/translations-mobile.png",
      fullPage: true,
    });

    console.log("✅ Translation validation completed!");
    console.log("📸 Screenshots saved for analysis");

    return {
      success: true,
      translationIssues,
      screenshots: [
        "translations-en-overview.png",
        "translations-vi-overview.png",
        "translations-mobile.png",
      ],
    };
  } catch (error) {
    console.error("❌ Error validating translations:", error);
    return { success: false, error: error.message };
  } finally {
    await browser.close();
  }
}

// Run validation
validateTranslations()
  .then((result) => {
    if (result.success) {
      console.log("\n🎉 Translation validation COMPLETED!");
      console.log(`✅ Screenshots captured: ${result.screenshots.length}`);
      if (result.translationIssues.length > 0) {
        console.log("⚠️ Translation issues found:");
        result.translationIssues.forEach((issue) => {
          console.log(`   ${issue.tab}: ${issue.issues.join(", ")}`);
        });
      } else {
        console.log("✅ No translation key issues detected");
      }
    } else {
      console.log("\n❌ Translation validation FAILED!");
      console.log(`Error: ${result.error}`);
    }
  })
  .catch((error) => {
    console.error("❌ Script execution failed:", error);
  });
