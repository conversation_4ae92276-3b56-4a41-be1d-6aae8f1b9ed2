"use client";

import type { AssetCategory } from "../types";
import { AssetCategoryCard } from "./AssetCategoryCard";

interface CategoriesListProps {
  categories: AssetCategory[];
  searchTerm: string;
  onSearchChange: (term: string) => void;
  onEdit: (category: AssetCategory) => void;
  onViewAssets: (category: AssetCategory) => void;
  onViewAnalytics: (category: AssetCategory) => void;
  onDelete: (category: AssetCategory) => void;
  getIconComponent: (iconName: string) => React.ReactNode;
}

export function CategoriesList({
  categories,
  searchTerm,
  onSearchChange,
  onEdit,
  onViewAssets,
  onViewAnalytics,
  onDelete,
  getIconComponent,
}: CategoriesListProps) {
  const filteredCategories = categories.filter(
    (category) =>
      category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      category.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-4">
      {filteredCategories.map((category) => (
        <AssetCategoryCard
          key={category.id}
          category={category}
          onEdit={onEdit}
          onViewAssets={onViewAssets}
          onViewAnalytics={onViewAnalytics}
          onDelete={onDelete}
          getIconComponent={getIconComponent}
        />
      ))}
    </div>
  );
}
