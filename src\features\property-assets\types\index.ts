import { Property } from "@/lib/apis/types/property_assets/property";

// Base interfaces
export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
  company_id: string;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
}

export interface PropertyImage {
  id: string;
  url: string;
  name: string;
  is_primary: boolean;
}

export interface PropertyLayout {
  id: string;
  property_id: string;
  name: string;
  description?: string;
  floor_number?: number;
  image_url: string;
  image_width: number;
  image_height: number;
  scale: number;
  offset_x: number;
  offset_y: number;
  unit_positions: UnitLayoutPosition[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface UnitLayoutPosition {
  id: string;
  layout_id: string;
  unit_id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  rotation?: number;
  shape_type: "rectangle" | "circle" | "polygon";
  style_config: {
    fill_color: string;
    stroke_color: string;
    stroke_width: number;
    opacity: number;
    label_text: string;
    label_font_size: number;
    label_color: string;
    visible: boolean;
  };
}

export interface CreateProperty {
  name: string;
  address: Address;
  property_type: "residential" | "commercial" | "mixed";
  description?: string;
  owner_name: string;
  owner_email: string;
  owner_phone: string;
  purchase_price?: number;
  purchase_date?: string;
  images?: { name: string; image: string }[];
  layouts?: CreatePropertyLayout[];
}

export interface CreatePropertyLayout {
  name: string;
  description?: string;
  floor_number?: number;
  image_data: string;
  image_width: number;
  image_height: number;
}

// Unit related interfaces
export interface Unit extends BaseEntity {
  property_id: string;
  unit_number: string;
  unit_type: "studio" | "1br" | "2br" | "3br" | "commercial" | "office" | "retail";
  floor: number;
  square_footage: number;
  bedrooms?: number;
  bathrooms?: number;
  description?: string;
  rent_amount: number;
  deposit_amount: number;
  status: "available" | "occupied" | "maintenance" | "inactive";
  amenities?: string[];
  images?: UnitImage[];
  property?: Property;
}

export interface UnitImage {
  id: string;
  url: string;
  name: string;
  is_primary: boolean;
}

export interface CreateUnit {
  property_id: string;
  unit_number: string;
  unit_type: "studio" | "1br" | "2br" | "3br" | "commercial" | "office" | "retail";
  floor: number;
  square_footage: number;
  bedrooms?: number;
  bathrooms?: number;
  description?: string;
  rent_amount: number;
  deposit_amount: number;
  amenities?: string[];
  images?: { name: string; image: string }[];
}

// Tenant related interfaces
export interface Tenant extends BaseEntity {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  identification_type: "passport" | "driver_license" | "national_id";
  identification_number: string;
  date_of_birth?: string;
  employment_status: "employed" | "self_employed" | "student" | "unemployed" | "retired";
  employer_name?: string;
  monthly_income?: number;
  status: "active" | "inactive" | "blacklisted";
  documents?: TenantDocument[];
}

export interface TenantDocument {
  id: string;
  document_type: "id" | "income_proof" | "reference" | "other";
  file_name: string;
  file_url: string;
  uploaded_at: string;
}

export interface CreateTenant {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  identification_type: "passport" | "driver_license" | "national_id";
  identification_number: string;
  date_of_birth?: string;
  employment_status: "employed" | "self_employed" | "student" | "unemployed" | "retired";
  employer_name?: string;
  monthly_income?: number;
  documents?: { document_type: string; file_name: string; file_data: string }[];
}

// Contract related interfaces
export interface Contract extends BaseEntity {
  property_id: string;
  unit_id: string;
  tenant_id: string;
  contract_type: "monthly" | "annual" | "profit_sharing" | "revenue_sharing";
  start_date: string;
  end_date?: string;
  rent_amount: number;
  deposit_amount: number;
  late_fee_amount?: number;
  rent_due_day: number;
  status: "active" | "terminated" | "expired" | "pending";
  terms_and_conditions?: string;
  profit_sharing_percentage?: number;
  revenue_sharing_percentage?: number;
  auto_renewal: boolean;
  notice_period_days: number;
  property?: Property;
  unit?: Unit;
  tenant?: Tenant;
  payments?: Payment[];
}

export interface CreateContract {
  property_id: string;
  unit_id: string;
  tenant_id: string;
  contract_type: "monthly" | "annual" | "profit_sharing" | "revenue_sharing";
  start_date: string;
  end_date?: string;
  rent_amount: number;
  deposit_amount: number;
  late_fee_amount?: number;
  rent_due_day: number;
  terms_and_conditions?: string;
  profit_sharing_percentage?: number;
  revenue_sharing_percentage?: number;
  auto_renewal: boolean;
  notice_period_days: number;
}

// Maintenance related interfaces
export interface MaintenanceRequest extends BaseEntity {
  property_id: string;
  unit_id?: string;
  tenant_id?: string;
  title: string;
  description: string;
  priority: "low" | "medium" | "high" | "urgent";
  category: "plumbing" | "electrical" | "hvac" | "appliance" | "structural" | "cosmetic" | "other";
  status: "open" | "in_progress" | "completed" | "cancelled";
  reported_date: string;
  scheduled_date?: string;
  completed_date?: string;
  estimated_cost?: number;
  actual_cost?: number;
  contractor_name?: string;
  contractor_phone?: string;
  notes?: string;
  images?: Array<{
    id: string;
    name: string;
    url: string;
  }>;
  property?: Property;
  unit?: Unit;
  tenant?: Tenant;
}

export interface CreateMaintenanceRequest {
  property_id: string;
  unit_id?: string;
  tenant_id?: string;
  title: string;
  description: string;
  priority: "low" | "medium" | "high" | "urgent";
  category: "plumbing" | "electrical" | "hvac" | "appliance" | "structural" | "cosmetic" | "other";
  scheduled_date?: string;
  estimated_cost?: number;
  contractor_name?: string;
  contractor_phone?: string;
  notes?: string;
  images?: Array<{
    name: string;
    image: string;
  }>;
}

// Payment related interfaces
export interface Payment extends BaseEntity {
  contract_id: string;
  amount: number;
  payment_date: string;
  payment_method: "cash" | "bank_transfer" | "credit_card" | "check";
  payment_type: "rent" | "deposit" | "late_fee" | "maintenance" | "other";
  description?: string;
  status: "pending" | "completed" | "failed" | "refunded";
  reference_number?: string;
  contract?: Contract;
}

export interface CreatePayment {
  contract_id: string;
  amount: number;
  payment_date: string;
  payment_method: "cash" | "bank_transfer" | "credit_card" | "check";
  payment_type: "rent" | "deposit" | "late_fee" | "maintenance" | "other";
  description?: string;
  reference_number?: string;
}

export interface CreateMaintenanceRequest {
  property_id: string;
  unit_id?: string;
  tenant_id?: string;
  title: string;
  description: string;
  priority: "low" | "medium" | "high" | "urgent";
  category: "plumbing" | "electrical" | "hvac" | "appliance" | "structural" | "cosmetic" | "other";
  scheduled_date?: string;
  estimated_cost?: number;
  contractor_name?: string;
  contractor_phone?: string;
  notes?: string;
  images?: { name: string; image: string }[];
}

// Financial interfaces
export interface FinancialSummary {
  total_properties: number;
  total_units: number;
  occupied_units: number;
  vacancy_rate: number;
  monthly_rental_income: number;
  monthly_expenses: number;
  net_monthly_income: number;
  ytd_rental_income: number;
  ytd_expenses: number;
  ytd_net_income: number;
}

export interface ProfitLossReport {
  period_start: string;
  period_end: string;
  property_id?: string;
  rental_income: number;
  other_income: number;
  total_income: number;
  maintenance_expenses: number;
  utility_expenses: number;
  insurance_expenses: number;
  property_tax_expenses: number;
  management_expenses: number;
  other_expenses: number;
  total_expenses: number;
  net_income: number;
  profit_margin: number;
}

export interface Transaction {
  id: string;
  property_id?: string;
  unit_id?: string;
  contract_id?: string;
  date: string;
  amount: number;
  type: "income" | "expense";
  category: string;
  description: string;
  payment_method?: string;
  reference_number?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateTransaction {
  property_id?: string;
  unit_id?: string;
  contract_id?: string;
  date: string;
  amount: number;
  type: "income" | "expense";
  category: string;
  description: string;
  payment_method?: string;
  reference_number?: string;
}

// Response interfaces
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

// Export all types
export type PropertyResponse = PaginatedResponse<Property>;
export type UnitResponse = PaginatedResponse<Unit>;
export type TenantResponse = PaginatedResponse<Tenant>;
export type ContractResponse = PaginatedResponse<Contract>;
export type PaymentResponse = PaginatedResponse<Payment>;
export type MaintenanceResponse = PaginatedResponse<MaintenanceRequest>;
export type TransactionResponse = PaginatedResponse<Transaction>;
export type PropertyLayoutResponse = PaginatedResponse<PropertyLayout>;
export type LayoutResponse = PaginatedResponse<Layout>;

// Layout related interfaces
export enum LayoutStatus {
  PENDING = "PENDING",
  IN_PROGRESS = "IN_PROGRESS",
  DONE = "DONE",
}

export enum ShapeType {
  RECTANGLE = "RECTANGLE",
  CIRCLE = "CIRCLE",
  POLYGON = "POLYGON",
}

export interface StyleConfig {
  fill_color?: string;
  stroke_color?: string;
  stroke_width?: string;
  opacity?: number;
  label_text?: string;
  label_font_size?: number;
  label_color?: string;
  visible?: boolean;
}

export interface LayoutUnitPosition {
  unit_id: string;
  x: number;
  y: number;
  rotation?: number;
  shape_type: string;
  style_config: StyleConfig;
}

export interface Layout extends BaseEntity {
  property_id: string;
  name: string;
  floor_number: number;
  description?: string;
  status: string;
  image?: {
    id: string;
    url: string;
    name: string;
    is_primary: boolean;
  };
  unit_layout_positions?: LayoutUnitPosition[];
}

export interface CreateLayout {
  property_id: string;
  name: string;
  floor_number: number;
  description?: string;
  status: string;
  image?: {
    name: string;
    image: string;
  };
  unit_layout_positions?: Omit<LayoutUnitPosition, "unit_id">[];
}

export interface UpdateLayout {
  name?: string;
  floor_number?: number;
  description?: string;
  status?: string;
  image?: {
    name: string;
    image: string;
  };
  unit_layout_positions?: Omit<LayoutUnitPosition, "unit_id">[];
}

export interface UpdateLayoutMapping {
  unit_layout_positions: LayoutUnitPosition[];
}

export interface LayoutStatistics {
  total_layouts: number;
  layouts_by_status: {
    PENDING: number;
    IN_PROGRESS: number;
    DONE: number;
  };
  layouts_by_property: Array<{
    property_id: string;
    property_name: string;
    layout_count: number;
  }>;
}
