"use client";

import { useTranslation } from "react-i18next";

import Empty from "@/components/ui/empty";

import { CurrentPlan, SubscriptionPlan } from "../hooks";
import CustomPlan from "./custom-plan";
import { BillingToggle, PricingCard } from "./index";
import { PlanSkeleton } from "./skeletons/plan";

interface PricingSectionProps {
  plans: SubscriptionPlan[];
  isAnnualBilling: boolean;
  onBillingToggle: () => void;
  formatPrice: (price: number | string, currency: string) => string;
  isLoading: boolean;
  currentPlan?: CurrentPlan;
}

export const PricingSection = ({
  plans,
  isAnnualBilling,
  onBillingToggle,
  formatPrice,
  isLoading,
  currentPlan,
}: PricingSectionProps) => {
  const { t } = useTranslation();

  // Separate popular and regular plans
  const popularPlan = plans.find((plan) => plan.isPopular || plan.is_popular);

  return (
    <div className="space-y-2 py-10" id="pricing-section">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold leading-tight tracking-tight text-foreground">
          {t("pages.subscription.pricing.title")}
        </h1>
        <p className="mt-2 text-sm text-muted-foreground">
          {t("pages.subscription.pricing.description")}
        </p>
      </div>

      {/* Billing Toggle */}
      <BillingToggle isAnnualBilling={isAnnualBilling} onToggle={onBillingToggle} />

      {/* Pricing Cards Layout */}
      {isLoading ? (
        <div className="grid gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 3 }).map((_, index) => (
            <PlanSkeleton key={index} />
          ))}
        </div>
      ) : plans.length === 0 ? (
        <Empty />
      ) : (
        <div className="flex flex-col gap-4">
          {/* Popular Card - Centered on md screens, full width on sm, part of grid on lg */}
          {popularPlan && (
            <div className="flex justify-center">
              <div className="w-full md:max-w-80 lg:hidden">
                <PricingCard
                  plan={popularPlan}
                  isAnnualBilling={isAnnualBilling}
                  formatPrice={formatPrice}
                  currentPlan={currentPlan}
                />
              </div>
            </div>
          )}

          {/* Regular Cards Grid */}
          <div className="grid gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
            {/* On lg screens, show all plans including popular */}
            {plans.map((plan) => (
              <div
                key={plan.id}
                className={`${plan.isPopular || plan.is_popular ? "hidden lg:block" : ""}`}>
                <PricingCard
                  plan={plan}
                  isAnnualBilling={isAnnualBilling}
                  formatPrice={formatPrice}
                  currentPlan={currentPlan}
                />
              </div>
            ))}
          </div>
          <CustomPlan />
        </div>
      )}
    </div>
  );
};
