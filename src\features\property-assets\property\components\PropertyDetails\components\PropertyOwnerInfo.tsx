"use client";

import { ReactNode } from "react";
import { useTranslation } from "react-i18next";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Property } from "@/lib/apis/types/property_assets/property";

// Reusable InlineInfoRow component
interface InlineInfoRowProps {
  label: string;
  value: ReactNode;
  icon?: ReactNode;
  showBorder?: boolean;
  valueClassName?: string;
}

function InlineInfoRow({
  label,
  value,
  icon,
  showBorder = true,
  valueClassName = "text-sm font-medium text-card-foreground",
}: InlineInfoRowProps) {
  return (
    <div
      className={`flex items-center justify-between py-2 ${showBorder ? "border-b border-border" : ""}`}>
      <div className="flex items-center gap-2">
        {icon && icon}
        <span className="text-sm text-card-foreground">{label}:</span>
      </div>
      <span className={valueClassName}>{value}</span>
    </div>
  );
}

interface PropertyOwnerInfoProps {
  property: Property;
}

export function PropertyOwnerInfo({ property }: PropertyOwnerInfoProps) {
  const { t } = useTranslation();

  return (
    <Card className="border-0 bg-card shadow-sm">
      <CardHeader className="pb-4">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {t("pages.properties.components.propertyOwnerInfo.ownerInformation")}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-0">
        <InlineInfoRow
          label={t("pages.properties.components.propertyOwnerInfo.ownerName")}
          value={property.owner?.name || "N/A"}
        />

        <InlineInfoRow
          label={t("pages.properties.components.propertyOwnerInfo.ownerEmail")}
          value={property.owner?.email || "N/A"}
        />

        <InlineInfoRow
          label={t("pages.properties.components.propertyOwnerInfo.ownerPhone")}
          value={property.owner?.phone || "N/A"}
        />

        {/* Purchase Information */}
        {property.purchase_information && (
          <>
            <InlineInfoRow
              label={t("pages.properties.components.propertyOwnerInfo.purchasePrice")}
              value={`$${property.purchase_information.price.toLocaleString()}`}
              valueClassName="text-sm font-medium text-success"
            />

            <InlineInfoRow
              label={t("pages.properties.components.propertyOwnerInfo.purchaseDate")}
              value={new Date(property.purchase_information.purchase_date).toLocaleDateString()}
              showBorder={false}
            />
          </>
        )}
      </CardContent>
    </Card>
  );
}
