"use client";

import { TooltipProvider } from "@/components/ui/tooltip";

import { useUnitMapping } from "../hooks/use-unit-mapping";
import { ControlsCard, DeleteDialog, LayoutArea, StatsCard, UnitList } from "./unit-mapping";
import type { UnitMappingInterfaceProps } from "./unit-mapping";

export function UnitMappingInterface({
  property,
  units,
  layouts,
  onLayoutSave,
  onLayoutDelete,
  onUnitUpdate,
  className = "",
}: UnitMappingInterfaceProps) {
  const {
    // State
    selectedLayoutId,
    viewMode,
    searchTerm,
    filterStatus,
    selectedUnitId,
    showDeleteDialog,
    layoutToDelete,
    unitMappings,
    isEditing,
    isLoading,
    error,

    // Computed values
    currentLayout,
    filteredUnits,
    totalUnits,
    mappedUnits,
    unmappedUnits,
    mappingProgress,

    // Actions
    setViewMode,
    setSearchTerm,
    setFilterStatus,
    setShowDeleteDialog,
    setLayoutToDelete,
    setIsEditing,

    // Event handlers
    handleLayoutSelect,
    handleUnitSelect,
    handleUnitPositionChange,
    handleSaveLayout,
    handleDeleteLayout,
    handleShapesChange,

    // Utility functions
    getUnitStatusColor,
    getUnitStatusVariant,
  } = useUnitMapping({
    property,
    units,
    layouts,
    onLayoutSave,
    onLayoutDelete,
    onUnitUpdate,
  });

  return (
    <TooltipProvider>
      <div className={`flex h-full gap-6 ${className}`}>
        {/* Left Sidebar - Unit List */}
        <div className="flex w-80 flex-col space-y-4">
          {/* Stats Card */}
          <StatsCard
            mappedUnits={mappedUnits}
            unmappedUnits={unmappedUnits}
            mappingProgress={mappingProgress}
            isLoading={isLoading}
          />

          {/* Controls */}
          <ControlsCard
            selectedLayoutId={selectedLayoutId}
            layouts={layouts}
            viewMode={viewMode}
            filterStatus={filterStatus}
            isEditing={isEditing}
            isLoading={isLoading}
            onLayoutSelect={handleLayoutSelect}
            onViewModeChange={setViewMode}
            onFilterStatusChange={setFilterStatus}
            onEditToggle={() => setIsEditing(!isEditing)}
            onSave={handleSaveLayout}
            onDelete={() => {
              setLayoutToDelete(selectedLayoutId);
              setShowDeleteDialog(true);
            }}
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            totalUnits={totalUnits}
            mappedUnits={mappedUnits}
          />

          {/* Unit List */}
          <UnitList
            units={filteredUnits}
            selectedUnitId={selectedUnitId}
            selectedLayoutId={selectedLayoutId}
            unitMappings={unitMappings}
            isLoading={isLoading}
            onUnitSelect={handleUnitSelect}
            getUnitStatusColor={getUnitStatusColor}
            getUnitStatusVariant={getUnitStatusVariant}
          />
        </div>

        {/* Main Layout Area */}
        <div className="flex-1">
          <LayoutArea
            currentLayout={currentLayout}
            units={units}
            isEditing={isEditing}
            isLoading={isLoading}
            onSave={handleSaveLayout}
            onShapesChange={handleShapesChange}
            onUnitClick={handleUnitSelect}
            onUnitPositionChange={handleUnitPositionChange}
          />
        </div>

        {/* Delete Layout Dialog */}
        <DeleteDialog
          open={showDeleteDialog}
          onOpenChange={setShowDeleteDialog}
          onConfirm={handleDeleteLayout}
        />
      </div>
    </TooltipProvider>
  );
}
