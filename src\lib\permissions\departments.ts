import { authProtectedPaths } from "@/constants/paths";

export const DEPARTMENT_PERMISSIONS = {
  // Department Management
  [authProtectedPaths.DEPARTMENT]: ["GET_DEPARTMENT"],
  [`${authProtectedPaths.DEPARTMENT}/new`]: ["CREATE_DEPARTMENT"],
  [authProtectedPaths.DEPARTMENT_ID]: ["GET_DEPARTMENT"],
  [`${authProtectedPaths.DEPARTMENT}/:id/edit`]: ["UPDATE_DEPARTMENT"],
  [`${authProtectedPaths.DEPARTMENT}/:id/description`]: ["UPDATE_DEPARTMENT_DESCRIPTION"],
  [`${authProtectedPaths.DEPARTMENT}/:id/delete`]: ["DELETE_DEPARTMENT"],
} as const;

export type DepartmentPermissionKey = keyof typeof DEPARTMENT_PERMISSIONS;
export type DepartmentPermissionValue = (typeof DEPARTMENT_PERMISSIONS)[DepartmentPermissionKey];
