import { Eye, Loader2, MapPin, PencilLine, Plus, Save } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import type { Unit } from "../../../types";
import { LayoutOverlay } from "../LayoutOverlay";
import type { LayoutData } from "../LayoutViewer";
import { LayoutViewer } from "../LayoutViewer";

interface LayoutAreaProps {
  currentLayout: LayoutData | undefined;
  units: Unit[];
  isEditing: boolean;
  isLoading: boolean;
  onSave: () => void;
  onShapesChange: (shapes: any[]) => void;
  onUnitClick: (unit: Unit) => void;
  onUnitPositionChange: (unitPosition: any) => void;
  onToggleMode: () => void;
}

export function LayoutArea({
  currentLayout,
  units,
  isEditing,
  isLoading,
  onSave,
  onShapesChange,
  onUnitClick,
  onUnitPositionChange,
  onToggleMode,
}: LayoutAreaProps) {
  const { t } = useTranslation();

  return (
    <Card className="h-full">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            <MapPin className="size-5" />
            {currentLayout?.name || t("pages.layouts.noLayoutSelected")}
          </span>
          <div className="flex items-center gap-2">
            <Button variant={isEditing ? "outline" : "default"} size="sm" onClick={onToggleMode}>
              {isEditing ? (
                <span className="flex items-center gap-2">
                  <Eye className="size-4" /> {t("common.view")}
                </span>
              ) : (
                <span className="flex items-center gap-2">
                  <PencilLine className="size-4" /> {t("common.edit")}
                </span>
              )}
            </Button>
            {isEditing && (
              <>
                <Badge variant="secondary">{t("common.editMode")}</Badge>
                <Button variant="outline" size="sm" onClick={onSave} disabled={isLoading}>
                  {isLoading ? (
                    <Loader2 className="mr-2 size-4 animate-spin" />
                  ) : (
                    <Save className="mr-2 size-4" />
                  )}
                  {t("common.save")}
                </Button>
              </>
            )}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="h-[calc(100%-5rem)]">
        {currentLayout ? (
          isEditing ? (
            <LayoutOverlay
              layoutImage={currentLayout.imageUrl}
              units={units}
              initialShapes={currentLayout.unitPositions.map((pos) => ({
                ...pos,
                shape: "rectangle" as const,
                fill: "#3b82f6",
                stroke: "#1e40af",
                strokeWidth: 2,
                opacity: 0.7,
                label: units.find((u) => u.id === pos.unitId)?.unit_number || "Unit",
                fontSize: 12,
                fontFamily: "Arial",
                textColor: "#ffffff",
                visible: true,
              }))}
              onShapesChange={onShapesChange}
              width={800}
              height={600}
            />
          ) : (
            <LayoutViewer
              layoutData={currentLayout}
              units={units}
              onUnitClick={onUnitClick}
              onUnitPositionChange={onUnitPositionChange}
              isEditable={false}
              showControls={true}
            />
          )
        ) : (
          <div className="flex h-full items-center justify-center">
            <div className="text-center">
              <MapPin className="mx-auto mb-4 size-16 text-muted-foreground" />
              <h3 className="mb-2 text-lg font-semibold text-foreground">
                {t("pages.layouts.noLayoutSelected")}
              </h3>
              <p className="mb-4 text-sm text-muted-foreground">
                {t("pages.layouts.selectLayoutToStart")}
              </p>
              <Button>
                <Plus className="mr-2 size-4" />
                {t("pages.layouts.createLayout")}
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
