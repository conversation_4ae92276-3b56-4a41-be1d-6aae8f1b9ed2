"use client";

import { useLayoutEffect } from "react";
import { useRouter } from "next/navigation";
import { AlertCircle } from "lucide-react";
import { useTranslation } from "react-i18next";

import { useLayout } from "@/components/providers";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { authProtectedPaths } from "@/constants/paths";

import { PropertyBasicInfo } from "./components/PropertyBasicInfo";
import { PropertyImages } from "./components/PropertyImages";
import { PropertyOwnerInfo } from "./components/PropertyOwnerInfo";
import { PropertyStatsCards } from "./components/PropertyStatsCards";
import { usePropertyDetails } from "./usePropertyDetails";

export function PropertyDetailsComponent({ propertyId }: { propertyId: string }) {
  const { t } = useTranslation();
  const router = useRouter();
  const { setFooter } = useLayout();
  const {
    property,
    units,
    stats,
    propertyLoading,
    propertyError,
    showDeleteDialog,
    deletePropertyMutation,
    getPropertyTypeLabels,
    getStatusBadgeConfig,
    getUnitStatusBadgeConfig,
    getUnitTypeLabel,
    handleEdit,
    handleDelete,
    handleAddUnit,
    handleDeleteDialogChange,
  } = usePropertyDetails({ propertyId });

  // Set footer using useLayoutEffect to avoid infinite loops
  useLayoutEffect(() => {
    const footerComponent = (
      <div className="flex w-full items-center justify-end">
        <div className="flex items-center gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push(authProtectedPaths.PROPERTIES as any)}
            className="h-9 px-4 py-2">
            {t("common.cancel")}
          </Button>
          <Button
            type="submit"
            form="property-form"
            className="h-9 px-4 py-2"
            onClick={() =>
              router.push(`${authProtectedPaths.PROPERTIES}/${propertyId}/edit` as any)
            }>
            {t("common.update")}
          </Button>
        </div>
      </div>
    );

    setFooter(footerComponent);

    return () => {
      setFooter(null);
    };
  }, [setFooter, router, t, propertyId]);

  // Loading state
  if (propertyLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <div className="mx-auto size-8 animate-spin rounded-full border-b-2 border-primary"></div>
          <p className="mt-2 text-sm text-muted-foreground">{t("common.loading")}</p>
        </div>
      </div>
    );
  }

  // Error state
  if (propertyError || !property) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <AlertCircle className="mx-auto mb-4 size-12 text-destructive" />
          <p className="text-sm text-destructive">{t("pages.properties.loadError")}</p>
          <Button variant="outline" className="mt-4">
            {t("common.goBack")}
          </Button>
        </div>
      </div>
    );
  }

  const propertyTypeLabels = getPropertyTypeLabels();
  const statusBadgeConfig = getStatusBadgeConfig();

  const getStatusBadge = (status: string) => {
    const variants = statusBadgeConfig.variants;
    const labels = statusBadgeConfig.labels;

    return <Badge variant={variants[status] || "secondary"}>{labels[status] || status}</Badge>;
  };

  const getPropertyTypeLabel = (type: string) => {
    return propertyTypeLabels[type] || type;
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
        {/* Main Content Area - Left Side */}
        <div className="space-y-4 lg:col-span-2">
          {/* Property Images Section */}
          <Card className="space-y-4 border-0 p-4 shadow-sm">
            <div className="flex flex-col gap-1">
              <h1 className="text-base font-semibold text-foreground">{property.name}</h1>
              <span>{property.address.address1}</span>
            </div>

            <PropertyImages property={property} />
            {/* Property Details Section */}
            <PropertyBasicInfo
              property={property}
              totalUnits={stats.totalUnits}
              getPropertyTypeLabel={getPropertyTypeLabel}
            />
          </Card>

          {/* Units Table Section */}
          <Card className="border-0 shadow-sm">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Units</CardTitle>
                <Button onClick={handleAddUnit} className="gap-2">
                  Add unit
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="border-b bg-muted/50">
                      <tr>
                        <th className="p-3 text-left text-sm font-medium">
                          <input type="checkbox" className="rounded border-gray-300" />
                        </th>
                        <th className="p-3 text-left text-sm font-medium">Unit Info</th>
                        <th className="p-3 text-left text-sm font-medium">Unit Type</th>
                        <th className="p-3 text-left text-sm font-medium">Size (sq ft)</th>
                        <th className="p-3 text-left text-sm font-medium">Rent (đ)</th>
                        <th className="p-3 text-left text-sm font-medium">Status</th>
                        <th className="p-3 text-left text-sm font-medium">Floor</th>
                        <th className="p-3 text-left text-sm font-medium">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {units.length > 0 ? (
                        units.slice(0, 5).map((unit) => {
                          const statusConfig = getUnitStatusBadgeConfig(unit.status);
                          return (
                            <tr key={unit.id} className="border-b hover:bg-muted/30">
                              <td className="p-3">
                                <input type="checkbox" className="rounded border-gray-300" />
                              </td>
                              <td className="p-3 font-medium">{unit.unit_number}</td>
                              <td className="p-3 text-muted-foreground">
                                {getUnitTypeLabel(unit.unit_type)}
                              </td>
                              <td className="p-3 text-muted-foreground">
                                {unit.square_footage?.toLocaleString() || "N/A"}
                              </td>
                              <td className="p-3 text-muted-foreground">
                                {unit.rent_amount?.toLocaleString() || "N/A"}
                              </td>
                              <td className="p-3">
                                <Badge
                                  variant={statusConfig.variant}
                                  className={statusConfig.className}>
                                  {unit.status}
                                </Badge>
                              </td>
                              <td className="p-3 text-muted-foreground">{unit.floor || "N/A"}</td>
                              <td className="p-3">
                                <div className="flex items-center gap-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="size-8 p-0 hover:bg-primary/10">
                                    <span className="text-lg">⋯</span>
                                  </Button>
                                </div>
                              </td>
                            </tr>
                          );
                        })
                      ) : (
                        <tr>
                          <td colSpan={8} className="p-8 text-center text-muted-foreground">
                            <p>No units found</p>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Sidebar - Statistics and Owner Info */}
        <div className="space-y-4">
          {/* Summary Statistics Cards */}
          <PropertyStatsCards stats={stats} />
          {/* Owner Information */}
          <PropertyOwnerInfo property={property} />
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={handleDeleteDialogChange}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("pages.properties.deleteProperty")}</DialogTitle>
            <DialogDescription>
              {t("pages.properties.deleteConfirmation", { name: property.name })}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => handleDeleteDialogChange(false)}>
              {t("common.cancel")}
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={deletePropertyMutation.isPending}>
              {deletePropertyMutation.isPending ? t("common.deleting") : t("common.delete")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
