"use client";

import { Category } from "@/lib/apis/types/category";

import { CategoryItem } from "./category-item";

interface CategoryNode {
  category: Category;
  children: CategoryNode[];
  level: number;
}

interface CategoryTreeItemProps {
  node: CategoryNode;
  onEdit: (category: Category) => void;
  onDelete: (id: string) => void;
  isEditing: Category | null;
  onCancelEdit: () => void;
}

export function CategoryTreeItem({
  node,
  onEdit,
  onDelete,
  isEditing,
  onCancelEdit,
}: CategoryTreeItemProps) {
  return (
    <div className="space-y-3">
      {/* Parent category */}
      <CategoryItem
        category={node.category}
        onEdit={onEdit}
        onDelete={onDelete}
        isEditing={isEditing?.id === node.category.id}
        onCancelEdit={onCancelEdit}
        level={node.level}
      />

      {/* Child categories in nested container */}
      {node.children.length > 0 && (
        <div className="ml-6">
          <div className="rounded-lg border border-gray-200 bg-gray-50 p-4">
            <div className="space-y-3">
              {node.children.map((childNode) => (
                <CategoryTreeItem
                  key={childNode.category.id}
                  node={childNode}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  isEditing={isEditing}
                  onCancelEdit={onCancelEdit}
                />
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
