name: 🚀 Vercel Deployment

on:
  push:
    branches:
      - dev
  workflow_dispatch:

env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}

jobs:
  deploy:
    runs-on:
      - self-hosted
      - onex-erp
    strategy:
      matrix:
        project: ["onexbots.com", "onexsync.com", "optiwarehouse.com"]
    environment: ${{ github.ref == 'refs/heads/main' && 'Production' || 'Development' }}

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔑 Set ENV and Secrets
        id: set_env
        run: |
          echo "NEXT_PUBLIC_PRODUCT=${{ matrix.project }}" >> $GITHUB_ENV

          case "${{ matrix.project }}" in
            "optiwarehouse.com")
              echo "VERCEL_PROJECT_ID=${{ secrets.VERCEL_PROJECT_ID }}" >> $GITHUB_ENV
              if [[ "${{ github.ref_name }}" == "main" ]]; then
                echo "NEXT_PUBLIC_API_URL=${{ vars.OPTIWAREHOUSE_API_URL_PROD }}" >> $GITHUB_ENV
                echo "NEXT_PUBLIC_BASE_URL=https://app.optiwarehouse.com" >> $GITHUB_ENV
              else
                echo "NEXT_PUBLIC_API_URL=${{ vars.OPTIWAREHOUSE_API_URL_DEV }}" >> $GITHUB_ENV
                echo "NEXT_PUBLIC_BASE_URL=https://dev.optiwarehouse.com" >> $GITHUB_ENV
              fi
              ;;
            "onexbots.com")
              echo "VERCEL_PROJECT_ID=${{ secrets.VERCEL_PROJECT_ID_ONXBOTS }}" >> $GITHUB_ENV
              if [[ "${{ github.ref_name }}" == "main" ]]; then
                echo "NEXT_PUBLIC_API_URL=${{ vars.ONEXBOTS_API_URL_PROD }}" >> $GITHUB_ENV
                echo "NEXT_PUBLIC_BASE_URL=https://app.onexbots.com" >> $GITHUB_ENV
              else
                echo "NEXT_PUBLIC_API_URL=${{ vars.ONEXBOTS_API_URL_DEV }}" >> $GITHUB_ENV
                echo "NEXT_PUBLIC_BASE_URL=https://dev.onexbots.com" >> $GITHUB_ENV
              fi
              ;;
            "onexsync.com")
              echo "VERCEL_PROJECT_ID=${{ secrets.VERCEL_PROJECT_ID_ONEXSYNC }}" >> $GITHUB_ENV
              if [[ "${{ github.ref_name }}" == "main" ]]; then
                echo "NEXT_PUBLIC_API_URL=${{ vars.ONEXSYNC_API_URL_PROD }}" >> $GITHUB_ENV
                echo "NEXT_PUBLIC_BASE_URL=https://app.onexsync.com" >> $GITHUB_ENV
              else
                echo "NEXT_PUBLIC_API_URL=${{ vars.ONEXSYNC_API_URL_DEV }}" >> $GITHUB_ENV
                echo "NEXT_PUBLIC_BASE_URL=https://dev.onexsync.com" >> $GITHUB_ENV
              fi
              ;;
          esac

          if [[ "${{ github.ref_name }}" == "main" ]]; then
            echo "NEXT_PUBLIC_API_URL_ONEXBOTS=${{ vars.ONEXBOTS_AGENT_API_URL_PROD }}" >> $GITHUB_ENV
            echo "NEXT_PUBLIC_WS_URL=${{ vars.WS_URL_PROD }}" >> $GITHUB_ENV
          else
            echo "NEXT_PUBLIC_API_URL_ONEXBOTS=${{ vars.ONEXBOTS_AGENT_API_URL_DEV }}" >> $GITHUB_ENV
            echo "NEXT_PUBLIC_WS_URL=${{ vars.WS_URL_DEV }}" >> $GITHUB_ENV
          fi

      - name: 🔑 Set ENV
        run: |
          echo "NEXT_PUBLIC_API_URL=${{ vars.ONEXSYNC_API_URL_PROD }}" >> $GITHUB_ENV
          echo "VERCEL_TOKEN=${{ secrets.VERCEL_TOKEN }}" >> $GITHUB_ENV
          echo "GITHUB_REF_NAME=${{ github.ref_name }}" >> $GITHUB_ENV
          echo "MATRIX=${{ matrix.project }}" >> $GITHUB_ENV

      - name: 🐳 Build & Deploy Vercel Project on Remote Server
        uses: appleboy/ssh-action@v0.1.7
        id: build_and_deploy
        with:
          host: ${{ secrets.HOST_BUILD }}
          username: ${{ secrets.USERNAME_BUILD }}
          password: ${{ secrets.PASSWORD_BUILD }}
          port: 22
          timeout: 30m
          command_timeout: 30m
          envs: VERCEL_PROJECT_ID,VERCEL_ORG_ID,VERCEL_TOKEN,GITHUB_SHA,GITHUB_REF_NAME,MATRIX
          script: |
            ssh -i /home/<USER>/.ssh/vps_id_ed25519 -p 10022 onexapis@localhost << EOF
              source ~/.nvm/nvm.sh
              nvm use 22
              export NODE_OPTIONS="--max-old-space-size=8192"
              export VERCEL_PROJECT_ID="$VERCEL_PROJECT_ID"
              export VERCEL_ORG_ID="$VERCEL_ORG_ID"
              export VERCEL_TOKEN="$VERCEL_TOKEN"
              export GITHUB_SHA="$GITHUB_SHA"
              export GITHUB_REF_NAME="$GITHUB_REF_NAME"
              
              set -e
              echo "==> Deploying branch \$GITHUB_REF_NAME"
              cd project/onex-erp/
              
              git fetch origin
              git checkout \$GITHUB_REF_NAME
              git reset --hard origin/\$GITHUB_REF_NAME

              mkdir -p .vercel
              echo "{\"projectId\":\"\$VERCEL_PROJECT_ID\",\"orgId\":\"\$VERCEL_ORG_ID\"}" > .vercel/project.json

              echo "==> Step 2: Pull Vercel Env"
              if [[ "\$GITHUB_REF_NAME" == "main" ]]; then
                ENVIRONMENT="production"
                DEPLOY_FLAG="--prod"
                BUILD_FLAG="--prod"
              else
                ENVIRONMENT="preview"
                DEPLOY_FLAG=""
                BUILD_FLAG=""
              fi
              export VERCEL_ENV=\$ENVIRONMENT

              vercel pull --yes --environment=\$ENVIRONMENT --token=\$VERCEL_TOKEN

              echo "==> Step 3: Build thủ công bằng Next.js"
              rm -rf .next
              yarn install --frozen-lockfile

              NODE_OPTIONS="--max-old-space-size=12288" vercel build \$BUILD_FLAG --token=\$VERCEL_TOKEN

              echo "==> Step 4: Deploy với vercel deploy (prebuilt)"
              RAW_OUTPUT=\$(vercel deploy --prebuilt --archive=tgz \$DEPLOY_FLAG --token=\$VERCEL_TOKEN)

              echo "==> Raw deploy output:"
              echo "\$RAW_OUTPUT"
              echo "DEPLOYMENT_URL=\$RAW_OUTPUT" > /tmp/deployment_url.txt
            EOF

      - name: 📢 Send Success Notification
        if: success()
        run: |
          COMMIT_MSG="${{ github.event.head_commit.message }}"
          JIRA_LINK="${{ vars.JIRA_LINK }}"

          # Format the title (first line of commit message)
          TITLE=$(echo "$COMMIT_MSG" | head -n1)
          FORMATTED_TITLE=$(echo "$TITLE" | sed -E 's#(OWS-[0-9]+)#<'"$JIRA_LINK"'\1|\1>#g')

          # Extract and format the body (remaining lines after the first line)
          BODY=$(echo "$COMMIT_MSG" | tail -n +2 | sed '/^$/d')
          if [ ! -z "$BODY" ]; then
            FORMATTED_BODY=$(echo "$BODY" | sed -E 's#(OWS-[0-9]+)#<'"$JIRA_LINK"'\1|\1>#g' | sed 's/^/* /')
          else
            FORMATTED_BODY="No additional details"
          fi

          # Convert comma-separated user IDs to Slack mentions
          MENTIONS=$(echo "${{ vars.SLACK_TEST_USER_IDS }}" | tr ',' '\n' | sed 's/^[[:space:]]*//;s/[[:space:]]*$//' | sed 's/.*/<@&>/' | tr -d '\n')

          curl -X POST -H 'Content-type: application/json' --data '{
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "✅ Deployment Successful!",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Environment:*\n${{ github.ref == 'refs/heads/main' && 'Production' || 'Development' }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Branch:*\n`${{ github.ref_name }}`"
                  }
                ]
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Deployment URL:*\n<${{ env.NEXT_PUBLIC_BASE_URL }}|View Deployment> 🔗"
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Summary:*\n'"$FORMATTED_TITLE"'"
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Details:*\n'"$FORMATTED_BODY"'"
                }
              },
              {
                "type": "context",
                "elements": [
                  {
                    "type": "mrkdwn",
                    "text": "cc: '"$MENTIONS"'"
                  }
                ]
              },
              {
                "type": "divider"
              }
            ]
          }' ${{ vars.DEPLOY_WEBHOOK_URL }}

      - name: 📢 Send Failure Notification
        if: failure()
        run: |
          COMMIT_MSG="${{ github.event.head_commit.message }}"
          JIRA_LINK="${{ vars.JIRA_LINK }}"

          # Format the title (first line of commit message)
          TITLE=$(echo "$COMMIT_MSG" | head -n1)
          FORMATTED_TITLE=$(echo "$TITLE" | sed -E 's#(OWS-[0-9]+)#<'"$JIRA_LINK"'\1|\1>#g')

          # Extract and format the body (remaining lines after the first line)
          BODY=$(echo "$COMMIT_MSG" | tail -n +2 | sed '/^$/d')
          if [ ! -z "$BODY" ]; then
            FORMATTED_BODY=$(echo "$BODY" | sed -E 's#(OWS-[0-9]+)#<'"$JIRA_LINK"'\1|\1>#g' | sed 's/^/* /')
          else
            FORMATTED_BODY="No additional details"
          fi

          # Convert comma-separated user IDs to Slack mentions
          MENTIONS=$(echo "${{ vars.SLACK_DEV_USER_IDS }}" | tr ',' '\n' | sed 's/^[[:space:]]*//;s/[[:space:]]*$//' | sed 's/.*/<@&>/' | tr -d '\n')

          curl -X POST -H 'Content-type: application/json' --data '{
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "❌ Deployment Failed!",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Environment:*\n${{ github.ref == 'refs/heads/main' && 'Production' || 'Development' }}"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Branch:*\n`${{ github.ref_name }}`"
                  }
                ]
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Summary:*\n'"$FORMATTED_TITLE"'"
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Details:*\n'"$FORMATTED_BODY"'"
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "*Workflow Details:*\n<${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Workflow Run> 🔍"
                }
              },
              {
                "type": "context",
                "elements": [
                  {
                    "type": "mrkdwn",
                    "text": "🚨 '"$MENTIONS"' Please check!"
                  }
                ]
              },
              {
                "type": "divider"
              }
            ]
          }' ${{ vars.REVIEW_WEBHOOK_URL }}
