export interface IGetLayoutsParams {
  page?: number;
  limit?: number;
  query?: string;
  property_id?: string;
  status?: string;
  floor_number?: number;
  [key: string]: unknown;
}

export const layoutKeys = {
  all: () => ["layout"] as const,
  lists: () => [...layoutKeys.all(), "list"] as const,
  list: (params: IGetLayoutsParams) => [...layoutKeys.lists(), params] as const,
  details: () => [...layoutKeys.all(), "detail"] as const,
  detail: (id: string) => [...layoutKeys.details(), id] as const,
  statistics: () => [...layoutKeys.all(), "statistics"] as const,
  byProperty: (propertyId: string) => [...layoutKeys.all(), "property", propertyId] as const,
  byStatus: (status: string) => [...layoutKeys.all(), "status", status] as const,
  byFloor: (floorNumber: number) => [...layoutKeys.all(), "floor", floorNumber] as const,
};

export const QUERY_KEYS = {
  LAYOUTS: ["layouts"] as const,
  LAYOUT_STATISTICS: ["layoutStatistics"] as const,
} as const;
