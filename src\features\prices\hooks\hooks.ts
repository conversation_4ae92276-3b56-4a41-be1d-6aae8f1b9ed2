import { useInfiniteQuery } from "@tanstack/react-query";

import { priceGroupApi } from "@/lib/apis/product";

import { QUERY_KEYS } from "./keys";

const PAGE_SIZE = 20;

export const usePriceGroups = (query?: { limit?: number }) => {
  const limit = query?.limit ?? PAGE_SIZE;

  const { data, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage } = useInfiniteQuery({
    queryKey: QUERY_KEYS.PRICE_GROUPS,
    queryFn: ({ pageParam = 0 }) => priceGroupApi.list({ page: pageParam as number, limit }),
    getNextPageParam: (lastPage) => {
      const totalPages = Math.ceil(Number(lastPage.total) / Number(lastPage.limit));
      if (Number(lastPage.page) < totalPages) {
        return Number(lastPage.page) + 1;
      }
      return undefined;
    },
    initialPageParam: 0,
  });

  const priceGroups = data?.pages.flatMap((page) => page.items) ?? [];

  return {
    priceGroups,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  };
};
