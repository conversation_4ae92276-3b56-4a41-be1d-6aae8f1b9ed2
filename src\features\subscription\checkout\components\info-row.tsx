"use client";

import { ReactNode } from "react";

interface InfoRowProps {
  label: string;
  value: ReactNode;
  labelClassName?: string;
  valueClassName?: string;
}

export const InfoRow: React.FC<InfoRowProps> = ({
  label,
  value,
  labelClassName = "text-sm text-muted-foreground",
  valueClassName = "text-base text-muted-foreground",
}) => {
  return (
    <div className="flex items-center justify-between">
      <span className={labelClassName}>{label}</span>
      <span className={valueClassName}>{value}</span>
    </div>
  );
};
