import { ChevronDown, ChevronRight } from "lucide-react";
import { useTranslation } from "react-i18next";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { PermissionGroup } from "@/lib/apis/permission";

import { ModuleContent } from "./module-content";

interface GroupContentProps {
  group: PermissionGroup;
  expandedModules: Set<string>;
  onToggleModuleExpansion: (moduleKey: string) => void;
  onToggleModulePermissions: (groupId: string, moduleId: string) => void;
  onToggleGroup: (groupId: string, moduleId: string, typeName: "CRUD" | "ACTION") => void;
  onTogglePermission: (groupId: string, moduleId: string, permissionId: string) => void;
}

export function GroupContent({
  group,
  expandedModules,
  onToggleModuleExpansion,
  onToggleModulePermissions,
  onToggleGroup,
  onTogglePermission,
}: GroupContentProps) {
  const { t } = useTranslation();
  return (
    <div className="space-y-4">
      {group.modules.map((module) => {
        const moduleKey = `${group.id}_${module.id}`;
        const isExpanded = expandedModules.has(moduleKey);

        return (
          <Card key={module.id} className="overflow-hidden p-0 shadow-none">
            <CardHeader className="bg-accent">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="size-6 p-0"
                    onClick={() => onToggleModuleExpansion(moduleKey)}>
                    {isExpanded ? (
                      <ChevronDown className="size-4" />
                    ) : (
                      <ChevronRight className="size-4" />
                    )}
                  </Button>
                  <span className="font-medium">
                    {t(`pages.roleManagement.modules.${module.name.toLowerCase()}`)}
                  </span>
                </div>
                <div className="flex items-center gap-4">
                  <div className="flex w-[88px] items-center justify-center gap-2">
                    <Checkbox
                      checked={module.permissions.every((p) => p.checked)}
                      onCheckedChange={() => onToggleModulePermissions(group.id, module.id)}
                    />
                  </div>
                </div>
              </div>
            </CardHeader>

            {isExpanded && (
              <CardContent className="p-0">
                <div className="">
                  {/* CRUD Permissions */}
                  <ModuleContent
                    module={module}
                    groupId={group.id}
                    isExpanded={true}
                    onToggleExpansion={() => {}} // Not needed here
                    onToggleModulePermissions={() => onToggleModulePermissions(group.id, module.id)}
                    onToggleGroup={onToggleGroup}
                    onTogglePermission={onTogglePermission}
                  />
                </div>
              </CardContent>
            )}
          </Card>
        );
      })}
    </div>
  );
}
