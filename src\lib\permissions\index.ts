import { ACCOUNT_PERMISSIONS, AccountPermissionKey, AccountPermissionValue } from "./accounts";
import { CRM_PERMISSIONS, CRMPermissionKey, CRMPermissionValue } from "./crm";
import {
  DEPARTMENT_PERMISSIONS,
  DepartmentPermissionKey,
  DepartmentPermissionValue,
} from "./departments";
import { ORDER_PERMISSIONS, OrderPermissionKey, OrderPermissionValue } from "./orders";
import { PRODUCT_PERMISSIONS, ProductPermissionKey, ProductPermissionValue } from "./products";
import { ROLE_PERMISSIONS, RolePermissionKey, RolePermissionValue } from "./roles";
import { SETTINGS_PERMISSIONS, SettingsPermissionKey, SettingsPermissionValue } from "./settings";
import { STAFF_PERMISSIONS, StaffPermissionKey, StaffPermissionValue } from "./staff";

// Combine all permission modules into a single route permission map
export const ROUTE_PERMISSION_MAP = {
  ...STAFF_PERMISSIONS,
  ...PRODUCT_PERMISSIONS,
  ...CRM_PERMISSIONS,
  ...ORDER_PERMISSIONS,
  ...SETTINGS_PERMISSIONS,
  ...ACCOUNT_PERMISSIONS,
  ...DEPARTMENT_PERMISSIONS,
  ...ROLE_PERMISSIONS,
} as const;

// Export individual permission modules for specific use cases
export {
  STAFF_PERMISSIONS,
  PRODUCT_PERMISSIONS,
  CRM_PERMISSIONS,
  ORDER_PERMISSIONS,
  SETTINGS_PERMISSIONS,
  ACCOUNT_PERMISSIONS,
  DEPARTMENT_PERMISSIONS,
  ROLE_PERMISSIONS,
};

// Export types
export type {
  StaffPermissionKey,
  StaffPermissionValue,
  ProductPermissionKey,
  ProductPermissionValue,
  CRMPermissionKey,
  CRMPermissionValue,
  OrderPermissionKey,
  OrderPermissionValue,
  SettingsPermissionKey,
  SettingsPermissionValue,
  AccountPermissionKey,
  AccountPermissionValue,
  DepartmentPermissionKey,
  DepartmentPermissionValue,
  RolePermissionKey,
  RolePermissionValue,
};
