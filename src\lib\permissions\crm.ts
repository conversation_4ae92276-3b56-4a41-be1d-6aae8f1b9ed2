import { authProtectedPaths } from "@/constants/paths";

export const CRM_PERMISSIONS = {
  // Customer Management
  [authProtectedPaths.CUSTOMERS]: ["LIST_CUSTOMER", "LIST_CUSTOMER_IN_CHARGE"],
  [authProtectedPaths.CUSTOMERS_NEW]: ["CREATE_CUSTOMER"],
  [authProtectedPaths.CUSTOMERS_ID]: ["GET_CUSTOMER"],
  [`${authProtectedPaths.CUSTOMERS}/:id/edit`]: ["UPDATE_CUSTOMER"],
  [`${authProtectedPaths.CUSTOMERS}/:id/delete`]: ["DELETE_CUSTOMER"],
  [`${authProtectedPaths.CUSTOMERS}/:id/phone`]: ["SHOW_CUSTOMER_PHONE"],
  [`${authProtectedPaths.CUSTOMERS}/:id/group`]: ["SHOW_CUSTOMER_GROUP"],
  [`${authProtectedPaths.CUSTOMERS}/export`]: ["CUSTOMER_LIST_EXPORT_FILE"],

  // Opportunity Management
  [`${authProtectedPaths.OPPORTUNITIES_ID}`]: ["GET_OPPORTUNITY"],
  [`${authProtectedPaths.OPPORTUNITIES_ID}/edit`]: ["UPDATE_OPPORTUNITY"],
  [`${authProtectedPaths.OPPORTUNITIES_ID}/delete`]: ["DELETE_OPPORTUNITY"],
  [`${authProtectedPaths.OPPORTUNITIES_ID}/assign`]: ["ASSIGN_STAFF_TO_OPPORTUNITY"],
  [`${authProtectedPaths.OPPORTUNITIES_ID}/close-date`]: [
    "UPDATE_OPPORTUNITY_EXPECTED_CLOSING_DATE",
  ],
  [`${authProtectedPaths.OPPORTUNITIES_ID}/priority`]: ["UPDATE_OPPORTUNITY_PRIORITY"],
  [`${authProtectedPaths.OPPORTUNITIES_ID}/note`]: ["UPDATE_OPPORTUNITY_NOTE"],
  [`${authProtectedPaths.OPPORTUNITIES_ID}/revenue`]: ["UPDATE_OPPORTUNITY_EXPECTED_REVENUE"],
  [`${authProtectedPaths.OPPORTUNITIES_ID}/won-lost`]: ["MARK_OPPORTUNITY_WON_LOST"],
  [`${authProtectedPaths.OPPORTUNITIES_ID}/history`]: ["LIST_OPPORTUNITY_HISTORY"],
  [`${authProtectedPaths.OPPORTUNITIES_ID}/order-history`]: ["LIST_OPPORTUNITY_ORDER_HISTORY"],

  // Activity Management
  [authProtectedPaths.ACTIVITIES]: ["LIST_ACTIVITIES", "LIST_ACTIVITIES_IN_CHARGE"],
  [`${authProtectedPaths.ACTIVITIES}/new`]: ["CREATE_ACTIVITIES"],
  [`${authProtectedPaths.ACTIVITIES}/:id`]: ["GET_ACTIVITY"],
  [`${authProtectedPaths.ACTIVITIES}/:id/edit`]: ["UPDATE_ACTIVITIES"],
  [`${authProtectedPaths.ACTIVITIES}/:id/delete`]: ["DELETE_ACTIVITIES"],
  [`${authProtectedPaths.ACTIVITIES}/:id/assign`]: ["ASSIGN_STAFF_TO_ACTIVITIES"],

  // Pipeline/Stage Management
  [authProtectedPaths.PIPELINES]: ["GET_STAGE"],
  [`${authProtectedPaths.PIPELINES}/new`]: ["CREATE_STAGE"],
  [`${authProtectedPaths.PIPELINES}/:id`]: ["GET_STAGE"],
  [`${authProtectedPaths.PIPELINES}/:id/edit`]: ["UPDATE_STAGE"],
  [`${authProtectedPaths.PIPELINES}/:id/delete`]: ["DELETE_STAGE"],
  [`${authProtectedPaths.PIPELINES}/:id/revenue`]: ["GET_STAGE_TOTAL_EXPECTED_REVENUE"],
} as const;

export type CRMPermissionKey = keyof typeof CRM_PERMISSIONS;
export type CRMPermissionValue = (typeof CRM_PERMISSIONS)[CRMPermissionKey];
