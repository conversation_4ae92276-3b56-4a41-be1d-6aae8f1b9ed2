"use client";

import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";

import { AddCurrencySelector } from "@/features/settings/language-currency/components/AddCurrencySelector";
import { AddLanguageSelector } from "@/features/settings/language-currency/components/AddLanguageSelector";
import { CurrencySelector } from "@/features/settings/language-currency/components/CurrencySelector";
import { LanguageCurrencySkeleton } from "@/features/settings/language-currency/components/LanguageCurrencySkeleton";
import { LanguageSelector } from "@/features/settings/language-currency/components/LanguageSelector";
import { useLanguageCurrency } from "@/features/settings/language-currency/hooks/useLanguageCurrency";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";

export default function LanguageCurrencyPage() {
  const { t } = useTranslation();
  const router = useRouter();

  const {
    selectedLanguages,
    displayedLanguages,
    selectedCurrencies,
    displayedCurrencies,
    isLoadingLanguage,
    isLoadingCurrency,
    isVersionLoading,
    handleLanguageToggle,
    handleAddLanguage,
    handleRemoveLanguage,
    handleRemoveLanguageFromDisplay,
    handleUpdateLanguage,
    hasLanguageChanges,
    getAllLanguagesForSelection,
    getAvailableLanguagesForSelection,
    getAvailableLanguagesForAdd,
    handleCurrencyToggle,
    handleAddCurrency,
    handleRemoveCurrency,
    handleRemoveCurrencyFromDisplay,
    handleUpdateCurrency,
    hasCurrencyChanges,
    getAllCurrenciesForSelection,
    getAvailableCurrenciesForAdd,
  } = useLanguageCurrency();

  // Show loading state while version data is being fetched or mutations are running
  if (isVersionLoading) {
    return <LanguageCurrencySkeleton />;
  }

  return (
    <Card className="mx-4 mb-4 border-none">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">
          {t("pages.settings.languageCurrency")}
        </CardTitle>
      </CardHeader>

      {/* Content */}
      <div className="max-w-[736px] space-y-4 px-4 pb-4">
        {/* Language Section */}
        <Card className="rounded-lg border border-border">
          <CardHeader className="p-4">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium leading-[1.43] text-foreground">
                {t("pages.settings.language.language")}
              </CardTitle>
              <div className="relative">
                <AddLanguageSelector
                  selectedLanguages={selectedLanguages}
                  onAddLanguage={handleAddLanguage}
                  getAllLanguagesForSelection={getAvailableLanguagesForAdd}
                />
              </div>
            </div>
          </CardHeader>
          <CardContent className="px-6 pb-6 pt-0">
            <div className="space-y-4">
              <LanguageSelector
                selectedLanguages={selectedLanguages}
                displayedLanguages={displayedLanguages}
                onLanguageToggle={handleLanguageToggle}
                onRemoveLanguage={handleRemoveLanguageFromDisplay}
              />
            </div>
          </CardContent>
          <CardFooter className="p-0">
            <div className="flex w-full justify-end py-3 pr-4">
              <Button
                onClick={handleUpdateLanguage}
                disabled={!hasLanguageChanges() || isLoadingLanguage}
                loading={isLoadingLanguage}>
                {t("pages.settings.language.update")}
              </Button>
            </div>
          </CardFooter>
        </Card>

        {/* Currency Section */}
        <Card className="rounded-lg border border-border">
          <CardHeader className="p-4">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium leading-[1.43] text-foreground">
                {t("pages.settings.language.currency")}
              </CardTitle>
              <div className="relative">
                <AddCurrencySelector
                  selectedCurrencies={selectedCurrencies}
                  onAddCurrency={handleAddCurrency}
                  getAllCurrenciesForSelection={getAvailableCurrenciesForAdd}
                />
              </div>
            </div>
          </CardHeader>
          <CardContent className="px-6 pb-6 pt-0">
            <div className="space-y-4">
              <CurrencySelector
                selectedCurrencies={selectedCurrencies}
                displayedCurrencies={displayedCurrencies}
                onCurrencyToggle={handleCurrencyToggle}
                onRemoveCurrency={handleRemoveCurrencyFromDisplay}
              />
            </div>
          </CardContent>
          <CardFooter className="p-0">
            <div className="flex w-full justify-end py-3 pr-4">
              <Button
                onClick={handleUpdateCurrency}
                disabled={!hasCurrencyChanges() || isLoadingCurrency}
                loading={isLoadingCurrency}>
                {t("pages.settings.language.update")}
              </Button>
            </div>
          </CardFooter>
        </Card>
      </div>
    </Card>
  );
}
