import { z } from "zod";

export const changePasswordSchema = z
  .object({
    currentPassword: z.string().min(1, "auth.currentPasswordRequired"),
    newPassword: z.string().min(8, "auth.newPasswordRequired"),
    confirmPassword: z.string().min(1, "auth.confirmPasswordRequired"),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "auth.passwordsDontMatch",
    path: ["confirmPassword"],
  });
