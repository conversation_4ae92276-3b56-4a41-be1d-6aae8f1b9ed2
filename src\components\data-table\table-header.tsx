import { ColumnDef, flexRender } from "@tanstack/react-table";
import { Settings2 } from "lucide-react";

import { SortDirection } from "@/components/custom-table/hooks/use-data-table";
import { Button } from "@/components/ui/button";
import { TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { cn } from "@/lib/utils";

import SortIcon from "./sort-icon";

interface TableHeaderProps<TData> {
  table: any; // ReactTable<TData>
  columns: CustomColumn<TData>[];
  getInitialParams: Record<string, unknown>;
  handleSort: (column: CustomColumn<unknown>) => void;
  loading?: boolean;
  hasDataPermission: boolean;
  onViewDialogOpen: () => void;
}

export type CustomColumn<TData> = ColumnDef<TData> & {
  sorter?: boolean;
  isMainColumn?: boolean;
  sortKey?: string;
  hidden?: boolean;
  textCenter?: boolean;
};

export function TableHeaderComponent<TData>({
  table,
  columns,
  getInitialParams,
  handleSort,
  loading = false,
  hasDataPermission,
  onViewDialogOpen,
}: TableHeaderProps<TData>) {
  return (
    <TableHeader className="sticky top-0 z-30 bg-card">
      {table.getHeaderGroups().map((headerGroup: any) => (
        <TableRow key={headerGroup.id}>
          {headerGroup.headers.map((header: any) => {
            const column = header.column.columnDef as CustomColumn<TData>;
            const isSortable = column.sorter;
            return (
              <TableHead
                key={header.id}
                className={cn(
                  "relative",
                  header.id === "select"
                    ? "sticky left-0 z-30 w-[48px] bg-card transition-colors group-hover:bg-table-hover group-data-[state=selected]:bg-muted"
                    : header.id === "actions"
                      ? "sticky right-0 z-30 max-w-[60px] bg-card text-center transition-colors group-hover:bg-table-hover group-data-[state=selected]:bg-muted"
                      : (column as CustomColumn<TData>).isMainColumn
                        ? "max-w-[200px] truncate sm:max-w-[250px] md:max-w-[300px] lg:max-w-[400px] 2xl:max-w-[500px]"
                        : "",
                  isSortable ? "cursor-pointer" : ""
                )}
                onClick={() => isSortable && handleSort(column as CustomColumn<unknown>)}>
                <div
                  className={cn(
                    "flex items-center gap-2 text-nowrap",
                    column.textCenter ? "justify-center" : "justify-start"
                  )}>
                  {header.isPlaceholder || header.id === "actions" ? (
                    <div className="flex w-full justify-end">
                      <Button
                        disabled={loading || !hasDataPermission}
                        className="flex-none"
                        variant="outline"
                        size="sm"
                        leftIcon={<Settings2 size={16} />}
                        onClick={onViewDialogOpen}
                      />
                    </div>
                  ) : (
                    flexRender(header.column.columnDef.header, header.getContext())
                  )}

                  {isSortable && (
                    <SortIcon
                      directionSort={getInitialParams[`sort_${column.sortKey}`] as SortDirection}
                    />
                  )}
                </div>
              </TableHead>
            );
          })}
        </TableRow>
      ))}
    </TableHeader>
  );
}
