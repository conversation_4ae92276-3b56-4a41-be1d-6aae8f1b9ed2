"use client";

import { Minus } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Button } from "@/components/ui/button";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { PortalCombobox } from "@/components/ui/portal-combobox";

import type { RoleAssignment } from "../../validator/employee";

// Role Assignment Component
interface RoleAssignmentProps {
  index: number;
  assignment: RoleAssignment;
  watchedRoles: any[];
  branches: any[];
  roles: any[];
  isLoadingBranches: boolean;
  isLoadingRoles: boolean;
  onUpdate: (index: number, field: "branch" | "roles", value: string | string[]) => void;
  onRemove: (index: number) => void;
  canRemove: boolean;
}

export function RoleAssignment({
  index,
  assignment,
  watchedRoles,
  branches,
  roles,
  isLoadingBranches,
  isLoadingRoles,
  onUpdate,
  onRemove,
  canRemove,
}: RoleAssignmentProps) {
  const { t } = useTranslation();

  // Get available branches (exclude already selected ones)
  const getAvailableBranches = (currentIndex: number) => {
    const selectedBranches = watchedRoles
      .map((role, idx) => (idx !== currentIndex ? role.branch : ""))
      .filter(Boolean);

    return branches.map((branch) => ({
      ...branch,
      // Don't disable the "All" option, but disable other branches if "All" is selected elsewhere
      disabled:
        branch.id === "all"
          ? selectedBranches.includes("all") &&
            currentIndex !== watchedRoles.findIndex((r) => r.branch === "all")
          : selectedBranches.includes(branch.id || branch.value),
    }));
  };

  return (
    <div className="grid grid-cols-2 gap-4">
      <div className="space-y-2">
        <FormField
          name={`roles.${index}.branch`}
          render={({ field }) => (
            <FormItem>
              <FormLabel required>{t("pages.settings.branch", "Branch")}</FormLabel>
              <FormControl>
                <PortalCombobox
                  value={field.value || ""}
                  onValueChange={(value) => {
                    field.onChange(value);
                    onUpdate(index, "branch", value);
                  }}
                  items={getAvailableBranches(index)}
                  placeholder={t("pages.settings.selectBranch", "Select branch")}
                  variantButton="outline"
                  isLoading={isLoadingBranches}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="flex items-end gap-2">
        <div className="flex-1 space-y-2">
          <FormField
            name={`roles.${index}.roles`}
            render={({ field }) => (
              <FormItem>
                <FormLabel required>{t("pages.employeePermission.role", "Role")}</FormLabel>
                <FormControl>
                  <div className="flex items-center gap-2">
                    <PortalCombobox
                      value={field.value || []}
                      onValueChange={(value) => {
                        field.onChange(value);
                        onUpdate(index, "roles", value);
                      }}
                      items={roles}
                      placeholder={t("pages.employeePermission.selectRoles", "Select roles")}
                      variantButton="outline"
                      isLoading={isLoadingRoles}
                      multiple={true}
                      isShowDoneButton={false}
                    />
                    {canRemove && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => onRemove(index)}
                        className="size-8 text-destructive hover:text-destructive">
                        <Minus className="size-4" />
                      </Button>
                    )}
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
    </div>
  );
}
