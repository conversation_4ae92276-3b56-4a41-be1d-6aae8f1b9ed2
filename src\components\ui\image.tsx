"use client";

import { useState } from "react";
import Image, { StaticImageData } from "next/image";

import NoImage from "@/assets/images/NoImage.png";

interface CustomImageProps {
  src?: string | null | StaticImageData | React.ComponentType<any>;
  alt: string;
  fill?: boolean;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  defaultImage?: string;
  sizes?: string;
}

export const CustomImage = ({
  src,
  alt,
  fill = false,
  width,
  defaultImage = NoImage.src,
  height,
  sizes,
  className = "",
  priority = false,
}: CustomImageProps) => {
  const [error, setError] = useState(false);

  const handleError = () => {
    setError(true);
  };

  const isValidSrc = (source?: string | null | StaticImageData): boolean => {
    if (!source) return false;
    if (error) return false;

    // If it's StaticImageData, consider it valid
    if (typeof source === "object" && "src" in source) return true;

    // Check if it's a valid HTTP URL or base64 data URL
    return source.startsWith("http") || source.startsWith("data:") || source.endsWith(".svg");
  };

  // If src is a React component (SVG), render it directly
  if (typeof src === "function") {
    const SVGComponent = src;
    return (
      <SVGComponent
        className={className}
        width={width}
        height={height}
        aria-label={alt}
        role="img"
      />
    );
  }

  // Only allow string or StaticImageData for next/image
  if (typeof src !== "string" && !(typeof src === "object" && src !== null && "src" in src)) {
    return null;
  }

  // Ensure src is always a string
  const imageSrc: string = isValidSrc(src)
    ? typeof src === "object" && src !== null && "src" in src
      ? src.src
      : (src as string)
    : defaultImage;

  const imageProps = {
    src: imageSrc,
    className: `${className} transition-opacity duration-300`,
    onError: handleError,
    priority,
  };

  if (fill) {
    return <Image alt={alt} fill {...imageProps} />;
  }

  return <Image alt={alt} width={width} height={height} {...imageProps} sizes={sizes} />;
};
