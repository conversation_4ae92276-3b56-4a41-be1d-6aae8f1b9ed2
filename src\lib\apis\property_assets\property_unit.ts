import { ENDPOINTS } from "@/constants/endpoints";

import { privateApi } from "../../api_helper";
import { ResponseAxiosDetail, ResponseList } from "../types/common";
import type { CreateUnit, Unit, UpdateUnit } from "../types/property_assets/property_unit";

// Property Unit APIs
export const propertyUnitApi = {
  // Get all property units with optional filtering and pagination
  list: async (params?: Record<string, unknown>) => {
    return await privateApi.get<ResponseList<Unit>>(ENDPOINTS.PROPERTY_ASSETS.UNITS, {
      params,
    });
  },

  // Create a new property unit
  create: async (data: CreateUnit) => {
    return await privateApi.post<ResponseAxiosDetail<Unit>>(
      ENDPOINTS.PROPERTY_ASSETS.CREATE_UNIT,
      data
    );
  },

  // Update an existing property unit
  update: async (id: string, data: UpdateUnit) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.UPDATE_UNIT.replace(":id", id);
    return await privateApi.put<ResponseAxiosDetail<Unit>>(url, data);
  },

  // Delete a property unit
  delete: async (id: string) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.DELETE_UNIT.replace(":id", id);
    return await privateApi.delete<{ success: boolean; message?: string }>(url);
  },

  // Get property unit by ID
  getById: async (id: string) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.GET_UNIT.replace(":id", id);
    return await privateApi.get<ResponseAxiosDetail<Unit>>(url);
  },

  // Get units by property ID
  getByPropertyId: async (
    propertyId: string,
    params?: {
      page?: number;
      limit?: number;
      search?: string;
      asset_category_id?: string;
      type?: string;
      floor_number?: number;
      status?: string;
    }
  ) => {
    return await privateApi.get<ResponseList<Unit>>(
      ENDPOINTS.PROPERTY_ASSETS.PROPERTY_UNITS.replace(":propertyId", propertyId),
      {
        params,
      }
    );
  },

  // Get units by asset category ID
  getByAssetCategory: async (
    assetCategoryId: string,
    params?: {
      page?: number;
      limit?: number;
      search?: string;
      property_id?: string;
      type?: string;
      floor_number?: number;
      status?: string;
    }
  ) => {
    return await privateApi.get<ResponseList<Unit>>(ENDPOINTS.PROPERTY_ASSETS.UNITS, {
      params: {
        ...params,
        asset_category_id: assetCategoryId,
      },
    });
  },

  // Get units by type
  getByType: async (
    type: string,
    params?: {
      page?: number;
      limit?: number;
      search?: string;
      property_id?: string;
      asset_category_id?: string;
      floor_number?: number;
      status?: string;
    }
  ) => {
    return await privateApi.get<ResponseList<Unit>>(ENDPOINTS.PROPERTY_ASSETS.UNITS, {
      params: {
        ...params,
        type,
      },
    });
  },

  // Get units by status
  getByStatus: async (
    status: string,
    params?: {
      page?: number;
      limit?: number;
      search?: string;
      property_id?: string;
      asset_category_id?: string;
      type?: string;
      floor_number?: number;
    }
  ) => {
    return await privateApi.get<ResponseList<Unit>>(ENDPOINTS.PROPERTY_ASSETS.UNITS, {
      params: {
        ...params,
        status,
      },
    });
  },

  // Get units by floor number
  getByFloorNumber: async (
    floorNumber: number,
    params?: {
      page?: number;
      limit?: number;
      search?: string;
      property_id?: string;
      asset_category_id?: string;
      type?: string;
      status?: string;
    }
  ) => {
    return await privateApi.get<ResponseList<Unit>>(ENDPOINTS.PROPERTY_ASSETS.UNITS, {
      params: {
        ...params,
        floor_number: floorNumber,
      },
    });
  },
};
