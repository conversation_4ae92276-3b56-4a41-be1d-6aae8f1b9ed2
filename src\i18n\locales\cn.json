{"nav": {"blog": "博客", "operations": "运营", "overview": "概览", "patientManagement": "病人管理", "doctorManagement": "医生管理", "medicalSupplies": "医疗用品", "invoicesPayments": "发票与付款", "report": "报告", "administration": "管理", "product": "产品", "productList": "产品列表", "newProduct": "新增产品", "editProduct": "编辑产品", "variantsList": "变体列表", "brandList": "品牌列表", "categoryList": "分类列表", "order": "订单", "orderList": "订单列表", "orderDetail": "订单详情", "orderEdit": "编辑订单", "orderProcess": "处理订单", "returnOrderList": "退货订单列表", "packageList": "包裹列表", "integration": "集成", "fetchEvent": "获取事件", "detailFetchEvent": "事件详情", "syncRecords": "同步数据", "channel": "渠道", "supportedChannels": "渠道列表", "installChannel": "安装新渠道", "logistics": "物流", "shippingProviderList": "物流供应商列表", "purchaseOrder": "采购订单", "purchaseOrderList": "采购订单列表", "supplierList": "供应商列表", "customers": "客户", "customerDashboard": "仪表盘", "customerList": "客户列表", "customerDetail": "客户详情", "customerGroupList": "客户分组列表", "loyaltyProgram": "忠诚度计划", "rewardProgram": "奖励计划", "finance": "财务", "account": "账户", "paymentMethod": "支付方式", "transaction": "交易", "inventory": "库存", "locationList": "位置列表", "inventoryList": "库存列表", "stockAdjustmentList": "库存调整列表", "stockRelocateList": "库存转移列表", "promotion": "促销", "discountList": "折扣产品列表", "voucherList": "优惠券列表", "import": "导入", "importList": "导入列表", "recordList": "记录列表", "website": "网站", "blogCategory": "博客分类", "blogList": "博客列表", "notification": "通知", "notificationList": "通知列表", "loyaltyApp": "忠诚度应用", "pos": "销售POS", "terminalList": "终端列表", "shiftList": "班次列表", "posFnB": "餐饮POS", "settings": "设置", "dashboard": "仪表盘", "productReport": "产品报告", "productDetail": "产品详情", "orderManual": "新增订单", "productMapping": "产品映射", "productMappingDetail": "映射详情", "productMappingAttribute": "产品映射属性", "staff": "员工", "staffList": "员工列表", "department": "部门", "knowledge": "知识库", "task": "任务", "conversation": "对话", "interact": "互动", "editStaff": "编辑员工", "activities": "活动", "opportunities": "商机", "crm": "客户关系管理", "opportunityDetail": "商机详情", "pipelines": "销售流程", "subscription": "订阅", "checkout": "结账", "propertyAssets": "物业资产", "properties": "物业", "layoutManagement": "布局管理", "units": "单元", "contracts": "合同", "tenants": "租户", "financial": "财务", "maintenance": "维护", "reports": "报告", "propertyAssetsDashboard": "物业资产仪表盘", "propertyDetail": "物业详情", "newProperty": "新增物业", "editProperty": "编辑物业", "unitDetail": "单元详情", "newUnit": "新增单元", "editUnit": "编辑单元", "contractDetail": "合同详情", "newContract": "新增合同", "editContract": "编辑合同", "tenantDetail": "租户详情", "newTenant": "新增租户", "editTenant": "编辑租户", "payments": "付款", "paymentDetail": "付款详情", "newPayment": "新增付款", "editPayment": "编辑付款", "maintenanceDetail": "维护详情", "newMaintenance": "新增维护", "editMaintenance": "编辑维护", "assetCategories": "资产分类", "assetCategoryDetail": "资产分类详情", "newAssetCategory": "新增资产分类", "editAssetCategory": "编辑资产分类", "documentManagement": "文档管理", "documentDetail": "文档详情", "newDocument": "新增文档", "editDocument": "编辑文档", "propertyGallery": "物业画廊", "propertyGalleryDetail": "画廊详情", "newPropertyGallery": "新增画廊项", "editPropertyGallery": "编辑画廊项", "propertyValuation": "物业估值", "propertyValuationDetail": "估值详情", "newPropertyValuation": "新增估值", "editPropertyValuation": "编辑估值"}, "quota": {"storage": "存储容量", "products": "商品数量", "orders": "订单数量", "messages": "消息数量", "staffs": "员工数量", "knowledge": "知识库", "capacity": "容量", "knowledge_capacity": "知识库容量"}, "product": {"image": "商品图片", "title": "商品名称", "description": "描述", "price": "价格", "sku": "SKU编码", "brand": "品牌", "category": "类别", "inventory": "库存", "notMapped": "未关联"}, "productMapping": {"lastSynced": "最后同步", "errorLoading": "加载商品关联详情时出错", "manualRetry": "手动重试", "cancelledMessage": "商品关联已取消", "mappingStatus": "关联状态", "variant": "变体"}, "groups": {"crm": "客户关系管理", "operations": "运营", "virtual_staff": "虚拟员工", "product": "商品", "property_assets": "不动产资产"}, "branch": {"Ho Chi Minh": "胡志明市", "Ha Noi": "河内", "Da Nang": "岘港", "Hai Phong": "海防", "Can Tho": "芹苴", "Binh Duong": "平阳", "Binh Phuoc": "平福", "All": "全部", "title": "选择分支机构", "all": "所有分支机构", "addBranch": "添加新分支机构", "branch": "分支机构", "shortcuts": {"alt": "Alt", "plus": "+"}, "daily": "每日", "weekly": "每周", "monthly": "每月", "yearly": "每年", "annually": "每年", "refresh": "刷新"}, "profile": {"updateSuccess": "语言设置已成功更新", "updateError": "语言设置更新失败", "free": "免费", "profile": "个人资料", "settings": "设置", "darkMode": "深色模式", "on": "开启", "off": "关闭", "language": "语言", "english": "英语", "vietnamese": "越南语", "japanese": "日语", "selectLanguage": "选择语言", "searchLanguage": "搜索语言...", "noLanguagesFound": "未找到语言。", "current": "当前", "logout": "退出登录", "founder": "创始人", "usedSpace": "已用容量", "upgrade": "升级", "message": "消息", "documents": "文档", "staff": "员工", "storage": "存储"}, "storeInformation": {"storeInformation": "店铺信息", "basicInformation": "基本信息", "basicInformationDescription": "供客户联系您的信息。", "advancedInformation": "详细信息", "advancedInformationDescription": "配置 网站链接、价格、店铺地址等高级设置。", "storeName": "店铺名称", "storeNamePlaceholder": "例如：ABC商店", "phone": "电话号码", "phonePlaceholder": "0123456789", "email": "电子邮箱", "emailPlaceholder": "<EMAIL>", "businessSector": "行业", "selectBusinessSector": "选择...", "storeUrl": "店铺URL", "storeUrlPlaceholder": "例如：ABC商店", "defaultPriceGroup": "默认价格组", "selectPriceGroup": "选择...", "address": "地址", "addressPlaceholder": "输入店铺地址", "province": "省/市", "selectProvince": "选择...", "district": "区/县", "selectDistrict": "选择...", "ward": "坊/乡", "selectWard": "选择..."}, "auth": {"passwordResetSuccess": "密码重置成功", "changePasswordTitle": "修改密码", "changePasswordSubtitle": "输入当前密码和新密码", "changePasswordSuccess": "密码修改成功", "changePasswordError": "密码修改失败", "currentPasswordRequired": "当前密码是必填项", "currentPasswordPlaceholder": "输入当前密码", "currentPassword": "当前密码", "passwordsDontMatch": "两次密码不一致", "minPasswordLength": "密码长度必须至少8位", "brandSection": {"title": "14天免费试用"}, "incorrectUsernameOrPassword": "用户名或密码错误。", "userExist": "用户名已存在！", "userExistAndVerified": "该邮箱已注册并验证。请登录或使用其他邮箱。", "logoutSuccess": "退出成功", "logoutError": "退出失败", "gender": "性别", "genderPlaceholder": "选择性别", "male": "男", "female": "女", "other": "其他", "preferNotToSay": "不愿透露", "dob": "出生日期", "dobPlaceholder": "选择出生日期", "username": "用户名", "usernamePlaceholder": "输入用户名", "login": "登录", "register": "注册", "forgotPasswordDescription": "请输入邮箱地址以接收重置步骤！", "forgotPasswordTitle": "忘记密码？", "forgotPasswordSubtitle": "请输入邮箱以接收重置密码的步骤", "resetPassword": "重置密码", "resetPasswordTitle": "重置密码", "resetPasswordDescription": "请输入验证码和新密码", "resetPasswordSubtitle": "请输入验证码和新密码", "resetPasswordButton": "重置密码", "resetPasswordSuccess": "密码重置成功", "resetPasswordSuccessDescription": "您可以使用新密码登录", "resetPasswordError": "无法重置密码", "resetPasswordLoading": "正在重置...", "confirmPassword": "确认密码", "confirmPasswordPlaceholder": "确认密码", "backToLogin": "返回登录界面", "backToForgotPassword": "返回登录", "loginTitle": "登录", "loginSubtitle": "请输入用户名或邮箱地址登录", "email": "邮箱地址", "emailPlaceholder": "m@example", "emailPlaceholderSignUp": "请输入邮箱地址", "verifyEmail": "邮箱验证", "verifyEmailButton": "验证邮箱", "verifyEmailSuccess": "邮箱验证成功", "verifyEmailError": "邮箱验证失败", "verifyEmailLoading": "正在验证...", "verifyEmailCode": "请输入发送到邮箱的验证码", "verifyEmailCodePlaceholder": "输入验证码", "verifyEmailCodeButton": "验证验证码", "verifyEmailCodeSuccess": "验证码验证成功", "verifyEmailCodeError": "验证码验证失败", "verifyEmailCodeLoading": "正在验证验证码...", "newPassword": "新密码", "newPasswordPlaceholder": "输入新密码", "verificationCode": "验证码", "verificationCodePlaceholder": "输入验证码", "verificationCodeButton": "验证", "verificationCodeSuccess": "验证成功", "verificationCodeError": "验证失败", "verificationCodeDescription": "已向 {{username}} 发送验证码，请在下方输入。", "sendInstructions": "发送步骤", "sending": "发送中...", "resetting": "重置中...", "password": "密码", "passwordPlaceholder": "输入密码", "rememberMe": "记住登录信息", "loginButton": "登录", "loginWithGoogle": "使用Google登录", "loginWithGithub": "使用Github登录", "noAccount": "没有账号？", "signUp": "注册", "signUpTitle": "注册", "signUpSubtitle": "请注册以登录管理后台", "signUpButton": "注册", "signUpSuccess": "注册成功！请验证邮箱", "signUpError": "注册失败", "signUpLoading": "注册中...", "alreadyHaveAccount": "已有账号？", "sendNewCode": "重新发送", "resendCodeSuccess": "验证码已重新发送", "resendCodeError": "无法重新发送验证码", "usernameOrEmail": "用户名或邮箱", "usernameOrEmailPlaceholder": "输入用户名或邮箱", "forgot": "忘记密码", "or": "或", "loginSuccess": "登录成功", "loginError": "登录失败", "loginLoading": "登录中...", "usernameRequired": "请输入用户名", "emailRequired": "请输入邮箱地址", "passwordRequired": "请输入密码", "confirmPasswordRequired": "请确认密码", "invalidPassword": "密码长度必须至少8位", "forgotPasswordSuccess": "重置密码的步骤已发送到邮箱", "forgotPasswordError": "无法发送重置密码的步骤", "newPasswordRequired": "新密码是必填项", "pleaseChangePassword": "请修改您的密码", "passwordsDoNotMatch": "两次密码不一致", "passwordMustBeAtLeast8Characters": "密码长度必须至少8位", "codeRequired": "请输入验证码", "resendCodeCountdown": "{{seconds}}秒后可重新发送"}, "onboarding": {"step1": {"title": "您是从哪里了解到 OnexBots 的？", "options": {"facebook": "Facebook", "zalo": "<PERSON><PERSON>", "youtube": "YouTube", "instagram": "Instagram", "tiktok": "TikTok", "google": "Google", "linkedin": "LinkedIn", "referral": "推荐 / 口碑", "other": "其他"}, "otherPlaceholder": "X、Threads、Whatsapp 等"}, "step2": {"title": "您是否使用过 AI 聊天软件？", "options": {"never": "从未使用过", "tried": "使用过", "regularly": "经常使用"}}, "step3": {"title": "您从事哪个行业？", "options": {"ecommerce": "电子商务", "travel": "旅游", "real_estate": "房地产", "health": "医疗健康 / 美容", "education": "教育", "other": "其他"}, "otherPlaceholder": "IT、物流 等"}, "step3_part2": {"title": "您公司的员工人数是多少？", "options": {"just_me": "只有我一个人", "2-9": "2〜9人", "10-49": "10〜49人", "50-199": "50〜199人", "200-499": "200〜499人", "500+": "500人以上"}, "inputLabel": "您的网址（可选）", "inputPlaceholder": "www.example.com"}, "step4": {"title": "您的企业属于哪个领域？", "options": {"specialty_clinic": "综合医院", "aesthetic_clinic": "美容诊所 / 水疗", "cosmetic_surgery": "整形美容", "nutrition_clinic": "营养治疗 / 运动医学", "telemedicine": "远程医疗 / 医疗科技", "pharma": "医药 / 化妆品零售连锁", "other": "其他"}, "otherPlaceholder": "综合健康中心、生物科技初创公司 等"}, "step5": {"title": "您需要哪方面专家的支持？", "options": {"consulting": "咨询", "customer_care": "客户服务", "accounting": "会计", "marketing": "营销", "other": "其他"}, "otherPlaceholder": "法律咨询、IT 支持 等"}, "step6": {"title": "您使用 OnexBots 的目的是什么？", "options": {"feedback": "收集与分析反馈", "pressure": "缓解高峰期压力", "channels": "同时管理多个渠道", "quality": "提升服务质量", "responses": "自动快速回复", "monitor": "员工培训与监控", "other": "其他"}, "otherPlaceholder": "获客、客户教育 等"}, "buttons": {"back": "返回", "skip": "跳过", "continue": "继续", "done": "完成"}, "otherInput": {"pleaseSpecify": "请具体说明", "optional": "（可选）"}}, "common": {"submit": "提交", "dataCreatedBySystem": "根数据", "exceedTotalSize": "总文件大小不能超过 5MB", "tryingToAdd": "尝试添加", "description": "描述", "fileSizeMustNotExceed": "文件大小不能超过", "onlyImageFilesAllowed": "仅允许图片文件", "resetToDefault": "重置为默认", "settings": "设置", "viewOnWeb": "在网页上查看", "confirmCancel": "此操作无法撤销。确定要取消吗？", "selected": "已选择", "pickADateRange": "选择时间范围", "other": "其他", "escTo": "按 Esc 键以", "at": "于", "areYouAbsolutelySure": "你确定要执行吗？", "areYouAbsolutelySureDescription": "此操作无法撤销。确定要继续吗？", "canNotDeleteStage": "无法删除此列", "canNotDeleteStageDescription": "要删除，请先将所有案件从此列移出。", "selectCountry": "选择国家", "displayCustomizer": "显示自定义", "customizeTheDisplayOfContentColumnsAccordingToYourPreferences": "根据个人偏好自定义内容列显示。", "noDataAvailable": "暂无数据", "start": "开始", "back": "返回", "words": "单词", "confirm": "确认", "apply": "应用", "totalSize": "总大小", "aspectRatio": "比例", "formats": "格式", "recommendedSize": "推荐尺寸", "search": "搜索", "filter": "筛选", "reset": "重置", "setAsDefault": "设为默认", "saveFilters": "保存筛选条件", "sort": "排序", "view": "查看", "add": "添加", "update": "更新", "edit": "编辑", "delete": "删除", "cancel": "取消", "save": "保存", "saving": "正在保存...", "close": "关闭", "clear": "清除", "loading": "正在加载...", "loadingMore": "正在加载更多...", "deleting": "正在删除...", "toggleGrid": "切换网格视图", "snapToGrid": "对齐到网格", "alignHorizontally": "水平对齐", "alignVertically": "垂直对齐", "noData": "无数据", "success": "成功", "uploadImage": "拖放图片，或", "upload": "上传", "uploading": "正在上传...", "fileSizeError": "文件大小必须小于 5MB", "uploadError": "图片上传失败", "imageUploadError": "员工图片上传失败", "areYouSure": "确定要执行吗？", "leaveDesc": "未保存的更改将会丢失。", "deleteTaskConfirmation": "此操作无法撤销。任务将被永久删除。", "deleteProductConfirmation": "此操作无法撤销。商品将被永久删除。", "deleteListProductConfirmation": "此操作无法撤销。{{count}} 个商品将被永久删除。", "deleteOpportunityConfirmation": "此操作无法撤销。案件将被永久删除。", "deleteEmployeeConfirmation": "此操作无法撤销。{{count}} 个员工将被永久删除。", "install": "安装", "configure": "配置", "deleteSuccess": "商品已成功删除", "deleteError": "无法删除商品", "deleteSuccessDescription": "商品已成功删除", "actions": "操作", "bulkActions": "批量操作", "all": "全部", "sortBy": "排序依据", "select": "选择", "label": "标签", "issues": "问题", "status": {"available": "可用", "occupied": "使用中", "maintenance": "维护中", "inactive": "未激活", "active": "激活", "status": "状态", "healthy": "正常", "issues": "有问题", "issue": "问题", "complete": "完成", "inProgress": "进行中", "processing": "处理中", "error": "错误", "ready": "已准备好"}, "error": "发生错误", "saveChanges": "保存更改", "unsavedChanges": "未保存的更改", "unsavedChangesDescription": "存在未保存的更改。确定要关闭吗？", "discard": "放弃", "keepEditing": "继续编辑", "week": "周", "month": "月", "quarter": "季度", "year": "年", "units": "单位", "leaveWithoutSavingDescription": "存在未保存的更改。确定要退出吗？", "leaveWithoutSaving": "不保存退出", "leave": "退出", "restore": "恢复", "stay": "保留", "knowledgeUpdated": "知识已成功更新", "knowledgeDeleted": "知识已成功删除", "areYouSureDescription": "确定要删除此知识吗？", "staffUpdated": "员工信息已成功更新", "updateStaffError": "无法更新员工信息", "areYouSureConfirm": "确认", "areYouSureCancel": "取消", "updateAttribute": "更新属性", "time": {"month": "月", "timeAgo": {"seconds": "{{count}} 秒前", "seconds_plural": "{{count}} 秒前", "minutes": "{{count}} 分钟前", "minutes_plural": "{{count}} 分钟前", "hours": "{{count}} 小时前", "hours_plural": "{{count}} 小时前", "days": "{{count}} 天前", "days_plural": "{{count}} 天前", "months": "{{count}} 个月前", "months_plural": "{{count}} 个月前", "years": "{{count}} 年前", "years_plural": "{{count}} 年前", "invalidDate": "无效日期"}}, "empty": {"title": "这里什么都没有！", "description": "未找到匹配结果。"}, "create": "创建", "noFileSelected": "未选择文件", "uploadSuccess": "上传成功", "fileTooLarge": "文件大小超过最大限制 {{max}}MB", "lastUpdated": "最后更新", "name": "名称", "loadMore": "加载更多", "markAsDone": "标记为完成", "zoomIn": "放大", "zoomOut": "缩小", "fitToScreen": "适应屏幕", "backToOverview": "返回概览", "progress": "进度", "opacity": "不透明度", "visible": "可见", "properties": "属性", "duplicate": "复制", "quickActions": "快速操作", "imageLoadError": "无法加载图片", "uploadNew": "上传新文件", "viewMode": "查看模式", "editMode": "编辑模式", "total": "总计", "noPermission": "您没有执行此操作的权限", "noRoutePermission": "您没有访问此页面的权限", "noModuleAccess": "您没有访问此模块的权限"}, "tenants": {"title": "<PERSON><PERSON><PERSON><PERSON> thuê", "addTenant": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON><PERSON> thuê", "editTenant": "Chỉnh sửa người thuê", "tenant": "<PERSON><PERSON><PERSON><PERSON> thuê", "tenantDetail": "<PERSON> tiết ngư<PERSON>i thuê", "tenantDetails": "<PERSON> tiết ngư<PERSON>i thuê", "tenantId": "<PERSON>ã người thuê", "tenantName": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> thuê", "sections": {"personalInfo": "Thông tin cá nhân", "contactInfo": "<PERSON>h<PERSON>ng tin liên hệ", "emergencyContact": "<PERSON><PERSON><PERSON> h<PERSON> khẩn cấp", "leaseInfo": "Thông tin thuê", "documents": "<PERSON><PERSON><PERSON> l<PERSON>", "paymentInfo": "Thông tin thanh toán", "notes": "<PERSON><PERSON><PERSON>", "identification": "<PERSON><PERSON><PERSON><PERSON> tờ tùy thân", "employment": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON>"}, "fields": {"firstName": "<PERSON><PERSON><PERSON>", "lastName": "Họ", "fullName": "<PERSON><PERSON> tên", "email": "Email", "phone": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "address": "Địa chỉ", "dateOfBirth": "<PERSON><PERSON><PERSON>", "occupation": "<PERSON><PERSON><PERSON>", "emergencyContactName": "<PERSON><PERSON><PERSON> liên hệ khẩn cấp", "emergencyContactPhone": "SĐT li<PERSON>n hệ khẩn cấp", "relationship": "<PERSON><PERSON><PERSON> quan hệ", "leaseStart": "<PERSON><PERSON><PERSON> b<PERSON>t đầu thuê", "leaseEnd": "<PERSON><PERSON><PERSON> kết thúc thuê", "rentAmount": "<PERSON><PERSON><PERSON><PERSON> thuê", "securityDeposit": "Tiền đặt cọc", "unit": "Đơn vị", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "notes": "<PERSON><PERSON><PERSON>", "documents": "<PERSON><PERSON><PERSON> l<PERSON>", "identificationType": "<PERSON><PERSON><PERSON> gi<PERSON>y tờ", "identificationNumber": "Số gi<PERSON>y tờ", "employmentStatus": "<PERSON><PERSON><PERSON> trạng công việc", "employerName": "<PERSON><PERSON>n công ty", "monthlyIncome": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> hàng tháng"}, "placeholders": {"enterFirstName": "<PERSON><PERSON><PERSON><PERSON> tên", "enterLastName": "<PERSON><PERSON><PERSON><PERSON>", "enterEmail": "<PERSON><PERSON><PERSON><PERSON> địa chỉ email", "enterPhone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "enterOccupation": "<PERSON><PERSON><PERSON><PERSON> ngh<PERSON> nghiệp", "selectUnit": "<PERSON><PERSON>n đơn vị", "selectStatus": "<PERSON><PERSON><PERSON> trạng thái", "enterNotes": "<PERSON><PERSON><PERSON><PERSON>hi <PERSON>ú", "firstName": "<PERSON><PERSON><PERSON><PERSON> tên", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "email": "Enter email address", "phone": "Enter phone number", "identificationType": "Select identification type", "identificationNumber": "Enter identification number", "employmentStatus": "Select employment status", "employerName": "Enter employer name", "monthlyIncome": "Enter monthly income", "emergencyContactName": "Enter emergency contact name", "emergencyContactPhone": "Enter emergency contact phone"}, "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "pending": "Chờ xử lý", "terminated": "<PERSON><PERSON> chấm d<PERSON>"}, "actions": {"save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "edit": "Chỉnh sửa", "delete": "Xóa", "view": "<PERSON>em chi tiết"}, "messages": {"saveSuccess": "<PERSON><PERSON><PERSON> ng<PERSON>i thuê thành công", "saveError": "Lỗi khi lưu người thuê", "deleteSuccess": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i thuê thành công", "deleteError": "Lỗi khi xóa người thuê", "deleteConfirm": "Bạn có chắc chắn muốn xóa người thuê này?", "required": "Trư<PERSON>ng này là bắ<PERSON> buộc", "createSuccess": "Tenant created successfully", "updateSuccess": "Tenant updated successfully", "createError": "Failed to create tenant", "updateError": "Failed to update tenant"}, "contracts": {"title": "<PERSON><PERSON><PERSON>", "contract": "<PERSON><PERSON><PERSON>", "contractId": "<PERSON><PERSON> hợp đồng", "startDate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "endDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "monthlyRent": "<PERSON><PERSON><PERSON><PERSON> thuê hàng tháng", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "viewContract": "<PERSON><PERSON> đ<PERSON>"}, "quickStats": "<PERSON><PERSON><PERSON><PERSON> kê nhanh", "activeContracts": "<PERSON><PERSON><PERSON> đồng hoạt động", "documents": "<PERSON><PERSON><PERSON> l<PERSON>", "stats": {"totalContracts": "<PERSON><PERSON>ng số hợp đồng", "activeContracts": "<PERSON><PERSON><PERSON> đồng đang hoạt động", "joinDate": "<PERSON><PERSON><PERSON> gia nh<PERSON>p"}, "employmentStatus": {"employed": "<PERSON><PERSON> vi<PERSON><PERSON> làm", "unemployed": "<PERSON><PERSON><PERSON><PERSON>", "self_employed": "Tự kinh doanh", "student": "Sin<PERSON> viên", "retired": "Retired"}, "identificationTypes": {"passport": "<PERSON><PERSON> ch<PERSON>", "national_id": "CMND/CCCD", "driver_license": "Bằng lái xe"}, "errors": {"notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy người thuê", "notFoundDescription": "<PERSON>ười thuê mà bạn đang tìm không tồn tại hoặc đã bị xóa."}, "deleteConfirmation": {"title": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> thuê", "description": "Bạn có chắc chắn muốn xóa người thuê này? Hành động này không thể hoàn tác.", "hasActiveContracts": "<PERSON><PERSON><PERSON>i thuê này có hợp đồng đang hoạt động. Bạn có chắc chắn muốn xóa?"}, "createTenant": "<PERSON><PERSON><PERSON>", "createDescription": "<PERSON><PERSON><PERSON><PERSON> thông tin chi tiết để tạo người thuê mới", "editDescription": "Update the tenant information"}, "maintenance": {"form": {"requestId": "<PERSON><PERSON> yêu c<PERSON>u", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "priority": "<PERSON><PERSON> <PERSON> tiên", "category": "<PERSON><PERSON>", "reportedDate": "<PERSON><PERSON><PERSON> b<PERSON>o c<PERSON>o", "description": "<PERSON><PERSON>", "estimatedCost": "Chi phí <PERSON><PERSON> t<PERSON>h", "contractorName": "<PERSON><PERSON><PERSON> nhà thầu", "contractorPhone": "SĐT nhà thầu", "notes": "<PERSON><PERSON><PERSON>", "actualCost": "<PERSON> phí thực tế"}, "details": {"overview": "<PERSON><PERSON><PERSON> quan", "timeline": "<PERSON><PERSON><PERSON> thời gian", "daysSinceReported": "<PERSON><PERSON><PERSON> kể từ khi báo cáo", "cost": "Chi phí", "related": "<PERSON><PERSON><PERSON> quan", "contractor": "<PERSON><PERSON><PERSON> thầu"}, "status": {"in_progress": "<PERSON><PERSON> ti<PERSON>n hành", "pending": "Chờ xử lý", "completed": "<PERSON><PERSON><PERSON> th<PERSON>", "cancelled": "<PERSON><PERSON> hủy", "open": "Mở"}, "priority": {"low": "<PERSON><PERSON><PERSON><PERSON>", "medium": "<PERSON>rung bình", "high": "<PERSON>", "urgent": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>p"}, "category": {"plumbing": "<PERSON><PERSON> thống n<PERSON>", "electrical": "<PERSON><PERSON><PERSON><PERSON>", "hvac": "<PERSON><PERSON><PERSON><PERSON> hòa thông gió", "structural": "<PERSON><PERSON><PERSON> c<PERSON>", "appliance": "<PERSON><PERSON><PERSON><PERSON> bị", "cosmetic": "<PERSON><PERSON><PERSON><PERSON> mỹ", "cleaning": "<PERSON><PERSON>", "security": "An ninh", "general": "<PERSON>", "other": "K<PERSON><PERSON><PERSON>"}, "messages": {"deleteSuccess": "<PERSON><PERSON><PERSON> y<PERSON>u cầu bảo trì thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa yêu cầu bảo trì", "statusUpdateSuccess": "<PERSON><PERSON><PERSON> nhật trạng thái thành công", "statusUpdateError": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật trạng thái"}, "errors": {"notFound": "<PERSON><PERSON><PERSON><PERSON>", "notFoundDescription": "<PERSON><PERSON><PERSON> cầu bảo trì bạn đang tìm không tồn tại hoặc đã bị xóa."}, "dialog": {"deleteTitle": "<PERSON><PERSON><PERSON>", "deleteDescription": "Bạn có chắc chắn muốn xóa yêu cầu bảo trì này? Hành động này không thể hoàn tác."}}, "shapes": {"rectangle": "<PERSON><PERSON><PERSON> chữ nhật", "circle": "<PERSON><PERSON><PERSON> tròn", "polygon": "<PERSON><PERSON>"}, "pages": {"editor": {"placeholder": "按「/」键使用命令...", "characters": "字符", "words": "单词"}, "blogList": {"title": "博客列表", "headers": {"image": "图片", "title": "标题", "category": "分类", "updatedAt": "更新时间", "actions": "操作"}, "filters": {"search": {"placeholder": "搜索博客..."}, "category": "分类", "createdAt": "创建时间", "updatedAt": "更新时间"}, "actions": {"import": "导入", "addBlog": "添加博客"}}, "checkout": {"title": "结账", "orderInformation": {"title": "订单信息", "confirm": "确认", "cancel": "取消", "subscribeTo": "订阅计划", "registrationTime": "注册时间", "price": "价格", "creationDate": "创建日期", "paymentMethod": "支付方式", "issueInvoice": "开具发票", "companyName": "公司名称", "taxId": "税号", "address": "地址", "email": "公司邮箱", "vat": "增值税（0%）", "total": "总金额", "pay": "支付", "companyNamePlaceholder": "例：ABC有限公司", "taxIdPlaceholder": "输入税号", "addressPlaceholder": "输入地址", "emailPlaceholder": "例：<EMAIL>"}, "qrPayment": {"title": "扫码支付", "accountName": "账户名称", "accountNumber": "账户号码", "bankName": "银行名称", "amount": "金额", "content": "内容", "cancelOrder": "取消订单", "success": "支付成功！", "redirectingIn": "{{countdown}}秒后跳转到注册页面..."}}, "opportunities": {"noOrderYet": "暂无订单", "clickTabToAddOrder": "点击标签添加订单", "orders": "订单", "opportunities": "商机", "deleteSuccess": "商机已成功删除", "deleteError": "无法删除商机", "title": "商机", "add": "添加", "search": "搜索...", "addOpportunity": "新商机", "contact": "客户", "salesperson": "负责人", "expectedClosing": "预计结束日期", "tags": "标签", "phone": "电话号码", "enterContactName": "输入客户名称...", "enterEmailAddress": "输入电子邮箱...", "enterTags": "输入标签...", "enterPhoneNumber": "输入电话号码...", "opportunityDetail": "商机详情", "enterOpportunityTitle": "输入商机名称...", "enterProbability": "输入概率...", "expectedRevenue": "预计收入", "probability": "概率", "customerInfo": "客户信息", "salesPerson": "销售负责人", "notes": "备注", "enterRevenue": "输入收入...", "selectAssignee": "选择负责人", "enterExpectedClosingDate": "选择预计结束日期...", "status": {"won": "赢得", "lost": "丢失", "ongoing": "进行中"}, "lostReason": {"title": "丢单原因", "description": "请选择丢单原因或输入自定义原因。", "selectReason": "选择原因", "selectPlaceholder": "选择原因...", "searchPlaceholder": "搜索原因...", "noResults": "未找到原因。", "customReason": "自定义原因", "customPlaceholder": "请输入原因...", "priceTooHigh": "价格过高", "competitorWon": "竞争对手获胜", "noBudget": "没有预算", "timingIssues": "时间问题", "technicalRequirements": "技术要求未满足", "decisionMakerChanged": "决策人变更", "projectCancelled": "项目已取消", "poorFit": "不符合需求", "noLongerInterested": "失去兴趣", "stopResponding": "停止回应", "foundBetterAlternative": "找到更好的替代方案", "productDidNotMeetExpectations": "产品未达到预期"}}, "stages": {"egDiscuss": "例：会议", "createOpportunity": "创建商机", "opportunityNamePlaceholder": "例：面向医院的聊天机器人", "contactPlaceholder": "例：<PERSON><PERSON><PERSON><PERSON>", "editColumn": "编辑列", "columnName": "列名", "isWonStage": "这是成交阶段吗？", "deleted": "列删除成功", "added": "列添加成功", "failedToCreateStage": "无法创建列", "yesterday": "昨天", "overdue": "已过期", "today": "今天", "upcoming": "即将到来", "noDueDate": "无截止日期", "noActivity": "暂无活动", "inDays": "{{count}}天后", "tomorrow": "明天", "daysAgo": "{{count}}天前", "filters": {"createdAt": "创建日期", "updatedAt": "更新日期", "outcome": "结果"}, "newColumn": "新列", "newColumnPlaceholder": "输入列名并按 Enter", "unassigned": "未分配", "editActivity": "编辑活动", "fold": "折叠", "edit": "编辑", "delete": "删除", "view": "查看", "addColumnAfter": "在后面添加列", "addColumnBefore": "在前面添加列", "deleteColumn": "删除列", "search": "搜索...", "title": "进度管理", "add": "添加", "addOpportunity": "新建商机", "status": "状态", "opportunity": "商机", "contact": "客户", "phone": "电话号码", "expectedRevenue": "预期收入", "priority": "优先级", "assignee": "负责人", "selectAssignee": "选择负责人", "noScheduledActivities": "没有已安排的活动", "noActivitiesForStatus": "状态 {{status}} 下无活动", "scheduleActivity": "安排活动", "activityType": "活动类型", "selectActivityType": "选择类型", "searchActivityTypes": "搜索类型...", "dueDate": "截止日期", "summary": "概要", "notes": "备注", "typeSomething": "请输入...", "searchAssignee": "搜索负责人...", "noAssigneeFound": "未找到负责人", "pickADate": "选择日期", "schedule": "日程", "actionName": "操作名称", "newActivityType": "新活动类型", "salesperson": "销售人员", "expectedClosing": "预期结束日期", "tags": "标签", "headers": {"contact": "客户", "email": "邮箱", "opportunity": "商机", "stage": "阶段", "status": "状态", "assignee": "负责人", "createdAt": "创建日期", "updatedAt": "更新日期", "customer": "客户", "amount": "金额", "closeDate": "结束日期", "probability": "概率", "expectedClosingDate": "预期结束日期", "expectedRevenue": "预期收入", "activities": "活动"}, "sort": {"title": "排序", "order": "顺序", "closingDate": "结束日期", "dealValue": "交易金额", "created": "创建日期", "lastActivity": "最近活动", "winProbability": "成交概率", "rating": "评级", "lowestToHighest": "升序", "highestToLowest": "降序"}}, "onexbotsDashboard": {"title": "OnexBots 仪表盘", "filters": {"period": "周期", "daily": "每日", "weekly": "每周", "monthly": "每月", "yearly": "每年", "staff": "员工"}, "stats": {"conversations": "会话数", "users": "用户数", "accuracyRate": "准确率", "averageResponseTime": "平均响应时间", "viewMore": "查看更多", "fromLastPeriod": "与上期相比", "noComparisonData": "无对比数据"}, "accuracyRateChart": {"title": "准确率", "description": "将回答的准确性与知识库进行比较", "tooltip": {"rate": "准确率", "resolved": "已解决", "total": "问题总数"}}, "responseTimeChart": {"title": "平均响应时间", "description": "整体平均响应时间", "fastest": "最快", "slowest": "最慢", "tooltip": {"average": "平均", "min": "最小", "max": "最大"}}}, "onboarding": {"welcomeTo": "欢迎来到", "skip": "跳过", "done": "完成"}, "overview": {"title": "概览", "filters": {"period": "周期", "daily": "每日", "weekly": "每周", "monthly": "每月", "yearly": "每年", "selectLocation": "选择设施", "refresh": "刷新"}, "stats": {"totalFacilities": "设施总数", "totalPatients": "患者总数", "averageOccupancy": "平均入住率", "totalRevenue": "总收入", "viewMore": "查看更多", "fromLastMonth": "与上月相比"}, "patientStats": {"title": "患者统计", "outpatient": "门诊", "inpatient": "住院"}, "topTwenty": {"title": "前二十", "icdDiagnoses": "ICD 诊断", "prescribedMedications": "处方药"}, "costs": {"averageTreatmentCosts": "平均治疗费用", "insurancePayments": "保险支付", "insurance": "保险", "service": "服务", "specialCare": "特殊护理"}, "treatmentOutcomes": {"title": "治疗结果", "recovered": "康复", "improved": "改善", "unchanged": "无变化", "deteriorated": "恶化", "deceased": "死亡", "left": "出院"}}, "customers": {"title": "顧客一覧", "filters": {"search": {"placeholder": "名前で検索"}, "group": "グループ", "createdAt": "作成日", "updatedAt": "最終更新"}, "name": "名前", "phone": "電話番号", "email": "メール", "address": "住所", "group": "グループ", "createdAt": "作成日", "updatedAt": "更新日", "orderHistory": "注文履歴"}, "orders": {"print": "打印", "sendEmail": "发送邮件", "enterServiceName": "输入服务名称", "serviceNameCannotBeEmpty": "服务名称不能为空", "order": "订单", "printInvoice": "打印发票", "printOrder": "打印订单", "printReceipt": "打印收据", "productInformation": "产品信息", "qty": "数量", "totalAmount": "总金额", "notes": "备注", "hasBeenCreatedSuccessfully": "已成功创建订单！", "orderStatus": {"draft": "草稿", "shipping": "配送中", "await_packing": "梱包待ち", "delivered": "配送済み", "pending": "待处理", "confirmed": "已确认", "cancelled": "已取消", "completed": "已完成", "partial": "部分完成", "returned": "已退货"}, "orderPaymentStatus": {"unpaid": "未付款", "pending": "待付款", "paid": "已付款", "partiallyPaid": "部分付款", "cancelled": "已取消"}, "filters": {"shift": "工作班次", "status": "订单状态", "paymentStatus": "付款状态", "createdAt": "创建日期", "updatedAt": "更新日期"}, "orderHistory": "订单历史", "amount": "数量", "redeemPoints": "兑换积分", "loyalPoints": "累计积分", "updatedAt": "更新日期", "title": "订单列表", "searchPriceGroup": "搜索价格组", "noPriceGroupsFound": "未找到价格组", "searchBranch": "搜索分店", "noBranchFound": "未找到分店", "emptyServiceName": "请输入服务名称", "updateOrder": "更新", "selectGender": "选择性别", "tax": "税金", "shipping": "运费", "addCustomer": "添加客户", "save": "保存", "defaultShippingAddress": "默认送货地址", "defaultBillingAddress": "默认账单地址", "noAddressesFound": "未找到地址", "edit": "编辑", "removeRecipientInfo": "删除收件人信息", "addRecipientInfo": "添加收件人信息", "enterName": "输入姓名", "enterAddress": "输入地址", "enterPhoneNumber": "输入电话号码", "enterCompanyName": "输入公司名称", "selectWard": "选择区/镇", "searchWard": "搜索区/镇", "searchDistrict": "搜索县/市", "selectDistrict": "选择县/市", "selectProvince": "选择省份", "searchProvince": "搜索省份", "province": "省份", "district": "县/市", "ward": "区/镇", "addAddress": "添加地址", "address": "地址", "noProvincesFound": "未找到省份", "noDistrictsFound": "未找到县/市", "noWardsFound": "未找到区/镇", "shippingDefault": "默认送货地址", "billingDefault": "默认账单地址", "editCustomer": "编辑客户", "Name": "姓名", "phoneNumber": "电话号码", "email": "邮箱", "gender": "性别", "enterEmail": "输入邮箱", "birthday": "生日", "pickADate": "选择日期", "customerGroup": "客户分组", "selectCustomerGroup": "选择客户分组", "companyName": "公司名称", "addresses": "地址", "submit": "确认", "accumulatedPoints": "累计积分", "groupName": "分组名称", "placeholder": "按名称、SKU、条形码搜索", "quantity": "数量", "price": "价格", "total": "总计", "noProductsFound": "未找到商品", "addService": "添加服务（F9）", "loadingMore": "正在加载更多...", "addProduct": "添加商品", "available": "有库存", "onHand": "现有库存", "note": "备注", "maximumAvailableQuantity": "最大可用数量", "branch": "分店", "loadingCustomerDetails": "正在加载客户详情...", "customer": "客户", "shippingAddress": "送货地址", "billingAddress": "账单地址", "noCustomersFound": "未找到客户", "loading": "加载中...", "searchCustomer": "搜索客户", "payment": "付款", "addPromotion": "添加促销", "products": "商品", "subtotal": "小计", "discount": "折扣", "voucher": "优惠码", "fees": "服务费", "promotions": "促销", "notePlaceholder": "输入备注", "tags": "标签", "tagsPlaceholder": "输入标签", "noProductsInOrder": "订单中没有商品。", "cancel": "取消", "addOrder": "添加订单", "success": "订单已成功创建！", "error": "无法处理订单", "adjustPrice": "调整价格", "adjustPriceSuccess": "价格调整成功！", "adjustPriceError": "无法调整价格", "adjustPriceDescription": "调整所选商品的价格", "adjustPricePlaceholder": "输入新价格", "adjustPriceButton": "调整价格", "adjustPriceCancel": "取消", "setNewPrice": "设置新价格", "value": "金额", "percent": "百分比", "addProductToOrderWarning": "请向订单中添加商品", "selectCustomer": "请选择客户", "addVoucher": "添加优惠券", "voucherCode": "优惠码", "voucherCodePlaceholder": "输入优惠码", "voucherCodeButton": "添加优惠券", "voucherCodeSuccess": "优惠券已成功添加", "voucherCodeError": "无法添加优惠券", "confirm": "确定要执行此操作吗？", "confirmCancel": "确认", "confirmDelete": "删除", "cancelWarning": "此操作无法撤销。是否取消更改？", "cancelDelete": "此操作无法撤销。是否删除该项目？"}, "variants": {"title": "变体", "filters": {"search": {"placeholder": "按名称、代码、条形码搜索"}}}, "products": {"products": {"title": "商品", "addBulk": {"title": "批量添加商品", "notice": "注意：", "templateInstructions": "为防止数据不一致，请按照模板填写文件。", "simpleTemplate": "下载简单模板", "advancedTemplate": "下载高级模板", "here": "这里", "supportedFiles": "支持文件格式：.xlsx, .xls, .csv", "dragAndDrop": "将文件拖放到此处，或", "uploadButton": "上传", "fileUpload": "上传文件", "cancel": "取消", "import": "导入", "importing": "正在导入...", "selectFileError": "请选择要上传的文件", "importSuccess": "商品已成功导入", "importError": "导入商品时发生错误"}, "filters": {"search": {"placeholder": "按名称、SKU、条形码搜索", "placeholderBrand": "搜索品牌..."}, "product": "商品", "source": "来源", "category": "类别", "brand": "品牌", "createdAt": "创建日期", "updatedAt": "更新日期", "otherFilters": {"title": "其他筛选条件", "all": "全部", "description": "前两个筛选器会优先显示在主页上，可根据需要自定义。"}, "dateOptions": {"allTime": "全部时间", "today": "今天", "yesterday": "昨天", "lastWeek": "上周", "thisWeek": "本周", "lastMonth": "上个月", "thisMonth": "本月", "customize": "自定义"}, "deletionFailed": "删除变体失败", "deletedSuccessfully": "删除变体成功"}, "headers": {"productInfo": "商品信息", "category": "类别", "brand": "品牌", "updatedAt": "更新时间", "createdAt": "创建日期", "available": "库存数量", "variant": "变体"}, "actions": {"addProduct": "添加商品", "addManual": {"title": "添加商品", "onThisPage": "目录", "sections": {"basicInfo": "基本信息", "options": "商品选项", "units": "包装单位", "prices": "价格信息", "measurements": "尺寸"}}, "addQuick": "快速添加", "addBulk": "批量添加", "refresh": "刷新", "saveFilters": "保存筛选器", "reset": "重置", "filter": "筛选"}}, "deletionFailed": "删除变体失败", "deletedSuccessfully": "删除变体成功", "descriptionDeleteOption": "此操作无法撤销。与包装单位、价格、尺寸相关的数据将被永久删除。", "descriptionDeleteValueOption": "此操作无法撤销。与价格和尺寸相关的数据将被永久删除。", "name": "名称", "sku": "SKU代码", "barcode": "条形码", "option1": "选项1", "option2": "选项2", "option3": "选项3", "unit": "单位", "weight": "重量", "height": "高度", "width": "宽度", "length": "长度", "variantDetails": "变体详情", "variants": "变体", "source": "来源", "category": "类别", "brand": "品牌", "createdAt": "创建日期", "description": "描述", "viewLess": "收起", "viewMore": "查看更多", "noPricesAvailable": "暂无价格信息", "prices": "价格", "tags": "标签", "inventory": {"noMatchResult": "无匹配结果", "title": "库存", "branch": "分店", "history": "历史记录", "allBranches": "所有分店", "inventory": "库存", "packing": "包装", "shipping": "配送", "minValue": "最小值", "maxValue": "最大值", "staff": "员工", "transactionType": "交易类型", "change": "变更", "quantity": "数量", "reference": "参考", "available": "可用库存", "incoming": "预计入库", "onHand": "现有库存"}, "addManual": {"title": "手动添加商品", "onThisPage": "目录", "publish": "发布", "sections": {"addVariant": "如果有多个选项（如尺寸或颜色），请创建变体。", "variant": "变体", "all": "全部", "apply": "应用", "variantPlaceholder": "选择变体", "usedByAllVariants": "用于所有变体", "usedByThis": "用于此变体", "unitPlaceholder": "选择单位", "unitSearchPlaceholder": "搜索单位", "variantSearchPlaceholder": "搜索变体", "unitEmptyText": "未找到单位", "addType": "添加类型", "addUnit": "添加单位", "basicInfo": "基本信息", "options": "商品选项", "option": "选项", "units": "包装单位", "prices": "价格信息", "measurements": "尺寸", "selectImages": "选择图片", "submit": "提交", "cancel": "取消", "add": "添加", "edit": "编辑", "save": "保存", "stay": "停留", "leave": "离开", "values": "值", "valuesPlaceholder": "值1, 值2, 值3", "optionsPlaceholder": "输入选项名称", "addOption": "添加选项", "optionName": "选项名称", "addValue": "添加值", "remove": "删除", "valuesPlaceholderInput": "输入值", "duplicateValue": "该值已存在", "createVariant": "如果有多个选项（如尺寸或颜色），请创建变体。"}, "basicInfo": {"brandPlaceholder": "选择品牌", "brandSearchPlaceholder": "搜索品牌", "brandEmptyText": "未找到品牌", "categoryPlaceholder": "选择类别", "categorySearchPlaceholder": "搜索类别", "categoryEmptyText": "未找到类别", "tagsPlaceholder": "输入标签", "images": "图片", "name": "名称", "description": "描述", "shortDescription": "简短描述", "brand": "品牌", "category": "类别", "sku": "SKU代码", "tags": "标签", "price": "价格", "uploadImage": "上传图片", "optimize": "优化", "required": "此字段为必填项", "imageRequired": "至少需要一张图片", "nameRequired": "商品名称为必填项", "nameWarning": "请输入商品名称以提高展示效果", "descriptionWarning": "请输入描述以优化内容", "skuRequired": "SKU代码为必填项", "priceRequired": "价格为必填项"}, "options": {"addOption": "添加选项", "optionName": "选项名称", "values": "值", "addValue": "添加值", "remove": "删除"}, "units": {"title": "包装单位", "addUnit": "添加单位", "unitName": "单位名称", "ratio": "比例", "remove": "删除"}, "prices": {"title": "价格信息", "addGroup": "添加新的价格组", "groupName": "组名", "price": "价格", "apply": "应用", "applyAll": "应用到全部"}, "measurements": {"weight": "重量", "height": "高度", "width": "宽度", "length": "长度", "apply": "应用", "applyAll": "应用到全部"}, "buttons": {"cancel": "取消", "add": "添加", "edit": "更新", "save": "保存", "stay": "停留", "leave": "离开"}, "dialogs": {"leaveTitle": "确定要离开吗？", "leaveDesc": "未保存的更改将会丢失。"}, "validation": {"hasErrors": "验证错误", "checkFields": "请检查所有必填项后重试"}, "success": "商品已创建", "successUpdate": "商品已更新", "successDescription": "商品已成功创建", "successDescriptionUpdate": "商品已成功更新", "error": "错误", "errorDescription": "无法创建商品，请重试。", "errorDescriptionUpdate": "无法更新商品，请重试。"}}, "choosePlan": {"forIndividuals": "个人版", "forCompanies": "企业版", "monthly": "月付", "annually": "年付", "chooseAPlan": "选择方案", "planDescription": {"firstSection": "请选择最适合您业务需求的方案。", "middleSection": "您可以随时升级或降级。", "secondSection": "所有方案均包含基本功能。"}, "planData": {"Up to 50 variants": "最多 50 个变体的概要统计", "Real-time inventory syncing": "实时库存同步", "Ideal for startups (1,000 items)": "适合初创企业（最多 1,000 个商品）", "Analytics dashboard": "分析仪表板", "User-friendly interface": "用户友好界面", "Support for multiple currencies and languages": "支持多种货币和语言", "Real time inventory": "实时库存管理"}, "planNames": {"Free": "免费版", "Starter": "入门版", "Pro": "专业版", "Agency": "代理版"}, "mostPopular": "最受欢迎", "numberIntegrations": "集成数量", "explainNoIntegrations": "无集成", "getStarted": "开始使用"}, "synchronization": {"platforms": {"source": "来源", "destination": "目标"}, "title": {"success": "已将 {{source}} 连接到 {{destination}}", "error": "同步设置错误"}, "description": "请选择符合您兴趣和目标的领域。", "error": {"missingConnection": "未找到连接", "connectionError": "连接错误", "sourceNotFound": "未找到来源", "destinationNotFound": "未找到目标"}, "success": {"completeTitle": "连接完成！", "gotoDashboard": "前往仪表板"}, "syncSetting": {"title": "同步设置", "product": {"title": "商品", "description": "将商品从 {{source}} 同步到 {{destination}}"}, "inventory": {"title": "库存", "description": "持续同步库存数量"}, "order": {"title": "订单", "description": "将 {{destination}} 的订单导入到 {{source}}"}, "buttonTitle": "连接到 {{destination}}"}}, "syncRecords": {"title": "同步记录", "filters": {"search": {"placeholder": "搜索退货订单..."}, "status": "状态", "recordType": "记录类型", "channel": "渠道", "connectionId": "连接 ID", "fetchEventId": "获取事件 ID"}, "columns": {"channel": "渠道", "header": "记录类型", "fetchEventId": "获取事件 ID", "connectionId": "连接 ID", "lastUpdated": "最后更新", "fetchedAt": "获取时间", "finishedAt": "完成时间", "publishedAt": "发布时间", "transformedAt": "转换时间"}}, "fetchEvents": {"title": "获取事件", "filters": {"search": {"placeholder": "搜索获取事件..."}, "status": "状态", "actionType": "操作类型", "actionGroup": "操作组", "eventTime": "事件时间", "eventSource": "事件来源", "fetchEventId": "获取事件 ID"}, "columns": {"channel": "渠道", "header": "记录类型", "fetchEventId": "获取事件 ID", "connectionId": "连接 ID", "lastUpdated": "最后更新", "fetchedAt": "获取时间", "finishedAt": "完成时间", "publishedAt": "发布时间", "transformedAt": "转换时间"}, "headers": {"channel": "渠道", "actionType": "操作类型", "actionGroup": "操作组", "eventSource": "事件来源", "eventTime": "事件时间", "status": "状态", "actions": "操作"}}, "fetchEventDetail": {"title": "{{source}} 的获取事件详情", "actionGroup": "操作组", "connectionId": "连接 ID", "actionType": "操作类型", "eventSource": "事件来源", "retryCount": "重试次数", "status": "状态", "continuationToken": "续传令牌", "objectId": "对象 ID", "eventTime": "事件时间", "createdAt": "创建时间", "updatedAt": "更新时间", "eventNumber": "{{number}}. 无 ID"}, "channel": {"title": "连接列表", "filters": {"search": {"placeholder": "搜索连接..."}, "status": "状态"}, "headers": {"channel": "渠道", "status": "状态", "url": "URL", "createdAt": "创建时间", "lastUpdated": "最后更新", "actions": "操作"}, "actions": {"install": "安装新渠道", "configure": "配置", "activate": "启用", "deactivate": "停用"}}, "supportedChannels": {"title": "渠道列表", "filters": {"search": {"placeholder": "搜索渠道..."}}}, "installChannel": {"title": "安装渠道"}, "settings": {"passwordsDontMatch": "密码不匹配", "eachRoleAssignmentMustHaveABranchAndAtLeastOneRoleSelected": "每个角色分配必须有一个分支和至少一个角色", "cannotMixAllBranchesWithIndividualBranchSelections": "不能混合所有分支和单独的分支选择", "emailInvalid": "邮箱格式不正确", "emailRequired": "邮箱是必填的", "passwordRequired": "密码是必填的", "confirmPasswordRequired": "确认密码是必填的", "employeeNameRequired": "员工姓名是必填的", "usernameMinLength": "用户名必须至少3个字符", "usernameInvalid": "用户名只能包含字母、数字和下划线", "branchRequired": "分支是必填的", "atLeastOneRoleRequired": "至少一个角色是必填的", "atLeastOneRoleAssignmentRequired": "至少一个角色分配是必填的", "employeeAccountAlreadyExists": "用户账户已存在", "employeeAccountExpired": "用户账户已过期。请使用 RESEND 操作重置用户账户", "employeeAccountDoesNotExist": "用户账户不存在", "accessDenied": "访问被拒绝", "employeeUpdatedSuccess": "员工更新成功", "employeeCreatedSuccess": "员工创建成功", "employeeDeletedSuccess": "员工删除成功", "employeeDeletedError": "无法删除员工", "employeeUpdatedError": "无法更新员工", "employeeCreatedError": "无法创建员工", "emailAlreadyInUse": "邮箱已存在", "phoneNumberAlreadyInUse": "手机号已存在", "phone": "手机号", "birthday": "生日", "address": "地址", "shopInfo": "店铺信息", "profileSettings": "个人资料设置", "profileSettingsDescription": "头像、姓名、密码", "storeInformation": "店铺信息", "storeInformationDescription": "联系方式、URL", "appearance": "外观", "appearanceDescription": "Logo、颜色、主题", "languageCurrency": "语言与货币", "languageCurrencyDescription": "语言设置、货币支持", "employeesPermissions": "员工与权限", "employeesPermissionsDescription": "分配角色、管理角色", "themeSetting": "主题设置", "saveSuccess": "颜色已保存", "saveError": "无法保存颜色", "colorSetting": "颜色设置", "lightMode": "浅色模式", "darkMode": "深色模式", "logoSetting": "Logo 设置", "language": {"language": "语言", "addLanguage": "添加语言", "remove": "删除", "addCurrency": "添加货币", "update": "更新", "currency": "货币"}, "theme": {"title": "主题", "description": "自定义应用的主题。", "lightMode": "浅色模式", "darkMode": "深色模式"}, "logo": {"title": "Logo", "lightTheme": "(浅色模式)", "darkTheme": "(深色模式)", "description": "自定义显示在店铺的 Logo。", "lightModeLogo": "浅色模式 Logo", "darkModeLogo": "深色模式 Logo", "lightModeIcon": "浅色模式图标", "darkModeIcon": "深色模式图标", "favicon": "网站图标", "lightModeLogoDescription": "上传浅色模式 Logo（推荐尺寸：180x40px）", "darkModeLogoDescription": "上传深色模式 Logo（推荐尺寸：180x40px）", "lightModeIconDescription": "上传浅色模式图标（推荐尺寸：40x40px）", "darkModeIconDescription": "上传深色模式图标（推荐尺寸：40x40px）", "faviconDescription": "上传网站 favicon（推荐尺寸：32x32px）", "saveSuccess": "Logo 已保存", "saveError": "无法保存 Logo", "noChangesToSave": "没有可保存的更改", "resetSuccess": "Logo 已重置", "resetError": "无法重置 Logo"}, "colors": {"title": "主题颜色", "description": "自定义应用的主题颜色。", "brandColor": "品牌颜色", "lightMode": "(浅色模式)", "darkMode": "(深色模式)"}, "color": {"saveSuccess": "主题颜色已保存", "saveError": "无法保存主题颜色", "resetSuccess": "主题颜色已重置", "resetError": "无法重置主题颜色"}}, "profile": {"title": "个人资料", "contactInformation": "联系方式", "contactInformationDescription": "客户联系你的信息", "changePassword": "修改密码", "aspectRatio": "长宽比", "formats": "格式", "name": "姓名", "username": "用户名", "email": "邮箱", "phone": "电话号码", "update": "更新", "avatar": "头像", "changePasswordDiaglog": {"title": "修改密码", "oldPassword": "当前密码", "newPassword": "新密码", "confirmPassword": "确认密码", "enterOldPassword": "请输入当前密码", "enterNewPassword": "请输入新密码", "enterConfirmPassword": "请输入确认密码", "passwordRequirements": "密码必须至少 8 个字符", "passwordChangedSuccessfully": "密码已成功修改", "passwordChangeFailed": "无法修改密码", "passwordsDoNotMatch": "两次输入的密码不一致", "passwordTooShort": "密码必须至少 8 个字符", "forgotPassword": "忘记密码？", "allFieldsRequired": "所有字段为必填", "currentPasswordRequired": "当前密码是必需的", "confirmPasswordRequired": "确认密码是必需的", "passwordComplexity": "密码必须包含至少一个小写字母、一个大写字母和一个数字", "passwordAttemptsExceeded": "密码尝试次数已超过", "passwordDoesNotMatch": "密码不正确"}, "forgotPasswordDialog": {"emailRequired": "请输入邮箱地址", "codeRequired": "请输入验证码", "codeSentSuccessfully": "验证码已成功发送", "codeSendFailed": "无法发送验证码", "codeVerifiedSuccessfully": "验证码已验证成功", "codeVerificationFailed": "验证码验证失败", "codeResentSuccessfully": "验证码已重新发送", "codeResendFailed": "无法重新发送验证码", "passwordChangedSuccessfully": "密码已成功修改", "passwordChangeFailed": "无法修改密码", "passwordsDoNotMatch": "两次输入的密码不一致", "passwordTooShort": "密码必须至少 8 个字符", "allFieldsRequired": "所有字段为必填", "emailStep": {"title": "发送验证码", "description": "请输入邮箱地址并点击“发送验证码”以接收重置密码的验证码。", "emailLabel": "邮箱地址", "emailPlaceholder": "请输入邮箱地址", "sending": "发送中...", "sendCode": "发送验证码"}, "verificationStep": {"title": "输入验证码", "description": "请输入发送到 {{email}} 的验证码", "codeLabel": "验证码", "codePlaceholder": "验证码", "yourEmail": "你的邮箱", "resend": "重新发送", "verifying": "验证中...", "continue": "继续"}, "resetPasswordStep": {"title": "修改密码", "description": "请输入新密码进行更改", "newPasswordLabel": "新密码", "newPasswordPlaceholder": "请输入新密码", "confirmPasswordLabel": "确认密码", "confirmPasswordPlaceholder": "请输入确认密码", "changing": "修改中...", "continue": "继续"}, "successStep": {"title": "密码已修改", "description": "你可以使用新密码登录。", "loginButton": "登录"}}}, "storeInformation": {"success": "店铺信息更新成功", "error": "无法更新店铺信息", "title": "店铺信息", "contactInformation": "联系方式", "contactInformationDescription": "管理联系方式", "changePassword": "修改密码", "aspectRatio": "长宽比", "formats": "格式", "name": "姓名", "username": "用户名", "email": "邮箱", "phone": "电话号码", "update": "更新", "storeInformationDescription": "管理店铺联系方式和设置"}, "languageCurrency": {"title": "语言与货币", "description": "管理应用的语言和货币设置", "language": "语言", "currency": "货币", "addLanguage": "添加语言", "addCurrency": "添加货币", "remove": "删除", "update": "更新", "back": "返回", "languageDescription": "请选择你想支持的语言", "currencyDescription": "请选择你想支持的货币", "updating": "更新中...", "confirmRemoveTitle": "你确定要删除吗？", "confirmRemoveDescription": "此操作无法撤销。此语言将被永久删除。", "currencySettingsUpdatedSuccessfully": "货币设置已更新成功", "currencySettingsUpdatedFailed": "更新货币设置失败"}, "productMappingList": {"syncSuccess": "商品同步请求已成功接收", "syncFail": "接收商品链接请求失败", "noConnection": "无连接", "title": "商品链接", "description": "管理从 Shopify 到 TikTok Shop 的商品链接", "filters": {"search": {"placeholder": "搜索"}, "status": {"all": "所有商品", "synced": "已同步", "mapped": "已链接", "unMapped": "未链接", "errors": "错误"}}, "alert": {"title": "要同步商品吗？", "description": "您将尝试在两个平台之间同步所有商品。此过程可能需要一些时间。", "note": "注意：", "noteDescription": "如果只想同步部分商品，请在继续前从表格中选择。", "confirm": "同步", "cancel": "取消", "areYouSure": "确定要继续吗？", "unmapSuccess": "商品链接已成功解除", "unmapFail": "解除商品链接失败"}, "groupButton": {"settingButton": "设置", "syncButton": "同步商品"}, "status": {"synced": "已同步", "mapped": "已链接", "unmapped": "未链接", "error": "错误"}, "actions": {"unmap": "解除链接", "map": "链接", "fix": "修复属性"}, "headers": {"product": "商品 {{product}}", "price": "价格", "last_synced": "最后同步", "status": "状态", "actions": "操作"}, "nomap": "未链接到 {{destination}}"}, "productMapping": {"advancedMapping": {"title": "高级链接设置", "description": "设置商品数据链接的高级规则。", "sourceField": "源字段", "transformationType": "转换类型", "addTransformation": "添加转换", "removeTransformation": "删除转换", "ruleConfiguration": "规则设置", "outputPreview": "输出预览", "finalOutput": "最终输出", "applyTransformations": "应用转换", "transformationChain": "转换链", "sampleData": "示例数据", "preview": "预览", "output": "输出", "singleValue": "单一值", "transformationForm": "转换", "exampleUsage": "使用示例", "selectFieldsPlaceholder": "选择字段", "searchFieldsPlaceholder": "搜索字段...", "source": "来源", "searchTransformationTypes": "搜索转换类型...", "selectTransformationTypes": "选择转换类型..."}, "lastSynced": "最后同步", "errorLoading": "加载商品同步详情时出错", "manualRetry": "手动重试", "cancelledMessage": "商品同步已取消", "mappingStatus": "同步状态"}, "staff": {"name": "姓名", "phoneNumber": "电话号码", "howCanICallYou": "请问您叫什么名字？", "enterYourPhoneNumber": "请输入您的电话号码", "title": "员工列表", "filters": {"department": "部门", "role": "角色", "search": {"placeholder": "搜索员工..."}}, "actionButton": {"create": "创建员工"}, "columns": {"staff": "员工", "role": "角色", "skills": "技能", "task": "任务", "conversations": "会话", "actions": "操作"}, "actions": {"view": "查看", "edit": "编辑", "delete": "删除"}, "maxCharactersReached": "已达到最大字符数", "online": "在线", "noStaff": "暂无员工。", "loading": "加载中...", "interact": "互动", "createStaff": "创建员工", "staffName": "员工姓名", "enterStaffName": "输入员工姓名", "staffNameRequired": "请输入员工姓名", "maxCharacters": "最多250个字符", "selectDepartment": "选择部门", "searchDepartments": "搜索部门...", "selectRole": "选择角色", "searchRoles": "搜索角色...", "creating": "创建中", "update": "更新", "role": "角色", "department": "部门", "expertise": "专业领域", "knowledgeWarning": "当前知识不足以准确回答。请添加更多详细信息以提升表现。", "score": "评分", "avatar": {"title": "头像", "xbotAvatar": "XBot头像", "image": "图片", "selectedAvatar": "已选择头像", "avatar": "头像"}, "knowledge": {"tab": "知识", "baby": "初学者", "warning": "当前知识不足以准确回答。请添加更多详细信息以提升表现。"}, "interactionStyle": {"tab": "互动风格", "description": "设置员工与客户的互动方式", "communicationTone": "沟通语气", "languagePreferences": "语言偏好", "responseLength": "回复长度", "personalityTraits": "性格特征", "temper": "情绪", "formal": "正式", "casual": "非正式", "detailed": "详细", "concise": "简洁", "creative": "有创意", "analytical": "分析型", "ethicalConstraints": "道德约束", "contentFiltering": "启用内容过滤", "instruction": "指令", "instructionPlaceholder": "请输入该员工的自定义指令（可选）", "greeting": "问候语", "greetingPlaceholder": "您好！我是OneXBots的虚拟员工，请随时咨询我！", "welcomeMessage": "您好！我是{{botName}}。今天我能为您做些什么？"}, "skills": {"tab": "数据访问设置", "description": "设置员工可访问的数据", "products": "商品", "orders": "订单", "inventory": "库存"}, "staffInfo": {"tab": "员工信息", "description": "管理员工的基本信息"}, "task": {"tab": "任务", "description": "管理员工的任务和职责", "noTasks": "暂无任务"}, "editStaff": {"validation": {"nameRequired": "姓名为必填项", "roleRequired": "角色为必填项", "departmentRequired": "部门为必填项", "greetingMaxLength": "问候语不能超过100个字符"}, "tabs": {"staffInfo": "员工信息", "interactionStyle": "互动风格", "knowledge": "知识", "skills": "技能", "task": "任务"}, "staffInfo": "员工信息", "interactionStyle": "互动风格", "knowledge": "知识", "skills": "技能", "task": "任务", "integration": "集成", "embedCodeInstructions": "将虚拟员工小部件嵌入网页的步骤", "embedCodeTitle": "嵌入虚拟员工小部件", "title": "编辑员工", "staffName": "员工姓名", "rolePurpose": "角色/目的", "department": "部门", "domainExpertise": "专业领域", "customExpertisePlaceholder": "输入专业领域并按Enter键", "noRoleFound": "未找到角色", "noDepartmentFound": "未找到部门", "selectRole": "选择角色", "selectDepartment": "选择部门", "searchRoles": "搜索角色...", "searchDepartments": "搜索部门...", "namePhoneRequirement": "必须输入姓名和电话号码", "roles": {"contentWriter": "内容撰写员", "seoSpecialist": "SEO专家", "socialMediaManager": "社交媒体经理", "marketingSpecialist": "营销专家"}, "embedVirtualStaffWidget": "嵌入虚拟员工小部件", "themesColor": "主题颜色", "embedCode": "嵌入代码", "greeting": "来自OneXBots的问候", "departments": {"engineering": "工程", "marketing": "市场营销", "sales": "销售", "support": "客服支持"}, "expertise": {"contentWriting": "内容写作", "customerSupport": "客户支持", "dataAnalysis": "数据分析", "emailMarketing": "电子邮件营销", "graphicDesign": "平面设计", "projectManagement": "项目管理", "seo": "SEO", "socialMedia": "社交媒体管理"}}, "embed": {"instructions": {"title": "嵌入步骤", "step1Title": "步骤1: 添加脚本", "step1Description": "复制脚本标签，并粘贴到网页的<head>标签内或</body>标签之前。", "step2Title": "步骤2: 添加容器小部件", "step2Description": "复制div元素，并粘贴到希望显示虚拟员工小部件的位置。小部件会自动在该位置初始化。", "step3Title": "步骤3: 自定义小部件（可选）", "step3Description": "通过在网页中添加CSS，可以自定义小部件界面。容器小部件的ID为xbot-container。", "step4Title": "步骤4: 验证集成", "step4Description": "添加脚本后，刷新页面，确保虚拟员工小部件正确显示。小部件应显示员工信息，并允许访客互动。", "troubleshootingTitle": "故障排除", "troubleshooting1": "确保脚本URL可从网页访问。", "troubleshooting2": "使用浏览器开发者工具查看错误信息。", "troubleshooting3": "确保ID和员工姓名正确。", "troubleshooting4": "确保网页允许加载外部脚本。"}, "script": {"title": "嵌入代码", "copy": "复制", "copied": "已复制！", "containerInstructions": "1. 在希望显示小部件的位置添加容器", "scriptInstructions": "2. 在希望显示小部件的位置添加脚本"}}, "promptExperiment": {"title": "提示测试", "datasetName": "数据集名称", "datasetNamePlaceholder": "输入数据集名称", "datasetNameRequired": "数据集名称为必填项", "runName": "运行名称", "runNamePlaceholder": "输入运行名称", "runNameRequired": "运行名称为必填项", "cancel": "取消", "runExperiment": "运行实验", "success": "提示测试已成功运行", "error": "无法运行提示测试"}}, "department": {"subscriptionNotFound": "没有可用于创建部门的订阅", "deleteDepartmentTitle": "确定要删除吗？", "deleteDepartmentDescription": "确定要删除该部门吗？此操作无法撤销。", "updateDepartmentSuccess": "部门已成功更新", "updateDepartmentError": "无法更新部门", "deleteDepartmentSuccess": "部门已成功删除", "deleteDepartmentError": "无法删除部门", "createDepartmentSuccess": "部门已成功创建", "departmentNameAlreadyExists": "部门名称已存在", "createDepartmentFail": "无法创建部门。", "title": "部门", "editDepartment": "编辑部门", "createDepartment": "创建部门", "createStaff": "创建员工", "departmentName": "部门名称", "enterDepartmentName": "输入部门名称", "description": "描述", "enterDescription": "输入描述...", "departmentNameRequired": "请输入部门名称", "viewStaff": "查看员工", "staffCount": "{{count}} 位员工", "additionalStaff": "{{count}} 位员工", "interact": "互动", "upload": "上传", "deleteKnowledge": "删除知识", "deleteKnowledgeDescription": "确定要删除该知识吗？此操作无法撤销。", "deleteKnowledgeSuccess": "知识已成功删除", "deleteKnowledgeError": "无法删除知识", "deleteKnowledgeConfirm": "删除", "deleteKnowledgeCancel": "取消", "knowledgeWarning": "当前知识不足以准确回答。请添加更多详细信息以提升性能。", "knowledge": {"tab": "知识", "baby": "初学者", "warning": "当前知识不足以准确回答。请添加更多详细信息以提升性能。", "status": {"error": "错误", "pending": "处理中", "success": "成功"}}}, "knowledge": {"title": "知识", "headers": {"file": "文件", "status": "状态", "size": "大小", "updatedAt": "更新时间"}, "filters": {"search": {"placeholder": "搜索文件..."}, "fileType": "文件类型", "status": "状态", "url": "URL", "file": "文件", "text": "文本"}, "actions": {"upload": "上传新文件"}, "upload": {"dragAndDrop": "将文件拖放到此处，或", "uploadButton": "上传", "supportedFiles": "PDF、DOCX、TXT 或 CSV", "totalSize": "总大小", "noFileSelected": "未选择文件", "uploadSuccess": "上传成功", "fileTooLarge": "文件超过最大大小 {{max}}MB", "file": "文件", "url": "输入网页", "text": "输入文本", "title": "上传知识", "invalidUrl": "URL 无效", "urlAlreadyAdded": "URL 已存在", "noUrlsToUpload": "请至少输入一个 URL", "uploadError": "上传时出错", "uploaded": "知识已上传", "knowledgeNameRequired": "知识名称是必填项", "knowledgeNameTooLong": "知识名称不能超过 250 个字符", "textRequired": "文本是必填项", "textTooLong": "文本不能超过 20000 个字符", "search": "搜索知识", "textTitle": "知识名称", "fileTitle": "来自文件的知识", "urlTitle": "来自 URL 的知识", "allTitle": "所有知识", "deleteTitle": "删除知识", "deleteDescription": "确定要删除该知识吗？此操作无法撤销。", "deleteSuccess": "知识已成功删除", "deleteError": "无法删除知识", "deleteConfirm": "删除", "deleteCancel": "取消", "leaveTitle": "不保存退出", "leaveDescription": "更改将不会被保存。", "leaveConfirm": "退出", "leaveCancel": "返回", "textInput": "直接输入", "textInputPlaceholder": "请在此输入知识文本...", "textInputTitle": "来自文本的知识", "textTitlePlaceholder": "输入知识标题", "pleaseSelectAtLeastOneFile": "请至少选择一个文件", "pleaseEnterAtLeastOneURL": "请至少输入一个 URL", "pleaseEnterAtLeastOneTextFile": "请至少输入一个文本文件", "pleaseEnterAllFields": "请输入标题和内容", "newest": "最新", "oldest": "最早", "noKnowledge": "暂无知识", "urlImport": "来自 URL 的知识", "urlImportDescription": "从网页导入知识", "deleteKnowledge": "删除知识", "deleteKnowledgeDescription": "确定要删除该知识吗？此操作无法撤销。", "deleteKnowledgeSuccess": "知识已成功删除", "deleteKnowledgeError": "无法删除知识", "deleteKnowledgeConfirm": "删除", "deleteKnowledgeCancel": "取消", "fileImport": "来自文件的知识", "fileImportDescription": "从文件导入知识", "fileImportSuccess": "知识已成功上传", "fileImportError": "无法上传知识", "fileImportConfirm": "上传", "fileImportCancel": "取消", "textImport": "来自文本的知识", "textImportDescription": "从文本导入知识", "textImportSuccess": "知识已成功上传", "textImportError": "无法上传知识", "textImportConfirm": "上传", "textImportCancel": "取消", "urlImportSuccess": "知识已成功上传", "urlImportError": "无法上传知识", "urlImportConfirm": "上传", "urlImportCancel": "取消", "deleteKnowledgeTitle": "删除知识"}, "status": {"error": "错误", "pending": "处理中", "processing": "处理中", "ready": "准备就绪"}}, "customer": {"details": {"customerDetails": "客户详情", "name": "姓名", "birthday": "生日", "gender": "性别", "phone": "电话号码", "email": "邮箱", "shippingAddress": "收货地址", "billingAddress": "账单地址", "groupName": "分组名称", "totalLoyalPoints": "总积分", "totalRedeemPoints": "总兑换积分", "tags": "标签", "noTags": "---"}, "purchase": {"purchaseInfo": "购买信息", "totalSpent": "总支出", "totalProductsPurchased": "购买商品总数", "purchasedOrder": "已购买订单", "totalProductsReturned": "退货商品总数", "lastOrderAt": "最后下单日期"}, "sales": {"suggestionInfo": "建议信息", "defaultPriceGroup": "默认价格组", "defaultPaymentMethod": "默认支付方式", "discountPercent": "折扣率"}, "order": {"orderHistory": "订单历史"}}, "conversation": {"title": "会话", "whatCanIHelpWith": "我能帮你做什么？", "saySomething": "说点什么...", "filters": {"search": {"placeholder": "搜索..."}, "source": "来源", "unread": "未读", "read": "已读", "assignee": "负责人"}}, "tasks": {"deleteDescription": "确定要删除吗？此操作无法撤销。", "promptContent": "提示内容", "shortDescription": "简短描述", "shortDescriptionPlaceholder": "请在此输入知识文本...", "namePlaceholder": "输入任务名称", "promptContentPlaceholder": "请在此输入知识文本...", "editTask": "编辑任务", "addTask": "添加任务", "save": "保存", "add": "添加", "promptHelper": "在提示中使用 [变量] 来插入字段。每个字段必须具有唯一名称。如果相同变量出现多次，用户只需输入一次。"}, "activities": {"title": "活动", "unknown": "未知", "opportunity_created": "创建机会", "field_labels": {"order_changed": "订单已更改", "title": "标题", "status": "状态", "stage_changed": "列已更改", "assignee": "负责人", "assignee_changed": "负责人已更改", "customer_id": "客户", "customer_changed": "客户已更改", "expected_revenue": "预计收入", "probability": "概率", "priority": "优先级", "expected_closing": "预计结束", "position": "位置", "position_changed": "位置已更改", "color": "颜色", "tags": "标签", "user": "用户", "note": "备注", "note_changed": "备注已更改", "schedule_activities": "计划活动", "schedule_activities_changed": "已添加计划活动", "schedule_activities_updated_at": "计划活动更新时间", "schedule_activities_created_at": "计划活动创建时间", "schedule_activities_due_date": "计划活动截止日期", "schedule_activities_status": "计划活动状态", "schedule_activities_summary": "计划活动摘要", "schedule_activities_note": "计划活动备注", "schedule_activities_assignee": "计划活动负责人", "schedule_activities_user": "计划活动用户", "schedule_activities_count": "计划活动数量", "representative_phone": "电话号码", "representative_contact": "联系人", "representative_email": "邮箱", "colors": {"neutral": "灰色", "blue": "蓝色", "green": "绿色", "yellow": "黄色", "red": "红色", "teal": "蓝绿色", "pink": "粉色", "orange": "橙色", "purple": "紫色", "sky": "天蓝色"}, "priorities": {"very_low": "非常低", "low": "低", "medium": "中", "high": "高"}, "schedule_activity_status": {"status": "计划活动状态", "pending": "进行中", "completed": "已完成", "cancelled": "已取消", "done": "已完成"}, "opportunity_status": {"status": "状态", "ongoing": "进行中", "won": "已赢得", "lost": "已丢失"}}, "scheduleActivity": {"groupButton": {"markAsDone": "完成", "edit": "编辑", "cancel": "取消"}}, "history": {"tabs": {"history": "历史", "comments": "评论", "placeholder": "输入评论..."}}, "headers": {"activity": "活动", "assignedTo": "负责人", "summary": "摘要", "dueDate": "截止日期", "status": "状态", "updatedAt": "更新时间"}, "filters": {"search": {"placeholder": "搜索..."}, "status": "状态", "assignee": "负责人"}, "status": {"overdue": "已逾期", "today": "今天", "upcoming": "即将到来", "noduedate": "无截止日期", "no_activity": "无活动"}, "updateSuccess": "活动已更新", "updateError": "无法更新活动", "deleteSuccess": "活动已删除", "deleteError": "无法删除活动", "error": "创建活动类型时出错", "errorActivityTypeExists": "相同名称的活动类型已存在", "createSuccess": "活动类型已创建", "createError": "无法创建活动类型"}, "opportunityDetail": {"updateStatusSuccess": {"title": "成功", "description": "机会状态已成功更新。"}, "updateStatusError": {"title": "错误", "description": "无法更新机会状态，请重试。"}, "customerInfo": {"name": "姓名", "company": "公司", "country": "国家", "address": "地址", "billingAddress": "（账单地址）", "province": "省份", "ward": "区", "phone": "电话号码", "job": "职位", "website": "网站", "district": "区", "placeholder": "选择客户"}, "updateError": {"title": "错误", "description": "无法更新机会，请重试。", "probability": "概率必须在0到100之间。", "representativeEmail": "邮箱格式不正确", "customer": "客户是必填项"}, "updateSuccess": {"title": "成功", "description": "机会已成功更新。"}, "updateCustomerError": {"title": "错误", "description": "无法更新客户，请重试。"}, "updateCustomerSuccess": {"title": "成功", "description": "客户已成功更新。"}, "createError": {"title": "错误", "description": "无法创建机会，请重试。"}, "createSuccess": {"title": "成功", "description": "机会已成功创建。"}}, "subscription": {"currentPlan": {"title": "当前方案：", "expiresOn": "到期日", "cancelSubscription": "取消订阅", "upgrade": "升级", "expired": "已过期"}, "expired": {"title": "已达到限制", "description": "您当前的方案对 AI 员工数量有限制。升级以创建更多 AI 员工吧。", "yourUsage": "使用量：", "upgradeButton": "升级", "seeAllPlans": "查看所有方案", "planTitle": "方案已过期", "planDescription": "您当前的方案已过期。要继续使用服务，请升级方案。", "expiredPlan": "过期时间：", "upgrade": "升级", "logout": "退出登录"}, "exceedQuota": {"title": "已达到限制", "description": "您当前的方案对 {{quotaType}} 数量有限制。升级以创建更多 {{quotaType}} 吧。", "yourUsage": "使用量：", "upgradeButton": "升级", "seeAllPlans": "查看所有方案", "quotaTypes": {"staff": "AI 员工", "knowledge_capacity": "知识", "message": "消息", "product": "商品", "order": "订单"}}, "usageStats": {"title": "使用统计", "messages": "消息", "staff": "AI 员工", "storage": "存储", "unlimited": "无限制"}, "pricing": {"save": "节省", "annually": "年付", "title": "价格", "description": "查看适合个人和企业的灵活价格方案。", "mostPopular": "最受欢迎", "upgrade": "升级", "more": "更多", "showLess": "收起"}, "billing": {"annualPlan": "年度方案", "savings": "节省 15%"}, "customPlan": {"title": "自定义方案", "description": "根据您的需求量身定制。功能、容量和费用灵活调整，适合有特殊需求的企业。", "contactInfo": "联系方式", "companyName": "公司名称", "companyNamePlaceholder": "请输入公司名称", "contactEmail": "联系邮箱", "emailPlaceholder": "请输入联系邮箱", "requirements": "需求", "messages": "消息", "staff": "AI 员工", "storage": "知识容量 (MB)", "currentMessages": "当前", "currentStaff": "当前", "currentStorage": "当前", "assistants": "AI 员工", "additionalRequirements": "其他需求", "additionalRequirementsPlaceholder": "请告诉我们您需要的功能或要求...", "included": "始终包含", "multilingualSupport": "多语言支持", "prioritySupport": "优先支持", "customIntegrations": "自定义集成", "submit": "提交自定义方案请求", "contact": "我们将在 24 小时内与您联系", "success": "自定义方案请求已发送！", "error": "无法发送自定义方案请求，请重试。", "emailRequired": "请输入联系邮箱", "companyRequired": "请输入公司名称", "features": {"productManagement": "商品管理", "orderManagement": "订单管理", "knowledgeManagement": "知识管理", "departmentManagement": "部门管理", "employeeManagement": "员工管理", "crm": "客户关系管理"}, "configuration": {"virtualAssistants": "AI 员工", "messages": "消息", "knowledgeCapacity": "知识容量 (MB)", "aiModel": "AI 模型", "multilingual": "多语言", "scheduleCustomerCare": "客户支持排班", "customIntegration": "自定义集成"}, "button": "联系我们", "dialog": {"title": "自定义套餐请求发送成功", "message": "我们已收到您的请求，将很快与您联系并提供更多详细信息。感谢您选择我们的服务。", "countdown": "{{countdown}} 秒后关闭..."}}, "faq": {"title": "常见问题", "description": "没有找到您想要的答案？", "description2": "请联系我们。", "q1": "可以访问吗？", "a1": "可以。我们遵循 WAI-ARIA 设计模板。", "q2": "可以自定义样式吗？", "a2": "可以。我们提供与其他组件匹配的默认样式。", "q3": "有动画效果吗？", "a3": "有。默认会生成动画，但您可以选择关闭。", "q4": "可以在我的网站上使用吗？", "a4": "可以。此组件可用于任何 React 应用程序。", "q5": "怎么开始使用？", "a5": "只需导入组件并在 JSX 中使用即可。"}}, "layouts": {"title": "レイアウト管理", "description": "不動産のフロアプランと単位マッピングを管理します", "totalLayouts": "合計レイアウト", "mappedUnits": "マッピングされた単位", "mapped": "マッピングされた", "unmapped": "未マッピング", "mappingStats": "マッピング統計", "selectLayout": "レイアウトを選択", "openMapping": "マッピングを開く", "floorPlans": "フロアプラン", "addLayout": "レイアウトを追加", "createLayout": "レイアウトを作成", "createLayoutDescription": "不動産の新しいフロアプランを作成します", "layoutNamePlaceholder": "例: 1階, 地下1階", "layoutDescriptionPlaceholder": "このレイアウトの説明", "searchLayouts": "レイアウトを検索...", "noLayouts": "レイアウトが見つかりません", "noLayoutsFound": "検索条件に一致するレイアウトが見つかりません", "createFirstLayout": "最初のレイアウトを作成して開始します", "tryDifferentSearch": "検索キーワードを変更してお試しください", "selectPropertyFirst": "不動産を選択してフロアプランを表示および管理します", "deleteLayout": "レイアウトを削除", "deleteLayoutConfirmation": "このレイアウトを削除してもよろしいですか？この操作は元に戻すことができません。", "saveSuccess": "レイアウトを保存しました", "deleteSuccess": "レイアウトを削除しました", "createSuccess": "レイアウトを作成しました", "dimensions": "寸法", "uploadImage": "フロアプランをアップロード", "uploadImageDescription": "フロアプランの画像をアップロードして単位のマッピングを開始します", "noLayoutSelected": "レイアウトが選択されていません", "selectLayoutToStart": "レイアウトを選択して単位のマッピングを開始します", "mappedToLayout": "レイアウトにマッピングされた", "assignUnit": "単位を割り当て", "selectUnit": "単位を選択", "mappingProgress": "マッピングの進行状況", "systemHealth": "システムの状態", "weeklyProgress": "週次の進行状況", "recentChanges": "最近の変更", "complete": "完了", "incomplete": "未完了", "withIssues": "問題がある", "duplicateAll": "すべてを複製", "exportAll": "すべてをエクスポート", "importAll": "すべてをインポート", "shareAll": "すべてを共有", "generateReport": "レポートを生成", "saveAsTemplate": "テンプレートとして保存", "viewReport": "レポートを表示", "lastActivity": "最終アクティビティ", "totalUnits": "合計単位", "exportSuccess": "データのエクスポートに成功しました", "exportError": "データのエクスポートに失敗しました", "importSuccess": "<PERSON><PERSON><PERSON><PERSON> thành công {{count}} b<PERSON> cục", "importError": "Lỗi nhập dữ liệu bố cục", "importParseError": "<PERSON><PERSON><PERSON> dạng tệp không hợp lệ. <PERSON><PERSON> lòng chọn tệp xuất bố cục hợp lệ.", "saveError": "Lỗi lưu bố cục: {{error}}", "deleteError": "Lỗi x<PERSON>a bố cục: {{error}}", "createError": "Lỗi tạo bố cục: {{error}}", "templateCreated": "Tạo mẫu thành công", "templateCreateError": "Lỗi tạo mẫu", "createTemplate": "Tạo mẫu", "createTemplateDescription": "<PERSON><PERSON><PERSON> bố cục này thành mẫu để sử dụng lại cho các bất động sản khác", "templateName": "<PERSON><PERSON>n mẫu", "templateNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên mẫu", "templateDescription": "<PERSON><PERSON>", "templateDescriptionPlaceholder": "<PERSON><PERSON> tả tùy chọn cho mẫu này", "templateCategory": "<PERSON><PERSON>", "categories": {"custom": "<PERSON><PERSON><PERSON> chỉnh", "residential": "<PERSON><PERSON> d<PERSON> c<PERSON>", "commercial": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại", "mixed": "Hỗn hợp"}, "overlapWarning": "<PERSON><PERSON><PERSON> b<PERSON>o chồng lấp", "overlapDetected": "<PERSON><PERSON><PERSON> {{count}} hình chồng lấp", "floorNumber": "<PERSON><PERSON> tầng", "floorNumberDescription": "Số tầng tùy chọn cho bố cục này", "dropImageHere": "<PERSON><PERSON><PERSON> hình ảnh mặt bằng vào đây hoặc nhấp để chọn", "supportedFormats": "Hỗ trợ JPG, PNG, SVG, WebP (tối đa 10MB)", "chooseFile": "<PERSON><PERSON><PERSON>", "imageUploaded": "<PERSON><PERSON><PERSON> hình <PERSON>nh thành công", "uploading": "<PERSON><PERSON> t<PERSON> lên", "invalidFileType": "<PERSON><PERSON> lòng chọn tệp hình <PERSON>nh hợp lệ", "fileTooLarge": "<PERSON><PERSON><PERSON> thư<PERSON><PERSON> tệp quá lớn. T<PERSON><PERSON> đa cho phép 10MB", "uploadFailed": "Lỗi tải lên hình <PERSON>nh", "dropUnitHere": "<PERSON>h<PERSON> đơn vị vào đây để đặt trên bố cục"}, "properties": {"title": "物业", "add": "添加物业", "selectProperty": "选择物业", "createProperty": "创建物业", "editProperty": "编辑物业", "basicInformation": "基本信息", "addressInformation": "地址信息", "ownerInformation": "业主信息", "purchaseInformation": "购买信息", "images": "物业图片", "updateSuccess": "物业更新成功", "createSuccess": "物业创建成功", "updateError": "无法更新物业", "createError": "无法创建物业", "headers": {"propertyInfo": "物业信息", "name": "物业名称", "address": "地址", "type": "类型", "description": "描述", "totalUnits": "总单元数", "status": "状态", "updatedAt": "更新时间", "actions": "操作", "image": "物业图片"}, "address": {"street": "街道地址", "city": "城市", "state": "省/州", "zipCode": "邮政编码", "country": "国家", "province": "省/州", "district": "区/县", "ward": "街道/市", "zip": "邮政编码"}, "owner": {"name": "业主姓名", "email": "业主邮箱", "phone": "业主电话"}, "purchase": {"price": "购买价格", "date": "购买日期"}, "types": {"residential": "住宅", "commercial": "商业", "mixed": "混合用途"}, "status": {"active": "活跃", "inactive": "非活跃", "maintenance": "维护中", "pending": "待处理"}, "placeholders": {"name": "输入物业名称", "type": "选择物业类型", "description": "输入物业描述", "street": "输入街道地址", "city": "输入城市", "state": "输入省/州", "zipCode": "输入邮政编码", "country": "输入国家", "ownerName": "输入业主姓名", "ownerEmail": "输入业主邮箱", "ownerPhone": "输入业主电话号码", "purchasePrice": "输入购买价格", "uploadImages": "点击上传图片或拖拽", "selectProvince": "选择省份", "selectDistrict": "选择区县", "selectWard": "选择街道", "zip": "输入邮政编码", "status": "选择物业状态"}, "filters": {"search": {"placeholder": "搜索物业..."}, "type": "物业类型", "status": "状态", "createdAt": "创建日期", "updatedAt": "更新日期"}, "actions": {"addManual": "手动添加物业", "view": "查看", "edit": "编辑", "delete": "删除", "save": "保存", "cancel": "取消", "submit": "提交"}, "deleteSuccess": "物业删除成功", "deleteError": "无法删除物业", "loadError": "无法加载物业信息", "deleteProperty": "删除物业", "deleteConfirmation": "您确定要删除 {{name}} 吗？此操作无法撤销。", "noImages": "暂无图片", "unitStatus": "单元状态概览", "confirmDelete": "您确定要删除此物业吗？", "confirmDeleteDescription": "此操作无法撤销。物业将被永久删除。", "backToList": "返回物业列表", "saveChanges": "保存更改", "discardChanges": "放弃更改", "stats": {"totalUnits": "总单元数", "occupancyRate": "入住率", "occupiedUnits": "已入住单元", "availableUnits": "可用单元"}, "components": {"propertyBasicInfo": {"type": "类型", "totalUnits": "总单元数", "description": "描述"}, "propertyImages": {"noImages": "暂无图片", "propertyImage": "物业图片"}, "propertyOwnerInfo": {"ownerInformation": "业主信息", "ownerName": "业主姓名", "ownerEmail": "业主邮箱", "ownerPhone": "业主电话", "purchasePrice": "购买价格", "purchaseDate": "购买日期"}, "propertyHeader": {"edit": "编辑", "delete": "删除"}, "propertyStatsCards": {"totalUnits": "总单元数", "occupiedUnits": "已入住单元", "availableUnits": "可用单元", "maintenance": "维护", "occupancyRate": "入住率"}}}, "contracts": {"title": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> đồng", "search": "<PERSON><PERSON><PERSON> kiếm hợp đồng...", "searchPlaceholder": "<PERSON><PERSON><PERSON> kiếm theo số hợp đồng", "export": "<PERSON><PERSON><PERSON> dữ liệu", "confirmDelete": "Bạn có chắc chắn muốn xóa hợp đồng nà<PERSON>?", "viewContracts": "<PERSON><PERSON> đ<PERSON>", "headers": {"contractInfo": "<PERSON><PERSON><PERSON><PERSON> tin hợp đồng", "tenant": "<PERSON><PERSON><PERSON><PERSON> thuê", "property": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "duration": "<PERSON><PERSON><PERSON><PERSON> hạn", "rentAmount": "<PERSON><PERSON><PERSON> thu<PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>"}, "filters": {"status": "<PERSON><PERSON><PERSON><PERSON> thái", "contractType": "<PERSON><PERSON><PERSON> h<PERSON> đ<PERSON>ng", "property": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "startDate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "endDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "clear": "Xóa bộ lọc"}, "status": {"draft": "<PERSON><PERSON><PERSON>", "active": "<PERSON><PERSON> l<PERSON>", "expired": "<PERSON><PERSON><PERSON>", "terminated": "<PERSON><PERSON> hủy", "pending": "<PERSON>ờ <PERSON>"}, "types": {"monthly": "<PERSON>", "annual": "<PERSON>", "profitSharing": "<PERSON><PERSON> sẻ lợi nhuận", "revenueSharing": "<PERSON>a sẻ doanh thu"}, "actions": {"view": "Xem", "edit": "Chỉnh sửa", "delete": "Xóa"}, "createContract": "<PERSON><PERSON><PERSON> đ<PERSON>", "editContract": "Chỉnh sửa hợp đồng", "createDescription": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> đồng bất động sản mới với các điều khoản linh hoạt", "editDescription": "<PERSON><PERSON><PERSON> nhật chi tiết và điều kho<PERSON>n hợp đồng", "sections": {"propertyUnit": "Chọn Bất động sản & <PERSON><PERSON><PERSON> hộ", "contractDetails": "<PERSON> tiết hợp đồng", "financialTerms": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>n tài ch<PERSON>h", "termsConditions": "<PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON> ki<PERSON>n"}, "fields": {"property": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "unit": "<PERSON><PERSON><PERSON>", "tenant": "<PERSON><PERSON><PERSON><PERSON> thuê", "contractType": "<PERSON><PERSON><PERSON> h<PERSON> đ<PERSON>ng", "startDate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "endDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "rentDueDay": "<PERSON><PERSON><PERSON> ti<PERSON>n", "rentAmount": "<PERSON><PERSON> tiền thuê", "depositAmount": "Tiền đặt cọc", "lateFeeAmount": "<PERSON><PERSON> chậm trễ", "noticePeriodDays": "<PERSON><PERSON>ờ<PERSON> gian b<PERSON><PERSON> tr<PERSON> (ngày)", "autoRenewal": "<PERSON><PERSON> hạn tự động", "profitSharingPercentage": "<PERSON><PERSON><PERSON> trăm chia sẻ lợi nhuận", "revenueSharingPercentage": "<PERSON><PERSON><PERSON> tr<PERSON>m chia sẻ doanh thu", "termsConditions": "<PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON> ki<PERSON>n"}, "placeholders": {"property": "<PERSON><PERSON><PERSON> bất động sản", "unit": "<PERSON><PERSON><PERSON> c<PERSON>n hộ", "tenant": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> thuê", "contractType": "<PERSON><PERSON><PERSON> lo<PERSON>i hợp đồng", "rentDueDay": "<PERSON><PERSON><PERSON><PERSON><PERSON> (1-31)", "rentAmount": "<PERSON><PERSON><PERSON><PERSON> số tiền thuê hàng tháng", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> tiền đặt cọc", "lateFeeAmount": "<PERSON><PERSON><PERSON><PERSON> phí chậm tr<PERSON> (t<PERSON><PERSON> ch<PERSON>n)", "noticePeriodDays": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>i gian b<PERSON> tr<PERSON> (ngày)", "profitSharingPercentage": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> trăm (0-100)", "revenueSharingPercentage": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> trăm (0-100)", "termsConditions": "<PERSON><PERSON><PERSON><PERSON> điều khoản và điều kiện hợp đồng"}, "contractTypes": {"monthly": "<PERSON><PERSON><PERSON> theo tháng", "annual": "<PERSON><PERSON><PERSON> the<PERSON> n<PERSON>", "profitSharing": "<PERSON><PERSON> sẻ lợi nhuận", "revenueSharing": "<PERSON>a sẻ doanh thu"}, "descriptions": {"autoRenewal": "Tự động gia hạn hợp đồng khi hết hạn"}, "messages": {"createSuccess": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> đồng thành công", "createError": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> đồng thất bại", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t hợp đồng thành công", "updateError": "<PERSON><PERSON><PERSON> nhật hợp đồng thất bại"}, "contractDetails": "<PERSON>", "overview": "<PERSON><PERSON><PERSON>", "paymentHistory": "<PERSON><PERSON><PERSON>"}, "reports": {"title": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> B<PERSON><PERSON> động sản", "description": "<PERSON><PERSON><PERSON> báo cáo toàn diện và phân tích hiệu suất bất động sản", "generateReport": "Tạo Báo cáo", "downloadReport": "<PERSON><PERSON><PERSON>", "generating": "<PERSON><PERSON> tạo...", "categories": {"financial": {"title": "Báo c<PERSON>o T<PERSON> ch<PERSON>", "description": "<PERSON><PERSON> tích doanh thu, chi phí và lợi nhuận", "generate": "Tạo"}, "occupancy": {"title": "<PERSON><PERSON><PERSON> cáo Tỷ lệ <PERSON>p đ<PERSON>y", "description": "Tỷ lệ trống và giữ chân khách thuê", "generate": "Tạo"}, "performance": {"title": "<PERSON><PERSON> tích <PERSON>", "description": "Chỉ số hiệu suất bất động sản và đơn vị", "generate": "Tạo"}, "maintenance": {"title": "Báo c<PERSON>o <PERSON> trì", "description": "Chi phí bảo trì và xu hướng", "generate": "Tạo"}, "tenant": {"title": "<PERSON><PERSON> tích <PERSON>ch thuê", "description": "Thông tin nhân khẩu học và hành vi khách thuê", "generate": "Tạo"}, "custom": {"title": "Báo cáo Tù<PERSON> chỉnh", "description": "<PERSON>â<PERSON> dựng báo cáo tùy chỉnh với bộ lọc", "create": "Tạo"}}, "comingSoon": {"title": "<PERSON><PERSON> thống <PERSON> c<PERSON>o <PERSON> cao - Sắp ra mắt", "description": "<PERSON><PERSON> tích toà<PERSON>, báo cáo tùy chỉnh và tính năng xuất dữ liệu sẽ có sẵn tại đây."}}, "dashboard": {"propertyAssets": {"title": "<PERSON>ảng điều khiển tài sản bất động sản", "description": "Tổng quan hiệu suất danh mục bất động sản và các chỉ số chính"}, "metrics": {"totalProperties": "<PERSON>ổng số bất động sản", "totalUnits": "<PERSON><PERSON>ng số căn hộ", "activeContracts": "<PERSON><PERSON><PERSON> đồng đang hoạt động", "monthlyRevenue": "<PERSON><PERSON><PERSON> thu hàng tháng"}, "charts": {"occupancyOverview": "T<PERSON>ng quan tỷ lệ lấp đầy"}, "occupancy": {"occupied": "<PERSON><PERSON> thuê", "vacant": "<PERSON><PERSON><PERSON><PERSON>", "rate": "Tỷ lệ lấp đầy", "tenants": "<PERSON>ổng số khách thuê"}, "quickActions": {"title": "<PERSON><PERSON> t<PERSON> n<PERSON>h", "addProperty": "<PERSON><PERSON><PERSON><PERSON> bất động sản", "addUnit": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>n hộ", "createContract": "<PERSON><PERSON><PERSON> đ<PERSON>", "addTenant": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>ch thuê"}, "recentActivities": {"title": "<PERSON><PERSON><PERSON> động gần đây", "viewAll": "<PERSON><PERSON> tất cả hoạt động"}, "portfolio": {"title": "<PERSON><PERSON><PERSON> t<PERSON>t danh mục", "properties": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "units": "<PERSON><PERSON><PERSON>", "contracts": "<PERSON><PERSON><PERSON>", "revenue": "<PERSON><PERSON>h thu", "performance": "<PERSON><PERSON><PERSON> su<PERSON>t tổng thể"}}, "units": {"title": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "addUnit": "<PERSON><PERSON><PERSON><PERSON>", "viewUnits": "<PERSON><PERSON>", "createUnit": "<PERSON><PERSON><PERSON>", "editUnit": "Chỉnh <PERSON><PERSON><PERSON>", "createDescription": "<PERSON><PERSON><PERSON> c<PERSON>n hộ cho thuê mới", "editDescription": "<PERSON><PERSON><PERSON> nhật thông tin căn hộ", "deleteUnit": "<PERSON><PERSON><PERSON>", "deleteConfirmation": "Bạn có chắc chắn muốn xóa căn hộ {{unit}}? Hành động này không thể hoàn tác.", "deleteSuccess": "<PERSON><PERSON><PERSON> căn hộ thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa căn hộ", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t căn hộ thành công", "updateError": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật căn hộ: {{error}}", "createSuccess": "<PERSON><PERSON><PERSON> c<PERSON>n hộ thành công", "createError": "<PERSON><PERSON><PERSON><PERSON> thể tạo căn hộ", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải căn hộ", "unitNumber": "<PERSON><PERSON><PERSON>", "noImages": "<PERSON><PERSON><PERSON><PERSON> có hình <PERSON>nh", "noUnitsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy căn hộ", "noPropertiesAvailable": "<PERSON><PERSON><PERSON><PERSON> có bất động sản", "searchUnits": "<PERSON><PERSON><PERSON> kiếm căn hộ...", "unitList": "<PERSON><PERSON>", "assignUnit": "<PERSON><PERSON>", "selectUnit": "<PERSON><PERSON><PERSON> c<PERSON>n hộ", "basicInformation": "Thông Tin Cơ Bản", "specifications": "Thông Số K<PERSON>hu<PERSON>", "financialInformation": "Thông Tin Tài <PERSON>", "amenities": "<PERSON><PERSON><PERSON><PERSON>", "images": "<PERSON><PERSON><PERSON> Ảnh", "headers": {"unitInfo": "<PERSON><PERSON>ông Tin <PERSON>", "property": "<PERSON><PERSON><PERSON>", "unitNumber": "Số Căn Hộ", "type": "<PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON>", "rent": "<PERSON><PERSON><PERSON><PERSON>", "status": "Trạng <PERSON>", "tenant": "<PERSON><PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>", "floor": "<PERSON><PERSON><PERSON>", "squareFootage": "<PERSON><PERSON><PERSON> (m²)", "bedrooms": "<PERSON><PERSON>ng <PERSON>", "bathrooms": "Phòng Tắm", "rentAmount": "<PERSON><PERSON>", "depositAmount": "<PERSON><PERSON><PERSON><PERSON>"}, "filters": {"type": "<PERSON><PERSON><PERSON>", "status": "Trạng <PERSON>", "property": "<PERSON><PERSON><PERSON>", "rentRange": "Khoảng G<PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "search": {"placeholder": "<PERSON><PERSON><PERSON> kiếm căn hộ..."}}, "actions": {"addManual": "<PERSON><PERSON><PERSON><PERSON> Cô<PERSON>"}, "status": {"available": "<PERSON><PERSON> Sẵn", "occupied": "<PERSON><PERSON>", "maintenance": "Bảo Trì", "inactive": "Không Hoạt Động"}, "types": {"studio": "Studio", "1br": "1 Phòng Ngủ", "2br": "2 Phòng Ngủ", "3br": "3 Phòng Ngủ", "commercial": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "office": "<PERSON><PERSON><PERSON>", "retail": "B<PERSON> Lẻ"}, "placeholders": {"property": "<PERSON><PERSON><PERSON> bất động sản", "unitNumber": "<PERSON><PERSON><PERSON><PERSON> số căn hộ", "type": "<PERSON><PERSON><PERSON> lo<PERSON>i căn hộ", "description": "<PERSON><PERSON><PERSON><PERSON> mô tả căn hộ", "floor": "<PERSON><PERSON><PERSON><PERSON> số tầng", "squareFootage": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>ch", "bedrooms": "Số phòng ngủ", "bathrooms": "Số phòng tắm", "rentAmount": "<PERSON><PERSON><PERSON><PERSON> số tiền thuê", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> ti<PERSON> c<PERSON>c", "uploadImages": "<PERSON><PERSON><PERSON><PERSON> để tải lên hình <PERSON>nh căn hộ"}}, "tenants": {"title": "<PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "editTenant": "Chỉnh <PERSON><PERSON><PERSON>", "createTenant": "<PERSON><PERSON><PERSON>", "editDescription": "<PERSON><PERSON><PERSON> nhật thông tin người thuê", "createDescription": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i thuê mới", "searchPlaceholder": "<PERSON><PERSON><PERSON> kiếm người thuê...", "viewTenant": "<PERSON><PERSON>", "noTenant": "<PERSON><PERSON><PERSON><PERSON>", "sections": {"personalInfo": "Thông Tin Cá Nhân", "identification": "<PERSON><PERSON><PERSON><PERSON>ờ Tùy Thân", "employment": "<PERSON><PERSON><PERSON>", "emergencyContact": "<PERSON><PERSON><PERSON>"}, "fields": {"fullName": "<PERSON><PERSON>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Họ", "email": "Email", "phone": "<PERSON><PERSON><PERSON><PERSON>", "dateOfBirth": "<PERSON><PERSON><PERSON>", "status": "Trạng <PERSON>", "identificationType": "Loại Giấ<PERSON>", "identificationNumber": "Số Giấy Tờ", "employmentStatus": "Tình Trạng Công <PERSON>", "employerName": "<PERSON><PERSON><PERSON>", "monthlyIncome": "<PERSON><PERSON><PERSON>", "emergencyContactName": "<PERSON><PERSON><PERSON>", "emergencyContactPhone": "Số Đ<PERSON>"}, "headers": {"tenantInfo": "T<PERSON>ông Tin Ngườ<PERSON>", "contact": "<PERSON><PERSON><PERSON>", "employmentStatus": "Tình Trạng Công <PERSON>", "status": "Trạng <PERSON>", "emergencyContact": "<PERSON><PERSON><PERSON>", "joinDate": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>"}, "filters": {"leaseStatus": "<PERSON>rạng <PERSON>", "property": "<PERSON><PERSON><PERSON>"}, "status": {"active": "Ho<PERSON><PERSON>", "inactive": "Không Hoạt Động", "expired": "<PERSON><PERSON><PERSON>", "pending": "Chờ <PERSON>"}, "employmentStatus": {"employed": "<PERSON><PERSON>", "unemployed": "<PERSON><PERSON><PERSON><PERSON>", "self_employed": "<PERSON><PERSON>", "student": "<PERSON><PERSON> Viê<PERSON>"}, "actions": {"addManual": "<PERSON><PERSON><PERSON><PERSON>"}, "stats": {"totalContracts": "Tổng Số H<PERSON>", "activeContracts": "<PERSON><PERSON><PERSON>", "joinDate": "<PERSON><PERSON><PERSON>"}, "quickStats": "<PERSON><PERSON><PERSON><PERSON>", "activeContracts": "<PERSON><PERSON><PERSON>", "identificationTypes": {"passport": "<PERSON><PERSON>", "national_id": "CMND/CCCD", "driver_license": "Bằng Lái Xe"}, "errors": {"notFound": "<PERSON><PERSON><PERSON><PERSON>", "notFoundDescription": "<PERSON>ười thuê mà bạn đang tìm không tồn tại hoặc đã bị xóa."}, "documents": "<PERSON><PERSON><PERSON>", "deleteConfirmation": {"title": "<PERSON><PERSON><PERSON>", "description": "Bạn có chắc chắn muốn xóa người thuê này? Hành động này không thể hoàn tác.", "hasActiveContracts": "<PERSON><PERSON><PERSON>i thuê này có hợp đồng đang hoạt động. Bạn có chắc chắn muốn xóa?"}, "messages": {"deleteSuccess": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i thuê thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa người thuê"}}, "maintenance": {"title": "Bảo Trì", "add": "<PERSON><PERSON><PERSON>", "search": "T<PERSON><PERSON> kiếm bảo trì...", "searchPlaceholder": "T<PERSON><PERSON> kiếm theo tiêu đề yêu cầu", "export": "<PERSON><PERSON><PERSON>", "confirmDelete": "Bạn có chắc chắn muốn xóa yêu cầu bảo trì này?", "headers": {"requestInfo": "<PERSON><PERSON><PERSON><PERSON>", "property": "<PERSON><PERSON><PERSON>", "requester": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "priority": "Đ<PERSON> Ưu Tiên", "status": "Trạng <PERSON>", "assignedTo": "<PERSON><PERSON>", "estimatedCost": "Chi Phí Ước Tính", "dueDate": "<PERSON><PERSON><PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>"}, "filters": {"status": "Trạng <PERSON>", "priority": "Đ<PERSON> Ưu Tiên", "category": "<PERSON><PERSON>", "property": "<PERSON><PERSON><PERSON>"}, "priorities": {"low": "<PERSON><PERSON><PERSON><PERSON>", "medium": "<PERSON><PERSON>", "high": "<PERSON>", "urgent": "<PERSON><PERSON><PERSON><PERSON>"}, "categories": {"plumbing": "Ống Nước", "electrical": "<PERSON><PERSON><PERSON><PERSON>", "hvac": "<PERSON><PERSON><PERSON><PERSON>", "structural": "<PERSON><PERSON><PERSON>", "appliance": "<PERSON><PERSON><PERSON>t Bị", "cosmetic": "<PERSON><PERSON><PERSON><PERSON>", "cleaning": "<PERSON><PERSON>", "security": "<PERSON>", "general": "<PERSON>", "other": "K<PERSON><PERSON><PERSON>"}, "actions": {"addManual": "<PERSON><PERSON><PERSON><PERSON>", "view": "Xem", "edit": "Chỉnh Sửa", "delete": "Xóa"}, "createRequest": "<PERSON><PERSON><PERSON>", "editRequest": "Chỉnh <PERSON><PERSON><PERSON>", "createDescription": "<PERSON><PERSON><PERSON> yêu cầu bảo trì mới cho bất động sản của bạn", "editDescription": "<PERSON><PERSON><PERSON> nhật chi tiết yêu cầu bảo trì", "sections": {"propertyUnit": "Lựa Chọn Bất Động Sản & Căn Hộ", "requestDetails": "<PERSON>", "scheduling": "Lập L<PERSON> & Nhà Thầu", "costInfo": "Thông Tin Chi Phí", "additional": "T<PERSON>ông Tin Bổ Sung"}, "fields": {"property": "<PERSON><PERSON><PERSON>", "unit": "<PERSON><PERSON><PERSON>", "tenant": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON>i<PERSON><PERSON>", "description": "<PERSON><PERSON>", "priority": "Đ<PERSON> Ưu Tiên", "category": "<PERSON><PERSON>", "status": "Trạng <PERSON>", "scheduledDate": "<PERSON><PERSON><PERSON>", "completedDate": "<PERSON><PERSON><PERSON>", "contractorName": "<PERSON><PERSON><PERSON>", "contractorPhone": "Số Điện Thoại Nhà <PERSON>hầ<PERSON>", "estimatedCost": "Chi Phí Ước Tính ($)", "actualCost": "<PERSON> Phí <PERSON> ($)", "notes": "<PERSON><PERSON>", "images": "<PERSON><PERSON><PERSON>"}, "placeholders": {"property": "<PERSON><PERSON><PERSON> bất động sản", "unit": "<PERSON><PERSON><PERSON> c<PERSON>n hộ", "tenant": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> thuê", "title": "<PERSON><PERSON> tả ngắn gọn về vấn đề", "description": "<PERSON><PERSON> tả chi tiết về vấn đề bảo trì", "priority": "<PERSON><PERSON><PERSON> mức độ ưu tiên", "category": "<PERSON><PERSON><PERSON> danh mục", "status": "<PERSON><PERSON><PERSON> trạng thái", "contractorName": "<PERSON><PERSON><PERSON><PERSON> tên nhà thầu", "contractorPhone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại nhà thầu", "estimatedCost": "0.00", "actualCost": "0.00", "notes": "<PERSON><PERSON> chú hoặc hướng dẫn bổ sung"}, "options": {"propertyWide": "<PERSON><PERSON><PERSON> trì toàn bộ bất động sản", "noTenant": "<PERSON><PERSON><PERSON><PERSON> có người thuê cụ thể"}, "status": {"open": "Mở", "in_progress": "<PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON>", "cancelled": "Đã <PERSON>", "inProgress": "<PERSON><PERSON>"}, "messages": {"createSuccess": "<PERSON><PERSON><PERSON> y<PERSON>u cầu bảo trì thành công", "createError": "<PERSON><PERSON><PERSON><PERSON> thể tạo yêu cầu bảo trì", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t yêu cầu bảo trì thành công", "updateError": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật yêu cầu bảo trì"}, "viewRequests": "<PERSON><PERSON>"}, "payments": {"headers": {"paymentInfo": "Thông Tin Thanh Toán", "amount": "Số Tiền", "contract": "<PERSON><PERSON><PERSON>", "property": "<PERSON><PERSON><PERSON>", "status": "Trạng <PERSON>", "paymentDate": "<PERSON><PERSON><PERSON>", "createdAt": "Tạo Lú<PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>"}, "status": {"pending": "<PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON><PERSON>", "refunded": "<PERSON><PERSON><PERSON>"}}, "pipelines": {"headers": {"activities": "<PERSON><PERSON><PERSON> đ<PERSON>", "amount": "Số lượng", "assignee": "Ngườ<PERSON> phụ trách", "closeDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c  ", "createdAt": "<PERSON><PERSON><PERSON>", "customer": "<PERSON><PERSON><PERSON><PERSON>", "expectedClosingDate": "<PERSON><PERSON><PERSON> kết thúc dự kiến", "expectedRevenue": "<PERSON><PERSON><PERSON> thu dự kiến", "opportunity": "<PERSON><PERSON> hội", "pipeline": "<PERSON><PERSON><PERSON> đo<PERSON>n xử lý", "probability": "<PERSON><PERSON><PERSON>", "stage": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>"}}}, "table": {"pagination": {"rowsPerPage": "<PERSON><PERSON> hàng trên trang", "description": "{{start}} đến {{end}} hàng trong tổng số {{total}}", "next": "<PERSON><PERSON><PERSON><PERSON>", "previous": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "selected": {"title": "<PERSON><PERSON> ch<PERSON>n", "delete": "Xóa {{count}} đ<PERSON> chọn"}, "export": {"title": "<PERSON><PERSON><PERSON> dữ liệu", "description": "<PERSON><PERSON><PERSON> dữ liệu", "confirm": "<PERSON><PERSON><PERSON> dữ liệu", "cancel": "<PERSON><PERSON><PERSON>"}, "filter": {"clearFilter": "Xóa bộ lọc", "loadMore": "<PERSON><PERSON><PERSON>ê<PERSON>"}, "savedFilters": {"FilterTypeRequired": "Loại bộ lọc là bắt buộc", "NoFiltersToSave": "<PERSON><PERSON><PERSON><PERSON> có bộ lọc để lưu", "CreateFilterSuccess": "<PERSON><PERSON> lọc đã đ<PERSON><PERSON><PERSON> lưu thành công", "CreateFilterFail": "<PERSON><PERSON><PERSON><PERSON> thể lưu bộ lọc", "UpdateFilterSuccess": "<PERSON><PERSON> lọc đã đư<PERSON><PERSON> cập nhật thành công", "DeleteFilterSuccess": "<PERSON><PERSON> lọc đã đư<PERSON><PERSON> xóa thành công", "DeleteFilterFail": "<PERSON><PERSON><PERSON><PERSON> thể xóa bộ lọc", "UpdateFilterFail": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật bộ lọc", "UpdateFilter": "<PERSON><PERSON> lọc đã đ<PERSON><PERSON><PERSON> cập nh<PERSON>t", "settings": {"settings": "Cài đặt", "title": "Cài đặt tab", "description": "6 tab đầu tiên sẽ được ưu tiên hiển thị trên trang chính. Tùy chỉnh chúng dựa trên nhu cầu của bạn.", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tên tab", "delete": {"title": "Xóa bộ lọc đã lưu", "description": "Bạn có chắc chắn muốn xóa bộ lọc đã lưu này? Hành động này không thể hoàn tác.", "confirm": "Xóa"}}}}, "validation": {"titleRequired": "タイトルは必須です", "required": "このフィールドは必須です", "invalidEmail": "有効なメールアドレスを入力してください", "minLength": "少なくとも{{count}}文字以上必要です", "maxLength": "{{count}}文字以内にしてください", "passwordMismatch": "パスワードが一致しません", "invalidUsername": "ユーザー名は3文字以上必要です", "emailRequired": "メールアドレスを入力してください", "usernameRequired": "ユーザー名を入力してください", "passwordRequired": "パスワードを入力してください", "confirmPasswordRequired": "パスワードを確認してください", "invalidPassword": "パスワードは8文字以上必要です", "passwordsDoNotMatch": "パスワードが一致しません", "verificationCodeRequired": "確認コードを入力してください", "verificationCodeLength": "確認コードは6文字必要です", "sessionRequired": "このフィールドは必須です", "usernameSpecialCharacters": "ユーザー名は英数字とアンダースコアのみ使用できます", "skuFormat": "SKUは英数字とアンダースコアのみ使用できます", "skuRequired": "SKUは必須です", "nameRequired": "表示効率を最適化するために、商品名を入力してください", "nameTooLong": "商品名は250文字以内にしてください", "titleTooLong": "タイトルは100文字以内にしてください", "priceRequired": "価格は必須です", "imageRequired": "少なくとも1枚の画像を追加してください", "imageFormat": "画像の形式が無効です", "priceMustBePositive": "正の価格を入力してください", "invalidPrice": "価格が無効です", "wrongUsernameOrPassword": "ユーザー名またはパスワードが間違っています", "phoneRequired": "電話番号は必須です", "phoneNumberAlreadyExists": "電話番号がすでに存在します", "contactRequired": "連絡先は必須です", "customerRequired": "顧客は必須です", "emailOrPhoneRequired": "メールアドレスまたは電話番号は必須です", "activityTypeRequired": "アクティビティの種類は必須です", "summaryRequired": "概要は必須です", "propertyRequired": "不動産は必須です", "unitRequired": "単位は必須です", "tenantRequired": "顧客は必須です", "contractTypeRequired": "契約の種類は必須です", "startDateRequired": "開始日は必須です", "rentAmountMustBePositive": "賃料は正の数で入力してください", "depositAmountMustBePositive": "預金額は正の数で入力してください", "lateFeeAmountMustBePositive": "遅延料は正の数で入力してください", "rentDueDayMin": "支払い日は1日以上で入力してください", "rentDueDayMax": "支払い日は31日以内で入力してください", "percentageMax": "割合は100%以内にしてください", "noticePeriodMustBePositive": "通知期間は正の数で入力してください", "sharingPercentageRequired": "この契約の種類では、共有割合は必須です", "idRequired": "IDは必須です"}, "footer": {"crafted": "OneXAPIsによって作成されました", "by": "by", "team": "OneXAPIsチーム", "heart": "heart"}, "install": {"installing": "インストール中...", "pleaseWait": "しばらくお待ちください", "error": {"backToHome": "ホームに戻る", "notFound": "お探しのページが見つかりません", "installationFailed": "インストールに失敗しました", "missingSourceChannel": "ソースチャンネルが見つかりません", "authorizeDestination": "{channel_name}の認証を行ってください"}}, "error": {"backToHome": "ホームに戻る", "notFound": "ページが見つかりません", "notFoundDescription": "お探しのページが見つかりません。"}, "socialIntegration": {"authorize": "認証", "reAuthorize": "再認証", "newAuthorize": "新規認証", "authorized": "認証済み", "sessionExpired": "セッションが期限切れです", "failedToSwitchStatus": "接続状態の変更に失敗しました", "failedToSetup": "接続の設定に失敗しました", "facebookSetup": "Facebookの連携を設定", "zaloSetup": "Zalo OAの連携を設定", "defaultSetup": "連携を設定"}, "filter": {"allTime": "すべての時間", "today": "今日", "yesterday": "昨日", "lastWeek": "先週", "lastMonth": "先月", "last7Days": "先週", "last30Days": "先月", "last90Days": "先月", "thisWeek": "今週", "thisMonth": "今月", "thisYear": "今年", "customize": "カスタマイズ", "reset": "リセット", "apply": "適用"}, "financial": {"dashboard": {"title": "財務ダッシュボード", "description": "不動産の財務パフォーマンスと指標の概要"}, "timeRange": {"month": "月", "quarter": "四半期", "year": "年"}, "exportReport": "レポートをエクスポート", "metrics": {"totalRevenue": "総収入", "netIncome": "純収入", "occupancyRate": "稼働率", "totalExpenses": "総コスト"}, "charts": {"revenueTrend": "収益のトレンド", "expenseBreakdown": "コスト分析", "occupancyTrend": "稼働率のトレンド", "revenueAnalysis": "収益分析"}, "revenue": "<PERSON><PERSON>h thu", "netIncome": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> ròng", "performance": "<PERSON><PERSON><PERSON>", "profitMargin": "Tỷ suất lợi n<PERSON>n", "totalProperties": "<PERSON>ổng số bất động sản", "totalUnits": "<PERSON><PERSON>ng số căn hộ", "occupiedUnits": "<PERSON><PERSON><PERSON> hộ đã thuê", "vacancyRate": "Tỷ lệ trống", "ytdPerformance": "<PERSON><PERSON><PERSON> suất từ đầu năm", "topProperties": "<PERSON><PERSON><PERSON> động sản hiệu suất cao", "monthlyRevenue": "doanh thu hàng tháng", "expenses": "Chi phí", "alerts": {"title": "<PERSON><PERSON><PERSON> b<PERSON>o tài ch<PERSON>h", "rentDue": "<PERSON><PERSON><PERSON> đến hạn thu tiền thuê", "highExpenses": "<PERSON><PERSON><PERSON> báo chi phí cao", "leaseExpiring": "<PERSON><PERSON><PERSON> đồng sắ<PERSON> hết hạn"}, "revenueAnalytics": {"title": "<PERSON><PERSON> tích doanh thu", "description": "<PERSON><PERSON> tích doanh thu chi tiết với biểu đồ tương tác và xu hướng"}, "expenseAnalysis": {"title": "<PERSON>ân tích chi phí", "description": "<PERSON><PERSON> tích chi phí toàn diện và thông tin tối ưu hóa chi phí"}, "profitAnalysis": {"title": "<PERSON><PERSON> tích lợi n<PERSON>n", "description": "<PERSON> hướng lợi nhuận, biên lợi nhuận và theo dõi hiệu suất ROI"}}, "contracts": {"contractDetails": "<PERSON>", "contractId": "<PERSON><PERSON>", "overview": "<PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON><PERSON>", "progress": "<PERSON>i<PERSON><PERSON>", "daysRemaining": "ng<PERSON><PERSON> còn lại", "openEnded": "<PERSON><PERSON><PERSON>ng giới hạn thời gian", "autoRenewalEnabled": "<PERSON><PERSON> hạn tự động đ<PERSON><PERSON><PERSON> bật", "perMonth": "mỗi tháng", "dayOfMonth": " của mỗi tháng", "sharingTerms": "<PERSON><PERSON><PERSON><PERSON> Sẻ", "paymentSummary": "<PERSON><PERSON>ng <PERSON>", "revenueProjections": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON>ông Tin <PERSON>", "days": "ng<PERSON>y", "quickActions": "<PERSON><PERSON>", "fields": {"property": "<PERSON><PERSON><PERSON>", "unit": "<PERSON><PERSON><PERSON>", "tenant": "<PERSON><PERSON><PERSON><PERSON>", "startDate": "<PERSON><PERSON><PERSON>", "endDate": "<PERSON><PERSON><PERSON>", "rentAmount": "<PERSON><PERSON>", "depositAmount": "Tiền Đặt Cọc", "lateFeeAmount": "<PERSON><PERSON>", "rentDueDay": "<PERSON><PERSON><PERSON>", "profitSharingPercentage": "Phần Tr<PERSON><PERSON> Sẻ <PERSON>ợ<PERSON>n", "revenueSharingPercentage": "<PERSON>ần <PERSON>r<PERSON><PERSON> Sẻ <PERSON>h <PERSON>hu", "noticePeriod": "Thời G<PERSON>"}, "sections": {"financialTerms": "<PERSON><PERSON><PERSON><PERSON>", "termsConditions": "<PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON>"}, "stats": {"totalPayments": "Tổng Thanh Toán", "completedPayments": "<PERSON><PERSON>", "pendingPayments": "Chờ Thanh To<PERSON>", "totalCollected": "<PERSON><PERSON><PERSON>"}, "projections": {"expectedMonthly": "<PERSON><PERSON><PERSON>", "expectedAnnual": "<PERSON><PERSON><PERSON>"}, "actions": {"recordPayment": "<PERSON><PERSON> <PERSON>", "viewPayments": "<PERSON><PERSON>"}, "errors": {"notFound": "<PERSON><PERSON><PERSON><PERSON>", "notFoundDescription": "<PERSON><PERSON><PERSON> đồng bạn đang tìm không tồn tại hoặc đã bị xóa."}, "deleteConfirmation": {"title": "<PERSON><PERSON><PERSON>", "description": "Bạn có chắc chắn muốn xóa hợp đồng này? Hành động này không thể hoàn tác.", "activeContract": "<PERSON><PERSON><PERSON> là hợp đồng đang hoạt động. Việ<PERSON> xóa có thể ảnh hưởng đến các thanh toán và ghi chép hiện tại."}, "messages": {"deleteSuccess": "<PERSON><PERSON><PERSON> hợp đồng thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa hợp đồng"}}, "unit": {"title": "<PERSON><PERSON><PERSON>", "type": {"apartment": "<PERSON><PERSON><PERSON>", "studio": "Studio", "1br": "1 Phòng Ngủ", "2br": "2 Phòng Ngủ", "3br": "3 Phòng Ngủ", "4br": "4 Phòng Ngủ", "penthouse": "Penthouse", "duplex": "Duplex", "commercial": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "office": "<PERSON><PERSON><PERSON>", "retail": "B<PERSON> Lẻ", "warehouse": "<PERSON><PERSON>"}}, "numbers": {"abbreviations": {"thousand": "K", "million": "tr", "billion": "tỷ", "trillion": "nghìn tỷ"}, "currency": {"symbol": "₫", "code": "VND"}}, "payments": {"createPayment": "<PERSON><PERSON><PERSON>", "editPayment": "Chỉnh <PERSON><PERSON><PERSON>", "createDescription": "<PERSON><PERSON><PERSON> bản ghi <PERSON>h toán mới cho tiền thuê, tiền đặt cọc và các khoản phí khác", "editDescription": "<PERSON><PERSON><PERSON> nhật thông tin thanh toán và trạng thái", "sections": {"contract": "<PERSON><PERSON>ông Tin <PERSON>", "paymentDetails": "<PERSON>"}, "fields": {"contract": "<PERSON><PERSON><PERSON>", "amount": "Số Tiền", "paymentDate": "<PERSON><PERSON><PERSON>", "paymentType": "<PERSON><PERSON><PERSON>", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON>", "referenceNumber": "<PERSON><PERSON>", "status": "Trạng <PERSON>", "description": "<PERSON><PERSON>"}, "placeholders": {"contract": "<PERSON><PERSON><PERSON> h<PERSON> đồng", "amount": "0.00", "paymentType": "<PERSON><PERSON><PERSON> lo<PERSON>i thanh toán", "paymentMethod": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "referenceNumber": "<PERSON><PERSON><PERSON><PERSON> số tham chiếu", "status": "<PERSON><PERSON><PERSON> trạng thái", "description": "<PERSON><PERSON><PERSON><PERSON> mô tả hoặc ghi chú"}, "paymentTypes": {"rent": "<PERSON><PERSON><PERSON><PERSON>", "deposit": "Tiền Đặt Cọc", "lateFee": "<PERSON><PERSON> <PERSON>", "maintenance": "Bảo Trì", "other": "K<PERSON><PERSON><PERSON>"}, "paymentMethods": {"cash": "Tiền Mặt", "bankTransfer": "<PERSON><PERSON><PERSON><PERSON>", "creditCard": "Thẻ <PERSON>", "check": "Séc"}, "status": {"pending": "<PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON><PERSON>", "refunded": "<PERSON><PERSON><PERSON>"}, "contractInfo": {"rent": "<PERSON><PERSON><PERSON><PERSON>", "deposit": "Tiền Đặt Cọc", "dueDay": "<PERSON><PERSON><PERSON>"}, "quickFill": {"rent": "<PERSON><PERSON><PERSON>n <PERSON> T<PERSON>ền <PERSON>", "deposit": "<PERSON><PERSON><PERSON><PERSON>"}, "messages": {"createSuccess": "支払いを作成しました", "updateSuccess": "支払いを更新しました", "createError": "支払いを作成できませんでした", "updateError": "支払いを更新できませんでした", "deleteSuccess": "支払いを削除しました", "deleteError": "支払いを削除できませんでした"}}, "permissions": {"staff": {"CREATE_STAFF": "创建员工", "CREATE_STAFF_TASK": "创建员工任务", "CREATE_STAFF_KNOWLEDGE": "创建员工知识", "GET_STAFF": "查看员工", "LIST_STAFF": "员工列表", "UPDATE_STAFF": "更新员工", "UPDATE_STAFF_ROLE": "更新员工角色", "UPDATE_STAFF_INTERACT_INFO": "更新员工互动信息", "DELETE_STAFF_KNOWLEDGE": "删除员工知识", "DELETE_STAFF_TASK": "删除员工任务", "INTEGRATE_STAFF_TO_SOCIAL_PLATFORM": "将员工集成到社交平台"}, "account": {"CREATE_STAFF_ACCOUNT": "创建员工账户", "GET_STAFF_ACCOUNT": "查看员工账户", "LIST_STAFFS_ACCOUNT": "员工账户列表", "UPDATE_STAFF_ACCOUNT": "更新员工账户", "UPDATE_STAFF_ACCOUNT_AUTHORIZATION": "更新员工账户授权", "DISABLE_STAFF_ACCOUNT": "禁用员工账户", "DELETE_STAFF_ACCOUNT": "删除员工账户"}, "activity": {"CREATE_ACTIVITIES": "创建活动", "LIST_ACTIVITIES": "活动列表", "LIST_ACTIVITIES_IN_CHARGE": "负责的活动列表", "UPDATE_ACTIVITIES": "更新活动", "DELETE_ACTIVITIES": "删除活动", "ASSIGN_STAFF_TO_ACTIVITIES": "分配员工到活动"}, "branch": {"CREATE_BRAND": "创建品牌", "GET_BRAND": "查看品牌", "LIST_BRAND": "品牌列表", "UPDATE_BRAND": "更新品牌", "DELETE_BRAND": "删除品牌"}, "brand": {"BRAND_LIST_EXPORT_FILE": "品牌列表导出文件"}, "conversation": {"LIST_MESSAGE": "消息列表", "LIST_MESSAGE_IN_CHARGE": "负责的消息列表", "ASSIGN_STAFF_TO_CONVERSATION": "分配员工到对话", "REPLY_MESSAGE": "回复消息"}, "customer": {"CREATE_CUSTOMER": "创建客户", "GET_CUSTOMER": "查看客户", "LIST_CUSTOMER": "客户列表", "LIST_CUSTOMER_IN_CHARGE": "负责的客户列表", "UPDATE_CUSTOMER": "更新客户", "DELETE_CUSTOMER": "删除客户", "SHOW_CUSTOMER_PHONE": "显示客户电话", "CUSTOMER_LIST_EXPORT_FILE": "客户列表导出文件", "SHOW_CUSTOMER_GROUP": "显示客户分组"}, "department": {"CREATE_DEPARTMENT": "创建部门", "GET_DEPARTMENT": "查看部门", "UPDATE_DEPARTMENT": "更新部门", "UPDATE_DEPARTMENT_DESCRIPTION": "更新部门描述", "DELETE_DEPARTMENT": "删除部门"}, "knowledge": {"CREATE_KNOWLEDGE": "创建知识", "GET_KNOWLEDGE": "查看知识", "UPDATE_KNOWLEDGE": "更新知识", "DELETE_KNOWLEDGE": "删除知识"}, "opportunity": {"CREATE_OPPORTUNITY": "创建商机", "GET_OPPORTUNITY": "查看商机", "LIST_OPPORTUNITY": "商机列表", "LIST_OPPORTUNITY_IN_CHARGE": "负责的商机列表", "LIST_OPPORTUNITY_HISTORY": "商机历史列表", "LIST_OPPORTUNITY_ORDER_HISTORY": "商机订单历史列表", "UPDATE_OPPORTUNITY": "更新商机", "UPDATE_OPPORTUNITY_EXPECTED_CLOSING_DATE": "更新商机预期结束日期", "UPDATE_OPPORTUNITY_PRIORITY": "更新商机优先级", "UPDATE_OPPORTUNITY_NOTE": "更新商机备注", "UPDATE_OPPORTUNITY_EXPECTED_REVENUE": "更新商机预期收入", "DELETE_OPPORTUNITY": "删除商机", "ASSIGN_STAFF_TO_OPPORTUNITY": "分配员工到商机", "MARK_OPPORTUNITY_WON_LOST": "标记商机输赢"}, "order": {"CREATE_ORDER": "创建订单", "GET_ORDER": "查看订单", "LIST_ORDER": "订单列表", "LIST_ORDER_IN_CHARGE": "负责的订单列表", "UPDATE_ORDER": "更新订单", "UPDATE_ORDER_NOTE": "更新订单备注", "DELETE_ORDER": "删除订单", "ORDER_CONFIRM_STATUS": "订单确认状态", "SAVE_DRAFT_ORDER": "保存草稿订单", "ORDER_CONFIRM_PAID_STATUS": "订单确认已付款状态", "ORDER_CONFIRM_COMPLETED_STATUS": "订单确认已完成状态", "ORDER_CONFIRM_CANCELED_STATUS": "订单确认已取消状态", "ORDER_CONFIRM_PACKING_STATUS": "订单确认包装状态", "ORDER_IMPORT": "订单导入", "ORDER_LIST_EXPORT_FILE": "订单列表导出文件"}, "product": {"CREATE_PRODUCT": "创建产品", "CREATE_PRODUCT_QUICKLY": "快速创建产品", "GET_PRODUCT": "查看产品", "GET_PRODUCT_PRICE_GROUP": "查看产品价格组", "LIST_PRODUCT": "产品列表", "LIST_PUBLISHED_PRODUCT": "已发布产品列表", "UPDATE_PRODUCT": "更新产品", "UPDATE_PRODUCT_FILES": "更新产品文件", "DELETE_PRODUCT": "删除产品", "PRODUCT_LIST_EXPORT_FILE": "产品列表导出文件"}, "role": {"CREATE_ROLE": "创建角色", "LIST_ROLE": "角色列表", "UPDATE_ROLE": "更新角色", "DELETE_ROLE": "删除角色"}, "stage": {"CREATE_STAGE": "创建阶段", "GET_STAGE_TOTAL_EXPECTED_REVENUE": "获取阶段总预期收入", "UPDATE_STAGE": "更新阶段", "DELETE_STAGE": "删除阶段"}, "task": {"CREATE_TASK": "创建任务", "GET_TASK": "查看任务", "LIST_TASK": "任务列表", "UPDATE_TASK": "更新任务", "UPDATE_TASK_PROMPT": "更新任务提示", "DELETE_TASK": "删除任务"}, "variant": {"VARIANT_LIST_EXPORT_FILE": "变体列表导出文件"}}}