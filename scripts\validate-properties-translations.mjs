import puppeteer from "puppeteer";

import { navigateWithAuth } from "./utils/auth.mjs";

async function validatePropertiesTranslations() {
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: { width: 1920, height: 1080 },
  });

  try {
    const page = await browser.newPage();

    console.log("🔐 Authenticating and navigating to properties page...");
    await navigateWithAuth(page, "http://localhost:3000/property-assets/properties");

    // Wait for page to load
    await new Promise((resolve) => setTimeout(resolve, 3000));

    console.log("📸 Taking English properties page screenshot...");
    await page.screenshot({
      path: "/Users/<USER>/Desktop/Projects/onex-erp/screenshots/properties-translations-en.png",
      fullPage: true,
    });

    // Check for translation key issues
    const translationIssues = await page.evaluate(() => {
      const text = document.body.innerText;
      const issues = [];

      // Look for common translation key patterns
      const keyPatterns = [
        /pages\.properties\.\w+/g,
        /common\.\w+/g,
        /nav\.\w+/g,
        /\w+\.\w+\.\w+/g,
      ];

      keyPatterns.forEach((pattern) => {
        const matches = text.match(pattern);
        if (matches) {
          matches.forEach((match) => {
            if (!issues.includes(match)) {
              issues.push(match);
            }
          });
        }
      });

      return issues;
    });

    console.log("🔍 Translation issues found:", translationIssues);

    // Capture specific UI elements text
    const uiElements = await page.evaluate(() => {
      const elements = {};

      // Page title
      const title = document.querySelector("h1");
      if (title) elements.title = title.textContent?.trim();

      // Search placeholder
      const searchInput = document.querySelector("input[placeholder]");
      if (searchInput) elements.searchPlaceholder = searchInput.placeholder;

      // Filter dropdowns
      const filterSelects = Array.from(document.querySelectorAll("select"))
        .map((select) => select.textContent?.trim())
        .filter((text) => text && text.length > 0);
      elements.filterOptions = filterSelects;

      // Button texts
      const buttons = Array.from(document.querySelectorAll("button"))
        .map((btn) => btn.textContent?.trim())
        .filter((text) => text && text.length > 0 && !text.includes("common."));
      elements.buttonTexts = buttons.slice(0, 10); // First 10 buttons

      // Table headers
      const headers = Array.from(document.querySelectorAll("th"))
        .map((th) => th.textContent?.trim())
        .filter((text) => text && text.length > 0);
      elements.tableHeaders = headers;

      // Property card content
      const propertyNames = Array.from(
        document.querySelectorAll('h3, [class*="name"], [class*="title"]')
      )
        .map((el) => el.textContent?.trim())
        .filter((text) => text && text.length > 0 && text.length < 100)
        .slice(0, 5);
      elements.propertyNames = propertyNames;

      return elements;
    });

    console.log("🎯 UI Elements captured:", uiElements);

    // Test responsive design
    console.log("📱 Testing mobile responsive design...");
    await page.setViewport({ width: 375, height: 667 });
    await new Promise((resolve) => setTimeout(resolve, 1000));
    await page.screenshot({
      path: "/Users/<USER>/Desktop/Projects/onex-erp/screenshots/properties-translations-mobile.png",
      fullPage: true,
    });

    // Reset to desktop
    await page.setViewport({ width: 1920, height: 1080 });
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Test Vietnamese if language switching is available
    console.log("🇻🇳 Attempting to switch to Vietnamese...");

    // Look for language selector or settings
    const languageButton = await page.$(
      '[class*="lang"], [data-testid*="lang"], button:has-text("EN"), button:has-text("VI")'
    );
    if (languageButton) {
      try {
        await languageButton.click();
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Look for Vietnamese option
        const vietnameseOption = await page.$x(
          "//button[contains(text(), 'Tiếng Việt')] | //option[contains(text(), 'Tiếng Việt')] | //a[contains(text(), 'VI')] | //button[contains(text(), 'Vietnamese')]"
        );
        if (vietnameseOption.length > 0) {
          await vietnameseOption[0].click();
          await new Promise((resolve) => setTimeout(resolve, 2000));

          console.log("📸 Taking Vietnamese properties page screenshot...");
          await page.screenshot({
            path: "/Users/<USER>/Desktop/Projects/onex-erp/screenshots/properties-translations-vi.png",
            fullPage: true,
          });
        }
      } catch (e) {
        console.log("⚠️ Could not switch to Vietnamese:", e.message);
      }
    }

    console.log("✅ Properties translations validation completed!");

    return {
      success: true,
      translationIssues,
      uiElements,
      screenshots: [
        "properties-translations-en.png",
        "properties-translations-mobile.png",
        "properties-translations-vi.png",
      ],
    };
  } catch (error) {
    console.error("❌ Error validating properties translations:", error);
    return { success: false, error: error.message };
  } finally {
    await browser.close();
  }
}

// Run validation
validatePropertiesTranslations()
  .then((result) => {
    if (result.success) {
      console.log("\n🎉 Properties translations validation COMPLETED!");
      console.log(`✅ Screenshots captured: ${result.screenshots.length}`);

      if (result.translationIssues.length === 0) {
        console.log("✅ NO translation issues found");
      } else {
        console.log("⚠️ Translation issues found:");
        result.translationIssues.forEach((issue) => {
          console.log(`   - ${issue}`);
        });
      }

      console.log("\n📋 UI Elements Analysis:");
      console.log(`   Title: ${result.uiElements.title}`);
      console.log(`   Search Placeholder: ${result.uiElements.searchPlaceholder}`);
      console.log(`   Table Headers: ${result.uiElements.tableHeaders?.join(", ")}`);
      console.log(`   Button Texts: ${result.uiElements.buttonTexts?.join(", ")}`);
      console.log(`   Property Names: ${result.uiElements.propertyNames?.join(", ")}`);
    } else {
      console.log("\n❌ Properties translations validation FAILED!");
      console.log(`Error: ${result.error}`);
    }
  })
  .catch((error) => {
    console.error("❌ Script execution failed:", error);
  });
