import { useState } from "react";
import { useTranslation } from "react-i18next";

import { Button } from "@/components/ui/button";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { PortalCombobox } from "@/components/ui/portal-combobox";
import { Textarea } from "@/components/ui/textarea";

import { getLostReasons } from "../../data";

interface LostReasonDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (reason: string) => void;
  isLoading?: boolean;
}

export function LostReasonDialog({
  open,
  onOpenChange,
  onConfirm,
  isLoading = false,
}: LostReasonDialogProps) {
  const { t } = useTranslation();
  const [selectedReason, setSelectedReason] = useState("");
  const [customReason, setCustomReason] = useState("");
  const [showConfirm, setShowConfirm] = useState(false);

  const lostReasons = getLostReasons(t);

  const hasUnsavedInput = selectedReason !== "" || customReason.trim() !== "";

  const handleConfirm = () => {
    const finalReason = selectedReason || customReason.trim();
    if (finalReason) {
      onConfirm(finalReason);
      // Reset form
      setSelectedReason("");
      setCustomReason("");
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      if (hasUnsavedInput) {
        setShowConfirm(true);
        return;
      }
      // Reset form when closing
      setSelectedReason("");
      setCustomReason("");
    }
    onOpenChange(newOpen);
  };

  const handleCancel = () => {
    setShowConfirm(false);
  };

  const handleConfirmClose = () => {
    setShowConfirm(false);
    setSelectedReason("");
    setCustomReason("");
    onOpenChange(false);
  };

  const isConfirmDisabled = (!selectedReason && !customReason.trim()) || isLoading;

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{t("pages.opportunities.lostReason.title")}</DialogTitle>
            <DialogDescription>{t("pages.opportunities.lostReason.description")}</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label className="text-sm font-medium">
                {t("pages.opportunities.lostReason.selectReason")}
              </label>
              <PortalCombobox
                value={selectedReason}
                onValueChange={(value) => setSelectedReason(value as string)}
                items={lostReasons}
                placeholder={t("pages.opportunities.lostReason.selectPlaceholder")}
                searchPlaceholder={t("pages.opportunities.lostReason.searchPlaceholder")}
                emptyText={t("pages.opportunities.lostReason.noResults")}
              />
            </div>
            <div className="grid gap-2">
              <label className="text-sm font-medium">
                {t("pages.opportunities.lostReason.customReason")}
              </label>
              <Textarea
                placeholder={t("pages.opportunities.lostReason.customPlaceholder")}
                value={customReason}
                onChange={(e) => setCustomReason(e.target.value)}
                className="min-h-[80px]"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => handleOpenChange(false)} disabled={isLoading}>
              {t("common.cancel")}
            </Button>
            <Button onClick={handleConfirm} disabled={isConfirmDisabled} loading={isLoading}>
              {isLoading ? t("common.loading") : t("common.confirm")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <ConfirmDialog
        open={showConfirm}
        onOpenChange={setShowConfirm}
        title={t("common.areYouSure")}
        description={t("common.confirmCancel") || "Are you sure you want to discard your changes?"}
        onCancel={handleCancel}
        onConfirm={handleConfirmClose}
        confirmText={t("common.confirm")}
        cancelText={t("common.back")}
      />
    </>
  );
}
