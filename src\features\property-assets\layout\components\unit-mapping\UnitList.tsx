import { MapPin, Users } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";

import type { Unit } from "../../../types";
import { UnitListSkeleton } from "../LoadingSkeleton";

interface UnitListProps {
  units: Unit[];
  selectedUnitId: string | null;
  selectedLayoutId: string;
  unitMappings: Array<{ unitId: string; layoutId: string }>;
  isLoading: boolean;
  onUnitSelect: (unit: Unit) => void;
  getUnitStatusColor: (status: Unit["status"]) => string;
  getUnitStatusVariant: (
    status: Unit["status"]
  ) => "secondary" | "default" | "destructive" | "outline";
}

function UnitCard({
  unit,
  isMapped,
  isSelected,
  onUnitSelect,
  getUnitStatusColor,
  getUnitStatusVariant,
}: {
  unit: Unit;
  isMapped: boolean;
  isSelected: boolean;
  onUnitSelect: (unit: Unit) => void;
  getUnitStatusColor: (status: Unit["status"]) => string;
  getUnitStatusVariant: (
    status: Unit["status"]
  ) => "secondary" | "default" | "destructive" | "outline";
}) {
  const { t } = useTranslation();
  return (
    <div
      key={unit.id}
      draggable={!isMapped}
      className={`cursor-pointer rounded-lg border p-3 transition-all duration-200 ${
        isSelected
          ? "border-primary bg-primary/5"
          : "border-border hover:border-primary/50 hover:bg-muted/50"
      } ${!isMapped ? "cursor-grab active:cursor-grabbing" : "cursor-default opacity-60"}`}
      onClick={() => onUnitSelect(unit)}
      onDragStart={(e) => {
        if (!isMapped) {
          e.dataTransfer.setData(
            "text/plain",
            JSON.stringify({
              unitId: unit.id,
              unitNumber: unit.unit_number,
              unitType: unit.unit_type,
              status: unit.status,
            })
          );
          e.dataTransfer.effectAllowed = "copy";
          const dragImage = document.createElement("div");
          dragImage.className = "p-2 bg-primary text-primary-foreground rounded shadow-lg";
          dragImage.textContent = unit.unit_number;
          dragImage.style.position = "absolute";
          dragImage.style.top = "-1000px";
          document.body.appendChild(dragImage);
          e.dataTransfer.setDragImage(dragImage, 50, 25);
          setTimeout(() => document.body.removeChild(dragImage), 0);
        } else {
          e.preventDefault();
        }
      }}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className={`size-3 rounded-full ${getUnitStatusColor(unit.status)}`} />
          <div>
            <div className="text-sm font-medium">{unit.unit_number}</div>
            <div className="text-xs text-muted-foreground">
              {unit.unit_type} • {unit.square_footage} sq ft
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={getUnitStatusVariant(unit.status)} className="text-xs">
            {t(`pages.units.status.${unit.status}`)}
          </Badge>
          {isMapped && (
            <Tooltip>
              <TooltipTrigger>
                <MapPin className="size-4 text-success" />
              </TooltipTrigger>
              <TooltipContent>{t("pages.layouts.mappedToLayout")}</TooltipContent>
            </Tooltip>
          )}
        </div>
      </div>

      {unit.rent_amount && (
        <div className="mt-2 text-xs text-muted-foreground">
          ${unit.rent_amount.toLocaleString()}/month
        </div>
      )}
    </div>
  );
}

export function UnitList({
  units,
  selectedUnitId,
  selectedLayoutId,
  unitMappings,
  isLoading,
  onUnitSelect,
  getUnitStatusColor,
  getUnitStatusVariant,
}: UnitListProps) {
  const { t } = useTranslation();

  if (isLoading) {
    return <UnitListSkeleton />;
  }

  return (
    <div className="flex-1">
      {/* <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between text-sm">
          <span className="flex items-center gap-2">
            <Users className="size-4" />
            {t("pages.units.unitList")} ({units.length})
          </span>
        </CardTitle>
      </CardHeader> */}
      <div>
        <div className="space-y-2">
          {units.map((unit) => {
            const isMapped = unitMappings.some(
              (m) => m.unitId === unit.id && m.layoutId === selectedLayoutId
            );
            const isSelected = selectedUnitId === unit.id;

            return (
              <UnitCard
                key={unit.id}
                unit={unit}
                isMapped={isMapped}
                isSelected={isSelected}
                onUnitSelect={onUnitSelect}
                getUnitStatusColor={getUnitStatusColor}
                getUnitStatusVariant={getUnitStatusVariant}
              />
            );
          })}

          {units.length === 0 && (
            <div className="py-8 text-center text-muted-foreground">
              <Users className="mx-auto mb-2 size-12 opacity-50" />
              <p className="text-sm">{t("pages.units.noUnitsFound")}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
