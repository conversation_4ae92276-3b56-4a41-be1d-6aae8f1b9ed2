import { Loader2, Save, <PERSON> } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Button } from "@/components/ui/button";

interface UnitFormFooterProps {
  isSubmitting: boolean;
  isEditing: boolean;
  isFormValid: boolean;
  onCancel: () => void;
}

export function UnitFormFooter({
  isSubmitting,
  isEditing,
  isFormValid,
  onCancel,
}: UnitFormFooterProps) {
  const { t } = useTranslation();

  return (
    <div className="flex w-full items-center justify-between">
      <div className="flex items-center gap-3">
        <Button type="button" variant="outline" onClick={onCancel} className="h-9 px-4 py-2">
          <X className="mr-2 size-4" />
          {t("common.cancel") || "Cancel"}
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting || !isFormValid}
          form="unit-form"
          className="h-9 px-4 py-2">
          {isSubmitting && <Loader2 className="mr-2 size-4 animate-spin" />}
          <Save className="mr-2 size-4" />
          {isEditing ? t("common.update") || "Update" : t("common.add") || "Add"}
        </Button>
      </div>
    </div>
  );
}
