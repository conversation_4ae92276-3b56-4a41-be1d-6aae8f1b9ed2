"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useZodForm,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

import { useChangePassword } from "../hooks/useChangePassword";
import {
  changePasswordSchema,
  type ChangePasswordFormData,
} from "../utils/validators/change-password";
import { ForgotPasswordDialog } from "./ForgotPasswordDialog";

interface ChangePasswordDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const ChangePasswordDialog = ({ open, onOpenChange }: ChangePasswordDialogProps) => {
  const { t } = useTranslation();
  const [showForgotPasswordDialog, setShowForgotPasswordDialog] = useState(false);

  // Use the change password hook
  const { changePassword, isLoading, reset } = useChangePassword();

  // Initialize form with React Hook Form and Zod validation
  const form = useZodForm({
    schema: changePasswordSchema,
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  const onSubmit = async (data: ChangePasswordFormData) => {
    try {
      await changePassword(data.currentPassword, data.newPassword);

      // Reset form
      form.reset();
      onOpenChange(false);

      // Reset the mutation state
      reset();
    } catch (error) {
      // Error is already handled by the hook
      console.error("Password change failed:", error);
    }
  };

  const handleCancel = () => {
    form.reset();
    reset(); // Reset mutation state
    onOpenChange(false);
  };

  const handleForgotPassword = () => {
    setShowForgotPasswordDialog(true);
    onOpenChange(false); // Close the parent dialog
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="w-[600px] p-0">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 p-6">
              {t("pages.profile.changePasswordDiaglog.title")}
            </DialogTitle>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="w-full">
              <div className="grid gap-4 px-6 pb-8">
                {/* Current Password */}
                <FormField
                  control={form.control}
                  name="currentPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">
                        {t("pages.profile.changePasswordDiaglog.oldPassword")} *
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            {...field}
                            type="password"
                            placeholder={t("pages.profile.changePasswordDiaglog.enterOldPassword")}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* New Password */}
                <FormField
                  control={form.control}
                  name="newPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">
                        {t("pages.profile.changePasswordDiaglog.newPassword")} *
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            {...field}
                            type="password"
                            placeholder={t("pages.profile.changePasswordDiaglog.enterNewPassword")}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Confirm New Password */}
                <FormField
                  control={form.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">
                        {t("pages.profile.changePasswordDiaglog.confirmPassword")} *
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            {...field}
                            type="password"
                            placeholder={t(
                              "pages.profile.changePasswordDiaglog.enterConfirmPassword"
                            )}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="w-full border-t px-6 pb-6 pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Button
                      type="button"
                      variant="link"
                      onClick={handleForgotPassword}
                      className="p-0">
                      {t("pages.profile.changePasswordDiaglog.forgotPassword")}
                    </Button>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleCancel}
                      disabled={isLoading}>
                      {t("common.cancel")}
                    </Button>
                    <Button
                      type="submit"
                      disabled={isLoading || !form.formState.isValid}
                      loading={isLoading}>
                      {t("pages.profile.changePasswordDiaglog.title")}
                    </Button>
                  </div>
                </div>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Forgot Password Dialog */}
      <ForgotPasswordDialog
        open={showForgotPasswordDialog}
        onOpenChange={setShowForgotPasswordDialog}
      />
    </>
  );
};
