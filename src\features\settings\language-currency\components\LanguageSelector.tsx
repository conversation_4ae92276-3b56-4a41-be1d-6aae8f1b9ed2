import Image from "next/image";
import { useTranslation } from "react-i18next";

import { But<PERSON> } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

import { AVAILABLE_LANGUAGES } from "../constants";

interface LanguageSelectorProps {
  selectedLanguages: string[];
  displayedLanguages: string[];
  onLanguageToggle: (languageCode: string) => void;
  onRemoveLanguage: (languageCode: string) => void;
}

export const LanguageSelector = ({
  selectedLanguages,
  displayedLanguages,
  onLanguageToggle,
  onRemoveLanguage,
}: LanguageSelectorProps) => {
  const { t } = useTranslation();

  return (
    <div className="space-y-0">
      <RadioGroup
        key={selectedLanguages[0]} // Force re-render when selection changes
        value={selectedLanguages[0]}
        onValueChange={onLanguageToggle}
        className="space-y-0">
        {AVAILABLE_LANGUAGES.filter((lang) => displayedLanguages.includes(lang.code)).map(
          (language, index) => (
            <div
              key={language.code}
              className={`flex items-center justify-between py-2 ${
                index < displayedLanguages.length - 1 ? "border-b border-border" : ""
              }`}>
              <div className="flex items-center gap-6">
                <RadioGroupItem
                  value={language.code}
                  id={`language-${language.code}`}
                  className="border-primary text-primary"
                />
                <div className="flex items-center gap-2">
                  <Image src={language.flag} alt={language.name} width={16} height={16} />
                  <span className="text-sm leading-[1.43] text-foreground">
                    {language.nativeName}
                  </span>
                </div>
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => onRemoveLanguage(language.code)}
                className="px-3 py-2 text-sm font-medium leading-[1.71] text-muted-foreground hover:text-foreground"
                disabled={displayedLanguages.length === 1}>
                {t("pages.settings.language.remove")}
              </Button>
            </div>
          )
        )}
      </RadioGroup>
    </div>
  );
};
