"use client";

import { useMemo, useState } from "react";
import { Plus } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { CustomPagination } from "@/components/custom-pagination";
import GroupButton, { GroupButtonProps } from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { FilterTableProps, FilterType } from "@/components/data-table/types";
import { usePermission } from "@/components/provider/permission-provider";
import AccessDenied from "@/components/ui/access-denined";

import AddRoleDialog from "./add-role-dialog";
import { useRoles } from "./hooks/use-role";
import RoleGrid from "./role-grid";

export default function RoleTable({ setTab }: { setTab: (tab: "employee" | "role") => void }) {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch, handlePageChange, handlePageSizeChange } =
    useDatatable();
  const [openAddRoleDialog, setOpenAddRoleDialog] = useState(false);
  const [sourceRoleId, setSourceRoleId] = useState<string | null>(null);
  const [mode, setMode] = useState<"add" | "edit" | "duplicate">("add");
  const { hasPermission } = usePermission();
  const hasListRole = hasPermission("LIST_ROLE");
  const hasCreateRole = hasPermission("CREATE_ROLE");

  const options = useMemo(
    () => ({
      limit: Number(getInitialParams.limit),
      enabled: hasListRole,
      ...getInitialParams,
    }),
    [getInitialParams, hasListRole]
  );

  const { roles, isLoading, total, deleteRoleMutation, createRoleMutation, updateRoleMutation } =
    useRoles({
      ...options,
      onCreateSuccess: () => {
        toast.success(t("pages.employeePermission.roleCreatedSuccess"));
        handleCloseDialog();
      },
      onUpdateSuccess: () => {
        toast.success(t("pages.employeePermission.roleUpdatedSuccess"));
        handleCloseDialog();
      },
    });

  // Get pagination values from useDatatable
  const pageSize = Number(getInitialParams.pageSize) || 20;
  const currentPage = Number(getInitialParams.currentPage) || 0;

  const filterConfig: FilterTableProps = useMemo(() => {
    return {
      showSearch: true,
      filterType: "roles",
      searchPlaceHolder: t("common.searchPlaceholder"),
      initialValues: getInitialParams,
      listFilter: [
        {
          id: "created_at",
          type: "date",
          title: t("pages.products.filters.createdAt"),
          defaultValue: {
            from: getInitialParams["created_at_from"],
            to: getInitialParams["created_at_to"],
          },
        },
        {
          id: "updated_at",
          type: "date",
          title: t("pages.products.filters.updatedAt"),
          defaultValue: {
            from: getInitialParams["updated_at_from"],
            to: getInitialParams["updated_at_to"],
          },
        },
      ] as unknown as FilterType[],
      handleParamSearch,
      listLoading: isLoading,
    };
  }, [handleParamSearch, t, getInitialParams, isLoading]);

  const groupButtonConfig: GroupButtonProps = {
    buttons: [
      {
        type: "button",
        title: t("common.add"),
        icon: Plus,
        onClick: () => {
          setSourceRoleId(null);
          setMode("add");
          setOpenAddRoleDialog(true);
        },
        permission: "CREATE_ROLE",
      },
    ],
    showRefresh: false,
  };

  const handleEditRole = (roleId: string) => {
    setSourceRoleId(roleId);
    setMode("edit");
    setOpenAddRoleDialog(true);
  };

  const handleDuplicateRole = (roleId: string) => {
    setSourceRoleId(roleId);
    setMode("duplicate");
    setOpenAddRoleDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenAddRoleDialog(false);
    setSourceRoleId(null);
    setMode("add");
  };

  return (
    <TableCard tableKey="ROLE" className="mx-0 border-none">
      <TableHeader
        filterType="roles"
        data={roles}
        filterProps={filterConfig}
        rightComponent={<GroupButton {...groupButtonConfig} />}
        isSaveFilters={false}
        isExportable={false}
      />
      {hasListRole ? (
        <>
          <RoleGrid
            roles={roles}
            onEditRole={handleEditRole}
            onDuplicateRole={handleDuplicateRole}
            isLoading={isLoading}
            deleteRoleMutation={deleteRoleMutation}
            setTab={setTab}
          />
          <div className="mt-2">
            <CustomPagination
              total={total}
              pageSize={pageSize}
              currentPage={currentPage + 1}
              onPageSizeChange={handlePageSizeChange}
              onPageChange={(page) => handlePageChange(page - 1)}
            />
          </div>
        </>
      ) : (
        <AccessDenied />
      )}
      {hasCreateRole && (
        <AddRoleDialog
          open={openAddRoleDialog}
          onOpenChange={handleCloseDialog}
          sourceRoleId={sourceRoleId}
          mode={mode}
          createRoleMutation={createRoleMutation}
          updateRoleMutation={updateRoleMutation}
        />
      )}
    </TableCard>
  );
}
