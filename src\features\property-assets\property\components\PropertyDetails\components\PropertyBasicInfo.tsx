"use client";

import { ReactNode } from "react";
import { useTranslation } from "react-i18next";

import { CardContent } from "@/components/ui/card";
import { Property } from "@/lib/apis/types/property_assets/property";
import { cn } from "@/lib/utils";

// Reusable InlineInfo component
interface InlineInfoProps {
  label: string;
  value: ReactNode;
  showBorder?: boolean;
  className?: string;
  isColumn?: boolean;
}

function InlineInfo({
  label,
  value,
  showBorder = true,
  className,
  isColumn = false,
}: InlineInfoProps) {
  return (
    <div
      className={cn(
        "grid gap-4 py-2 text-sm text-foreground",
        !isColumn ? "grid-cols-12" : "grid-cols-1",
        showBorder && "border-b border-border",
        className
      )}>
      <label className="col-span-2">{label}:</label>
      <div className="col-span-10 font-medium">{value}</div>
    </div>
  );
}

interface PropertyBasicInfoProps {
  property: Property;
  totalUnits: number;
  getPropertyTypeLabel: (type: string) => string;
}

export function PropertyBasicInfo({
  property,
  totalUnits,
  getPropertyTypeLabel,
}: PropertyBasicInfoProps) {
  const { t } = useTranslation();

  return (
    <div>
      <CardContent className="p-4">
        <div className="space-y-0">
          <InlineInfo
            label={t("pages.properties.components.propertyBasicInfo.type")}
            value={getPropertyTypeLabel(property.type)}
          />
          <InlineInfo
            label={t("pages.properties.components.propertyBasicInfo.totalUnits")}
            value={totalUnits}
          />

          <InlineInfo
            label={t("pages.properties.components.propertyBasicInfo.description")}
            value={property.description}
            isColumn
          />
        </div>
      </CardContent>
    </div>
  );
}
