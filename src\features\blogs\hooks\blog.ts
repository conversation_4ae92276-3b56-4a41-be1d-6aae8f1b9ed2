import {
  InfiniteData,
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

import { SortDirection } from "@/components/custom-table/hooks/use-data-table";
import { blogsApi } from "@/lib/apis/blogs";
import { Blog, Category } from "@/lib/apis/types/blogs";
import { ResponseList } from "@/lib/apis/types/common";

import { blogKeys } from "./keys";

interface UseBlogsOptions {
  limit?: number;
  page?: number;
  query?: string;
  category?: string;
  locale?: string;
  created_at_from?: string;
  created_at_to?: string;
  updated_at_from?: string;
  updated_at_to?: string;
  enabled?: boolean;
  [key: string]: unknown;
}

export function useBlogs(options: UseBlogsOptions = {}) {
  const { limit = 20, enabled = true, ...restOptions } = options;
  const queryClient = useQueryClient();

  const query = useInfiniteQuery({
    queryKey: blogKeys.list({ limit, ...restOptions }),
    queryFn: ({ pageParam = 0 }) =>
      blogsApi.list({
        page: pageParam as number,
        limit,
        ["sort_updated_at"]: SortDirection.DESC,
        ...restOptions,
      }),
    getNextPageParam: (lastPage) => {
      const totalPages = Math.ceil(lastPage.total / Number(lastPage.limit));
      if (lastPage.page < totalPages) {
        return lastPage.page + 1;
      }
      return undefined;
    },
    initialPageParam: 0,
    enabled,
  });

  const blogs = query.data?.pages.flatMap((page) => page.items) ?? [];
  const total = query.data?.pages[0]?.total ?? 0;

  const useDeleteBlogMutation = useMutation({
    mutationFn: async (id: string) => {
      // TODO: Implement delete API when available
      // return blogsApi.delete(id);
      console.log("Delete blog:", id);
    },
    onSuccess: (_, deletedId) => {
      queryClient.setQueryData<InfiniteData<ResponseList<Blog>>>(
        blogKeys.list({ limit, ...restOptions }),
        (oldData) => {
          if (!oldData) return oldData;
          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.filter((item) => item.id !== deletedId),
            total: page.total - 1,
          }));
          return {
            ...oldData,
            pages: newPages,
          };
        }
      );
      toast.success("Blog deleted successfully");
    },
    onError: () => {
      toast.error("Blog deletion failed");
    },
  });

  const useDeleteListBlogMutation = useMutation({
    mutationFn: async (objectData: { listId: string[]; handleRestRows: () => void }) => {
      // TODO: Implement bulk delete API when available
      // return blogsApi.deleteList(objectData.listId);
      console.log("Delete blogs:", objectData.listId);
    },
    onSuccess: (_, objectDeletedId) => {
      queryClient.setQueryData<InfiniteData<ResponseList<Blog>>>(
        blogKeys.list({ limit, ...restOptions }),
        (oldData) => {
          if (!oldData) return oldData;
          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.filter((item) => !objectDeletedId.listId.includes(item.id)),
            total: page.total - objectDeletedId.listId.length,
          }));
          objectDeletedId?.handleRestRows();
          return {
            ...oldData,
            pages: newPages,
          };
        }
      );
      toast.success("Blogs deleted successfully");
    },
    onError: () => {
      toast.error("Blog deletion failed");
    },
  });

  return {
    ...query,
    blogs,
    total,
    useDeleteBlogMutation,
    useDeleteListBlogMutation,
  };
}

interface CreateBlog {
  title: string;
  description?: string;
  image?: string;
  category_id: string;
  locale: string;
  publish?: boolean;
  tags?: string;
}

interface UseAddBlogOptions {
  onSuccess?: (data: Blog) => void;
  onError?: (error: Error) => void;
}

export function useAddBlog(options: UseAddBlogOptions = {}) {
  const { onSuccess, onError } = options;
  const queryClient = useQueryClient();

  return useMutation<Blog, Error, CreateBlog>({
    mutationFn: async (data: CreateBlog): Promise<Blog> => {
      // TODO: Implement create API when available
      // const response = await blogsApi.create(data);
      // return response;
      console.log("Create blog:", data);
      throw new Error("Create blog API not implemented yet");
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: blogKeys.lists() });
      onSuccess?.(data);
    },
    onError,
  });
}

interface UpdateBlogPayload {
  id: string;
  data: Partial<Blog>;
}

interface UseUpdateBlogOptions {
  onSuccess?: (data: Blog) => void;
  onError?: (error: Error) => void;
}

export function useUpdateBlog(options: UseUpdateBlogOptions = {}) {
  const { onSuccess, onError } = options;
  const queryClient = useQueryClient();

  return useMutation<Blog, Error, UpdateBlogPayload>({
    mutationFn: async ({ id, data }: UpdateBlogPayload): Promise<Blog> => {
      // TODO: Implement update API when available
      // const response = await blogsApi.update(id, data);
      // return response;
      console.log("Update blog:", id, data);
      throw new Error("Update blog API not implemented yet");
    },
    onSuccess: (updatedBlog) => {
      queryClient.setQueryData<InfiniteData<ResponseList<Blog>>>(blogKeys.lists(), (oldData) => {
        if (!oldData) return oldData;
        const newPages = oldData.pages.map((page) => ({
          ...page,
          items: page.items.map((item) => (item.id === updatedBlog.id ? updatedBlog : item)),
        }));
        return { ...oldData, pages: newPages };
      });
      queryClient.setQueryData(blogKeys.detail(updatedBlog.id), updatedBlog);
      onSuccess?.(updatedBlog);
    },
    onError,
  });
}

export const useBlogCategories = () => {
  return useInfiniteQuery<ResponseList<Category>>({
    queryKey: blogKeys.categories(),
    queryFn: async ({ pageParam = 0 }): Promise<ResponseList<Category>> => {
      // TODO: Implement blog categories API when available
      // const response = await blogsApi.getCategories({
      //   limit: 20,
      //   page: Number(pageParam),
      // });
      // return response;
      console.log("Get blog categories:", pageParam);
      throw new Error("Blog categories API not implemented yet");
    },
    initialPageParam: 0,
    getNextPageParam: (lastPage) => {
      if (lastPage.items.length < 20) return undefined;
      return Number(lastPage.page) + 1;
    },
  });
};

export function useBlog(id: string) {
  return useQuery<Blog>({
    queryKey: blogKeys.detail(id),
    queryFn: async () => {
      // TODO: Implement get by ID API when available
      // const response = await blogsApi.getById(id);
      // return response;
      console.log("Get blog by ID:", id);
      throw new Error("Get blog by ID API not implemented yet");
    },
    enabled: !!id,
  });
}

// Legacy hook for backward compatibility
export const useBlogList = (options: Record<string, unknown>) => {
  const { data, isLoading, isFetching, refetch } = useQuery<ResponseList<Blog>>({
    queryKey: blogKeys.list(options),
    queryFn: () => blogsApi.list(options),
  });

  return {
    data,
    isLoading,
    isFetching,
    refetch,
  };
};
