"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";

import { Button, Separator, Skeleton } from "@/components/ui";
import { Plan } from "@/lib/apis/crm_plan/types";

import { CheckoutData, CompanyInformation, RegistrationTime } from "../hooks/use-checkout";
import { CompanyInformationForm } from "./company-information-form";
import { OrderDetails } from "./order-details";
import { PricingSummary } from "./pricing-summary";
import { SubscriptionDetails } from "./subscription-details";

interface OrderInformationCardProps {
  subscriptionPlan: Plan;
  checkoutData: CheckoutData;
  formData: any;
  errors: any;
  isLoading?: boolean;
  isLoadingPlan?: boolean;
  isPaymentMode?: boolean;
  isCompanyInfoCompleted?: boolean;
  onUpdateCompanyInfo: (field: keyof CompanyInformation, value: string) => void;
  onConfirmCompanyInfo?: () => void;
  onRemoveCompanyInfo?: () => void;
  onRegistrationTimeChange?: (value: RegistrationTime) => void;
  onSubmit?: (e: React.FormEvent) => void;
}

export const OrderInformationCard = ({
  subscriptionPlan,
  checkoutData,
  formData,
  errors,
  isPaymentMode,
  isLoading,
  isLoadingPlan,
  isCompanyInfoCompleted,
  onUpdateCompanyInfo,
  onConfirmCompanyInfo,
  onRemoveCompanyInfo,
  onRegistrationTimeChange,
  onSubmit,
}: OrderInformationCardProps) => {
  const { t } = useTranslation();
  const [isCompanyFormExpanded, setIsCompanyFormExpanded] = useState(false);
  return (
    <form
      onSubmit={onSubmit}
      className="flex w-full max-w-md flex-col gap-2 rounded-xl bg-card p-4">
      <h2 className="text-lg font-medium text-card-foreground">
        {t("pages.checkout.orderInformation.title")}
      </h2>
      {isLoadingPlan ? (
        <SubscriptionSkeleton />
      ) : (
        <div className="flex w-full flex-col gap-2">
          {/* Subscription Details */}
          <SubscriptionDetails
            subscriptionPlan={subscriptionPlan}
            registrationTime={checkoutData.registrationTime}
            onRegistrationTimeChange={onRegistrationTimeChange}
            isPaymentMode={isPaymentMode}
          />

          {/* Separator */}
          <div className="h-px w-full bg-border" />

          {/* Order Details */}
          <OrderDetails
            creationDate={checkoutData.creationDate}
            paymentMethod={checkoutData.paymentMethod}
          />

          {/* Separator */}
          <Separator />

          {/* Company Information Form */}
          <CompanyInformationForm
            companyInfo={formData.invoice}
            isPaymentMode={isPaymentMode}
            onUpdateCompanyInfo={onUpdateCompanyInfo}
            onConfirm={onConfirmCompanyInfo}
            onRemove={onRemoveCompanyInfo}
            isCompleted={isCompanyInfoCompleted}
            onExpandedChange={setIsCompanyFormExpanded}
          />

          {/* Pricing Summary */}
          <PricingSummary
            isCompanyInfoCompleted={isCompanyInfoCompleted || false}
            totalAmount={checkoutData.totalAmount}
            currency={checkoutData.currency}
          />

          {/* Pay Button - Only show when company info is completed */}
          {!isCompanyFormExpanded && !isPaymentMode && (
            <Button
              type="submit"
              size="md"
              loading={isLoading}
              className="w-full bg-primary text-primary-foreground hover:bg-primary/90">
              {t("pages.checkout.orderInformation.pay")}
            </Button>
          )}
        </div>
      )}
    </form>
  );
};

const SubscriptionSkeleton = () => {
  return (
    <div className="flex w-full flex-col gap-3">
      <div className="flex items-center justify-between gap-4">
        <Skeleton className="h-6 w-40" />
        <Skeleton className="h-6 flex-auto" />
      </div>
      <div className="flex items-center justify-between gap-4">
        <Skeleton className="h-6 w-40" />
        <Skeleton className="h-6 flex-auto" />
      </div>
      <div className="flex items-center justify-between gap-4">
        <Skeleton className="h-6 w-40" />
        <Skeleton className="h-10 flex-auto" />
      </div>
      <div className="flex items-center justify-between gap-4">
        <Skeleton className="h-4 w-40" />
        <Skeleton className="h-4 flex-auto" />
      </div>
      <div className="flex items-center justify-between gap-4">
        <Skeleton className="h-6 w-40" />
        <Skeleton className="h-6 flex-auto" />
      </div>
      <Separator />
      <div className="flex items-center justify-between gap-4">
        <Skeleton className="h-12 w-40" />
        <Skeleton className="h-12 flex-auto" />
      </div>
    </div>
  );
};
