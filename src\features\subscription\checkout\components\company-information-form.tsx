"use client";

import { useEffect, useState } from "react";
import { Minus, Plus } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Button, Input, Separator } from "@/components/ui";

import { CompanyInformation } from "../hooks/use-checkout";

interface CompanyInformationFormProps {
  companyInfo: CompanyInformation;
  onUpdateCompanyInfo: (field: keyof CompanyInformation, value: string) => void;
  onConfirm?: () => void;
  onRemove?: () => void;
  isPaymentMode?: boolean;
  isCompleted?: boolean;
  onExpandedChange?: (expanded: boolean) => void;
}

// Reusable component for displaying company information as text
const CompanyInfoDisplay = ({ companyInfo }: { companyInfo: CompanyInformation }) => {
  const { t } = useTranslation();

  const infoFields = [
    { key: "companyName", label: t("pages.checkout.orderInformation.companyName") },
    { key: "taxId", label: t("pages.checkout.orderInformation.taxId") },
    { key: "address", label: t("pages.checkout.orderInformation.address") },
    { key: "email", label: t("pages.checkout.orderInformation.email") },
  ] as const;

  return (
    <div className="flex flex-col gap-2">
      {infoFields.map(({ key, label }) => (
        <div key={key} className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">{label}</span>
          <span className="text-base text-muted-foreground">{companyInfo[key]}</span>
        </div>
      ))}
    </div>
  );
};

// Reusable component for the company information input form
const CompanyInfoForm = ({
  companyInfo,
  onUpdateCompanyInfo,
  onConfirm,
  onCancel,
}: {
  companyInfo: CompanyInformation;
  onUpdateCompanyInfo: (field: keyof CompanyInformation, value: string) => void;
  onConfirm: () => void;
  onCancel: () => void;
}) => {
  const { t } = useTranslation();

  const hasCompanyInfo =
    companyInfo.companyName && companyInfo.taxId && companyInfo.address && companyInfo.email;

  const formFields = [
    {
      key: "companyName" as const,
      label: t("pages.checkout.orderInformation.companyName"),
      placeholder: t("pages.checkout.orderInformation.companyNamePlaceholder"),
      type: "text" as const,
    },
    {
      key: "taxId" as const,
      label: t("pages.checkout.orderInformation.taxId"),
      placeholder: t("pages.checkout.orderInformation.taxIdPlaceholder"),
      type: "text" as const,
    },
    {
      key: "address" as const,
      label: t("pages.checkout.orderInformation.address"),
      placeholder: t("pages.checkout.orderInformation.addressPlaceholder"),
      type: "text" as const,
    },
    {
      key: "email" as const,
      label: t("pages.checkout.orderInformation.email"),
      placeholder: t("pages.checkout.orderInformation.emailPlaceholder"),
      type: "email" as const,
    },
  ];

  return (
    <fieldset className="flex flex-col gap-3">
      {formFields.map(({ key, label, placeholder, type }) => (
        <Input
          key={key}
          label={label}
          type={type}
          placeholder={placeholder}
          value={companyInfo[key]}
          onChange={(e) => onUpdateCompanyInfo(key, e.target.value)}
        />
      ))}

      <div className="flex flex-col gap-2">
        <Button
          size="md"
          type="button"
          className="w-full"
          onClick={onConfirm}
          disabled={!hasCompanyInfo}>
          {t("pages.checkout.orderInformation.confirm")}
        </Button>
        <Button size="md" variant="outline" type="button" className="w-full" onClick={onCancel}>
          {t("pages.checkout.orderInformation.cancel")}
        </Button>
      </div>
    </fieldset>
  );
};

// Reusable component for the toggle button
const ToggleButton = ({
  isExpanded,
  isCompleted,
  isPaymentMode = false,
  onClick,
}: {
  isExpanded: boolean;
  isCompleted: boolean;
  isPaymentMode?: boolean;
  onClick: () => void;
}) => {
  const { t } = useTranslation();
  if (isPaymentMode)
    return (
      <h3 className="text-base font-medium text-card-foreground">
        {t("pages.checkout.orderInformation.issueInvoice")}
      </h3>
    );
  return (
    <Button
      variant="link"
      type="button"
      onClick={onClick}
      className="flex w-full justify-between gap-2 px-0">
      {t("pages.checkout.orderInformation.issueInvoice")}
      {isCompleted ? (
        <Minus className="size-4" />
      ) : isExpanded ? (
        <Minus className="size-4" />
      ) : (
        <Plus className="size-4" />
      )}
    </Button>
  );
};

export const CompanyInformationForm = ({
  companyInfo,
  onUpdateCompanyInfo,
  onConfirm,
  onRemove,
  isPaymentMode,
  isCompleted = false,
  onExpandedChange,
}: CompanyInformationFormProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { t } = useTranslation();
  // Notify parent when expanded state changes
  useEffect(() => {
    if (onExpandedChange) {
      onExpandedChange(isExpanded);
    }
  }, [isExpanded, onExpandedChange]);

  const handleToggle = () => {
    if (isCompleted) {
      // If completed, remove company info
      if (onRemove) {
        onRemove();
      }
    } else {
      // If not completed, toggle form visibility
      setIsExpanded(!isExpanded);
    }
  };

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm();
      setIsExpanded(false);
    }
  };

  const handleCancel = () => {
    setIsExpanded(false);
  };

  // Show company information as text when completed
  if (isCompleted) {
    return (
      <div className="flex flex-col gap-3">
        <ToggleButton
          isExpanded={false}
          isCompleted={true}
          isPaymentMode={isPaymentMode}
          onClick={handleToggle}
        />
        <CompanyInfoDisplay companyInfo={companyInfo} />
        <Separator />
      </div>
    );
  } else if (!isPaymentMode) {
    return (
      <div className="flex flex-col gap-3">
        <ToggleButton
          isExpanded={isExpanded}
          isCompleted={false}
          isPaymentMode={isPaymentMode}
          onClick={handleToggle}
        />

        {isExpanded && (
          <CompanyInfoForm
            companyInfo={companyInfo}
            onUpdateCompanyInfo={onUpdateCompanyInfo}
            onConfirm={handleConfirm}
            onCancel={handleCancel}
          />
        )}
        <Separator />
      </div>
    );
  } else return null;
};
