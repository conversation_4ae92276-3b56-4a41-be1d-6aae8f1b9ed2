"use client";

import { Suspense } from "react";
import { useTranslation } from "react-i18next";

// Components
import { LayoutManagementComponent } from "@/features/property-assets/layout";

// UI Components
import { CustomBreadcrumb } from "@/components/Layout/CustomBreadCrumb/custom-breadcrumb";
import { LoadingDotPulse } from "@/components/ui/loading-dot-pulse";

export default function LayoutManagementPage() {
  const { t } = useTranslation();

  return (
    <div className="p-6">
      {/* Header with Controls - Always Visible */}
      <div className="mb-6 flex items-center justify-between">
        <div>
          <CustomBreadcrumb />
          <h1 className="mt-2 text-2xl font-bold text-foreground">
            {t("pages.layouts.title") || "Layout Management"}
          </h1>
          <p className="text-sm text-muted-foreground">
            {t("pages.layouts.description") || "Manage property layouts and unit mapping"}
          </p>
        </div>
      </div>

      {/* Content Area */}
      <Suspense
        fallback={
          <div className="flex h-96 items-center justify-center">
            <LoadingDotPulse />
          </div>
        }>
        {/* <LayoutManagementComponent /> */}
      </Suspense>
    </div>
  );
}
