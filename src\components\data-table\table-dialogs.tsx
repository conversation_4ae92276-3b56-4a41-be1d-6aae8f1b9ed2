import { useTranslation } from "react-i18next";

import { ConfirmDialog } from "../ui/confirm-dialog";
import { DataTableViewDialog } from "./data-table-view-dialog";

interface TableDialogsProps {
  isDeleteDialogOpen: boolean;
  isViewDialogOpen: boolean;
  formattedSelectedRows: number[];
  deleteLoading: boolean;
  deleteConfirmationDescription?: string;
  table: any; // ReactTable<any>
  onDeleteConfirm: () => void;
  onDeleteDialogToggle: (open: boolean) => void;
  onViewDialogToggle: (open: boolean) => void;
}

export function TableDialogs({
  isDeleteDialogOpen,
  isViewDialogOpen,
  formattedSelectedRows,
  deleteLoading,
  deleteConfirmationDescription,
  table,
  onDeleteConfirm,
  onDeleteDialogToggle,
  onViewDialogToggle,
}: TableDialogsProps) {
  const { t } = useTranslation();

  return (
    <>
      <ConfirmDialog
        open={isDeleteDialogOpen}
        variant="destructive"
        onOpenChange={onDeleteDialogToggle}
        onConfirm={onDeleteConfirm}
        title={t("common.areYouSure")}
        description={
          formattedSelectedRows.length === 1
            ? deleteConfirmationDescription
              ? t(deleteConfirmationDescription, { count: formattedSelectedRows.length })
              : t("common.deleteProductConfirmation")
            : t("common.deleteListProductConfirmation", { count: formattedSelectedRows.length })
        }
        confirmText={deleteLoading ? t("common.deleting") : t("common.delete")}
        cancelText={t("common.cancel")}
        loading={deleteLoading}
        isControl
      />
      <DataTableViewDialog
        table={table}
        open={isViewDialogOpen}
        onOpenChange={onViewDialogToggle}
      />
    </>
  );
}
