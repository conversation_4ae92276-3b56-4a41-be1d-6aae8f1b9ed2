"use client";

import { useEffect } from "react";
import <PERSON><PERSON><PERSON><PERSON>, { DocViewerRenderers } from "react-doc-viewer";
import { useTranslation } from "react-i18next";

import { useKnowledgeDetail } from "@/features/bots/knowledge/hooks/knowledge";
import { useKnowledgePreview } from "@/features/bots/knowledge/hooks/useKnowledgePreview";
import { Knowledge } from "@/features/bots/knowledge/types";

import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";

interface KnowledgePreviewModalProps {
  knowledge: Knowledge | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function KnowledgePreviewModal({
  knowledge,
  open,
  onOpenChange,
}: KnowledgePreviewModalProps) {
  const { t } = useTranslation();

  // Handle URL source directly without modal
  useEffect(() => {
    if (open && knowledge?.source === "URL" && knowledge.url) {
      window.open(knowledge.url, "_blank", "noopener,noreferrer");
      onOpenChange(false);
    }
  }, [open, knowledge, onOpenChange]);

  const { data: detailedKnowledge, isLoading: isDetailLoading } = useKnowledgeDetail(
    knowledge?.id || ""
  );

  const { content, presignedUrl, contentType, isUrlContent, isLoading, error, loadContent } =
    useKnowledgePreview(knowledge, detailedKnowledge);

  useEffect(() => {
    if (open && knowledge && knowledge.source !== "URL") {
      loadContent();
    }
  }, [open, knowledge, loadContent]);

  useEffect(() => {
    if (isUrlContent && detailedKnowledge?.raw_data) {
      const url = detailedKnowledge.raw_data.replace(/^b['"]?/, "").replace(/['"]?$/, "");
      window.open(url, "_blank", "noopener,noreferrer");
      onOpenChange(false);
    }
  }, [isUrlContent, detailedKnowledge, onOpenChange]);

  // Don't render modal for URL sources or URL content
  if (!knowledge || knowledge.source === "URL" || isUrlContent) return null;

  const showLoading = isDetailLoading || isLoading;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="h-[80vh] w-[calc(80vh*595/842)] p-0">
        <div className="size-full bg-card shadow">
          <div className="size-full overflow-hidden">
            {showLoading ? (
              <LoadingSkeleton />
            ) : error ? (
              <ErrorContent error={error} />
            ) : presignedUrl && contentType ? (
              <PreviewContent presignedUrl={presignedUrl} contentType={contentType} />
            ) : (
              <TextContent content={content} />
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

interface PreviewContentProps {
  presignedUrl: string;
  contentType: string;
}

function PreviewContent({ presignedUrl, contentType }: PreviewContentProps) {
  const isPdf = contentType === "application/pdf";

  if (isPdf) {
    return (
      <iframe
        id="pdf-viewer"
        title="PDF Viewer"
        width="100%"
        height="100%"
        loading="lazy"
        src={presignedUrl}
        className="border-0"
      />
    );
  }

  return (
    <div className="size-[200%] origin-top-left scale-50">
      <DocViewer
        documents={[
          {
            uri: presignedUrl,
            fileType: contentType,
          },
        ]}
        pluginRenderers={DocViewerRenderers}
        style={{ height: "100%", width: "100%" }}
        config={{
          header: {
            disableHeader: true,
            disableFileName: true,
          },
        }}
      />
    </div>
  );
}

interface TextContentProps {
  content: string;
}

function TextContent({ content }: TextContentProps) {
  return (
    <div className="size-full overflow-y-auto overflow-x-hidden p-6">
      <pre className="m-0 whitespace-pre-wrap break-all border-0 bg-transparent p-0 font-sans text-sm leading-relaxed text-foreground">
        {content}
      </pre>
    </div>
  );
}

function LoadingSkeleton() {
  return (
    <div className="flex h-full items-center justify-center p-6">
      <Skeleton className="size-full max-w-md rounded-lg" />
    </div>
  );
}

interface ErrorContentProps {
  error: string;
}

function ErrorContent({ error }: ErrorContentProps) {
  const { t } = useTranslation();

  return (
    <div className="flex h-full items-center justify-center p-6">
      <div className="text-center">
        <div className="mb-4 text-6xl">⚠️</div>
        <h3 className="mb-2 text-lg font-semibold text-foreground">
          {t("pages.knowledge.preview.errorTitle")}
        </h3>
        <p className="text-sm text-muted-foreground">
          {t("pages.knowledge.preview.errorDescription")}
        </p>
      </div>
    </div>
  );
}
