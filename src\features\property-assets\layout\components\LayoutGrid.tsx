import Empty from "@/components/ui/empty";
import type { Layout } from "@/lib/apis/types/property_assets/layout";

import { LayoutCard } from "./LayoutCard";
import { LayoutGridSkeleton } from "./unit-mapping/layout-grid-skeleton";

interface LayoutGridProps {
  layouts: Layout[];
  loading?: boolean;
}

// Mock data for demonstration when API returns empty results
const mockLayouts: Layout[] = [
  {
    id: "mock-1",
    company_id: "mock-company-1",
    name: "Tầng 2-5 - <PERSON><PERSON><PERSON>",
    property_id: "mock-property-1",
    floor_number: 2,
    description: "Luxury apartment layout for floors 2-5",
    status: "Complete",
    unit_layout_positions: [
      {
        unit_id: "unit-1",
        x: 100,
        y: 150,
        shape_type: "RECTANGLE",
        style_config: {
          fill_color: "#FF910B",
          stroke_color: "#D4D4D8",
          stroke_width: "1px",
          opacity: 1,
          label_text: "Unit 1",
          label_font_size: 12,
          label_color: "#000000",
          visible: true,
        },
      },
      {
        unit_id: "unit-2",
        x: 250,
        y: 150,
        shape_type: "RECTANGLE",
        style_config: {
          fill_color: "#FF910B",
          stroke_color: "#D4D4D8",
          stroke_width: "1px",
          opacity: 1,
          label_text: "Unit 2",
          label_font_size: 12,
          label_color: "#000000",
          visible: true,
        },
      },
      {
        unit_id: "unit-3",
        x: 400,
        y: 150,
        shape_type: "RECTANGLE",
        style_config: {
          fill_color: "#FF910B",
          stroke_color: "#D4D4D8",
          stroke_width: "1px",
          opacity: 1,
          label_text: "Unit 3",
          label_font_size: 12,
          label_color: "#000000",
          visible: true,
        },
      },
    ],
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-20T14:30:00Z",
  },
  {
    id: "mock-2",
    company_id: "mock-company-1",
    name: "Tầng 6-8 - Căn Hộ Trung Cấp",
    property_id: "mock-property-1",
    floor_number: 6,
    description: "Mid-range apartment layout for floors 6-8",
    status: "In Progress",
    unit_layout_positions: [
      {
        unit_id: "unit-4",
        x: 120,
        y: 200,
        shape_type: "RECTANGLE",
        style_config: {
          fill_color: "#16A34A",
          stroke_color: "#D4D4D8",
          stroke_width: "1px",
          opacity: 1,
          label_text: "Unit 4",
          label_font_size: 12,
          label_color: "#000000",
          visible: true,
        },
      },
      {
        unit_id: "unit-5",
        x: 280,
        y: 200,
        shape_type: "RECTANGLE",
        style_config: {
          fill_color: "#16A34A",
          stroke_color: "#D4D4D8",
          stroke_width: "1px",
          opacity: 1,
          label_text: "Unit 5",
          label_font_size: 12,
          label_color: "#000000",
          visible: true,
        },
      },
    ],
    created_at: "2024-01-10T09:00:00Z",
    updated_at: "2024-01-18T16:45:00Z",
  },
  {
    id: "mock-3",
    company_id: "mock-company-1",
    name: "Tầng 9-12 - Căn Hộ Studio",
    property_id: "mock-property-1",
    floor_number: 9,
    description: "Studio apartment layout for floors 9-12",
    status: "Pending",
    unit_layout_positions: [],
    created_at: "2024-01-05T11:00:00Z",
    updated_at: "2024-01-05T11:00:00Z",
  },
];

export function LayoutGrid({ layouts, loading = false }: LayoutGridProps) {
  // Show skeleton when loading
  if (loading) {
    return <LayoutGridSkeleton />;
  }

  // Use mock data if no layouts are provided
  if (layouts && layouts.length === 0) return <Empty />;

  return (
    <div className="space-y-4">
      {/* Layout Grid */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {layouts.map((layout) => (
          <LayoutCard key={layout.id} layout={layout} />
        ))}
      </div>
    </div>
  );
}
