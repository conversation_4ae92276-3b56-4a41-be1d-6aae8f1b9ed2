"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";

import { useDistricts, useProvinces, useWards } from "@/features/customer/hooks/address";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { PortalCombobox } from "@/components/ui/portal-combobox";
import useDebounce from "@/hooks/use-debounce";

interface AddressSectionProps {
  form: any;
}

export function AddressSection({ form }: AddressSectionProps) {
  const { t } = useTranslation();

  // Search state for address fields
  const [provinceQuery, setProvinceQuery] = useState("");
  const [districtQuery, setDistrictQuery] = useState("");
  const [wardQuery, setWardQuery] = useState("");

  // Debounced search queries
  const debouncedProvinceQuery = useDebounce(provinceQuery, 800);
  const debouncedDistrictQuery = useDebounce(districtQuery, 800);
  const debouncedWardQuery = useDebounce(wardQuery, 800);

  // Address API hooks
  const {
    provinces,
    isLoading: isLoadingProvinces,
    fetchNextPage: fetchNextProvinces,
    hasNextPage: hasNextProvinces,
    isFetchingNextPage: isFetchingNextProvinces,
  } = useProvinces(debouncedProvinceQuery);

  const {
    districts,
    isLoading: isLoadingDistricts,
    fetchNextPage: fetchNextDistricts,
    hasNextPage: hasNextDistricts,
    isFetchingNextPage: isFetchingNextDistricts,
  } = useDistricts(form.watch("address.province"), debouncedDistrictQuery);

  const {
    wards,
    isLoading: isLoadingWards,
    fetchNextPage: fetchNextWards,
    hasNextPage: hasNextWards,
    isFetchingNextPage: isFetchingNextWards,
  } = useWards(form.watch("address.province"), form.watch("address.district"), debouncedWardQuery);

  // Handle province change
  const handleProvinceChange = (value: string | string[]) => {
    const province = provinces.find((p) => p.code === value);
    form.setValue("address.province", province?.code || "");
    // Clear dependent fields when province changes
    form.setValue("address.district", "");
    form.setValue("address.ward", "");
  };

  // Handle district change
  const handleDistrictChange = (value: string | string[]) => {
    const district = districts.find((d) => d.code === value);
    form.setValue("address.district", district?.code || "");
    // Clear ward when district changes
    form.setValue("address.ward", "");
  };

  // Handle ward change
  const handleWardChange = (value: string | string[]) => {
    const ward = wards.find((w) => w.code === value);
    form.setValue("address.ward", ward?.code || "");
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-base font-medium text-foreground">
          {t("pages.properties.addressInformation")}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Street Address */}
        <FormField
          control={form.control}
          name="address.address1"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-foreground">
                {t("pages.properties.address.street")} <span className="text-destructive">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder={t("pages.properties.placeholders.street")}
                  className="h-9 border-input focus:border-primary focus:ring-ring"
                  {...field}
                />
              </FormControl>
              <FormMessage className="text-destructive" />
            </FormItem>
          )}
        />

        {/* Province, District, Ward Row */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <FormField
            control={form.control}
            name="address.province"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-foreground">
                  {t("pages.properties.address.province")}
                </FormLabel>
                <FormControl>
                  <PortalCombobox
                    items={provinces.map((p) => ({
                      id: p.code,
                      name: p.name_with_type,
                      displayValue: p.name_with_type,
                    }))}
                    value={field.value}
                    onValueChange={handleProvinceChange}
                    placeholder={t("pages.properties.placeholders.selectProvince")}
                    emptyText={t("common.empty.description", "No provinces found.")}
                    isLoading={isLoadingProvinces}
                    onLoadMore={hasNextProvinces ? fetchNextProvinces : undefined}
                    hasNextPage={hasNextProvinces}
                    isLoadingMore={isFetchingNextProvinces}
                    onSearchQueryChange={setProvinceQuery}
                  />
                </FormControl>
                <FormMessage className="text-destructive" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="address.district"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-foreground">
                  {t("pages.properties.address.district")}
                </FormLabel>
                <FormControl>
                  <PortalCombobox
                    items={districts.map((d) => ({
                      id: d.code,
                      name: d.name_with_type,
                      displayValue: d.name_with_type,
                    }))}
                    value={field.value}
                    onValueChange={handleDistrictChange}
                    placeholder={t("pages.properties.placeholders.selectDistrict")}
                    emptyText={t("common.empty.description", "No districts found.")}
                    isLoading={isLoadingDistricts}
                    onLoadMore={hasNextDistricts ? fetchNextDistricts : undefined}
                    hasNextPage={hasNextDistricts}
                    isLoadingMore={isFetchingNextDistricts}
                    onSearchQueryChange={setDistrictQuery}
                  />
                </FormControl>
                <FormMessage className="text-destructive" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="address.ward"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-foreground">
                  {t("pages.properties.address.ward")}
                </FormLabel>
                <FormControl>
                  <PortalCombobox
                    items={wards.map((w) => ({
                      id: w.code,
                      name: w.name_with_type,
                      displayValue: w.name_with_type,
                    }))}
                    value={field.value}
                    onValueChange={handleWardChange}
                    placeholder={t("pages.properties.placeholders.selectWard")}
                    emptyText={t("common.empty.description", "No wards found.")}
                    isLoading={isLoadingWards}
                    onLoadMore={hasNextWards ? fetchNextWards : undefined}
                    hasNextPage={hasNextWards}
                    isLoadingMore={isFetchingNextWards}
                    onSearchQueryChange={setWardQuery}
                  />
                </FormControl>
                <FormMessage className="text-destructive" />
              </FormItem>
            )}
          />
        </div>
        {/* Zip Code */}
        <FormField
          control={form.control}
          name="address.zip"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-foreground">
                {t("pages.properties.address.zip")}
              </FormLabel>
              <FormControl>
                <Input
                  placeholder={t("pages.properties.placeholders.zip")}
                  className="h-9 border-input focus:border-primary focus:ring-ring"
                  {...field}
                />
              </FormControl>
              <FormMessage className="text-destructive" />
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  );
}
