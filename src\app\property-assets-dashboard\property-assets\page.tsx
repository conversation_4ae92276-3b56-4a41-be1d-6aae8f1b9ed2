"use client";

import { useState } from "react";
import {
  BarChart3,
  Building2,
  Calculator,
  DollarSign,
  FileText,
  Home,
  Image,
  Users,
} from "lucide-react";

import { AssetCategories } from "@/features/property-assets/components/AssetCategories";
import { DocumentLibrary } from "@/features/property-assets/components/DocumentManagement";
// Financial Management Components
import {
  ExpenseBreakdown,
  FinancialMetricsCards,
  ProfitTrendChart,
  RevenueCharts,
} from "@/features/property-assets/components/Financial";
import { PropertyGallery } from "@/features/property-assets/components/PropertyImageGallery";
// Unit Management Components
import {
  AvailabilityCalendar,
  BulkUnitOperations,
  OccupancyAnalytics,
  UnitRevenueTracking,
  UnitSpecifications,
} from "@/features/property-assets/components/UnitManagement";
// Property Management Components
import {
  PropertyValuation,
  ValuationHistory,
} from "@/features/property-assets/property/components/PropertyValuation";

import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function PropertyAssetsPage() {
  const [activeTab, setActiveTab] = useState("overview");

  const dashboardStats = {
    totalProperties: 12,
    totalUnits: 248,
    occupancyRate: 92.5,
    monthlyRevenue: 875000,
    totalValue: 45800000,
    avgRentPerUnit: 3250,
  };

  const quickStats = [
    {
      title: "Total Properties",
      value: dashboardStats.totalProperties,
      icon: Building2,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Total Units",
      value: dashboardStats.totalUnits,
      icon: Home,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "Occupancy Rate",
      value: `${dashboardStats.occupancyRate}%`,
      icon: Users,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
    {
      title: "Monthly Revenue",
      value: `$${(dashboardStats.monthlyRevenue / 1000).toFixed(0)}K`,
      icon: DollarSign,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
  ];

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Property Assets Dashboard</h1>
          <p className="text-muted-foreground">Comprehensive property and unit management system</p>
        </div>
        <Badge variant="outline" className="border-green-600 text-green-600">
          Live System
        </Badge>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
        {quickStats.map((stat, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                  <p className="text-2xl font-bold">{stat.value}</p>
                </div>
                <div className={`rounded-full p-3 ${stat.bgColor}`}>
                  <stat.icon className={`size-6 ${stat.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="financial">Financial</TabsTrigger>
          <TabsTrigger value="properties">Properties</TabsTrigger>
          <TabsTrigger value="units">Units</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="operations">Operations</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BarChart3 className="size-5" />
                  <span>Financial Overview</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <FinancialMetricsCards className="grid-cols-2" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Users className="size-5" />
                  <span>Occupancy Overview</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <OccupancyAnalytics className="h-64" />
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calculator className="size-5" />
                  <span>Property Valuations</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <PropertyValuation />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="size-5" />
                  <span>Document Library</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <DocumentLibrary />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Image className="size-5" />
                  <span>Property Gallery</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <PropertyGallery />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Financial Tab */}
        <TabsContent value="financial" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            <FinancialMetricsCards />
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              <RevenueCharts />
              <ExpenseBreakdown />
            </div>
            <ProfitTrendChart />
          </div>
        </TabsContent>

        {/* Properties Tab */}
        <TabsContent value="properties" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            <PropertyValuation />
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              <ValuationHistory />
              <AssetCategories />
            </div>
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              <DocumentLibrary />
              <PropertyGallery />
            </div>
          </div>
        </TabsContent>

        {/* Units Tab */}
        <TabsContent value="units" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            <UnitSpecifications />
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              <AvailabilityCalendar />
              <UnitRevenueTracking />
            </div>
          </div>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            <OccupancyAnalytics />
            <UnitRevenueTracking />
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              <RevenueCharts />
              <ProfitTrendChart />
            </div>
          </div>
        </TabsContent>

        {/* Operations Tab */}
        <TabsContent value="operations" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            <BulkUnitOperations />
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              <AvailabilityCalendar />
              <DocumentLibrary />
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
