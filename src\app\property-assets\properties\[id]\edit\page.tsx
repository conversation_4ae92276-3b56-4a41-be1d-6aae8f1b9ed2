"use client";

import { Loader2 } from "lucide-react";

import { useProperty } from "@/features/property-assets";
import { PropertyForm } from "@/features/property-assets/property/components/PropertyForm";

interface EditPropertyPageProps {
  params: {
    id: string;
  };
}

export default function EditPropertyPage({ params }: EditPropertyPageProps) {
  const propertyId = params.id as string;
  console.log(propertyId);
  const { data: property, isLoading, error } = useProperty(propertyId);

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="text-center">
            <Loader2 className="mx-auto mb-4 size-8 animate-spin" />
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !property) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h1 className="mb-4 text-2xl font-bold">Property Not Found</h1>
          <p className="text-muted-foreground">
            The property you're looking for doesn't exist or has been removed.
          </p>
        </div>
      </div>
    );
  }

  return <PropertyForm initialData={property} isEditing={true} />;
}
