import { useEffect, useState } from "react";
import Image from "next/image";
import { useTranslation } from "react-i18next";

import { PortalCombobox } from "@/components/ui/portal-combobox";

interface AddLanguageSelectorProps {
  selectedLanguages: string[];
  onAddLanguage: (languageCode: string) => void;
  getAllLanguagesForSelection: () => Array<{
    code: string;
    name: string;
    nativeName: string;
    flag: any;
    isDisabled: boolean;
    isDirty?: boolean;
  }>;
}

export const AddLanguageSelector = ({
  selectedLanguages,
  onAddLanguage,
  getAllLanguagesForSelection,
}: AddLanguageSelectorProps) => {
  const { t } = useTranslation();
  const [languageItems, setLanguageItems] = useState<any[]>([]);

  // Update language items when selectedLanguages changes
  useEffect(() => {
    const allLanguages = getAllLanguagesForSelection();
    const items = allLanguages.map((language) => ({
      id: language.code,
      name: language.name,
      displayValue: language.nativeName,
      flag: language.flag,
      code: language.code,
      disabled: language.isDisabled,
      isDirty: language.isDirty,
    }));
    setLanguageItems(items);
  }, [selectedLanguages, getAllLanguagesForSelection]);

  const handleLanguageSelect = (value: string | string[]) => {
    // Handle both single and multiple selection
    if (Array.isArray(value)) {
      // Multiple selection - add all non-disabled languages
      value.forEach((languageCode) => {
        const selectedLanguage = languageItems.find((lang) => lang.id === languageCode);
        if (selectedLanguage && !selectedLanguage.disabled) {
          onAddLanguage(languageCode);
        }
      });
    } else {
      // Single selection
      const selectedLanguage = languageItems.find((lang) => lang.id === value);
      if (selectedLanguage && !selectedLanguage.disabled) {
        onAddLanguage(value);
      }
    }
  };

  // Custom icon display for languages
  const iconDisplay = (item: any) => (
    <div className="mr-2 flex items-center gap-2">
      <Image src={item.flag} alt={item.name} width={16} height={16} />
    </div>
  );

  return (
    <PortalCombobox
      value=""
      onValueChange={handleLanguageSelect}
      items={languageItems}
      placeholder={t("pages.settings.language.addLanguage")}
      searchPlaceholder={t("pages.settings.language.searchLanguage")}
      emptyText="No languages available"
      iconDisplay={iconDisplay}
      showCheck={true}
      variantButton="outline"
      multiple={true}
      showSelectedCount={true}
      showPlusForAvailable={true}
      popoverWidth="w-[200px]"
      align="center"
    />
  );
};
