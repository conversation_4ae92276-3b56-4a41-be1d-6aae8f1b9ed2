"use client";

import { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { columns } from "@/features/integration/FetchEvents/columns";
import {
  ActionGroupOptions,
  ActionTypeOptions,
  EventSourceOption,
  StatusOptions,
} from "@/features/integration/FetchEvents/filter-options";
import { useFetchEvents } from "@/features/integration/hooks/fetch-events";

import { TableContainer } from "@/components/custom-table/container/table-container";
import GroupButton, { GroupButtonProps } from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { EFilterType, FilterTableProps, FilterType } from "@/components/data-table/types";
import { usePermission } from "@/components/provider/permission-provider";

// export default function FetchEventsPage() {
//   return <FetchEventsContent />;
// }

export default function FetchEventsPage() {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch } = useDatatable();
  const { hasPermission } = usePermission();
  const hasListFetchEvents = hasPermission("LIST_FETCH_EVENTS");

  const options = useMemo(
    () => ({
      limit: Number(getInitialParams.limit),
      enabled: hasListFetchEvents,
      ...getInitialParams,
    }),
    [getInitialParams, hasListFetchEvents]
  );

  const { fetchEvents, total, isLoading, isFetching, refetch } = useFetchEvents(options);

  const isTableLoading = isLoading || isFetching;

  const filterConfig: FilterTableProps = useMemo(() => {
    return {
      showSearch: true,
      filterType: "fetchEvents",
      searchPlaceHolder: t("pages.fetchEvents.filters.search.placeholder"),
      numberOfFilters: 2,
      initialValues: getInitialParams,
      listFilter: [
        {
          id: "action_type",
          type: EFilterType.SELECT_BOX,
          title: t("pages.fetchEvents.filters.actionType"),
          defaultValue: getInitialParams["action_type"],
          dataOption: ActionTypeOptions,
        },
        {
          id: "source.channel_name",
          type: EFilterType.SELECT_BOX,
          title: t("pages.products.filters.source"),
          defaultValue: getInitialParams["source.channel_name"],
          isChannelFetch: true,
          remote: true,
        },
        {
          id: "status",
          type: EFilterType.SELECT_BOX,
          title: t("pages.fetchEvents.filters.status"),
          defaultValue: getInitialParams["status"],
          dataOption: StatusOptions,
        },
        {
          id: "action_group",
          type: EFilterType.SELECT_BOX,
          title: t("pages.fetchEvents.filters.actionGroup"),
          defaultValue: getInitialParams["action_group"],
          dataOption: ActionGroupOptions,
        },
        {
          id: "event_source",
          type: EFilterType.SELECT_BOX,
          title: t("pages.fetchEvents.filters.eventSource"),
          defaultValue: getInitialParams["event_source"],
          dataOption: EventSourceOption,
        },
        {
          id: "event_time",
          type: "date",
          defaultValue: getInitialParams["event_time"],
          title: t("pages.fetchEvents.filters.eventTime"),
        },

        {
          id: "created_at",
          type: EFilterType.DATE,
          title: t("pages.products.filters.createdAt"),
          defaultValue: {
            from: getInitialParams["created_at_from"],
            to: getInitialParams["created_at_to"],
          },
        },
        {
          id: "updated_at",
          type: EFilterType.DATE,
          title: t("pages.products.filters.updatedAt"),
          defaultValue: {
            from: getInitialParams["updated_at_from"],
            to: getInitialParams["updated_at_to"],
          },
        },
      ] as FilterType[],
      handleParamSearch,
      listLoading: isTableLoading,
    };
  }, [handleParamSearch, isTableLoading, t, getInitialParams]);

  const groupButtonConfig: GroupButtonProps = {
    onRefresh: () => refetch(),
    isRefreshLoading: isFetching,
  };

  return (
    <TableCard tableKey="FETCH_EVENTS" className="border-none">
      <TableHeader
        title={t("pages.fetchEvents.title")}
        filterProps={filterConfig as FilterTableProps}
        filterType="fetchEvents"
        data={fetchEvents || []}
        rightComponent={<GroupButton {...groupButtonConfig} />}
      />
      <TableContainer
        columns={columns(t)}
        data={fetchEvents || []}
        loading={isTableLoading}
        total={total}
        pageSize={Number(getInitialParams.limit)}
        currentPage={Number(getInitialParams.page)}
      />
    </TableCard>
  );
}
