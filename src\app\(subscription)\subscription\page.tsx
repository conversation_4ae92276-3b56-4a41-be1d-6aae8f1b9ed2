"use client";

import {
  CurrentPlanCard,
  FAQSection,
  PricingSection,
  UsageStatsCard,
} from "@/features/subscription/subscription/components";
import { CurrentPlan, useSubscription } from "@/features/subscription/subscription/hooks";

import { Card, Separator } from "@/components/ui";

export default function SubscriptionPage() {
  const {
    isAnnualBilling,
    currentPlan,
    usageStats,
    plans,
    isLoading,
    handleBillingToggle,
    handleCancelSubscription,
    formatPrice,
    subscriptionData,
  } = useSubscription();
  // Check if user has an active subscription
  const hasActiveSubscription = !!subscriptionData;

  return (
    <div className="flex flex-col gap-4 p-4 pt-0">
      {/* Current Plan and Usage Stats Container */}
      {hasActiveSubscription && (
        <Card className="grid grid-cols-1 border-2 border-primary p-4">
          <CurrentPlanCard
            isAnnualBilling={isAnnualBilling}
            currentPlan={currentPlan as CurrentPlan}
            onCancelSubscription={handleCancelSubscription}
          />
          <Separator className="my-4" />
          <UsageStatsCard usageStats={usageStats} />
        </Card>
      )}

      <PricingSection
        plans={plans}
        isAnnualBilling={isAnnualBilling}
        onBillingToggle={handleBillingToggle}
        formatPrice={formatPrice}
        currentPlan={currentPlan as CurrentPlan}
        isLoading={isLoading}
      />

      {/* FAQ Section */}
      <FAQSection />
    </div>
  );
}
