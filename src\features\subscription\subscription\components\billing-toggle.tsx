"use client";

import { useTranslation } from "react-i18next";

import { Switch } from "@/components/ui/switch";

interface BillingToggleProps {
  isAnnualBilling: boolean;
  onToggle: () => void;
}

export const BillingToggle = ({ isAnnualBilling, onToggle }: BillingToggleProps) => {
  const { t } = useTranslation();

  return (
    <div className="flex items-center justify-end gap-2 p-2">
      <div className="flex items-center gap-2">
        <span className="text-sm font-medium text-foreground">
          {t("pages.subscription.billing.annualPlan")}
        </span>
        <span className="text-sm font-medium text-muted-foreground">
          ({t("pages.subscription.billing.savings")})
        </span>
        <Switch checked={isAnnualBilling} onCheckedChange={onToggle} />
      </div>
    </div>
  );
};
