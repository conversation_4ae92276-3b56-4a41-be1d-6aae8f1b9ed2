import config from "@/config";
import { CONTACT_ENDPOINTS } from "@/constants/endpoints";

import { adminApi } from "../api_helper";
import { ResponseAxiosDetail } from "./types/common";

interface ContactPayload {
  name: string;
  phone: string;
  email: string | null;
  organization_name: string | null;
  message: string;
  category: string | null;
  subject: string;
  source: string;
  enabled_notification: boolean;
}

interface ContactFormData {
  fullName: string;
  phoneNumber: string;
  email?: string;
  company?: string;
  message: string;
  interests?: string[];
  subscribe?: boolean;
}

const baseURL = config.API_URL_ONEXBOTS;

export const contactApi = {
  sendMessage: async (payload: ContactPayload) => {
    return await adminApi.post<ResponseAxiosDetail<any>>(CONTACT_ENDPOINTS.SEND_MESSAGE, payload);
  },
};

export type { ContactPayload, ContactFormData };
