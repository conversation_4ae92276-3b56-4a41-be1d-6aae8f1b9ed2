import { useEffect, useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { authKeys } from "@/features/auth/hooks/keys";

import { authApi } from "@/lib/apis/auth";

interface UseResendCodeOptions {
  onSuccess?: () => void;
}

export const useResendCode = (options: UseResendCodeOptions = {}) => {
  const { t } = useTranslation();
  const [countdown, setCountdown] = useState(0);
  const [isCheckingDelay, setIsCheckingDelay] = useState(true);

  // Save countdown for password reset
  const saveCountdown = (username: string, delay = 60) => {
    try {
      const expiryTimestamp = Date.now() + delay * 1000;
      localStorage.setItem(`resend_reset_countdown_${username}`, expiryTimestamp.toString());
      setCountdown(delay);
    } catch (error) {
      console.error("Error saving password reset countdown:", error);
    }
  };

  // Get remaining countdown for password reset
  const getCountdown = (username: string): number => {
    try {
      const storedValue = localStorage.getItem(`resend_reset_countdown_${username}`);
      if (!storedValue) return 0;

      const expiryTimestamp = parseInt(storedValue, 10);
      const currentTime = Date.now();

      const remainingSeconds = Math.max(0, Math.floor((expiryTimestamp - currentTime) / 1000));
      return remainingSeconds;
    } catch (error) {
      console.error("Error getting password reset countdown:", error);
      return 0;
    }
  };

  // Decrease countdown every second
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown((prev) => Math.max(0, prev - 1));
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const {
    mutate: onSubmit,
    isPending: loading,
    error,
    isSuccess: success,
  } = useMutation({
    mutationKey: authKeys.forgotPassword(""),
    mutationFn: async (username: string) => {
      if (!username) {
        throw new Error(t("auth.usernameRequired"));
      }
      return authApi.forgot({ username });
    },
    onSuccess: (_, username) => {
      toast.success(t("auth.forgotPasswordSuccess"));
      saveCountdown(username);
      options.onSuccess?.();
    },
    onError: (error) => {
      const message = error instanceof Error ? error.message : t(String(error));
      toast.error(t("common.error"), {
        description: message,
      });
    },
  });

  // Check countdown for username
  const checkCountdown = (username: string) => {
    setIsCheckingDelay(true);
    const remainingTime = getCountdown(username);
    setCountdown(remainingTime);
    setIsCheckingDelay(false);
    return remainingTime;
  };

  return {
    onSubmit,
    loading,
    error,
    success,
    countdown,
    isCheckingDelay,
    checkCountdown,
  };
};
