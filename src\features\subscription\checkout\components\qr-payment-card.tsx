"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import Lot<PERSON> from "lottie-react";
import { X } from "lucide-react";
import { useTranslation } from "react-i18next";

import { useSubscriptionByIdPolling } from "@/features/subscription/hooks/subscription";

import AnimationDone from "@/assets/lottie/AnimationDone.json";
import { Button } from "@/components/ui";
import { authProtectedPaths } from "@/constants/paths";

interface QRPaymentCardProps {
  accountName: string;
  accountNumber: string;
  bankName: string;
  amount: number;
  currency: string;
  transferContent: string;
  onCancelOrder: () => void;
  onPaymentSuccess?: () => void;
  isLoading?: boolean;
  qrUrl: string;
  subscriptionId?: string;
  paymentStatus: PaymentStatus;
}

type PaymentStatus = "PENDING" | "ACTIVE";

export const QRPaymentCard = ({
  accountName,
  accountNumber,
  bankName,
  amount,
  currency,
  transferContent,
  onCancelOrder,
  isLoading,
  qrUrl = "",
  subscriptionId,
  paymentStatus,
}: QRPaymentCardProps) => {
  const { t } = useTranslation();
  const router = useRouter();
  console.log(paymentStatus);
  // Poll subscription status if pending
  const { data: subscriptionData } = useSubscriptionByIdPolling(
    subscriptionId || "",
    paymentStatus === "PENDING" && !!subscriptionId,
    5000
  );
  console.log(qrUrl);

  useEffect(() => {
    console.log("subscriptionData", subscriptionData);

    if (paymentStatus === "ACTIVE") {
      console.log("BACK TO SUBS");
      const redirectTimeout = setTimeout(() => {
        window.location.href = authProtectedPaths.SUBSCRIPTION;
      }, 5000);
      return () => {
        clearTimeout(redirectTimeout);
      };
    }
  }, [paymentStatus]);

  const formatPrice = (amount: number) => {
    return new Intl.NumberFormat("vi-VN").format(amount);
  };
  // handle payment success : show toast to redirect to subscription page after 5s

  const paymentInfor = [
    {
      label: t("pages.checkout.qrPayment.accountName"),
      value: accountName,
      showBorder: false,
    },
    {
      label: t("pages.checkout.qrPayment.accountNumber"),
      value: accountNumber,
    },
    {
      label: t("pages.checkout.qrPayment.bankName"),
      value: bankName,
    },
    {
      label: t("pages.checkout.qrPayment.amount"),
      value: (
        <div className="flex items-center gap-1">
          <span>{formatPrice(amount)}</span>
          <span className="text-muted-foreground">{currency}</span>
        </div>
      ),
    },
    {
      label: t("pages.checkout.qrPayment.content"),
      value: transferContent,
    },
  ];

  return (
    <div className="flex size-full flex-col items-center justify-center gap-8 rounded-xl bg-card p-2 pt-16 md:p-6">
      <div className="flex w-full flex-col items-center justify-center gap-2.5">
        {paymentStatus === "ACTIVE" && (
          <h3 className="text-xl font-bold text-foreground">
            {t("pages.checkout.qrPayment.success")}
          </h3>
        )}
        <div className="relative w-[70%] md:w-3/5">
          <img
            src={qrUrl || ""}
            alt="Example QR Code"
            className={`aspect-square h-auto w-full object-contain ${paymentStatus === "ACTIVE" ? "blur-sm" : ""}`}
          />
          {paymentStatus === "ACTIVE" && (
            <div className="absolute left-1/2 top-1/2 flex size-32 -translate-x-1/2 -translate-y-1/2 items-center justify-center">
              <Lottie
                animationData={AnimationDone}
                loop={false}
                autoplay={true}
                style={{ width: 128, height: 128 }}
              />
            </div>
          )}
        </div>
      </div>

      {/* Payment Information */}
      <div className="flex w-full flex-col">
        {paymentInfor.map((item, index) => (
          <PaymentInfoItem
            key={index}
            label={item.label}
            value={item.value}
            isLast={index === paymentInfor.length - 1}
          />
        ))}
      </div>

      {/* Cancel Order Button - only show when waiting */}
      {paymentStatus === "PENDING" && (
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
          onClick={onCancelOrder}
          loading={isLoading}>
          <X className="size-4" />
          {t("pages.checkout.qrPayment.cancelOrder")}
        </Button>
      )}
    </div>
  );
};
interface PaymentInfoItemProps {
  label: string;
  value: string | React.ReactNode;
  isLast?: boolean;
}
const PaymentInfoItem = ({ label, value, isLast = false }: PaymentInfoItemProps) => {
  return (
    <div
      className={`flex items-center justify-between border-b border-border py-2 ${isLast ? "border-b-0" : ""}`}>
      <span className="text-base text-muted-foreground">{label}:</span>
      <span className="text-base text-foreground">{value}</span>
    </div>
  );
};
