"use client";

import React, { useEffect, useState } from "react";
import { Separator } from "@radix-ui/react-select";
import { Plus } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Form, useZodForm } from "@/components/ui/form";

import { useAddEmployee } from "../hooks/use-add-employee";
import { useEmployee, useEmployees } from "../hooks/use-employee";
import { useEmployeeData } from "../hooks/use-employee-data";
import { addEmployeeSchema, editEmployeeSchema } from "../validator/employee";
import type {
  AddEmployeeFormData,
  EditEmployeeFormData,
  RoleAssignment,
} from "../validator/employee";
import { AddEmployeeDialogSkeleton } from "./dialogs/add-employee-dialog-skeleton";
import { BasicInfo } from "./dialogs/basic-info";
import { Password } from "./dialogs/password";
import { RoleAssignment as RoleAssignmentComponent } from "./dialogs/role-assignment";

interface AddEmployeeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  loading?: boolean;
  employee_id?: string;
  mode?: "add" | "edit" | "duplicate"; // Add duplicate mode
}

// Role Assignments Component
interface RoleAssignmentsProps {
  watchedRoles: any[];
  roleAssignments: RoleAssignment[];
  branches: any[];
  roles: any[];
  isLoadingBranches: boolean;
  isLoadingRoles: boolean;
  onUpdate: (index: number, field: "branch" | "roles", value: string | string[]) => void;
  onRemove: (index: number) => void;
  onAdd: () => void;
}

function RoleAssignments({
  watchedRoles,
  roleAssignments,
  branches,
  roles,
  isLoadingBranches,
  isLoadingRoles,
  onUpdate,
  onRemove,
  onAdd,
}: RoleAssignmentsProps) {
  const { t } = useTranslation();

  return (
    <div className="space-y-2">
      <Button type="button" variant="secondary" size="sm" onClick={onAdd} className="h-8 px-2">
        <Plus className="mr-1 size-4" />
        {t("pages.settings.assignNewRole", "Assign new role")}
      </Button>

      {roleAssignments.map((assignment, index) => (
        <RoleAssignmentComponent
          key={assignment.id}
          index={index}
          assignment={assignment}
          watchedRoles={watchedRoles}
          branches={branches}
          roles={roles}
          isLoadingBranches={isLoadingBranches}
          isLoadingRoles={isLoadingRoles}
          onUpdate={onUpdate}
          onRemove={onRemove}
          canRemove={roleAssignments.length > 1}
        />
      ))}
    </div>
  );
}

export function AddEmployeeDialog({
  open,
  onOpenChange,
  loading = false,
  employee_id,
  mode = "add",
}: AddEmployeeDialogProps) {
  const { t } = useTranslation();
  const { branches, roles, isLoadingBranches, isLoadingRoles } = useEmployeeData();
  const { data: employee, isLoading: isLoadingEmployee } = useEmployee(employee_id || "", {
    enabled: open && !!employee_id,
  });

  const { updateEmployeeMutation } = useEmployees();
  const addEmployeeMutation = useAddEmployee({
    onSuccess: () => {
      const message =
        mode === "edit"
          ? t("pages.settings.employeeUpdatedSuccess")
          : t("pages.settings.employeeCreatedSuccess");
      toast.success(message);
      onOpenChange(false);
    },
    onError: (error: any) => {
      if (error === "User account already exists") {
        toast.error(t("pages.settings.employeeAccountAlreadyExists"));
        return;
      } else if (
        error ===
        "User account already exists and is expired. Please call with action RESEND to reset user account"
      ) {
        toast.error(t("pages.settings.employeeAccountExpired"));
        return;
      } else if (error === "Incorrect username or password.") {
        toast.error(t("pages.settings.employeeAccountDoesNotExist"));
        return;
      } else if (error === "Access Denied") {
        toast.error(t("pages.settings.accessDenied"));
        return;
      } else if (error === "Email already exists") {
        toast.error(t("pages.settings.emailAlreadyInUse"));
        return;
      } else if (error === "Phone number already exists") {
        toast.error(t("pages.settings.phoneNumberAlreadyInUse"));
        return;
      }
      const message =
        mode === "edit"
          ? t("pages.settings.employeeUpdatedError")
          : t("pages.settings.employeeCreatedError");
      toast.error(message);
    },
  });

  const [roleAssignments, setRoleAssignments] = useState<RoleAssignment[]>([
    { id: "1", branch: "", roles: [] },
  ]);

  const form = useZodForm({
    schema: mode === "add" ? addEmployeeSchema : editEmployeeSchema,
    defaultValues:
      mode === "add"
        ? {
            employeeName: "",
            username: "",
            email: "",
            phone_number: "",
            birthdate: undefined,
            address: "",
            password: "",
            confirmPassword: "",
            roles: [{ branch: "", roles: [] }],
          }
        : {
            employeeName: "",
            username: undefined,
            email: undefined,
            phone_number: "",
            birthdate: undefined,
            address: "",
            roles: [{ branch: "", roles: [] }],
          },
    mode: "onChange", // Enable real-time validation
  });

  const {
    handleSubmit,
    formState: { isValid, isDirty },
    setValue,
    watch,
    reset,
    trigger,
  } = form;
  const watchedRoles = watch("roles");

  // Handle role changes - clear roles when branch changes, handle "All" branches
  useEffect(() => {
    if (watchedRoles && watchedRoles.length > 0) {
      const newRoles = [...watchedRoles];
      let hasChanges = false;

      // Check if any branch has "all" selected
      const hasAllBranches = newRoles.some((role) => role.branch === "all");

      if (hasAllBranches) {
        // If "All" is selected, keep only one assignment with "all"
        const allRole = newRoles.find((role) => role.branch === "all");
        if (allRole) {
          newRoles.splice(0, newRoles.length, allRole);
          setRoleAssignments([{ id: "1", branch: "all", roles: [] }]);
          hasChanges = true;
        }
      } else {
        // Clear roles when branch changes (but not when loading initial data)
        newRoles.forEach((role, index) => {
          if (role.branch && !role.roles.length) {
            // This means branch was just changed, clear roles
            newRoles[index] = { ...role, roles: [] };
            hasChanges = true;
          }
        });
      }

      if (hasChanges) {
        setValue("roles", newRoles, { shouldDirty: true, shouldTouch: true });
      }
    }
  }, [watchedRoles, setValue]);

  // Reset form when dialog opens in add mode
  useEffect(() => {
    if (open && mode === "add") {
      reset();
      setRoleAssignments([{ id: "1", branch: "", roles: [] }]);
    }
  }, [open, mode, reset]);

  // Update form schema when mode changes
  useEffect(() => {
    if (open) {
      // Reset form with new schema
      reset();
      setRoleAssignments([{ id: "1", branch: "", roles: [] }]);
    }
  }, [mode, open, reset]);

  // Fill form with existing data when editing or duplicating
  React.useEffect(() => {
    if (employee && (mode === "edit" || mode === "duplicate") && open) {
      setValue("employeeName", employee.name || "", { shouldDirty: false });
      if (mode === "edit") {
        // In edit mode, don't set username and email as they are disabled
        setValue("username", undefined, { shouldDirty: false });
        setValue("email", undefined, { shouldDirty: false });
      } else {
        // In duplicate mode, set username and email
        setValue(
          "username",
          mode === "duplicate" ? `${employee.username}_copy` : employee.username || "",
          { shouldDirty: false }
        );
        setValue("email", employee.email || "", { shouldDirty: false });
      }
      setValue(
        "phone_number",
        employee.phone_number
          ? employee.phone_number.startsWith("+84")
            ? employee.phone_number.substring(3)
            : employee.phone_number
          : "",
        { shouldDirty: false }
      );
      setValue("address", employee.address || "", { shouldDirty: false });

      // Set birthday if available
      if (employee.birthdate) {
        // Fix timezone issue by creating date in local timezone
        const dateStr = employee.birthdate;
        const [year, month, day] = dateStr.split("-").map(Number);
        const localDate = new Date(year, month - 1, day);
        setValue("birthdate", localDate, { shouldDirty: false });
      }

      // Set role assignments if available
      if (employee.user_roles && employee.user_roles.length > 0) {
        // Map user_roles to the form structure
        const mappedRoles = employee.user_roles.map((userRole) => ({
          branch: userRole.branch_id || "",
          roles: userRole.role_ids || [],
        }));

        setRoleAssignments(
          mappedRoles.map((_, index) => ({ id: (index + 1).toString(), branch: "", roles: [] }))
        );
        setValue("roles", mappedRoles, { shouldDirty: false });
      } else if (employee.role) {
        // Fallback to single role
        setRoleAssignments([{ id: "1", branch: "", roles: [] }]);
        setValue("roles", [{ branch: "", roles: [employee.role] }], { shouldDirty: false });
      } else {
        // Ensure there's at least one role assignment
        setRoleAssignments([{ id: "1", branch: "", roles: [] }]);
        setValue("roles", [{ branch: "", roles: [] }], { shouldDirty: false });
      }
    }
  }, [employee, mode, setValue, open]);

  const addRoleAssignment = () => {
    const newId = (roleAssignments.length + 1).toString();
    setRoleAssignments([...roleAssignments, { id: newId, branch: "", roles: [] }]);
    const newRoles = [...watchedRoles, { branch: "", roles: [] }];
    setValue("roles", newRoles, { shouldDirty: true, shouldTouch: true });
    trigger("roles"); // Trigger validation
  };

  const removeRoleAssignment = (index: number) => {
    if (roleAssignments.length > 1) {
      const newAssignments = roleAssignments.filter((_: any, i: number) => i !== index);
      setRoleAssignments(newAssignments);
      const newRoles = watchedRoles.filter((_: any, i: number) => i !== index);
      setValue("roles", newRoles, { shouldDirty: true, shouldTouch: true });
      trigger("roles"); // Trigger validation
    }
  };

  const updateRoleAssignment = (
    index: number,
    field: "branch" | "roles",
    value: string | string[]
  ) => {
    // This function is now minimal since the form fields and watch effect handle most logic
    // It's kept for backward compatibility but the main logic is in the watch effect
  };

  const handleFormSubmit = async (data: AddEmployeeFormData | EditEmployeeFormData) => {
    try {
      // Validate form before submission
      const validationResult = await trigger();
      if (!validationResult) {
        return;
      }

      // Check if passwords match (only for add mode or when passwords are provided)
      if (mode === "add") {
        const addData = data as AddEmployeeFormData;
        if (addData.password !== addData.confirmPassword) {
          toast.error(t("pages.settings.passwordsDoNotMatch"));
          return;
        }
        // Pass branches data to handle "All" option
        const dataWithBranches = { ...addData, branches };
        await addEmployeeMutation.mutateAsync(dataWithBranches);
        reset();
        setRoleAssignments([{ id: "1", branch: "", roles: [] }]);
        // Note: Dialog closing is handled in addEmployeeMutation.onSuccess callback
      } else if (mode === "edit") {
        // Transform form data to match the update mutation expectations
        const updateData = {
          name: data.employeeName,
          phone_number: data.phone_number || "",
          address: data.address || "",
          birthdate: data.birthdate
            ? (() => {
                // Fix timezone issue by creating date in local timezone
                const date = new Date(data.birthdate);
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, "0");
                const day = String(date.getDate()).padStart(2, "0");
                const formattedDate = `${year}-${month}-${day}`;
                return formattedDate;
              })()
            : undefined,
          user_roles: data.roles.reduce((acc: Record<string, string[]>, role: any) => {
            if (role.branch && role.roles && role.roles.length > 0) {
              // Handle "all" branch option - send "all": [] instead of expanding
              if (role.branch === "all") {
                acc["all"] = role.roles;
              } else {
                acc[role.branch] = role.roles;
              }
            }
            return acc;
          }, {}),
        };

        await updateEmployeeMutation.mutateAsync({
          id: employee_id || "",
          data: updateData,
        });
        reset();
        setRoleAssignments([{ id: "1", branch: "", roles: [] }]);
        onOpenChange(false); // Close dialog after successful update
      }
      // Call the create/update employee API

      setRoleAssignments([{ id: "1", branch: "", roles: [] }]);
    } catch (error) {
      console.error("Error creating/updating employee:", error);
    }
  };

  const handleCancel = () => {
    reset();
    setRoleAssignments([{ id: "1", branch: "", roles: [] }]);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="flex max-h-[95vh] max-w-[600px] flex-col overflow-y-hidden px-0 pb-0">
        <DialogHeader className="flex-none px-6">
          <DialogTitle className="text-lg font-semibold">
            {mode === "edit"
              ? t("pages.employeePermission.editEmployee")
              : mode === "duplicate"
                ? t("pages.employeePermission.duplicateEmployee")
                : t("pages.settings.addEmployee")}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={handleSubmit(handleFormSubmit)}
            className="flex flex-auto flex-col overflow-y-hidden">
            {/* Form Content */}
            <div className="flex flex-auto flex-col gap-2 overflow-auto px-6 py-4">
              {isLoadingEmployee && (mode === "edit" || mode === "duplicate") ? (
                <AddEmployeeDialogSkeleton />
              ) : (
                <>
                  <BasicInfo mode={mode} />
                  {/* Only show password fields in add mode */}
                  {mode === "add" && <Password mode={mode} />}
                  <Separator />
                  <RoleAssignments
                    watchedRoles={watchedRoles}
                    roleAssignments={roleAssignments}
                    branches={branches}
                    roles={roles}
                    isLoadingBranches={isLoadingBranches}
                    isLoadingRoles={isLoadingRoles}
                    onUpdate={updateRoleAssignment}
                    onRemove={removeRoleAssignment}
                    onAdd={addRoleAssignment}
                  />
                </>
              )}
            </div>

            {/* Footer */}
            <DialogFooter className="flex flex-none gap-2 !space-x-0 border-t border-border px-6 py-3">
              <Button type="button" variant="outline" onClick={handleCancel} disabled={loading}>
                {t("common.cancel", "Cancel")}
              </Button>
              <Button
                type="submit"
                disabled={loading || ((mode === "edit" || mode === "duplicate") && !isDirty)}
                loading={
                  addEmployeeMutation.isLoading || updateEmployeeMutation.isPending || loading
                }>
                {mode === "edit"
                  ? t("common.update", "Update")
                  : mode === "duplicate"
                    ? t("common.duplicate", "Duplicate")
                    : t("pages.settings.add", "Add")}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
