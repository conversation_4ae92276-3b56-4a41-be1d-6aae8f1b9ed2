"use client";

import { useState } from "react";
import { Plus } from "lucide-react";

import { AnalyticsSidebar } from "@/features/property-assets/asset-categories/components/AnalyticsSidebar";
import { CategoriesList } from "@/features/property-assets/asset-categories/components/CategoriesList";
import { CreateCategoryDialog } from "@/features/property-assets/asset-categories/components/CreateCategoryDialog";
import { EditCategoryDialog } from "@/features/property-assets/asset-categories/components/EditCategoryDialog";
import { SummaryCard } from "@/features/property-assets/asset-categories/components/SummaryCard";
import {
  useAssetCategories,
  useDeleteAssetCategory,
} from "@/features/property-assets/asset-categories/hooks/use-asset-categories";
import type { AssetCategory } from "@/features/property-assets/asset-categories/types";
import { getIconComponent } from "@/features/property-assets/asset-categories/utils/iconHelpers";

import { Card } from "@/components/ui";
import { Button } from "@/components/ui/button";

export default function AssetCategoriesPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddCategoryOpen, setIsAddCategoryOpen] = useState(false);
  const [isEditCategoryOpen, setIsEditCategoryOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<AssetCategory | null>(null);

  // Fetch asset categories from API
  const { data: categoriesResponse, isLoading } = useAssetCategories();
  const deleteCategoryMutation = useDeleteAssetCategory();

  const apiCategories = categoriesResponse?.items || [];
  const isLoadingCategories = isLoading;

  // Convert API categories to component-compatible format
  const categories = apiCategories.map((cat) => ({
    ...cat,
    description: cat.description || "", // Ensure description is always a string
    icon: cat.icon || "", // Ensure icon is always a string
    color: cat.color || "#000000", // Ensure color is always a string
    assetCount: 0, // TODO: Implement when asset count is available in API
    totalValue: 0, // TODO: Implement when total value is available in API
    avgValue: 0, // TODO: Implement when average value is available in API
    performanceScore: 0, // TODO: Implement when performance score is available in API
    isActive: true, // All categories are considered active for now
    created_at: cat.created_at || new Date().toISOString(),
    updated_at: cat.updated_at || new Date().toISOString(),
    company_id: cat.company_id || "",
    trend: "up" as const, // TODO: Implement when trend data is available in API
    trendValue: 0, // TODO: Implement when trend value is available in API
    subcategories: [], // TODO: Implement when subcategories are available in API
  }));

  // Calculate summary statistics - using available properties from API
  const totalCategories = categories.length;
  const activeCategories = categories.filter((cat) => cat.isActive).length;
  const totalAssets = categories.reduce((sum, cat) => sum + cat.assetCount, 0);
  const totalValue = categories.reduce((sum, cat) => sum + cat.totalValue, 0);
  const avgPerformance =
    categories.length > 0
      ? categories.reduce((sum, cat) => sum + cat.performanceScore, 0) / categories.length
      : 0;

  const handleEdit = (category: AssetCategory) => {
    setSelectedCategory(category);
    setIsEditCategoryOpen(true);
  };

  const handleViewAssets = (category: AssetCategory) => {
    // TODO: Implement view assets functionality
    console.log("View assets for:", category.name);
  };

  const handleViewAnalytics = (category: AssetCategory) => {
    // TODO: Implement view analytics functionality
    console.log("View analytics for:", category.name);
  };

  const handleDelete = (category: AssetCategory) => {
    if (confirm(`Are you sure you want to delete "${category.name}"?`)) {
      deleteCategoryMutation.mutate(category.id);
    }
  };

  return (
    <div className="m-4 mt-0 space-y-6">
      <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
        {/* Left Column - Main Content */}
        <Card className="col-span-1 space-y-4 border-none p-4 lg:col-span-2">
          <div>
            <div className="flex items-center justify-between">
              <h2 className="text-sm font-medium text-card-foreground">Categories</h2>
              <Button className="h-9 px-3 py-2" onClick={() => setIsAddCategoryOpen(true)}>
                <Plus className="mr-1 size-4" />
                Add category
              </Button>
            </div>
          </div>
          {/* Categories List */}
          {isLoadingCategories ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-sm text-muted-foreground">Loading categories...</div>
            </div>
          ) : (
            <CategoriesList
              categories={categories}
              searchTerm={searchTerm}
              onSearchChange={setSearchTerm}
              onEdit={handleEdit}
              onViewAssets={handleViewAssets}
              onViewAnalytics={handleViewAnalytics}
              onDelete={handleDelete}
              getIconComponent={getIconComponent}
            />
          )}
        </Card>

        {/* Right Column - Sidebar */}
        <div className="col-span-1 space-y-4">
          {/* Summary Cards */}
          <div className="space-y-4">
            {isLoadingCategories ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="h-20 animate-pulse rounded-lg bg-muted" />
                  <div className="h-20 animate-pulse rounded-lg bg-muted" />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="h-20 animate-pulse rounded-lg bg-muted" />
                  <div className="h-20 animate-pulse rounded-lg bg-muted" />
                </div>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <SummaryCard
                    title="Total Categories"
                    value={categories.length}
                    subtitle={`${activeCategories} active`}
                    icon="messages-square"
                    iconColor="text-yellow-600"
                  />
                  <SummaryCard
                    title="Total Assets"
                    value={totalAssets}
                    icon="users"
                    iconColor="text-green-600"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <SummaryCard
                    title="Total Value"
                    value={`$${(totalValue / 1000000).toFixed(1)}M`}
                    icon="star"
                    iconColor="text-blue-600"
                  />
                  <SummaryCard
                    title="Avg Performance"
                    value={`${avgPerformance.toFixed(0)}%`}
                    icon="clock-10"
                    iconColor="text-red-600"
                  />
                </div>
              </>
            )}
          </div>

          {/* Analytics Sidebar */}
          <AnalyticsSidebar categories={categories} />
        </div>
      </div>

      {/* Create Category Dialog */}
      <CreateCategoryDialog open={isAddCategoryOpen} onOpenChange={setIsAddCategoryOpen} />

      {/* Edit Category Dialog */}
      <EditCategoryDialog
        open={isEditCategoryOpen}
        onOpenChange={setIsEditCategoryOpen}
        category={selectedCategory}
      />
    </div>
  );
}
