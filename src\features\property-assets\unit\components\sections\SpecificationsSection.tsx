import { UseFormReturn } from "react-hook-form";
import { useTranslation } from "react-i18next";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface SpecificationsSectionProps {
  form: UseFormReturn<any>;
}

export function SpecificationsSection({ form }: SpecificationsSectionProps) {
  const { t } = useTranslation();

  return (
    <div className="rounded-lg border bg-card p-4">
      <h3 className="mb-4 text-sm font-medium text-card-foreground">
        {t("pages.units.specifications") || "Specifications"}
      </h3>
      <div className="space-y-4">
        {/* Floor and Square Footage */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="floor_number">{t("pages.units.floor") || "Floor"} *</Label>
            <Input
              type="number"
              min="1"
              {...form.register("floor_number", { valueAsNumber: true })}
            />
            {form.formState.errors.floor_number && (
              <p className="text-sm text-destructive">
                {String(form.formState.errors.floor_number.message)}
              </p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="square_footage">
              {t("pages.units.squareFootage") || "Square Footage"} *
            </Label>
            <Input
              type="number"
              min="0"
              {...form.register("square_footage", { valueAsNumber: true })}
            />
            {form.formState.errors.square_footage && (
              <p className="text-sm text-destructive">
                {String(form.formState.errors.square_footage.message)}
              </p>
            )}
          </div>
        </div>

        {/* Bedrooms and Bathrooms */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="bedrooms">{t("pages.units.bedrooms") || "Bedrooms"} *</Label>
            <Input type="number" min="0" {...form.register("bedrooms", { valueAsNumber: true })} />
            {form.formState.errors.bedrooms && (
              <p className="text-sm text-destructive">
                {String(form.formState.errors.bedrooms.message)}
              </p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="bathroom">{t("pages.units.bathrooms") || "Bathrooms"} *</Label>
            <Input type="number" min="1" {...form.register("bathroom", { valueAsNumber: true })} />
            {form.formState.errors.bathroom && (
              <p className="text-sm text-destructive">
                {String(form.formState.errors.bathroom.message)}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
