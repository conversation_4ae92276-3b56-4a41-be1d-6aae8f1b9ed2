import { useTranslation } from "react-i18next";

import { Checkbox } from "@/components/ui/checkbox";
import { uppercaseToTitleCase } from "@/utils/helpers/text-formatter";

interface PermissionGroupProps {
  moduleId: string;
  groupId: string;
  groupName: string;
  permissions: Array<{
    id: string;
    name: string;
    description?: string;
    checked: boolean;
  }>;
  onToggleGroup: (groupId: string, moduleId: string, typeName: "CRUD" | "ACTION") => void;
  onTogglePermission: (groupId: string, moduleId: string, permissionId: string) => void;
}

export function PermissionGroup({
  moduleId,
  groupId,
  groupName,
  permissions,
  onToggleGroup,
  onTogglePermission,
}: PermissionGroupProps) {
  const { t } = useTranslation();
  if (permissions.length === 0) return null;

  const allChecked = permissions.every((p) => p.checked);

  return (
    <div className="flex flex-col border-b border-border/40 last:border-b-0">
      {/* Group Header */}
      <div className="flex flex-1 items-center justify-between bg-background p-4">
        <div className="flex flex-1 items-center justify-between gap-2">
          <div className="text-sm font-medium">
            {groupName === "CRUD"
              ? t("pages.roleManagement.crud")
              : t("pages.roleManagement.action")}
          </div>
          <div className="flex w-[88px] items-center justify-center">
            <Checkbox
              checked={allChecked}
              onCheckedChange={() =>
                onToggleGroup(groupId, moduleId, groupName as "CRUD" | "ACTION")
              }
            />
          </div>
        </div>
      </div>

      {/* Group Permissions */}
      <div className="space-y-2 px-4">
        {permissions.map((permission) => (
          <div key={permission.id} className="flex items-center justify-between py-2">
            <div className="flex-1">
              <span className="text-sm">
                {t(
                  `permissions.${moduleId}.${permission.id}`,
                  uppercaseToTitleCase(permission.name, " ")
                )}
              </span>
              {/* {permission.description && (
                <p className="mt-1 text-xs text-muted-foreground">{permission.description}</p>
              )} */}
            </div>
            <div className="flex w-[88px] items-center justify-center">
              <Checkbox
                checked={permission.checked}
                onCheckedChange={() => onTogglePermission(groupId, moduleId, permission.id)}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
