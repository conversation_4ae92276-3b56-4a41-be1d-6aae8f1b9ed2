import { useState } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { authApi } from "@/lib/apis/auth";
import { mediaApi } from "@/lib/apis/media";
import { getAuthToken } from "@/lib/auth";

import { ProfileData, UpdateProfilePayload } from "../types";

// Helper function to transform UserAttributes to ProfileData
const transformUserAttributesToProfileData = (
  userAttributes: any[],
  username: string
): ProfileData => {
  const attributes = userAttributes || [];

  return {
    name: attributes.find((attr) => attr.Name === "name")?.Value || "",
    username: username || "",
    email: attributes.find((attr) => attr.Name === "email")?.Value || "",
    phone: attributes.find((attr) => attr.Name === "phone_number")?.Value || null,
    avatar: attributes.find((attr) => attr.Name === "picture")?.Value || null,
  };
};

// Helper function to transform ProfileData to UserAttributes format
const transformProfileDataToUserAttributes = (
  profileData: UpdateProfilePayload,
  originalData: ProfileData | undefined
): any[] => {
  const attributes = [];

  // Only include fields that have actually changed
  if (profileData.name !== originalData?.name) {
    attributes.push({ Name: "name", Value: profileData.name });
  }

  if (profileData.email !== originalData?.email) {
    attributes.push({ Name: "email", Value: profileData.email });
  }

  if (profileData.phone !== originalData?.phone) {
    attributes.push({ Name: "phone_number", Value: profileData.phone || "" });
  }

  if (profileData.avatar !== originalData?.avatar) {
    attributes.push({ Name: "picture", Value: profileData.avatar || "" });
  }

  return attributes;
};

// Helper function to get only dirty fields from form data
const getDirtyFields = (
  formData: any,
  originalData: ProfileData | undefined
): Partial<UpdateProfilePayload> => {
  const dirtyFields: Partial<UpdateProfilePayload> = {};

  if (formData.name !== originalData?.name) {
    dirtyFields.name = formData.name;
  }

  if (formData.email !== originalData?.email) {
    dirtyFields.email = formData.email;
  }

  if (formData.phone !== originalData?.phone) {
    dirtyFields.phone = formData.phone;
  }

  if (formData.avatar !== originalData?.avatar) {
    dirtyFields.avatar = formData.avatar;
  }

  return dirtyFields;
};

// Helper function to upload image and return URL
const handleImageUpload = async (
  imageContent: string,
  fileName: string
): Promise<{ id: string; name: string; url: string } | null> => {
  try {
    const prefix = "profile";
    const res = await mediaApi.uploadImage({
      prefix,
      name: fileName,
      image: imageContent,
    });

    if (res?.url) {
      return {
        id: res.id,
        name: fileName,
        url: res.url,
      };
    }
    return null;
  } catch (error) {
    console.error("Error uploading image:", error);
    throw new Error("Failed to upload image");
  }
};

export const useProfile = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);

  // Query key for profile data
  const profileQueryKey = ["profile"];

  // Get profile data using getInfo API
  const {
    data: profileData,
    isLoading: isLoadingProfile,
    error: profileError,
    refetch: refetchProfile,
  } = useQuery({
    queryKey: profileQueryKey,
    queryFn: async () => {
      const authData = getAuthToken();
      if (!authData?.Token?.AccessToken || !authData?.Token?.IdToken || !authData?.Username) {
        throw new Error("Authentication data not found");
      }

      const response = await authApi.getInfo(
        authData.Token.AccessToken,
        authData.Token.IdToken,
        authData.Username
      );

      return transformUserAttributesToProfileData(response.UserAttributes, response.Username || "");
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!getAuthToken()?.Token?.AccessToken, // Only run query if we have auth data
  });

  // Update profile mutation
  const updateProfileMutation = useMutation({
    mutationFn: async (payload: UpdateProfilePayload) => {
      const authData = getAuthToken();
      if (!authData?.Token?.AccessToken || !authData?.Username) {
        throw new Error("Authentication data not found");
      }

      // Handle avatar upload if it's a base64 image
      let avatarUrl = payload.avatar;
      if (
        payload.avatar &&
        typeof payload.avatar === "string" &&
        payload.avatar.startsWith("data:image")
      ) {
        const fileName = payload.avatar.includes(";name=")
          ? payload.avatar.split(";name=")[1].split(";")[0]
          : `avatar-${Date.now()}.png`;

        const imageData = await handleImageUpload(payload.avatar, fileName);
        if (imageData) {
          avatarUrl = imageData.url;
        }
      }

      // Create updated payload with processed avatar
      const processedPayload = {
        ...payload,
        avatar: avatarUrl,
      };

      // Transform profile data to user attributes format
      const userAttributes = transformProfileDataToUserAttributes(processedPayload, profileData);

      // Call the real API to update user attributes
      const response = await authApi.updateUserAttributes({
        ...userAttributes.reduce(
          (acc, attr) => {
            acc[attr.Name] = attr.Value;
            return acc;
          },
          {} as Record<string, string>
        ),
      });

      return response;
    },
    onSuccess: (data) => {
      toast.success(t("common.success"), {
        description: t("profile.profileUpdateSuccess") || data.message,
      });
      setIsEditing(false);
      // Invalidate and refetch profile data
      queryClient.invalidateQueries({ queryKey: profileQueryKey });
      // Also invalidate userInfo query to refresh sidebar user info
      queryClient.invalidateQueries({ queryKey: ["userInfo"] });
    },
    onError: (error: any) => {
      toast.error(t("common.error"), {
        description: t("profile.profileUpdateError") || error,
      });
    },
  });

  // Upload avatar mutation
  const uploadAvatarMutation = useMutation({
    mutationFn: async (file: File) => {
      // Convert File to base64 string first
      const base64String = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });

      // Use the same image upload logic
      const fileName = file.name || `avatar-${Date.now()}.png`;
      const imageData = await handleImageUpload(base64String, fileName);

      if (!imageData) {
        throw new Error("Failed to upload image");
      }

      return imageData;
    },
    onSuccess: (data) => {
      toast.success(t("common.success"), {
        description: t("profile.avatarUploadSuccess"),
      });
      // Invalidate and refetch profile data
      queryClient.invalidateQueries({ queryKey: profileQueryKey });
      // Also invalidate userInfo query to refresh sidebar user info
      queryClient.invalidateQueries({ queryKey: ["userInfo"] });
    },
    onError: (error: any) => {
      toast.error(t("common.error"), {
        description: error?.message || t("profile.avatarUploadError"),
      });
    },
  });

  // Update profile function
  const updateProfile = async (payload: UpdateProfilePayload) => {
    return updateProfileMutation.mutateAsync(payload);
  };

  // Update profile with only dirty fields
  const updateProfileDirty = async (formData: any) => {
    const dirtyFields = getDirtyFields(formData, profileData);

    // If no fields are dirty, don't make the API call
    if (Object.keys(dirtyFields).length === 0) {
      return { success: true, message: "No changes to update" };
    }

    // Add username to dirty fields for the API call
    // Ensure name is always included as it's required by UpdateProfilePayload
    const payloadWithUsername: UpdateProfilePayload = {
      name: dirtyFields.name || profileData?.name || "",
      username: profileData?.username || "",
      ...dirtyFields,
    };

    return updateProfileMutation.mutateAsync(payloadWithUsername);
  };

  // Upload avatar function
  const uploadAvatar = async (file: File) => {
    const result = await uploadAvatarMutation.mutateAsync(file);
    // Update the form with the new avatar URL
    if (result?.url) {
      // You might want to update the profile data here or let the form handle it
      return result;
    }
    return result;
  };

  return {
    // State
    profileData,
    isLoadingProfile,
    profileError,
    isEditing,
    setIsEditing,
    isLoading: updateProfileMutation.isPending,
    // Actions
    updateProfile,
    updateProfileDirty,
    uploadAvatar,
    refetchProfile,

    // Mutations
    updateProfileMutation,
    uploadAvatarMutation,
  };
};
