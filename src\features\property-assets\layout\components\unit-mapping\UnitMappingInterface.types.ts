import type { Unit } from "../../../types";
import type { LayoutData } from "../LayoutViewer";

export interface UnitMappingInterfaceProps {
  /** The property data */
  property: any; // TODO: Define Property type
  /** Array of units to display and manage */
  units: Unit[];
  /** Array of layouts available for mapping */
  layouts: LayoutData[];
  /** Callback when a layout is saved */
  onLayoutSave?: (layout: LayoutData) => void;
  /** Callback when a layout is deleted */
  onLayoutDelete?: (layoutId: string) => void;
  /** Callback when a unit is updated */
  onUnitUpdate?: (unit: Unit) => void;
  /** Additional CSS classes */
  className?: string;
}
