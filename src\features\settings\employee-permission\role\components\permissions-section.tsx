import { useTranslation } from "react-i18next";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { PermissionGroup } from "@/lib/apis/permission";
import { cn } from "@/lib/utils";

import { GroupContent } from "./group-content";

interface PermissionsSectionProps {
  isLoading: boolean;
  error: Error | null;
  groups: PermissionGroup[];
  activeTab: string;
  expandedModules: Set<string>;
  onTabChange: (value: string) => void;
  onToggleModuleExpansion: (moduleKey: string) => void;
  onToggleModulePermissions: (groupId: string, moduleId: string) => void;
  onToggleGroup: (groupId: string, moduleId: string, typeName: "CRUD" | "ACTION") => void;
  onTogglePermission: (groupId: string, moduleId: string, permissionId: string) => void;
}

export function PermissionsSection({
  isLoading,
  error,
  groups,
  activeTab,
  expandedModules,
  onTabChange,
  onToggleModuleExpansion,
  onToggleModulePermissions,
  onToggleGroup,
  onTogglePermission,
}: PermissionsSectionProps) {
  const { t } = useTranslation();

  return (
    <div>
      <div>
        <h3 className="mb-1 text-sm font-medium">{t("pages.roleManagement.permissions")}</h3>
        <p className="text-sm text-muted-foreground">
          {t("pages.roleManagement.permissionsDescription")}
        </p>
      </div>

      {/* Group Tabs */}
      <Tabs value={activeTab} onValueChange={onTabChange}>
        <TabsList className="items-end rounded-none border-b border-transparent bg-transparent px-2 py-0">
          {groups.map((group) => (
            <TabsTrigger
              key={group.id}
              value={group.id}
              className={cn(
                "group relative h-9 items-end rounded-none border-b-2 border-transparent px-5 text-sm font-medium data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:text-foreground data-[state=active]:shadow-none"
              )}>
              {t(`pages.roleManagement.groups.${group.id}`) || group.name}
            </TabsTrigger>
          ))}
        </TabsList>

        {/* Group Content */}
        {groups.map((group) => (
          <TabsContent key={group.id} value={group.id}>
            <GroupContent
              group={group}
              expandedModules={expandedModules}
              onToggleModuleExpansion={onToggleModuleExpansion}
              onToggleModulePermissions={onToggleModulePermissions}
              onToggleGroup={onToggleGroup}
              onTogglePermission={onTogglePermission}
            />
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
