"use client";

import { useTranslation } from "react-i18next";

import { StoreInformation } from "../types";
import { AdvancedInformationForm } from "./advanced-information-form";
import { BasicInformationForm } from "./basic-information-form";

interface StoreInformationFormProps {
  formData: StoreInformation;
  isLoading: boolean;
  onInputChange: (
    field: keyof StoreInformation,
    value: string | { name: string; id: string }
  ) => void;
  onUpdate: () => Promise<{ success: boolean; error?: string }>;
  onResetBasicInfo?: () => void;
  onResetAdvancedInfo?: () => void;
}

export const StoreInformationForm = ({
  formData,
  isLoading,
  onInputChange,
  onUpdate,
}: StoreInformationFormProps) => {
  const { t } = useTranslation();

  const handleSubmit = async () => {
    await onUpdate();
  };

  // Extract basic and advanced information data
  const basicInfoData = {
    storeName: formData.storeName,
    store_phone: formData.store_phone,
    store_email: formData.store_email,
    businessSector: formData.businessSector,
  };

  const advancedInfoData = {
    storeUrl: formData.storeUrl,
    store_default_price_group: formData.store_default_price_group,
    store_address: formData.store_address,
    store_provinces: formData.store_provinces,
    store_districts: formData.store_districts,
    store_wards: formData.store_wards,
  };

  return (
    <div className="max-w-4xl space-y-4">
      {/* Basic Information Form */}
      <BasicInformationForm
        formData={basicInfoData}
        isLoading={isLoading}
        onInputChange={onInputChange}
        onSubmit={handleSubmit}
        isFormDirty={formData.isBasicInfoDirty || false}
      />

      {/* Advanced Information Form */}
      <AdvancedInformationForm
        formData={advancedInfoData}
        isLoading={isLoading}
        onInputChange={onInputChange}
        onSubmit={handleSubmit}
        isFormDirty={formData.isAdvancedInfoDirty || false}
      />
    </div>
  );
};
