"use client";

import { useState } from "react";
import {
  AlertCircle,
  Bell,
  Calendar,
  CheckCircle,
  ChevronLeft,
  ChevronRight,
  Clock,
  Edit,
  Eye,
  Plus,
  Settings,
  User,
} from "lucide-react";
import { useTranslation } from "react-i18next";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";

interface AvailabilityCalendarProps {
  className?: string;
}

export function AvailabilityCalendar({ className }: AvailabilityCalendarProps) {
  const { t } = useTranslation();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedProperty, setSelectedProperty] = useState<string>("all");
  const [viewMode, setViewMode] = useState<"month" | "week" | "day">("month");
  const [isBookingOpen, setIsBookingOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedUnit, setSelectedUnit] = useState<any>(null);

  // Mock availability data
  const availabilityData = [
    {
      unitId: "1",
      unitNumber: "101",
      property: "Sunset Apartments",
      type: "1-Bedroom",
      status: "Available",
      availableFrom: "2024-02-01",
      availableTo: "2024-12-31",
      rent: 2200,
      bookings: [
        {
          id: "b1",
          type: "Viewing",
          date: "2024-02-15",
          time: "14:00",
          duration: 60,
          prospect: "John Smith",
          contact: "<EMAIL>",
          status: "Confirmed",
        },
        {
          id: "b2",
          type: "Viewing",
          date: "2024-02-18",
          time: "10:30",
          duration: 45,
          prospect: "Sarah Johnson",
          contact: "<EMAIL>",
          status: "Pending",
        },
      ],
      blackoutDates: ["2024-02-20", "2024-02-21"], // Maintenance
      restrictions: {
        minLease: 12,
        maxLease: 24,
        petPolicy: "Allowed",
        smokingPolicy: "No Smoking",
      },
    },
    {
      unitId: "2",
      unitNumber: "205",
      property: "Downtown Lofts",
      type: "2-Bedroom",
      status: "Available",
      availableFrom: "2024-01-15",
      availableTo: "2024-12-31",
      rent: 3500,
      bookings: [
        {
          id: "b3",
          type: "Application Review",
          date: "2024-02-16",
          time: "09:00",
          duration: 120,
          prospect: "Mike Davis",
          contact: "<EMAIL>",
          status: "Confirmed",
        },
      ],
      blackoutDates: [],
      restrictions: {
        minLease: 6,
        maxLease: 36,
        petPolicy: "Cats Only",
        smokingPolicy: "No Smoking",
      },
    },
    {
      unitId: "3",
      unitNumber: "103",
      property: "Garden View Complex",
      type: "Studio",
      status: "Occupied",
      availableFrom: "2024-08-01",
      availableTo: "2024-12-31",
      rent: 1800,
      bookings: [],
      blackoutDates: [],
      restrictions: {
        minLease: 12,
        maxLease: 12,
        petPolicy: "No Pets",
        smokingPolicy: "No Smoking",
      },
    },
  ];

  const bookingTypes = [
    { value: "viewing", label: "Property Viewing", icon: Eye, color: "blue" },
    { value: "application", label: "Application Review", icon: User, color: "green" },
    { value: "signing", label: "Lease Signing", icon: Edit, color: "purple" },
    { value: "maintenance", label: "Maintenance", icon: Settings, color: "orange" },
    { value: "inspection", label: "Inspection", icon: CheckCircle, color: "gray" },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Available":
        return "bg-success/10 text-success border-success/20";
      case "Occupied":
        return "bg-primary/10 text-primary border-primary/20";
      case "Maintenance":
        return "bg-warning/10 text-warning border-warning/20";
      case "Reserved":
        return "bg-secondary/10 text-secondary-foreground border-secondary/20";
      default:
        return "bg-muted text-muted-foreground border-muted-foreground/20";
    }
  };

  const getBookingStatusColor = (status: string) => {
    switch (status) {
      case "Confirmed":
        return "text-success";
      case "Pending":
        return "text-warning";
      case "Cancelled":
        return "text-destructive";
      default:
        return "text-muted-foreground";
    }
  };

  const filteredUnits = availabilityData.filter(
    (unit) => selectedProperty === "all" || unit.property === selectedProperty
  );

  // Generate calendar days for current month
  const generateCalendarDays = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day));
    }

    return days;
  };

  const getDayBookings = (date: Date) => {
    const dateStr = date.toISOString().split("T")[0];
    const bookings: any[] = [];

    filteredUnits.forEach((unit) => {
      unit.bookings.forEach((booking) => {
        if (booking.date === dateStr) {
          bookings.push({ ...booking, unit });
        }
      });
    });

    return bookings;
  };

  const isBlackoutDate = (date: Date, unit: any) => {
    const dateStr = date.toISOString().split("T")[0];
    return unit.blackoutDates.includes(dateStr);
  };

  const navigateMonth = (direction: "prev" | "next") => {
    const newDate = new Date(currentDate);
    newDate.setMonth(currentDate.getMonth() + (direction === "next" ? 1 : -1));
    setCurrentDate(newDate);
  };

  const monthNames = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-xl font-semibold text-foreground">Unit Availability Calendar</h2>
          <p className="text-sm text-muted-foreground">
            Manage unit availability, bookings, and viewing schedules
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Dialog open={isBookingOpen} onOpenChange={setIsBookingOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 size-4" />
                Add Booking
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Schedule New Booking</DialogTitle>
                <DialogDescription>
                  Book a viewing, inspection, or other appointment
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label>Unit</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select unit" />
                    </SelectTrigger>
                    <SelectContent>
                      {filteredUnits
                        .filter((u) => u.status === "Available")
                        .map((unit) => (
                          <SelectItem key={unit.unitId} value={unit.unitId}>
                            Unit {unit.unitNumber} - {unit.property}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>Booking Type</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      {bookingTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Date</Label>
                    <Input type="date" />
                  </div>
                  <div>
                    <Label>Time</Label>
                    <Input type="time" />
                  </div>
                </div>
                <div>
                  <Label>Duration (minutes)</Label>
                  <Input type="number" placeholder="60" />
                </div>
                <div>
                  <Label>Prospect Name</Label>
                  <Input placeholder="John Smith" />
                </div>
                <div>
                  <Label>Contact</Label>
                  <Input placeholder="<EMAIL> or phone" />
                </div>
                <div>
                  <Label>Notes</Label>
                  <Textarea placeholder="Additional notes..." />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsBookingOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setIsBookingOpen(false)}>Schedule Booking</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
        {/* Sidebar - Units & Filters */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Properties</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Select value={selectedProperty} onValueChange={setSelectedProperty}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Properties</SelectItem>
                  <SelectItem value="Sunset Apartments">Sunset Apartments</SelectItem>
                  <SelectItem value="Downtown Lofts">Downtown Lofts</SelectItem>
                  <SelectItem value="Garden View Complex">Garden View Complex</SelectItem>
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          {/* Available Units */}
          <Card className="mt-4">
            <CardHeader>
              <CardTitle className="text-base">Available Units</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {filteredUnits
                .filter((unit) => unit.status === "Available")
                .map((unit) => (
                  <div
                    key={unit.unitId}
                    className="cursor-pointer rounded-lg border p-3 hover:bg-muted/50">
                    <div className="mb-2 flex items-center justify-between">
                      <span className="font-medium">Unit {unit.unitNumber}</span>
                      <Badge className={getStatusColor(unit.status)}>{unit.status}</Badge>
                    </div>
                    <div className="space-y-1 text-sm text-muted-foreground">
                      <p>{unit.property}</p>
                      <p>{unit.type}</p>
                      <p className="font-medium text-foreground">
                        ${unit.rent.toLocaleString()}/mo
                      </p>
                    </div>
                    <div className="mt-2 text-xs text-muted-foreground">
                      Available: {new Date(unit.availableFrom).toLocaleDateString()}
                    </div>
                  </div>
                ))}
            </CardContent>
          </Card>

          {/* Upcoming Bookings */}
          <Card className="mt-4">
            <CardHeader>
              <CardTitle className="text-base">Upcoming Bookings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {filteredUnits
                .flatMap((unit) => unit.bookings.map((booking) => ({ ...booking, unit })))
                .slice(0, 3)
                .map((booking) => (
                  <div key={booking.id} className="rounded-lg border p-3">
                    <div className="mb-2 flex items-center justify-between">
                      <span className="text-sm font-medium">{booking.type}</span>
                      <Badge variant="outline" className={getBookingStatusColor(booking.status)}>
                        {booking.status}
                      </Badge>
                    </div>
                    <div className="space-y-1 text-sm">
                      <p>Unit {booking.unit.unitNumber}</p>
                      <p className="text-muted-foreground">{booking.prospect}</p>
                      <div className="flex items-center space-x-2 text-xs">
                        <Calendar className="size-3" />
                        <span>{new Date(booking.date).toLocaleDateString()}</span>
                        <Clock className="size-3" />
                        <span>{booking.time}</span>
                      </div>
                    </div>
                  </div>
                ))}
            </CardContent>
          </Card>
        </div>

        {/* Main Calendar */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <Button variant="outline" size="sm" onClick={() => navigateMonth("prev")}>
                    <ChevronLeft className="size-4" />
                  </Button>
                  <h3 className="text-lg font-semibold">
                    {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
                  </h3>
                  <Button variant="outline" size="sm" onClick={() => navigateMonth("next")}>
                    <ChevronRight className="size-4" />
                  </Button>
                </div>
                <div className="flex items-center space-x-2">
                  <Select value={viewMode} onValueChange={(value: any) => setViewMode(value)}>
                    <SelectTrigger className="w-24">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="month">Month</SelectItem>
                      <SelectItem value="week">Week</SelectItem>
                      <SelectItem value="day">Day</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" size="sm">
                    <Bell className="mr-2 size-4" />
                    Alerts
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {/* Calendar Grid */}
              <div className="grid grid-cols-7 gap-1">
                {/* Day Headers */}
                {dayNames.map((day) => (
                  <div
                    key={day}
                    className="p-2 text-center text-sm font-medium text-muted-foreground">
                    {day}
                  </div>
                ))}

                {/* Calendar Days */}
                {generateCalendarDays().map((day, index) => {
                  if (!day) {
                    return <div key={index} className="h-24 p-1"></div>;
                  }

                  const dayBookings = getDayBookings(day);
                  const isToday = day.toDateString() === new Date().toDateString();

                  return (
                    <div
                      key={index}
                      className={`h-24 cursor-pointer rounded border p-1 hover:bg-muted/50 ${
                        isToday ? "border-primary/20 bg-primary/10" : "border-muted-foreground/20"
                      }`}
                      onClick={() => setSelectedDate(day)}>
                      <div className={`text-sm ${isToday ? "font-bold text-primary" : ""}`}>
                        {day.getDate()}
                      </div>

                      {/* Bookings for this day */}
                      <div className="mt-1 space-y-1">
                        {dayBookings.slice(0, 2).map((booking) => {
                          const bookingType = bookingTypes.find((t) => t.label === booking.type);
                          return (
                            <Popover key={booking.id}>
                              <PopoverTrigger asChild>
                                <div
                                  className={`bg- cursor-pointer truncate rounded p-1 text-xs${bookingType?.color}-100 text-${bookingType?.color}-800`}>
                                  {booking.time} {booking.type}
                                </div>
                              </PopoverTrigger>
                              <PopoverContent className="w-64">
                                <div className="space-y-2">
                                  <div className="flex items-center justify-between">
                                    <h4 className="font-medium">{booking.type}</h4>
                                    <Badge className={getBookingStatusColor(booking.status)}>
                                      {booking.status}
                                    </Badge>
                                  </div>
                                  <div className="space-y-1 text-sm">
                                    <p>
                                      <strong>Unit:</strong> {booking.unit.unitNumber}
                                    </p>
                                    <p>
                                      <strong>Time:</strong> {booking.time}
                                    </p>
                                    <p>
                                      <strong>Duration:</strong> {booking.duration} min
                                    </p>
                                    <p>
                                      <strong>Prospect:</strong> {booking.prospect}
                                    </p>
                                    <p>
                                      <strong>Contact:</strong> {booking.contact}
                                    </p>
                                  </div>
                                  <div className="flex space-x-2">
                                    <Button size="sm" variant="outline">
                                      <Edit className="mr-1 size-3" />
                                      Edit
                                    </Button>
                                    <Button size="sm" variant="outline">
                                      <Eye className="mr-1 size-3" />
                                      View
                                    </Button>
                                  </div>
                                </div>
                              </PopoverContent>
                            </Popover>
                          );
                        })}
                        {dayBookings.length > 2 && (
                          <div className="text-xs text-muted-foreground">
                            +{dayBookings.length - 2} more
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Day Details */}
          {selectedDate && (
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>
                  {selectedDate.toLocaleDateString("en-US", {
                    weekday: "long",
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  })}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="bookings">
                  <TabsList>
                    <TabsTrigger value="bookings">Bookings</TabsTrigger>
                    <TabsTrigger value="availability">Availability</TabsTrigger>
                    <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
                  </TabsList>

                  <TabsContent value="bookings" className="space-y-4">
                    {getDayBookings(selectedDate).length === 0 ? (
                      <div className="py-8 text-center text-muted-foreground">
                        <Calendar className="mx-auto mb-4 size-12 opacity-50" />
                        <p>No bookings scheduled for this day</p>
                        <Button className="mt-4" onClick={() => setIsBookingOpen(true)}>
                          Schedule Booking
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {getDayBookings(selectedDate).map((booking) => (
                          <Card key={booking.id}>
                            <CardContent className="p-4">
                              <div className="mb-3 flex items-center justify-between">
                                <div className="flex items-center space-x-3">
                                  <div className="rounded-lg bg-primary/10 p-2">
                                    <Calendar className="size-4 text-primary" />
                                  </div>
                                  <div>
                                    <h4 className="font-medium">{booking.type}</h4>
                                    <p className="text-sm text-muted-foreground">
                                      Unit {booking.unit.unitNumber} - {booking.unit.property}
                                    </p>
                                  </div>
                                </div>
                                <Badge className={getBookingStatusColor(booking.status)}>
                                  {booking.status}
                                </Badge>
                              </div>
                              <div className="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                  <span className="text-muted-foreground">Time: </span>
                                  <span>{booking.time}</span>
                                </div>
                                <div>
                                  <span className="text-muted-foreground">Duration: </span>
                                  <span>{booking.duration} minutes</span>
                                </div>
                                <div>
                                  <span className="text-muted-foreground">Prospect: </span>
                                  <span>{booking.prospect}</span>
                                </div>
                                <div>
                                  <span className="text-muted-foreground">Contact: </span>
                                  <span>{booking.contact}</span>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="availability" className="space-y-4">
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      {filteredUnits.map((unit) => (
                        <Card key={unit.unitId}>
                          <CardContent className="p-4">
                            <div className="mb-3 flex items-center justify-between">
                              <div>
                                <h4 className="font-medium">Unit {unit.unitNumber}</h4>
                                <p className="text-sm text-muted-foreground">{unit.type}</p>
                              </div>
                              <Badge className={getStatusColor(unit.status)}>{unit.status}</Badge>
                            </div>
                            <div className="space-y-2 text-sm">
                              <div>
                                <span className="text-muted-foreground">Rent: </span>
                                <span className="font-medium">
                                  ${unit.rent.toLocaleString()}/mo
                                </span>
                              </div>
                              {unit.status === "Available" && (
                                <div>
                                  <span className="text-muted-foreground">Available from: </span>
                                  <span>{new Date(unit.availableFrom).toLocaleDateString()}</span>
                                </div>
                              )}
                              {isBlackoutDate(selectedDate, unit) && (
                                <div className="flex items-center space-x-2 text-warning">
                                  <AlertCircle className="size-4" />
                                  <span>Maintenance scheduled</span>
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </TabsContent>

                  <TabsContent value="maintenance" className="space-y-4">
                    <div className="py-8 text-center text-muted-foreground">
                      <Settings className="mx-auto mb-4 size-12 opacity-50" />
                      <p>No maintenance scheduled for this day</p>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
