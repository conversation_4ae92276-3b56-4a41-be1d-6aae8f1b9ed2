export interface IGetPropertiesParams {
  page?: number;
  limit?: number;
  query?: string;
  type?: string;
  status?: string;
  [key: string]: unknown;
}

export const propertyKeys = {
  all: () => ["property"] as const,
  lists: () => [...propertyKeys.all(), "list"] as const,
  list: (params: IGetPropertiesParams) => [...propertyKeys.lists(), params] as const,
  details: () => [...propertyKeys.all(), "detail"] as const,
  detail: (id: string) => [...propertyKeys.details(), id] as const,
  select: () => [...propertyKeys.all(), "select"] as const,
  units: (propertyId: string) => [...propertyKeys.all(), "units", propertyId] as const,
  byType: (type: string) => [...propertyKeys.all(), "type", type] as const,
  byStatus: (status: string) => [...propertyKeys.all(), "status", status] as const,
  byAddress: (address: string) => [...propertyKeys.all(), "address", address] as const,
};

export const QUERY_KEYS = {
  PROPERTIES: ["properties"] as const,
  PROPERTY_TYPES: ["propertyTypes"] as const,
  PROPERTY_STATUSES: ["propertyStatuses"] as const,
  PROPERTY_UNITS: ["propertyUnits"] as const,
} as const;
