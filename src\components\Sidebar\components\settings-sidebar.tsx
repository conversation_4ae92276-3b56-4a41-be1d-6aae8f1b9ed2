"use client";

import * as React from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { ArrowLeft } from "lucide-react";
import { useTheme } from "next-themes";
import { useTranslation } from "react-i18next";

import LogoOXBDark from "@/assets/logos/logo-OXB-dark.svg";
import LogoOXBLight from "@/assets/logos/logo-OXB-light.svg";
import { useSidebarContext } from "@/components/Sidebar/context/sidebar-context";
import { SettingsNavItem, settingsNavItems } from "@/components/Sidebar/setting-data";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import { Sidebar, SidebarContent, SidebarHeader, useSidebar } from "@/components/ui/sidebar";
import { IS_ONE_X_BOT } from "@/utils/constants/common";
import toNavUrl from "@/utils/helpers/nav-url-formatter";

interface SettingsSidebarProps {
  onBack: () => void;
}

const LogoComponent = () => {
  const { theme, systemTheme } = useTheme();
  const currentMode = theme === "system" ? systemTheme : theme;
  const isDark = currentMode === "dark";

  const LogoOneXBot = isDark ? LogoOXBDark : LogoOXBLight;

  return (
    <div className="flex size-full items-center justify-center">
      {IS_ONE_X_BOT ? (
        <LogoOneXBot
          maxWidth={300}
          height={48}
          className="max-h-[48px] max-w-full object-contain"
        />
      ) : (
        <LogoOneXBot width={300} height={48} className="max-h-[48px] max-w-full object-contain" />
      )}
    </div>
  );
};

const SettingsSidebarContent = ({ onBack }: { onBack: () => void }) => {
  const { t } = useTranslation();
  const pathname = usePathname();
  const router = useRouter();
  const { previousPage } = useSidebarContext();

  const groupedItems = React.useMemo(() => {
    const groups: Record<string, SettingsNavItem[]> = {};
    settingsNavItems.forEach((item) => {
      if (!groups[item.category]) {
        groups[item.category] = [];
      }
      groups[item.category].push(item);
    });
    return groups;
  }, []);

  const getCategoryTitle = (category: string) => {
    const titles: Record<string, string> = {
      settings: t("common.settings"),
    };
    return titles[category] || category;
  };

  const handleBack = () => {
    onBack();
    // Navigate back to the previous page stored in context
    router.push(previousPage as any);
  };

  return (
    <>
      <SidebarHeader className="px-4">
        <div className="flex h-16 shrink-0 items-center justify-center px-1">
          <div className="flex items-center gap-2 font-semibold">
            <LogoComponent />
          </div>
        </div>
        <div>
          <Button
            variant="ghost"
            onClick={handleBack}
            className="w-full justify-start gap-2 text-sm">
            <ArrowLeft className="size-4" />
            {t("common.back")}
          </Button>
        </div>
      </SidebarHeader>

      <ScrollArea className="flex-1">
        <SidebarContent className="flex min-h-0 flex-1 flex-col gap-4 p-4">
          {Object.entries(groupedItems).map(([category, items]) => (
            <div key={category} className="space-y-2">
              <h3 className="px-4 text-xs font-medium tracking-wider">
                {t(getCategoryTitle(category))}
              </h3>
              <div className="space-y-1">
                {items.map((item) => {
                  // const Icon = item.icon;
                  const isActive = pathname.startsWith(item.url);

                  return (
                    <Link key={item.url} href={toNavUrl(item.url)} className="hover:no-underline">
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className={`group flex cursor-pointer items-center gap-3 rounded-lg px-4 py-3 transition-colors ${
                          isActive
                            ? "bg-accent text-accent-foreground"
                            : "hover:bg-transparent hover:text-accent-foreground"
                        }`}>
                        {/* <Icon
                          className={`size-4 ${
                            isActive
                              ? "text-accent-foreground"
                              : "text-muted-foreground group-hover:text-accent-foreground"
                          }`}
                        /> */}
                        <div className="flex max-w-48 flex-col gap-1">
                          <p
                            className={`truncate text-sm font-medium ${
                              isActive ? "text-accent-foreground" : "text-muted-foreground"
                            }`}>
                            {t(item.title)}
                          </p>
                          <p className="truncate text-sm font-normal text-muted-foreground">
                            {t(item.description)}
                          </p>
                        </div>
                      </motion.div>
                    </Link>
                  );
                })}
              </div>
            </div>
          ))}
        </SidebarContent>
      </ScrollArea>
    </>
  );
};

export function SettingsSidebar({ onBack }: SettingsSidebarProps) {
  const { isTablet, openTablet, setOpenTablet } = useSidebar();

  // On mobile/tablet, show as a Sheet (drawer)
  if (isTablet) {
    return (
      <Sheet open={openTablet} onOpenChange={setOpenTablet}>
        <SheetContent side="left" className="w-[300px] p-0">
          <div className="flex h-full flex-col bg-card">
            <SettingsSidebarContent onBack={onBack} />
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  // On desktop, show as a regular sidebar
  return (
    <Sidebar className="z-50 bg-card" collapsible="none">
      <SettingsSidebarContent onBack={onBack} />
    </Sidebar>
  );
}
