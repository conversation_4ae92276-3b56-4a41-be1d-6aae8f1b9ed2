"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";

import { useDistricts, useProvinces, useWards } from "@/features/customer/hooks/address";
import { usePriceGroups } from "@/features/prices/hooks/hooks";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { PortalCombobox } from "@/components/ui/portal-combobox";
import useDebounce from "@/hooks/use-debounce";

import { StoreInformation } from "../types";

interface AdvancedInformationFormProps {
  formData: Pick<
    StoreInformation,
    | "storeUrl"
    | "store_default_price_group"
    | "store_address"
    | "store_provinces"
    | "store_districts"
    | "store_wards"
  >;
  isLoading: boolean;
  onInputChange: (
    field: keyof StoreInformation,
    value: string | { name: string; id: string }
  ) => void;
  onSubmit: () => Promise<void>;
  isFormDirty: boolean;
  onReset?: () => void;
}

export const AdvancedInformationForm = ({
  formData,
  isLoading,
  onInputChange,
  onSubmit,
  isFormDirty,
}: AdvancedInformationFormProps) => {
  const { t } = useTranslation();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Price groups
  const {
    priceGroups,
    isLoading: isLoadingPriceGroups,
    fetchNextPage: fetchNextPriceGroups,
    hasNextPage: hasNextPriceGroups,
    isFetchingNextPage: isFetchingNextPriceGroups,
  } = usePriceGroups();

  // Search state for address fields
  const [provinceQuery, setProvinceQuery] = useState("");
  const [districtQuery, setDistrictQuery] = useState("");
  const [wardQuery, setWardQuery] = useState("");

  // Debounced search queries
  const debouncedProvinceQuery = useDebounce(provinceQuery, 800);
  const debouncedDistrictQuery = useDebounce(districtQuery, 800);
  const debouncedWardQuery = useDebounce(wardQuery, 800);

  // Address API hooks
  const {
    provinces,
    isLoading: isLoadingProvinces,
    fetchNextPage: fetchNextProvinces,
    hasNextPage: hasNextProvinces,
    isFetchingNextPage: isFetchingNextProvinces,
  } = useProvinces(debouncedProvinceQuery);

  const {
    districts,
    isLoading: isLoadingDistricts,
    fetchNextPage: fetchNextDistricts,
    hasNextPage: hasNextDistricts,
    isFetchingNextPage: isFetchingNextDistricts,
  } = useDistricts(formData.store_provinces, debouncedDistrictQuery);

  const {
    wards,
    isLoading: isLoadingWards,
    fetchNextPage: fetchNextWards,
    hasNextPage: hasNextWards,
    isFetchingNextPage: isFetchingNextWards,
  } = useWards(formData.store_provinces, formData.store_districts, debouncedWardQuery);

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      await onSubmit();
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle province change
  const handleProvinceChange = (value: string | string[]) => {
    const province = provinces.find((p) => p.code === value);
    onInputChange("store_provinces", province?.code || "");
    // Clear dependent fields when province changes
    onInputChange("store_districts", "");
    onInputChange("store_wards", "");
  };

  // Handle district change
  const handleDistrictChange = (value: string | string[]) => {
    const district = districts.find((d) => d.code === value);
    onInputChange("store_districts", district?.code || "");
    // Clear ward when district changes
    onInputChange("store_wards", "");
  };

  // Handle ward change
  const handleWardChange = (value: string | string[]) => {
    const ward = wards.find((w) => w.code === value);
    onInputChange("store_wards", ward?.code || "");
  };

  return (
    <Card className="border border-border">
      <CardHeader>
        <CardTitle className="text-sm font-medium text-foreground">
          {t("storeInformation.advancedInformation")}
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          {t("storeInformation.advancedInformationDescription")}
        </p>
      </CardHeader>

      <CardContent className="px-6 py-3">
        <div className="space-y-2">
          {/* Store URL */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-foreground">
              {t("storeInformation.storeUrl")}
            </Label>
            <Input
              value={formData.storeUrl}
              onChange={(e) => onInputChange("storeUrl", e.target.value)}
              placeholder={t("storeInformation.storeUrlPlaceholder")}
              className="border-border"
            />
          </div>

          {/* Default Price Group */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-foreground">
              {t("storeInformation.defaultPriceGroup")}
            </Label>
            <PortalCombobox
              items={priceGroups.map((g) => ({ id: g.id, name: g.name, displayValue: g.name }))}
              value={formData.store_default_price_group?.id}
              onValueChange={(value) => {
                const priceGroup = priceGroups.find((g) => g.id === value);
                onInputChange(
                  "store_default_price_group",
                  priceGroup ? JSON.stringify(priceGroup) : ""
                );
              }}
              placeholder={t("storeInformation.selectPriceGroup")}
              emptyText={t("common.empty.description", "No price groups found.")}
              isLoading={isLoadingPriceGroups}
              onLoadMore={hasNextPriceGroups ? fetchNextPriceGroups : undefined}
              hasNextPage={hasNextPriceGroups}
              isLoadingMore={isFetchingNextPriceGroups}
            />
          </div>

          {/* Address */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-foreground">
              {t("storeInformation.address")}
            </Label>
            <Input
              value={formData.store_address}
              onChange={(e) => onInputChange("store_address", e.target.value)}
              placeholder={t("storeInformation.addressPlaceholder")}
              className="border-border"
            />
          </div>

          {/* Location Fields */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            {/* Province */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-foreground">
                {t("storeInformation.province")}
              </Label>
              <PortalCombobox
                items={provinces.map((p) => ({
                  id: p.code,
                  name: p.name_with_type,
                  displayValue: p.name_with_type,
                }))}
                value={formData.store_provinces}
                onValueChange={handleProvinceChange}
                placeholder={t("storeInformation.selectProvince")}
                emptyText={t("common.empty.description", "No provinces found.")}
                isLoading={isLoadingProvinces}
                onLoadMore={hasNextProvinces ? fetchNextProvinces : undefined}
                hasNextPage={hasNextProvinces}
                isLoadingMore={isFetchingNextProvinces}
                onSearchQueryChange={setProvinceQuery}
              />
            </div>

            {/* District */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-foreground">
                {t("storeInformation.district")}
              </Label>
              <PortalCombobox
                items={districts.map((d) => ({
                  id: d.code,
                  name: d.name_with_type,
                  displayValue: d.name_with_type,
                }))}
                value={formData.store_districts}
                onValueChange={handleDistrictChange}
                placeholder={t("storeInformation.selectDistrict")}
                emptyText={t("common.empty.description", "No districts found.")}
                isLoading={isLoadingDistricts}
                onLoadMore={hasNextDistricts ? fetchNextDistricts : undefined}
                hasNextPage={hasNextDistricts}
                isLoadingMore={isFetchingNextDistricts}
                onSearchQueryChange={setDistrictQuery}
              />
            </div>

            {/* Ward */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-foreground">
                {t("storeInformation.ward")}
              </Label>
              <PortalCombobox
                items={wards.map((w) => ({
                  id: w.code,
                  name: w.name_with_type,
                  displayValue: w.name_with_type,
                }))}
                value={formData.store_wards}
                onValueChange={handleWardChange}
                placeholder={t("storeInformation.selectWard")}
                emptyText={t("common.empty.description", "No wards found.")}
                isLoading={isLoadingWards}
                onLoadMore={hasNextWards ? fetchNextWards : undefined}
                hasNextPage={hasNextWards}
                isLoadingMore={isFetchingNextWards}
                onSearchQueryChange={setWardQuery}
              />
            </div>
          </div>
        </div>
      </CardContent>

      {/* Footer Actions */}
      <CardFooter className="flex items-center justify-end py-3">
        <Button
          type="submit"
          onClick={handleSubmit}
          disabled={!isFormDirty || isLoading || isSubmitting}
          loading={isSubmitting}>
          {t("common.update")}
        </Button>
      </CardFooter>
    </Card>
  );
};
