"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { useProperties, useProperty } from "@/features/property-assets/property/hooks/property";

import { authProtectedPaths } from "@/constants/paths";

import { useUnits } from "../../../hooks/useUnits";
import {
  type PropertyDetailsComponentProps,
  type PropertyStats,
  type PropertyTypeLabels,
  type StatusBadgeConfig,
} from "./PropertyDetailsComponent.types";

export const usePropertyDetails = ({ propertyId }: PropertyDetailsComponentProps) => {
  const { t } = useTranslation();
  const router = useRouter();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Data fetching
  const {
    data: property,
    isLoading: propertyLoading,
    error: propertyError,
  } = useProperty(propertyId);
  const { data: unitsData, isLoading: unitsLoading } = useUnits(propertyId, { limit: 1000 });
  const { useDeletePropertyMutation } = useProperties();
  const units = unitsData?.items || [];

  // Mutations
  const deletePropertyMutation = useDeletePropertyMutation;

  // Calculate stats
  const calculateStats = (): PropertyStats => {
    const totalUnits = units.length;
    const occupiedUnits = units.filter((unit) => unit.status === "occupied").length;
    const availableUnits = units.filter((unit) => unit.status === "available").length;
    const maintenanceUnits = units.filter((unit) => unit.status === "maintenance").length;
    const occupancyRate = totalUnits > 0 ? ((occupiedUnits / totalUnits) * 100).toFixed(1) : "0";

    return {
      totalUnits,
      occupiedUnits,
      availableUnits,
      maintenanceUnits,
      occupancyRate,
    };
  };

  const stats = calculateStats();

  // Property type labels
  const getPropertyTypeLabels = (): PropertyTypeLabels => ({
    RESIDENTIAL: t("pages.properties.types.residential"),
    COMMERCIAL: t("pages.properties.types.commercial"),
    MIXED_USE: t("pages.properties.types.mixed"),
  });

  // Status badge configuration
  const getStatusBadgeConfig = (): StatusBadgeConfig => ({
    variants: {
      ACTIVE: "default",
      INACTIVE: "secondary",
    },
    labels: {
      ACTIVE: t("common.status.active"),
      INACTIVE: t("common.status.inactive"),
    },
  });

  // Unit status badge configuration
  const getUnitStatusBadgeConfig = (status: string) => {
    const config = {
      occupied: { variant: "destructive" as const, className: "text-xs capitalize" },
      available: { variant: "default" as const, className: "text-xs capitalize" },
      maintenance: { variant: "secondary" as const, className: "text-xs capitalize" },
    };
    return (
      config[status as keyof typeof config] || {
        variant: "outline" as const,
        className: "text-xs capitalize",
      }
    );
  };

  // Unit type labels
  const getUnitTypeLabel = (unitType: string): string => {
    const labels: Record<string, string> = {
      "1br": "1 Bedroom",
      "2br": "2 Bedroom",
      "3br": "3 Bedroom",
      studio: "Studio",
      commercial: "Commercial",
      office: "Office",
      retail: "Retail",
    };
    return labels[unitType] || unitType;
  };

  // Event handlers
  const handleEdit = () => {
    router.push(`${authProtectedPaths.PROPERTIES}/${propertyId}/edit` as any);
  };

  const handleDelete = async () => {
    try {
      await deletePropertyMutation.mutateAsync(propertyId);
      toast.success(t("pages.properties.deleteSuccess"));
      router.push(authProtectedPaths.PROPERTIES as any);
    } catch (error) {
      toast.error(t("pages.properties.deleteError"));
    }
  };

  const handleAddUnit = () => {
    router.push(`${authProtectedPaths.UNITS}/new?property=${propertyId}` as any);
  };

  const handleDeleteDialogChange = (open: boolean) => {
    setShowDeleteDialog(open);
  };

  return {
    // Data
    property,
    units,
    stats,

    // Loading states
    propertyLoading,
    unitsLoading,

    // Errors
    propertyError,

    // UI state
    showDeleteDialog,

    // Mutations
    deletePropertyMutation,

    // Computed values
    getPropertyTypeLabels,
    getStatusBadgeConfig,
    getUnitStatusBadgeConfig,
    getUnitTypeLabel,

    // Event handlers
    handleEdit,
    handleDelete,
    handleAddUnit,
    handleDeleteDialogChange,
  };
};
