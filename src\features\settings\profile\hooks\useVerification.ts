import { useMutation } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { authKeys } from "@/features/auth/hooks/keys";

import { authApi } from "@/lib/apis/auth";

export const useVerification = () => {
  const { t } = useTranslation();

  // Verify code mutation
  const {
    mutate: verifyCode,
    isPending: isVerifyingCode,
    error: verifyCodeError,
    reset: resetVerifyCodeMutation,
    data: verificationData,
  } = useMutation({
    mutationKey: authKeys.verifyCodeReset(""),
    mutationFn: async ({ username, code }: { username: string; code: string }) => {
      console.log("username", username);
      console.log("code", code);
      if (!username || !code) {
        throw new Error(t("auth.verificationCodeRequired"));
      }
      return authApi.verifyCodeReset({ username, code });
    },
    onError: (error) => {
      const message = error instanceof Error ? error.message : t(String(error));
      toast.error(t("common.error"), {
        description: message,
      });
    },
  });

  return {
    verifyCode,
    isVerifyingCode,
    verifyCodeError,
    resetVerifyCodeMutation,
    session: verificationData?.session, // Extract session from response
  };
};
