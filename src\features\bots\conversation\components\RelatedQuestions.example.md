# Related Questions Feature - Usage Example

## How it works

### 1. Message Format

When a bot/staff message contains related questions in XML format:

```xml
Len nhung mèo có kích thước từ 3.5mm đến 4.0mm.
<PERSON><PERSON><PERSON><PERSON> ra, len nhung mèo cũng có trọng lượng 50gr và chiều dài 60m.

<related_questions>
    <question>Khi nào nên sử dụng len nhung mèo cho các dự án đan móc?</question>
    <question><PERSON><PERSON> thể làm những sản phẩm nào từ len nhung mèo?</question>
    <question>Len nhung mèo có phù hợp cho người mới bắt đầu không?</question>
</related_questions>
```

### 2. Display Behavior

- Main message content appears normally (without XML tags)
- Related questions display as interactive cards below the message
- Each question has:
  - Message icon
  - Hover effects
  - Click to send functionality

### 3. Interaction Flow

- **Click a question** → Sends it immediately as a new message
- **Clicked questions** → Show with checkmark and disabled state
- **Visual feedback** → Strikethrough text and muted colors for used questions
- **Multiple clicks** → Each question can only be clicked once

### 4. UX Features

- ✅ **Immediate send** - No need to manually type or press send
- ✅ **Visual tracking** - See which questions have been asked
- ✅ **Clean interface** - Clicked questions fade but remain visible for context
- ✅ **Dark mode support** - Uses semantic theme colors
- ✅ **Responsive design** - Works on all screen sizes

## Benefits

1. **Guided conversation** - Helps users explore topics systematically
2. **Reduced typing** - One-click to ask follow-up questions
3. **Context preservation** - See conversation flow and choices made
4. **Improved engagement** - Interactive elements encourage exploration
