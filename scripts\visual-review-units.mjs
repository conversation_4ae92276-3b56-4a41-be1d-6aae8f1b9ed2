#!/usr/bin/env node
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import puppeteer from "puppeteer";

import { navigateWithAuth } from "./utils/auth.mjs";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function visualReviewUnits() {
  let browser;

  try {
    console.log("🔍 Starting Visual Review of Property Assets Units...");

    // Launch browser with visible UI for review
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1920, height: 1080 },
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
      slowMo: 100, // Slow down for better observation
    });

    const page = await browser.newPage();

    // Navigate to property assets dashboard
    console.log("🔐 Navigating to Property Assets Dashboard...");
    await navigateWithAuth(page, "http://localhost:3000/property-assets-dashboard");

    // Wait for page to fully load
    await new Promise((resolve) => setTimeout(resolve, 3000));

    // Take initial screenshot
    await page.screenshot({
      path: "screenshots/units-review-1-overview.png",
      fullPage: true,
    });
    console.log("📸 Captured: Overview tab");

    // Check available tabs and click on Units tab
    console.log("🎯 Looking for Units tab...");

    // Wait for tabs to load and get available tab text
    const tabsAvailable = await page.evaluate(() => {
      const tabs = Array.from(document.querySelectorAll('[role="tab"], button'));
      return tabs.map((tab) => ({
        text: tab.textContent?.trim(),
        selector:
          tab.tagName.toLowerCase() +
          (tab.className ? "." + tab.className.split(" ").join(".") : ""),
      }));
    });

    console.log("Available tabs:", tabsAvailable.filter((t) => t.text).slice(0, 10));

    // Try different selectors for Units tab
    const unitsSelectors = [
      'button:has-text("Units")',
      '[data-value="units"]',
      'button[value="units"]',
      'button:contains("Units")',
      'button[data-testid="units-tab"]',
    ];

    let unitsClicked = false;
    for (const selector of unitsSelectors) {
      try {
        await page.waitForSelector(selector, { timeout: 2000 });
        await page.click(selector);
        console.log(`✅ Clicked Units tab using selector: ${selector}`);
        unitsClicked = true;
        break;
      } catch (e) {
        // Try next selector
      }
    }

    // If direct selectors don't work, try clicking by text content
    if (!unitsClicked) {
      try {
        await page.evaluate(() => {
          const buttons = Array.from(document.querySelectorAll("button"));
          const unitsButton = buttons.find((btn) => btn.textContent?.includes("Units"));
          if (unitsButton) {
            unitsButton.click();
            return true;
          }
          return false;
        });
        console.log("✅ Clicked Units tab using text content");
        unitsClicked = true;
      } catch (e) {
        console.log("⚠️  Could not find Units tab, continuing with current view");
      }
    }

    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Take Units tab screenshot
    await page.screenshot({
      path: "screenshots/units-review-2-units-tab.png",
      fullPage: true,
    });
    console.log("📸 Captured: Units tab");

    // Test Unit Specifications
    console.log("🏠 Testing Unit Specifications...");
    await new Promise((resolve) => setTimeout(resolve, 1000));
    await page.screenshot({
      path: "screenshots/units-review-3-specifications.png",
      fullPage: true,
    });

    // Try to open unit details modal
    try {
      const detailsButton = await page.$('button:has-text("Details")');
      if (detailsButton) {
        await detailsButton.click();
        await new Promise((resolve) => setTimeout(resolve, 1500));
        await page.screenshot({
          path: "screenshots/units-review-4-unit-details-modal.png",
          fullPage: true,
        });
        console.log("📸 Captured: Unit details modal");

        // Close modal
        await page.keyboard.press("Escape");
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    } catch (error) {
      console.log("ℹ️  Unit details modal not available or different selector");
    }

    // Scroll down to see more components
    await page.evaluate(() => window.scrollTo(0, 600));
    await new Promise((resolve) => setTimeout(resolve, 1000));
    await page.screenshot({
      path: "screenshots/units-review-5-scrolled-view.png",
      fullPage: true,
    });
    console.log("📸 Captured: Scrolled view");

    // Test mobile responsive view
    console.log("📱 Testing mobile view...");
    await page.setViewport({ width: 375, height: 812 });
    await new Promise((resolve) => setTimeout(resolve, 1500));
    await page.screenshot({
      path: "screenshots/units-review-6-mobile.png",
      fullPage: true,
    });
    console.log("📸 Captured: Mobile view");

    // Test tablet view
    console.log("📱 Testing tablet view...");
    await page.setViewport({ width: 768, height: 1024 });
    await new Promise((resolve) => setTimeout(resolve, 1500));
    await page.screenshot({
      path: "screenshots/units-review-7-tablet.png",
      fullPage: true,
    });
    console.log("📸 Captured: Tablet view");

    // Return to desktop view
    await page.setViewport({ width: 1920, height: 1080 });
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Test Analytics tab for comparison
    console.log("📊 Testing Analytics tab...");
    await page.click('button[value="analytics"]');
    await new Promise((resolve) => setTimeout(resolve, 2000));
    await page.screenshot({
      path: "screenshots/units-review-8-analytics-tab.png",
      fullPage: true,
    });
    console.log("📸 Captured: Analytics tab");

    // Test Operations tab
    console.log("⚙️ Testing Operations tab...");
    await page.click('button[value="operations"]');
    await new Promise((resolve) => setTimeout(resolve, 2000));
    await page.screenshot({
      path: "screenshots/units-review-9-operations-tab.png",
      fullPage: true,
    });
    console.log("📸 Captured: Operations tab");

    // Generate visual review report
    const visualReport = {
      timestamp: new Date().toISOString(),
      reviewType: "Visual Review - Property Assets Units",
      screenshots: [
        {
          name: "Overview Tab",
          file: "units-review-1-overview.png",
          description: "Initial dashboard overview",
        },
        {
          name: "Units Tab",
          file: "units-review-2-units-tab.png",
          description: "Main units management interface",
        },
        {
          name: "Unit Specifications",
          file: "units-review-3-specifications.png",
          description: "Unit specifications component",
        },
        {
          name: "Unit Details Modal",
          file: "units-review-4-unit-details-modal.png",
          description: "Unit details modal (if available)",
        },
        {
          name: "Scrolled View",
          file: "units-review-5-scrolled-view.png",
          description: "Lower portion of units interface",
        },
        {
          name: "Mobile View",
          file: "units-review-6-mobile.png",
          description: "Mobile responsive design",
        },
        {
          name: "Tablet View",
          file: "units-review-7-tablet.png",
          description: "Tablet responsive design",
        },
        {
          name: "Analytics Tab",
          file: "units-review-8-analytics-tab.png",
          description: "Analytics interface for comparison",
        },
        {
          name: "Operations Tab",
          file: "units-review-9-operations-tab.png",
          description: "Operations interface for comparison",
        },
      ],
      components: [
        "Unit Specifications Management",
        "Availability Calendar",
        "Unit Revenue Tracking",
        "Occupancy Analytics",
        "Bulk Unit Operations",
      ],
      notes: [
        "All screenshots captured for visual review",
        "Mobile and tablet responsiveness tested",
        "Multiple tabs reviewed for comparison",
        "Full page screenshots provide complete context",
      ],
    };

    // Save visual report
    const reportsDir = path.join(__dirname, "..", "test-reports");
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    fs.writeFileSync(
      path.join(reportsDir, "visual-review-units.json"),
      JSON.stringify(visualReport, null, 2)
    );

    console.log("\n📋 VISUAL REVIEW COMPLETE");
    console.log("=".repeat(50));
    console.log("📸 Screenshots captured: 9");
    console.log("📱 Responsive views: Mobile, Tablet, Desktop");
    console.log("🎯 Components reviewed: 5");
    console.log("📁 Files saved to screenshots/ directory");
    console.log("📊 Report saved to test-reports/visual-review-units.json");
    console.log("\n🔍 Review the screenshots to evaluate:");
    console.log("   • Layout and spacing");
    console.log("   • Component alignment");
    console.log("   • Color scheme consistency");
    console.log("   • Typography and readability");
    console.log("   • Responsive behavior");
    console.log("   • Interactive elements");
  } catch (error) {
    console.error("❌ Visual review failed:", error);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run visual review
visualReviewUnits().catch(console.error);
