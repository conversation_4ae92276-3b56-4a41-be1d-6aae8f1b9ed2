import { Skeleton } from "@/components/ui/skeleton";

export const CheckoutSkeleton = () => {
  return (
    <div className="flex flex-col gap-4 p-4 pt-0">
      <div className="flex flex-1 items-start justify-center overflow-hidden">
        <div className="flex w-full justify-center">
          <div className="flex w-full max-w-md flex-col gap-2 rounded-xl bg-card p-4">
            {/* Header skeleton */}
            <Skeleton className="h-6 w-48" />

            {/* Subscription details skeleton */}
            <div className="flex w-full flex-col gap-2">
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-20" />
              </div>
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-8 w-24" />
              </div>
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-16" />
                <div className="flex items-center gap-1">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-8" />
                </div>
              </div>
            </div>

            {/* Separator */}
            <div className="h-px w-full bg-border" />

            {/* Order details skeleton */}
            <div className="flex w-full flex-col gap-2">
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-20" />
              </div>
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-28" />
                <Skeleton className="h-4 w-24" />
              </div>
            </div>

            {/* Separator */}
            <div className="h-px w-full bg-border" />

            {/* Company information skeleton */}
            <div className="flex w-full flex-col gap-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>

            {/* Separator */}
            <div className="h-px w-full bg-border" />

            {/* Pricing summary skeleton */}
            <div className="flex w-full flex-col gap-2">
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-16" />
                <div className="flex items-center gap-1">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-8" />
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="size-4" />
                </div>
                <div className="flex items-center gap-1">
                  <Skeleton className="h-8 w-24" />
                  <Skeleton className="h-4 w-8" />
                </div>
              </div>
            </div>

            {/* Button skeleton */}
            <Skeleton className="h-10 w-full" />
          </div>
        </div>
      </div>
    </div>
  );
};
