export interface Category {
  id: string;
  name: string;
}

export interface Blog {
  id: string;
  author_id: string;
  slug: string;
  image?: string;
  description?: string;
  category: Category;
  company_id: string;
  created_at: string;
  updated_at: string;
  locale: string;
  title: string;
  org_blog_id: string;
  blog_type: string;
  tags?: string | null;
  publish: boolean;
  user: {
    name_staff: string;
    id: string;
  };
  total_comments?: number;
}
