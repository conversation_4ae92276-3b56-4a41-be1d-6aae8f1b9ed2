{"nav": {"blog": "블로그", "operation": "운영", "overview": "개요", "patientManagement": "환자 관리", "doctorManagement": "의사 관리", "medicalSupplies": "의료 용품", "invoicesPayments": "청구서 및 결제", "report": "보고서", "administration": "관리", "product": "상품", "productList": "상품 목록", "newProduct": "새 상품 추가", "editProduct": "상품 편집", "variantsList": "변형 목록", "brandList": "브랜드 목록", "categoryList": "카테고리 목록", "order": "주문", "orderList": "주문 목록", "orderDetail": "주문 상세", "orderEdit": "주문 편집", "orderProcess": "주문 처리", "returnOrderList": "반품 주문 목록", "packageList": "패키지 목록", "integration": "통합", "fetchEvent": "이벤트 정보 가져오기", "syncRecords": "데이터 동기화", "channel": "판매 채널", "logistics": "물류", "shippingProviderList": "배송 업체 목록", "purchaseOrder": "구매 주문서", "purchaseOrderList": "구매 주문서 목록", "supplierList": "공급업체 목록", "customers": "고객", "customerDashboard": "대시보드", "customerList": "고객 목록", "customerDetail": "고객 상세", "customerGroupList": "고객 그룹 목록", "loyaltyProgram": "충성도 프로그램", "rewardProgram": "포인트 프로그램", "finance": "재무", "account": "계정", "paymentMethod": "결제 방법", "transaction": "거래", "inventory": "재고", "locationList": "위치 목록", "inventoryList": "재고 목록", "stockAdjustmentList": "재고 조정 목록", "stockRelocateList": "재고 이동 목록", "promotion": "프로모션", "discountList": "할인 상품 목록", "voucherList": "바우처 목록", "import": "가져오기", "importList": "가져오기 목록", "recordList": "기록 목록", "website": "웹사이트", "blogCategory": "블로그 카테고리", "blogList": "블로그 목록", "notification": "알림", "notificationList": "알림 목록", "loyaltyApp": "충성도 앱", "pos": "POS 판매", "detailFetchEvent": "이벤트 상세 가져오기", "supportedChannels": "지원 채널 목록", "installChannel": "새 채널 설정", "terminalList": "단말기 목록", "shiftList": "근무 교대 목록", "posFnB": "F&B POS 판매", "settings": "설정", "dashboard": "대시보드", "productReport": "상품 보고서", "productDetail": "상품 상세", "orderManual": "주문 추가", "productMapping": "상품 동기화", "productMappingDetail": "동기화 상세", "productMappingAttribute": "상품 속성 동기화", "staff": "직원", "staffList": "직원 목록", "department": "부서", "conversation": "대화", "interact": "상호작용", "knowledge": "지식", "task": "작업", "editStaff": "직원 편집", "activities": "활동", "opportunities": "기회", "crm": "CRM", "opportunityDetail": "기회 상세", "pipelines": "파이프라인", "subscription": "구독", "checkout": "결제", "propertyAssets": "부동산 자산", "properties": "부동산", "layoutManagement": "레이아웃 관리", "units": "임대 유닛", "contracts": "계약", "tenants": "세입자", "financial": "재무", "maintenance": "유지보수", "reports": "보고서", "propertyAssetsDashboard": "부동산 자산 대시보드", "propertyDetail": "부동산 상세", "newProperty": "새 부동산", "editProperty": "부동산 편집", "unitDetail": "유닛 상세", "newUnit": "새 유닛", "editUnit": "유닛 편집", "contractDetail": "계약 상세", "newContract": "새 계약", "editContract": "계약 편집", "tenantDetail": "세입자 상세", "newTenant": "새 세입자", "editTenant": "세입자 편집", "payments": "결제", "paymentDetail": "결제 상세", "newPayment": "새 결제", "editPayment": "결제 편집", "maintenanceDetail": "유지보수 상세", "newMaintenance": "새 유지보수", "editMaintenance": "유지보수 편집", "assetCategories": "자산 카테고리", "assetCategoryDetail": "자산 카테고리 상세", "newAssetCategory": "새 자산 카테고리", "editAssetCategory": "자산 카테고리 편집", "documentManagement": "문서 관리", "documentDetail": "문서 상세", "newDocument": "새 문서", "editDocument": "문서 편집", "propertyGallery": "갤러리", "propertyGalleryDetail": "갤러리 상세", "newPropertyGallery": "새 이미지 추가", "editPropertyGallery": "이미지 편집", "propertyValuation": "자산 평가", "propertyValuationDetail": "평가 상세", "newPropertyValuation": "새 평가", "editPropertyValuation": "평가 편집"}, "quota": {"storage": "스토리지 용량", "products": "상품 수", "orders": "주문 수", "messages": "메시지 수", "staffs": "직원 수", "knowledge": "지식", "capacity": "용량", "knowledge_capacity": "지식 용량"}, "product": {"image": "상품 이미지", "title": "상품명", "description": "설명", "price": "가격", "sku": "SKU 코드", "brand": "브랜드", "category": "카테고리", "inventory": "재고", "notMapped": "미연결"}, "productMapping": {"lastSynced": "마지막 동기화", "errorLoading": "상품 연결 상세 불러오기 오류", "manualRetry": "수동 재시도", "cancelledMessage": "상품 연결이 취소되었습니다", "mappingStatus": "연결 상태", "variant": "변형"}, "groups": {"crm": "CRM", "operations": "운영", "virtual_staff": "가상 직원", "product": "상품", "property_assets": "부동산 자산"}, "branch": {"Ho Chi Minh": "호치민시", "Ha Noi": "하노이", "Da Nang": "다낭", "Hai Phong": "하이퐁", "Can Tho": "껀터", "Binh Duong": "빈즈엉", "Binh Phuoc": "빈푹", "All": "전체", "title": "지점 선택", "all": "모든 지점", "addBranch": "새 지점 추가", "branch": "지점", "shortcuts": {"alt": "Alt", "plus": "+"}, "daily": "매일", "weekly": "매주", "monthly": "매월", "yearly": "매년", "annually": "매년", "refresh": "새로고침"}, "profile": {"updateSuccess": "언어 설정이 성공적으로 업데이트되었습니다", "updateError": "언어 설정 업데이트 실패", "free": "무료", "profile": "프로필", "settings": "설정", "darkMode": "다크 모드", "on": "켜짐", "off": "꺼짐", "language": "언어", "english": "영어", "vietnamese": "베트남어", "japanese": "일본어", "selectLanguage": "언어 선택", "searchLanguage": "언어 검색...", "noLanguagesFound": "언어를 찾을 수 없습니다.", "current": "현재", "logout": "로그아웃", "founder": "창립자", "usedSpace": "사용된 용량", "upgrade": "업그레이드", "message": "메시지", "documents": "문서", "staff": "직원", "storage": "스토리지"}, "storeInformation": {"storeInformation": "매장 정보", "basicInformation": "기본 정보", "basicInformationDescription": "고객이 귀하에게 연락하기 위한 정보입니다.", "advancedInformation": "고급 정보", "advancedInformationDescription": "웹사이트 링크, 가격, 매장 주소 등의 고급 설정을 구성합니다.", "storeName": "매장 이름", "storeNamePlaceholder": "예: ABC샵", "phone": "전화번호", "phonePlaceholder": "0123456789", "email": "이메일 주소", "emailPlaceholder": "<EMAIL>", "businessSector": "업종", "selectBusinessSector": "선택...", "storeUrl": "매장 URL", "storeUrlPlaceholder": "예: ABC샵", "defaultPriceGroup": "기본 가격 그룹", "selectPriceGroup": "선택...", "address": "주소", "addressPlaceholder": "매장 주소를 입력하세요", "province": "도/시", "selectProvince": "선택...", "district": "구/군", "selectDistrict": "선택...", "ward": "동/읍/면", "selectWard": "선택..."}, "auth": {"passwordResetSuccess": "비밀번호가 성공적으로 재설정되었습니다", "minPasswordLength": "비밀번호는 8자 이상이어야 합니다", "changePasswordTitle": "비밀번호 변경", "changePasswordSubtitle": "현재 비밀번호와 새 비밀번호를 입력하세요", "changePasswordSuccess": "비밀번호가 성공적으로 변경되었습니다", "changePasswordError": "비밀번호 변경 실패", "changePasswordLoading": "비밀번호 변경 중...", "currentPasswordRequired": "현재 비밀번호는 필수입니다", "currentPasswordPlaceholder": "현재 비밀번호 입력", "currentPassword": "현재 비밀번호", "passwordsDontMatch": "비밀번호가 일치하지 않습니다", "brandSection": {"title": "14일 무료 체험"}, "incorrectUsernameOrPassword": "아이디 또는 비밀번호가 올바르지 않습니다.", "userExist": "아이디가 이미 존재합니다!", "userExistAndVerified": "이 이메일은 이미 등록 및 인증되었습니다. 로그인하거나 다른 이메일을 사용하세요.", "logoutSuccess": "로그아웃 성공", "logoutError": "로그아웃 오류", "gender": "성별", "genderPlaceholder": "성별 선택", "male": "남성", "female": "여성", "other": "기타", "preferNotToSay": "응답하지 않음", "dob": "생년월일", "dobPlaceholder": "생년월일 선택", "username": "아이디", "usernamePlaceholder": "아이디 입력", "login": "로그인", "register": "회원가입", "forgotPasswordDescription": "이메일 주소를 입력하여 안내를 받으세요!", "forgotPasswordTitle": "비밀번호를 잊으셨나요?", "forgotPasswordSubtitle": "비밀번호 재설정 안내를 받으려면 이메일을 입력하세요", "resetPassword": "비밀번호 재설정", "resetPasswordTitle": "비밀번호 재설정", "resetPasswordDescription": "인증 코드와 새 비밀번호를 입력하세요", "resetPasswordSubtitle": "인증 코드와 새 비밀번호를 입력하세요", "resetPasswordButton": "비밀번호 재설정", "resetPasswordSuccess": "비밀번호가 성공적으로 재설정되었습니다", "resetPasswordSuccessDescription": "새 비밀번호로 로그인할 수 있습니다", "resetPasswordError": "비밀번호를 재설정할 수 없습니다", "resetPasswordLoading": "재설정 중...", "confirmPassword": "비밀번호 확인", "confirmPasswordPlaceholder": "비밀번호 확인 입력", "backToLogin": "로그인 화면으로 돌아가기", "backToForgotPassword": "로그인으로 돌아가기", "loginTitle": "로그인", "loginSubtitle": "아이디 또는 이메일 주소를 입력하여 로그인하세요", "email": "이메일 주소", "emailPlaceholder": "m@example", "emailPlaceholderSignUp": "이메일 주소 입력", "verifyEmail": "이메일 인증", "verifyEmailButton": "이메일 인증", "verifyEmailSuccess": "이메일 인증 성공", "verifyEmailError": "이메일 인증 실패", "verifyEmailLoading": "인증 중...", "verifyEmailCode": "이메일로 전송된 코드를 입력하세요", "verifyEmailCodePlaceholder": "코드 입력", "verifyEmailCodeButton": "코드 인증", "verifyEmailCodeSuccess": "코드 인증 성공", "verifyEmailCodeError": "코드 인증 실패", "verifyEmailCodeLoading": "코드 인증 중...", "newPassword": "새 비밀번호", "newPasswordPlaceholder": "새 비밀번호 입력", "verificationCode": "인증 코드", "verificationCodePlaceholder": "인증 코드 입력", "verificationCodeButton": "인증", "verificationCodeSuccess": "인증 성공", "verificationCodeError": "인증 실패", "verificationCodeDescription": "{{username}}에게 코드를 전송했습니다. 아래에 입력하세요.", "sendInstructions": "안내 전송", "sending": "전송 중...", "resetting": "재설정 중...", "password": "비밀번호", "passwordPlaceholder": "비밀번호 입력", "rememberMe": "로그인 정보 기억하기", "loginButton": "로그인", "loginWithGoogle": "Google로 로그인", "loginWithGithub": "Github로 로그인", "noAccount": "계정이 없으신가요?", "signUp": "회원가입", "signUpTitle": "회원가입", "signUpSubtitle": "관리 화면에 로그인하려면 가입하세요", "signUpButton": "가입", "signUpSuccess": "가입 성공! 이메일 인증을 진행하세요", "signUpError": "가입 실패", "signUpLoading": "가입 중...", "alreadyHaveAccount": "이미 계정이 있으신가요?", "sendNewCode": "재전송", "resendCodeSuccess": "인증 코드를 재전송했습니다", "resendCodeError": "인증 코드를 재전송할 수 없습니다", "usernameOrEmail": "아이디 또는 이메일 주소", "usernameOrEmailPlaceholder": "아이디 또는 이메일 주소 입력", "forgot": "비밀번호를 잊으셨나요", "or": "또는", "loginSuccess": "로그인 성공", "loginError": "로그인 실패", "loginLoading": "로그인 중...", "usernameRequired": "아이디를 입력하세요", "emailRequired": "이메일 주소를 입력하세요", "passwordRequired": "비밀번호를 입력하세요", "confirmPasswordRequired": "비밀번호를 확인하세요", "invalidPassword": "비밀번호는 8자 이상이어야 합니다", "forgotPasswordSuccess": "비밀번호 재설정 안내를 이메일로 전송했습니다", "forgotPasswordError": "비밀번호 재설정 안내를 전송할 수 없습니다", "newPasswordRequired": "새 비밀번호는 필수입니다", "pleaseChangePassword": "비밀번호를 변경하세요", "passwordsDoNotMatch": "비밀번호가 일치하지 않습니다", "passwordMustBeAtLeast8Characters": "비밀번호는 8자 이상이어야 합니다", "codeRequired": "인증 코드를 입력하세요", "resendCodeCountdown": "{{seconds}}초 후에 재전송 가능"}, "onboarding": {"step1": {"title": "OnexBots를 어디서 알게 되었나요?", "options": {"facebook": "Facebook", "zalo": "<PERSON><PERSON>", "youtube": "YouTube", "instagram": "Instagram", "tiktok": "TikTok", "google": "Google", "linkedin": "LinkedIn", "referral": "추천 또는 지인 소개", "other": "기타"}, "otherPlaceholder": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>sapp 등"}, "step2": {"title": "AI 채팅 소프트웨어를 사용해본 적이 있나요?", "options": {"never": "사용해본 적 없음", "tried": "사용해본 적 있음", "regularly": "정기적으로 사용 중"}}, "step3": {"title": "어느 산업 분야에서 일하고 계신가요?", "options": {"ecommerce": "이커머스", "travel": "여행", "real_estate": "부동산", "health": "헬스케어 및 뷰티", "education": "교육", "other": "기타"}, "otherPlaceholder": "IT, 물류 등"}, "step3_part2": {"title": "귀사의 직원 수는 몇 명인가요?", "options": {"just_me": "나 혼자", "2-9": "2~9명", "10-49": "10~49명", "50-199": "50~199명", "200-499": "200~499명", "500+": "500명 이상"}, "inputLabel": "귀사의 URL (선택 사항)", "inputPlaceholder": "www.example.com"}, "step4": {"title": "귀사는 어떤 분야에서 활동하고 있나요?", "options": {"specialty_clinic": "종합병원", "aesthetic_clinic": "뷰티 클리닉/스파", "cosmetic_surgery": "성형외과", "nutrition_clinic": "영양 치료/스포츠 의학", "telemedicine": "원격 의료/의료 기술", "pharma": "의약품/화장품 소매 체인", "other": "기타"}, "otherPlaceholder": "종합 헬스케어 센터, 바이오테크 스타트업 등"}, "step5": {"title": "어떤 분야의 전문가 지원이 필요하신가요?", "options": {"consulting": "컨설팅", "customer_care": "고객 지원", "accounting": "회계", "marketing": "마케팅", "other": "기타"}, "otherPlaceholder": "법률 상담, IT 지원 등"}, "step6": {"title": "OnexBots를 사용하는 목적은 무엇인가요?", "options": {"feedback": "피드백 수집 및 분석", "pressure": "피크 시간대 부담 완화", "channels": "다중 채널 동시 대응", "quality": "응대 품질 향상", "responses": "자동 빠른 응답", "monitor": "직원 교육 및 모니터링", "other": "기타"}, "otherPlaceholder": "리드 확보, 고객 교육 등"}, "buttons": {"back": "뒤로", "skip": "건너뛰기", "continue": "계속", "done": "완료"}, "otherInput": {"pleaseSpecify": "자세히 입력해주세요", "optional": "(선택 사항)"}}, "common": {"submit": "제출", "dataCreatedBySystem": "루트 데이터", "exceedTotalSize": "총 파일 크기는 5MB를 초과할 수 없습니다", "tryingToAdd": "추가하려고 합니다", "description": "설명", "fileSizeMustNotExceed": "파일 크기는", "onlyImageFilesAllowed": "이미지 파일만 허용됩니다", "resetToDefault": "기본값으로 재설정", "settings": "설정", "viewOnWeb": "웹에서 보기", "confirmCancel": "이 작업은 되돌릴 수 없습니다. 정말로 취소하시겠습니까?", "selected": "선택됨", "pickADateRange": "기간 선택", "other": "기타", "escTo": "Esc 키로", "at": "에서", "areYouAbsolutelySure": "정말로 실행하시겠습니까?", "areYouAbsolutelySureDescription": "이 작업은 되돌릴 수 없습니다. 정말로 계속하시겠습니까?", "canNotDeleteStage": "이 열은 삭제할 수 없습니다", "canNotDeleteStageDescription": "삭제하려면 모든 기회를 이 열에서 이동하세요.", "selectCountry": "국가 선택", "displayCustomizer": "표시 사용자 지정", "customizeTheDisplayOfContentColumnsAccordingToYourPreferences": "콘텐츠 열 표시를 취향에 맞게 사용자 지정합니다.", "noDataAvailable": "데이터가 없습니다", "start": "시작", "back": "뒤로", "words": "단어", "confirm": "확인", "apply": "적용", "totalSize": "총 크기", "aspectRatio": "비율", "formats": "형식", "recommendedSize": "권장 크기", "search": "검색", "filter": "필터", "reset": "재설정", "setAsDefault": "기본값으로 설정", "saveFilters": "필터 저장", "sort": "정렬", "view": "보기", "add": "추가", "update": "업데이트", "edit": "편집", "delete": "삭제", "cancel": "취소", "save": "저장", "saving": "저장 중...", "close": "닫기", "clear": "지우기", "loading": "불러오는 중...", "loadingMore": "더 불러오는 중...", "deleting": "삭제 중...", "toggleGrid": "그리드 보기 전환", "snapToGrid": "그리드에 맞추기", "alignHorizontally": "수평 정렬", "alignVertically": "수직 정렬", "noData": "데이터 없음", "success": "성공", "uploadImage": "이미지를 드래그 앤 드롭하거나", "upload": "업로드", "uploading": "업로드 중...", "fileSizeError": "파일 크기는 5MB 미만이어야 합니다", "uploadError": "이미지 업로드에 실패했습니다", "imageUploadError": "직원 이미지 업로드에 실패했습니다", "areYouSure": "정말로 실행하시겠습니까?", "leaveDesc": "저장되지 않은 변경 사항은 손실됩니다.", "deleteTaskConfirmation": "이 작업은 되돌릴 수 없습니다. 작업이 완전히 삭제됩니다.", "deleteProductConfirmation": "이 작업은 되돌릴 수 없습니다. 상품이 완전히 삭제됩니다.", "deleteListProductConfirmation": "이 작업은 되돌릴 수 없습니다. {{count}}개의 상품이 완전히 삭제됩니다.", "deleteOpportunityConfirmation": "이 작업은 되돌릴 수 없습니다. 기회가 완전히 삭제됩니다.", "deleteEmployeeConfirmation": "이 작업은 되돌릴 수 없습니다. {{count}}명의 직원이 완전히 삭제됩니다.", "install": "설치", "configure": "구성", "deleteSuccess": "상품이 성공적으로 삭제되었습니다", "deleteError": "상품을 삭제할 수 없습니다", "deleteSuccessDescription": "상품이 성공적으로 삭제되었습니다", "actions": "작업", "bulkActions": "일괄 작업", "all": "전체", "sortBy": "정렬 기준", "select": "선택", "label": "라벨", "issues": "문제", "status": {"available": "사용 가능", "occupied": "사용 중", "maintenance": "점검 중", "inactive": "비활성", "active": "활성", "status": "상태", "healthy": "정상", "issues": "문제 있음", "issue": "문제", "complete": "완료", "inProgress": "진행 중", "processing": "처리 중", "error": "오류", "ready": "준비 완료", "pending": "대기 중", "success": "성공", "online": "온라인", "offline": "오프라인"}, "error": "오류가 발생했습니다", "saveChanges": "변경 사항 저장", "unsavedChanges": "저장되지 않은 변경 사항", "unsavedChangesDescription": "저장되지 않은 변경 사항이 있습니다. 닫으시겠습니까?", "discard": "버리기", "keepEditing": "계속 편집", "week": "주", "month": "월", "quarter": "분기", "year": "년", "units": "단위", "leaveWithoutSavingDescription": "저장되지 않은 변경 사항이 있습니다. 종료하시겠습니까?", "leaveWithoutSaving": "저장하지 않고 종료", "leave": "종료", "restore": "복원", "stay": "계속하기", "knowledgeUpdated": "지식이 성공적으로 업데이트되었습니다", "knowledgeDeleted": "지식이 성공적으로 삭제되었습니다", "areYouSureDescription": "이 지식을 삭제하시겠습니까?", "staffUpdated": "직원 정보가 성공적으로 업데이트되었습니다", "updateStaffError": "직원 정보를 업데이트할 수 없습니다", "areYouSureConfirm": "확인", "areYouSureCancel": "취소", "updateAttribute": "속성 업데이트", "time": {"month": "월", "timeAgo": {"seconds": "{{count}}초 전", "seconds_plural": "{{count}}초 전", "minutes": "{{count}}분 전", "minutes_plural": "{{count}}분 전", "hours": "{{count}}시간 전", "hours_plural": "{{count}}시간 전", "days": "{{count}}일 전", "days_plural": "{{count}}일 전", "months": "{{count}}개월 전", "months_plural": "{{count}}개월 전", "years": "{{count}}년 전", "years_plural": "{{count}}년 전", "invalidDate": "유효하지 않은 날짜"}}, "empty": {"title": "여기에는 아무것도 없습니다!", "description": "일치하는 결과를 찾을 수 없습니다."}, "create": "생성", "noFileSelected": "선택된 파일이 없습니다", "uploadSuccess": "업로드 성공", "fileTooLarge": "파일 크기가 최대 {{max}}MB를 초과합니다", "lastUpdated": "마지막 업데이트", "name": "이름", "loadMore": "더 불러오기", "markAsDone": "완료로 표시", "zoomIn": "확대", "zoomOut": "축소", "fitToScreen": "화면에 맞추기", "backToOverview": "개요로 돌아가기", "progress": "진행률", "opacity": "불투명도", "visible": "표시", "properties": "속성", "duplicate": "복제", "quickActions": "빠른 작업", "imageLoadError": "이미지를 불러올 수 없습니다", "uploadNew": "새로 업로드", "viewMode": "보기 모드", "editMode": "편집 모드", "total": "총계"}, "tenants": {"title": "<PERSON><PERSON><PERSON><PERSON> thuê", "addTenant": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON><PERSON> thuê", "editTenant": "Chỉnh sửa người thuê", "tenant": "<PERSON><PERSON><PERSON><PERSON> thuê", "tenantDetail": "<PERSON> tiết ngư<PERSON>i thuê", "tenantDetails": "<PERSON> tiết ngư<PERSON>i thuê", "tenantId": "<PERSON>ã người thuê", "tenantName": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> thuê", "sections": {"personalInfo": "Thông tin cá nhân", "contactInfo": "<PERSON>h<PERSON>ng tin liên hệ", "emergencyContact": "<PERSON><PERSON><PERSON> h<PERSON> khẩn cấp", "leaseInfo": "Thông tin thuê", "documents": "<PERSON><PERSON><PERSON> l<PERSON>", "paymentInfo": "Thông tin thanh toán", "notes": "<PERSON><PERSON><PERSON>", "identification": "<PERSON><PERSON><PERSON><PERSON> tờ tùy thân", "employment": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON>"}, "fields": {"firstName": "<PERSON><PERSON><PERSON>", "lastName": "Họ", "fullName": "<PERSON><PERSON> tên", "email": "Email", "phone": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "address": "Địa chỉ", "dateOfBirth": "<PERSON><PERSON><PERSON>", "occupation": "<PERSON><PERSON><PERSON>", "emergencyContactName": "<PERSON><PERSON><PERSON> liên hệ khẩn cấp", "emergencyContactPhone": "SĐT li<PERSON>n hệ khẩn cấp", "relationship": "<PERSON><PERSON><PERSON> quan hệ", "leaseStart": "<PERSON><PERSON><PERSON> b<PERSON>t đầu thuê", "leaseEnd": "<PERSON><PERSON><PERSON> kết thúc thuê", "rentAmount": "<PERSON><PERSON><PERSON><PERSON> thuê", "securityDeposit": "Tiền đặt cọc", "unit": "Đơn vị", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "notes": "<PERSON><PERSON><PERSON>", "documents": "<PERSON><PERSON><PERSON> l<PERSON>", "identificationType": "<PERSON><PERSON><PERSON> gi<PERSON>y tờ", "identificationNumber": "Số gi<PERSON>y tờ", "employmentStatus": "<PERSON><PERSON><PERSON> trạng công việc", "employerName": "<PERSON><PERSON>n công ty", "monthlyIncome": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> hàng tháng"}, "placeholders": {"enterFirstName": "<PERSON><PERSON><PERSON><PERSON> tên", "enterLastName": "<PERSON><PERSON><PERSON><PERSON>", "enterEmail": "<PERSON><PERSON><PERSON><PERSON> địa chỉ email", "enterPhone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "enterOccupation": "<PERSON><PERSON><PERSON><PERSON> ngh<PERSON> nghiệp", "selectUnit": "<PERSON><PERSON>n đơn vị", "selectStatus": "<PERSON><PERSON><PERSON> trạng thái", "enterNotes": "<PERSON><PERSON><PERSON><PERSON>hi <PERSON>ú", "firstName": "<PERSON><PERSON><PERSON><PERSON> tên", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "email": "Enter email address", "phone": "Enter phone number", "identificationType": "Select identification type", "identificationNumber": "Enter identification number", "employmentStatus": "Select employment status", "employerName": "Enter employer name", "monthlyIncome": "Enter monthly income", "emergencyContactName": "Enter emergency contact name", "emergencyContactPhone": "Enter emergency contact phone"}, "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "pending": "Chờ xử lý", "terminated": "<PERSON><PERSON> chấm d<PERSON>"}, "actions": {"save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "edit": "Chỉnh sửa", "delete": "Xóa", "view": "<PERSON>em chi tiết"}, "messages": {"saveSuccess": "<PERSON><PERSON><PERSON> ng<PERSON>i thuê thành công", "saveError": "Lỗi khi lưu người thuê", "deleteSuccess": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i thuê thành công", "deleteError": "Lỗi khi xóa người thuê", "deleteConfirm": "Bạn có chắc chắn muốn xóa người thuê này?", "required": "Trư<PERSON>ng này là bắ<PERSON> buộc", "createSuccess": "Tenant created successfully", "updateSuccess": "Tenant updated successfully", "createError": "Failed to create tenant", "updateError": "Failed to update tenant"}, "contracts": {"title": "<PERSON><PERSON><PERSON>", "contract": "<PERSON><PERSON><PERSON>", "contractId": "<PERSON><PERSON> hợp đồng", "startDate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "endDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "monthlyRent": "<PERSON><PERSON><PERSON><PERSON> thuê hàng tháng", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "viewContract": "<PERSON><PERSON> đ<PERSON>"}, "quickStats": "<PERSON><PERSON><PERSON><PERSON> kê nhanh", "activeContracts": "<PERSON><PERSON><PERSON> đồng hoạt động", "documents": "<PERSON><PERSON><PERSON> l<PERSON>", "stats": {"totalContracts": "<PERSON><PERSON>ng số hợp đồng", "activeContracts": "<PERSON><PERSON><PERSON> đồng đang hoạt động", "joinDate": "<PERSON><PERSON><PERSON> gia nh<PERSON>p"}, "employmentStatus": {"employed": "<PERSON><PERSON> vi<PERSON><PERSON> làm", "unemployed": "<PERSON><PERSON><PERSON><PERSON>", "self_employed": "Tự kinh doanh", "student": "Sin<PERSON> viên", "retired": "Retired"}, "identificationTypes": {"passport": "<PERSON><PERSON> ch<PERSON>", "national_id": "CMND/CCCD", "driver_license": "Bằng lái xe"}, "errors": {"notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy người thuê", "notFoundDescription": "<PERSON>ười thuê mà bạn đang tìm không tồn tại hoặc đã bị xóa."}, "deleteConfirmation": {"title": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> thuê", "description": "Bạn có chắc chắn muốn xóa người thuê này? Hành động này không thể hoàn tác.", "hasActiveContracts": "<PERSON><PERSON><PERSON>i thuê này có hợp đồng đang hoạt động. Bạn có chắc chắn muốn xóa?"}, "createTenant": "<PERSON><PERSON><PERSON>", "createDescription": "<PERSON><PERSON><PERSON><PERSON> thông tin chi tiết để tạo người thuê mới", "editDescription": "Update the tenant information"}, "maintenance": {"form": {"requestId": "<PERSON><PERSON> yêu c<PERSON>u", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "priority": "<PERSON><PERSON> <PERSON> tiên", "category": "<PERSON><PERSON>", "reportedDate": "<PERSON><PERSON><PERSON> b<PERSON>o c<PERSON>o", "description": "<PERSON><PERSON>", "estimatedCost": "Chi phí <PERSON><PERSON> t<PERSON>h", "contractorName": "<PERSON><PERSON><PERSON> nhà thầu", "contractorPhone": "SĐT nhà thầu", "notes": "<PERSON><PERSON><PERSON>", "actualCost": "<PERSON> phí thực tế"}, "details": {"overview": "<PERSON><PERSON><PERSON> quan", "timeline": "<PERSON><PERSON><PERSON> thời gian", "daysSinceReported": "<PERSON><PERSON><PERSON> kể từ khi báo cáo", "cost": "Chi phí", "related": "<PERSON><PERSON><PERSON> quan", "contractor": "<PERSON><PERSON><PERSON> thầu"}, "status": {"in_progress": "<PERSON><PERSON> ti<PERSON>n hành", "pending": "Chờ xử lý", "completed": "<PERSON><PERSON><PERSON> th<PERSON>", "cancelled": "<PERSON><PERSON> hủy", "open": "Mở"}, "priority": {"low": "<PERSON><PERSON><PERSON><PERSON>", "medium": "<PERSON>rung bình", "high": "<PERSON>", "urgent": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>p"}, "category": {"plumbing": "<PERSON><PERSON> thống n<PERSON>", "electrical": "<PERSON><PERSON><PERSON><PERSON>", "hvac": "<PERSON><PERSON><PERSON><PERSON> hòa thông gió", "structural": "<PERSON><PERSON><PERSON> c<PERSON>", "appliance": "<PERSON><PERSON><PERSON><PERSON> bị", "cosmetic": "<PERSON><PERSON><PERSON><PERSON> mỹ", "cleaning": "<PERSON><PERSON>", "security": "An ninh", "general": "<PERSON>", "other": "K<PERSON><PERSON><PERSON>"}, "messages": {"deleteSuccess": "<PERSON><PERSON><PERSON> y<PERSON>u cầu bảo trì thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa yêu cầu bảo trì", "statusUpdateSuccess": "<PERSON><PERSON><PERSON> nhật trạng thái thành công", "statusUpdateError": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật trạng thái"}, "errors": {"notFound": "<PERSON><PERSON><PERSON><PERSON>", "notFoundDescription": "<PERSON><PERSON><PERSON> cầu bảo trì bạn đang tìm không tồn tại hoặc đã bị xóa."}, "dialog": {"deleteTitle": "<PERSON><PERSON><PERSON>", "deleteDescription": "Bạn có chắc chắn muốn xóa yêu cầu bảo trì này? Hành động này không thể hoàn tác."}}, "shapes": {"rectangle": "<PERSON><PERSON><PERSON> chữ nhật", "circle": "<PERSON><PERSON><PERSON> tròn", "polygon": "<PERSON><PERSON>"}, "pages": {"editor": {"placeholder": "「/」키로 명령어 사용...", "characters": "문자", "words": "단어"}, "blogList": {"title": "블로그 목록", "headers": {"image": "이미지", "title": "제목", "category": "카테고리", "updatedAt": "업데이트 날짜", "actions": "작업"}, "filters": {"search": {"placeholder": "블로그 검색..."}, "category": "카테고리", "createdAt": "생성일", "updatedAt": "업데이트 날짜"}, "actions": {"import": "가져오기", "addBlog": "블로그 추가"}}, "checkout": {"title": "결제", "orderInformation": {"title": "주문 정보", "confirm": "확인", "cancel": "취소", "subscribeTo": "플랜 구독", "registrationTime": "등록 시간", "price": "가격", "creationDate": "생성일", "paymentMethod": "결제 방법", "issueInvoice": "청구서 발행", "companyName": "회사명", "taxId": "세금 번호", "address": "주소", "email": "회사 이메일", "vat": "부가세 (0%)", "total": "총 금액", "pay": "결제하기", "companyNamePlaceholder": "예: ABC 주식회사", "taxIdPlaceholder": "세금 번호 입력", "addressPlaceholder": "주소 입력", "emailPlaceholder": "예: <EMAIL>"}, "qrPayment": {"title": "QR 코드 송금", "accountName": "계좌 명의", "accountNumber": "계좌 번호", "bankName": "은행명", "amount": "금액", "content": "내용", "cancelOrder": "주문 취소", "success": "결제 성공!", "redirectingIn": "{{countdown}}초 후 등록 페이지로 이동합니다..."}}, "opportunities": {"noOrderYet": "주문이 없습니다", "clickTabToAddOrder": "탭을 클릭하여 주문 추가", "orders": "주문", "opportunities": "기회", "deleteSuccess": "기회가 성공적으로 삭제되었습니다", "deleteError": "기회를 삭제할 수 없습니다", "title": "기회", "add": "추가", "search": "검색...", "addOpportunity": "새 기회", "contact": "고객", "salesperson": "담당자", "expectedClosing": "예상 종료일", "tags": "태그", "phone": "전화번호", "enterContactName": "고객 이름 입력...", "enterEmailAddress": "이메일 주소 입력...", "enterTags": "태그 입력...", "enterPhoneNumber": "전화번호 입력...", "opportunityDetail": "기회 상세", "enterOpportunityTitle": "기회 이름 입력...", "enterProbability": "확률 입력...", "expectedRevenue": "예상 수익", "probability": "확률", "customerInfo": "고객 정보", "salesPerson": "영업 담당", "notes": "메모", "enterRevenue": "수익 입력...", "selectAssignee": "담당자 선택", "enterExpectedClosingDate": "예상 종료일 선택...", "status": {"won": "성공", "lost": "실패", "ongoing": "진행 중"}, "lostReason": {"title": "실패 사유", "description": "실패 사유를 선택하거나 직접 입력하세요.", "selectReason": "사유 선택", "selectPlaceholder": "사유 선택...", "searchPlaceholder": "사유 검색...", "noResults": "사유를 찾을 수 없습니다.", "customReason": "사용자 지정 사유", "customPlaceholder": "사유를 입력하세요...", "priceTooHigh": "가격이 너무 높음", "competitorWon": "경쟁사 승리", "noBudget": "예산 없음", "timingIssues": "타이밍 문제", "technicalRequirements": "기술 요구사항 불충족", "decisionMakerChanged": "의사결정자 변경", "projectCancelled": "프로젝트 취소", "poorFit": "니즈 불일치", "noLongerInterested": "관심 상실", "stopResponding": "응답 중단", "foundBetterAlternative": "더 나은 대안을 찾음", "productDidNotMeetExpectations": "제품이 기대에 미치지 못함"}}, "stages": {"egDiscuss": "예: 회의", "createOpportunity": "기회 생성", "opportunityNamePlaceholder": "예: 병원용 챗봇", "contactPlaceholder": "예: <PERSON><PERSON><PERSON><PERSON>", "editColumn": "열 편집", "columnName": "열 이름", "isWonStage": "성공 단계입니까?", "deleted": "열 삭제 성공", "added": "열 추가 성공", "failedToCreateStage": "열을 생성할 수 없습니다", "yesterday": "어제", "overdue": "기한 초과", "today": "오늘", "upcoming": "곧 있을 예정", "noDueDate": "기한 없음", "noActivity": "활동 없음", "inDays": "{{count}}일 후", "tomorrow": "내일", "daysAgo": "{{count}}일 전", "filters": {"createdAt": "생성일", "updatedAt": "업데이트 날짜", "outcome": "결과"}, "newColumn": "새 열", "newColumnPlaceholder": "열 이름을 입력하고 Enter 키를 누르세요", "unassigned": "미할당", "editActivity": "활동 편집", "fold": "접기", "edit": "편집", "delete": "삭제", "view": "보기", "addColumnAfter": "뒤에 열 추가", "addColumnBefore": "앞에 열 추가", "deleteColumn": "열 삭제", "search": "검색...", "title": "진행 관리", "add": "추가", "addOpportunity": "새 기회", "status": "상태", "opportunity": "기회", "contact": "고객", "phone": "전화번호", "expectedRevenue": "예상 수익", "priority": "우선순위", "assignee": "담당자", "selectAssignee": "담당자 선택", "noScheduledActivities": "예정된 활동이 없습니다", "noActivitiesForStatus": "{{status}} 상태에 활동이 없습니다", "scheduleActivity": "활동 예약", "activityType": "활동 유형", "selectActivityType": "유형 선택", "searchActivityTypes": "유형 검색...", "dueDate": "기한일", "summary": "개요", "notes": "메모", "typeSomething": "입력하세요...", "searchAssignee": "담당자 검색...", "noAssigneeFound": "담당자를 찾을 수 없습니다.", "pickADate": "날짜 선택", "schedule": "일정", "actionName": "작업 이름", "newActivityType": "새 활동 유형", "salesperson": "영업 담당", "expectedClosing": "예상 종료일", "tags": "태그", "headers": {"contact": "고객", "email": "이메일", "opportunity": "기회", "stage": "단계", "status": "상태", "assignee": "담당자", "createdAt": "생성일", "updatedAt": "업데이트 날짜", "customer": "고객", "amount": "금액", "closeDate": "종료일", "probability": "확률", "expectedClosingDate": "예상 종료일", "expectedRevenue": "예상 수익", "activities": "활동"}, "sort": {"title": "정렬", "order": "순서", "closingDate": "종료일", "dealValue": "거래 금액", "created": "생성일", "lastActivity": "마지막 활동", "winProbability": "성공 확률", "rating": "평가", "lowestToHighest": "오름차순", "highestToLowest": "내림차순"}}, "onexbotsDashboard": {"title": "OnexBots 대시보드", "filters": {"period": "기간", "daily": "일간", "weekly": "주간", "monthly": "월간", "yearly": "연간", "staff": "직원"}, "stats": {"conversations": "대화 수", "users": "사용자 수", "accuracyRate": "정확도", "averageResponseTime": "평균 응답 시간", "viewMore": "더 보기", "fromLastPeriod": "이전 기간 대비", "noComparisonData": "비교 데이터 없음"}, "accuracyRateChart": {"title": "정확도", "description": "지식 베이스와 비교한 응답의 정확성", "tooltip": {"rate": "정확도", "resolved": "해결됨", "total": "총 질문 수"}}, "responseTimeChart": {"title": "평균 응답 시간", "description": "전체 평균 응답 시간", "fastest": "가장 빠름", "slowest": "가장 느림", "tooltip": {"average": "평균", "min": "최소", "max": "최대"}}}, "onboarding": {"welcomeTo": "환영합니다", "skip": "건너뛰기", "done": "완료"}, "overview": {"title": "개요", "filters": {"period": "기간", "daily": "일간", "weekly": "주간", "monthly": "월간", "yearly": "연간", "selectLocation": "시설 선택", "refresh": "새로고침"}, "stats": {"totalFacilities": "총 시설 수", "totalPatients": "총 환자 수", "averageOccupancy": "평균 입주율", "totalRevenue": "총 수익", "viewMore": "더 보기", "fromLastMonth": "전월 대비"}, "patientStats": {"title": "환자 통계", "outpatient": "외래", "inpatient": "입원"}, "topTwenty": {"title": "상위 20", "icdDiagnoses": "ICD 진단", "prescribedMedications": "처방 약품"}, "costs": {"averageTreatmentCosts": "평균 치료비", "insurancePayments": "보험 지불", "insurance": "보험", "service": "서비스", "specialCare": "특별 케어"}, "treatmentOutcomes": {"title": "치료 결과", "recovered": "회복", "improved": "호전", "unchanged": "변화 없음", "deteriorated": "악화", "deceased": "사망", "left": "퇴원"}}, "customers": {"title": "고객 목록", "filters": {"search": {"placeholder": "이름으로 검색"}, "group": "그룹", "createdAt": "생성일", "updatedAt": "최종 업데이트"}, "name": "이름", "phone": "전화번호", "email": "이메일", "address": "주소", "group": "그룹", "createdAt": "생성일", "updatedAt": "업데이트일", "orderHistory": "주문 내역"}, "orders": {"print": "출력", "sendEmail": "이메일 전송", "serviceNameCannotBeEmpty": "서비스명을 입력하세요", "order": "주문", "printInvoice": "송장 출력", "printOrder": "주문 출력", "printReceipt": "영수증 출력", "productInformation": "상품 정보", "qty": "수량", "totalAmount": "총 금액", "notes": "메모", "hasBeenCreatedSuccessfully": "주문이 성공적으로 생성되었습니다!", "orderStatus": {"draft": "초안", "shipping": "배송중", "await_packing": "포장 대기", "delivered": "배송 완료", "pending": "대기", "confirmed": "확정", "cancelled": "취소", "completed": "완료", "partial": "부분 완료", "returned": "반품"}, "orderPaymentStatus": {"unpaid": "미지불", "pending": "대기", "paid": "지불 완료", "partiallyPaid": "부분 지불", "cancelled": "취소"}, "filters": {"shift": "근무 교대", "status": "주문 상태", "paymentStatus": "결제 상태", "createdAt": "생성일", "updatedAt": "업데이트일"}, "orderHistory": "주문 내역", "amount": "수량", "redeemPoints": "포인트 사용", "loyalPoints": "누적 포인트", "updatedAt": "업데이트일", "title": "주문 목록", "searchPriceGroup": "가격 그룹 검색", "noPriceGroupsFound": "가격 그룹을 찾을 수 없습니다", "searchBranch": "지점 검색", "noBranchFound": "지점을 찾을 수 없습니다", "emptyServiceName": "서비스명을 입력하세요", "updateOrder": "업데이트", "selectGender": "성별 선택", "tax": "세금", "shipping": "배송비", "addCustomer": "고객 추가", "save": "저장", "defaultShippingAddress": "기본 배송지", "defaultBillingAddress": "기본 청구지", "noAddressesFound": "주소를 찾을 수 없습니다", "edit": "수정", "removeRecipientInfo": "수령인 정보 삭제", "addRecipientInfo": "수령인 정보 추가", "enterName": "이름 입력", "enterAddress": "주소 입력", "enterPhoneNumber": "전화번호 입력", "enterCompanyName": "회사명 입력", "selectWard": "구/동 선택", "searchWard": "구/동 검색", "searchDistrict": "군/시 검색", "selectDistrict": "군/시 선택", "selectProvince": "도 선택", "searchProvince": "도 검색", "province": "도", "district": "군", "ward": "구/동", "addAddress": "주소 추가", "address": "주소", "noProvincesFound": "도를 찾을 수 없습니다", "noDistrictsFound": "군/시를 찾을 수 없습니다", "noWardsFound": "구/동을 찾을 수 없습니다", "shippingDefault": "기본 배송지", "billingDefault": "기본 청구지", "editCustomer": "고객 수정", "Name": "이름", "phoneNumber": "전화번호", "email": "이메일", "gender": "성별", "enterEmail": "이메일 입력", "birthday": "생일", "pickADate": "날짜 선택", "customerGroup": "고객 그룹", "selectCustomerGroup": "고객 그룹 선택", "companyName": "회사명", "addresses": "주소", "submit": "확인", "accumulatedPoints": "누적 포인트", "groupName": "그룹명", "placeholder": "이름, 코드, 바코드로 검색", "quantity": "수량", "price": "가격", "total": "합계", "noProductsFound": "상품을 찾을 수 없습니다", "addService": "서비스 추가 (F9)", "loadingMore": "더 불러오는 중...", "addProduct": "상품 추가", "available": "재고 있음", "onHand": "보유 재고", "note": "메모", "maximumAvailableQuantity": "최대 재고 수량", "branch": "지점", "loadingCustomerDetails": "고객 정보 불러오는 중...", "customer": "고객", "shippingAddress": "배송지", "billingAddress": "청구지", "noCustomersFound": "고객을 찾을 수 없습니다", "loading": "불러오는 중...", "searchCustomer": "고객 검색", "payment": "결제", "addPromotion": "프로모션 추가", "products": "상품", "subtotal": "소계", "discount": "할인", "voucher": "쿠폰 코드", "fees": "서비스 요금", "promotions": "프로모션", "notePlaceholder": "메모 입력", "tags": "태그", "tagsPlaceholder": "태그 입력", "noProductsInOrder": "주문에 상품이 포함되어 있지 않습니다.", "cancel": "취소", "addOrder": "주문 추가", "success": "주문이 성공적으로 생성되었습니다!", "error": "주문을 처리할 수 없습니다", "adjustPrice": "가격 조정", "adjustPriceSuccess": "가격이 성공적으로 조정되었습니다!", "adjustPriceError": "가격을 조정할 수 없습니다", "adjustPriceDescription": "선택한 상품의 가격 조정", "adjustPricePlaceholder": "새로운 가격 입력", "adjustPriceButton": "가격 조정", "adjustPriceCancel": "취소", "setNewPrice": "새 가격 설정", "value": "금액", "percent": "퍼센트", "addProductToOrderWarning": "주문에 상품을 추가하세요", "selectCustomer": "고객을 선택하세요", "addVoucher": "쿠폰 추가", "voucherCode": "쿠폰 코드", "voucherCodePlaceholder": "쿠폰 코드 입력", "voucherCodeButton": "쿠폰 추가", "voucherCodeSuccess": "쿠폰이 성공적으로 추가되었습니다", "voucherCodeError": "쿠폰을 추가할 수 없습니다", "confirm": "정말 실행하시겠습니까?", "confirmCancel": "확인", "confirmDelete": "삭제", "cancelWarning": "이 작업은 되돌릴 수 없습니다. 변경을 취소하시겠습니까?", "cancelDelete": "이 작업은 되돌릴 수 없습니다. 항목을 삭제하시겠습니까?"}, "variants": {"title": "변형", "filters": {"search": {"placeholder": "이름, 코드, 바코드로 검색"}}}, "products": {"products": {"title": "상품", "addBulk": {"title": "상품 일괄 추가", "notice": "주의:", "templateInstructions": "데이터 불일치를 방지하기 위해 템플릿에 따라 파일을 입력해주세요.", "simpleTemplate": "간단 템플릿 다운로드", "advancedTemplate": "고급 템플릿 다운로드", "here": "여기", "supportedFiles": "지원 파일 형식: .xlsx, .xls, .csv", "dragAndDrop": "파일을 이곳에 드래그 앤 드롭하거나", "uploadButton": "업로드", "fileUpload": "파일 업로드", "cancel": "취소", "import": "가져오기", "importing": "가져오는 중...", "selectFileError": "업로드할 파일을 선택해주세요", "importSuccess": "상품이 정상적으로 가져와졌습니다", "importError": "상품 가져오기 중 오류가 발생했습니다"}, "filters": {"search": {"placeholder": "이름, SKU, 바코드로 검색", "placeholderBrand": "브랜드 검색..."}, "product": "상품", "source": "소스", "category": "카테고리", "brand": "브랜드", "createdAt": "생성일", "updatedAt": "수정일", "otherFilters": {"title": "기타 필터", "all": "전체", "description": "처음 두 개의 필터가 메인 페이지에 우선 표시됩니다. 필요에 따라 맞춤 설정하세요."}, "dateOptions": {"allTime": "전체 기간", "today": "오늘", "yesterday": "어제", "lastWeek": "지난주", "thisWeek": "이번 주", "lastMonth": "지난달", "thisMonth": "이번 달", "customize": "사용자 지정"}, "deletionFailed": "변형 삭제에 실패했습니다", "deletedSuccessfully": "변형이 삭제되었습니다"}, "headers": {"productInfo": "상품 정보", "category": "카테고리", "brand": "브랜드", "updatedAt": "수정일시", "createdAt": "생성일", "available": "재고 수량", "variant": "변형"}, "actions": {"addProduct": "상품 추가", "addManual": {"title": "상품 추가", "onThisPage": "목차", "sections": {"basicInfo": "기본 정보", "options": "상품 옵션", "units": "포장 단위", "prices": "가격 정보", "measurements": "치수"}}, "addQuick": "빠른 추가", "addBulk": "일괄 추가", "refresh": "새로고침", "saveFilters": "필터 저장", "reset": "초기화", "filter": "필터"}}, "deletionFailed": "변형 삭제에 실패했습니다", "deletedSuccessfully": "변형이 삭제되었습니다", "descriptionDeleteOption": "이 작업은 되돌릴 수 없습니다. 포장 단위, 가격, 치수에 관한 데이터가 완전히 삭제됩니다.", "descriptionDeleteValueOption": "이 작업은 되돌릴 수 없습니다. 가격과 치수에 관한 데이터가 완전히 삭제됩니다.", "name": "이름", "sku": "SKU 코드", "barcode": "바코드", "option1": "옵션 1", "option2": "옵션 2", "option3": "옵션 3", "unit": "단위", "weight": "무게", "height": "높이", "width": "너비", "length": "길이", "variantDetails": "변형 상세", "variants": "변형", "source": "소스", "category": "카테고리", "brand": "브랜드", "createdAt": "생성일", "description": "설명", "viewLess": "간략히 보기", "viewMore": "더 보기", "noPricesAvailable": "가격 정보가 없습니다", "prices": "가격", "tags": "태그", "inventory": {"noMatchResult": "일치하는 결과가 없습니다", "title": "재고", "branch": "지점", "history": "이력", "allBranches": "전체 지점", "inventory": "재고", "packing": "포장", "shipping": "배송", "minValue": "최소값", "maxValue": "최대값", "staff": "직원", "transactionType": "거래 유형", "change": "변경", "quantity": "수량", "reference": "참조", "available": "사용 가능", "incoming": "입고 예정", "onHand": "현재 재고"}, "addManual": {"title": "상품 수동 추가", "onThisPage": "목차", "publish": "공개", "sections": {"addVariant": "사이즈나 색상 등 여러 선택지가 있는 경우 변형을 생성하세요.", "variant": "변형", "all": "전체", "apply": "적용", "variantPlaceholder": "변형 선택", "usedByAllVariants": "모든 변형에 사용", "usedByThis": "이 변형에 사용", "unitPlaceholder": "단위 선택", "unitSearchPlaceholder": "단위 검색", "variantSearchPlaceholder": "변형 검색", "unitEmptyText": "단위를 찾을 수 없습니다", "addType": "유형 추가", "addUnit": "단위 추가", "basicInfo": "기본 정보", "options": "상품 옵션", "option": "옵션", "units": "포장 단위", "prices": "가격 정보", "measurements": "치수", "selectImages": "이미지 선택", "submit": "제출", "cancel": "취소", "add": "추가", "edit": "수정", "save": "저장", "stay": "이 페이지에 머무르기", "leave": "나가기", "values": "값", "valuesPlaceholder": "값1, 값2, 값3", "optionsPlaceholder": "옵션 이름 입력", "addOption": "옵션 추가", "optionName": "옵션 이름", "addValue": "값 추가", "remove": "삭제", "valuesPlaceholderInput": "값 입력", "duplicateValue": "이미 존재하는 값입니다", "createVariant": "사이즈나 색상 등 여러 선택지가 있는 경우 변형을 생성하세요."}, "basicInfo": {"brandPlaceholder": "브랜드 선택", "brandSearchPlaceholder": "브랜드 검색", "brandEmptyText": "브랜드를 찾을 수 없습니다", "categoryPlaceholder": "카테고리 선택", "categorySearchPlaceholder": "카테고리 검색", "categoryEmptyText": "카테고리를 찾을 수 없습니다", "tagsPlaceholder": "태그 입력", "images": "이미지", "name": "이름", "description": "설명", "shortDescription": "짧은 설명", "brand": "브랜드", "category": "카테고리", "sku": "SKU 코드", "tags": "태그", "price": "가격", "uploadImage": "이미지 업로드", "optimize": "최적화", "required": "이 항목은 필수입니다", "imageRequired": "최소 1장의 이미지가 필요합니다", "nameRequired": "상품명은 필수입니다", "nameWarning": "노출 효과를 높이기 위해 상품명을 입력해주세요", "descriptionWarning": "콘텐츠 최적화를 위해 설명을 입력해주세요", "skuRequired": "SKU 코드는 필수입니다", "priceRequired": "가격은 필수입니다"}, "options": {"addOption": "옵션 추가", "optionName": "옵션 이름", "values": "값", "addValue": "값 추가", "remove": "삭제"}, "units": {"title": "포장 단위", "addUnit": "단위 추가", "unitName": "단위명", "ratio": "비율", "remove": "삭제"}, "prices": {"title": "가격 정보", "addGroup": "새 가격 그룹 추가", "groupName": "그룹명", "price": "가격", "apply": "적용", "applyAll": "전체 적용"}, "measurements": {"weight": "무게", "height": "높이", "width": "너비", "length": "길이", "apply": "적용", "applyAll": "전체 적용"}, "buttons": {"cancel": "취소", "add": "추가", "edit": "수정", "save": "저장", "stay": "이 페이지에 머무르기", "leave": "나가기"}, "dialogs": {"leaveTitle": "정말 나가시겠습니까?", "leaveDesc": "저장되지 않은 변경 사항은 사라집니다."}, "validation": {"hasErrors": "검증 오류", "checkFields": "모든 필수 항목을 확인하고 다시 시도해주세요"}, "success": "상품이 생성되었습니다", "successUpdate": "상품이 업데이트되었습니다", "successDescription": "상품이 정상적으로 생성되었습니다", "successDescriptionUpdate": "상품이 정상적으로 업데이트되었습니다", "error": "오류", "errorDescription": "상품을 생성하지 못했습니다. 다시 시도해주세요.", "errorDescriptionUpdate": "상품을 업데이트하지 못했습니다. 다시 시도해주세요."}}, "choosePlan": {"forIndividuals": "개인용", "forCompanies": "기업용", "monthly": "월간", "annually": "연간", "chooseAPlan": "플랜 선택", "planDescription": {"firstSection": "비즈니스 요구에 가장 적합한 플랜을 선택하세요.", "middleSection": "나중에 업그레이드나 다운그레이드가 가능합니다.", "secondSection": "모든 플랜에는 기본 기능이 포함되어 있습니다."}, "planData": {"Up to 50 variants": "최대 50개 변형 개요 통계", "Real-time inventory syncing": "실시간 재고 동기화", "Ideal for startups (1,000 items)": "스타트업에 적합 (1,000개 상품까지)", "Analytics dashboard": "분석 대시보드", "User-friendly interface": "사용자 친화적인 인터페이스", "Support for multiple currencies and languages": "다중 통화 및 언어 지원", "Real time inventory": "실시간 재고 관리"}, "planNames": {"Free": "무료", "Starter": "스타터", "Pro": "프로", "Agency": "에이전시"}, "mostPopular": "인기", "numberIntegrations": "통합 개수", "explainNoIntegrations": "통합이 없습니다", "getStarted": "시작하기"}, "synchronization": {"platforms": {"source": "소스", "destination": "대상"}, "title": {"success": "{{source}}를 {{destination}}에 연결했습니다", "error": "동기화 설정 오류"}, "description": "관심사와 타겟에 맞는 분야를 선택하세요.", "error": {"missingConnection": "연결을 찾을 수 없습니다", "connectionError": "연결 오류", "sourceNotFound": "소스를 찾을 수 없습니다", "destinationNotFound": "대상을 찾을 수 없습니다"}, "success": {"completeTitle": "연결 완료!", "gotoDashboard": "대시보드로 이동"}, "syncSetting": {"title": "동기화 설정", "product": {"title": "상품", "description": "{{source}}에서 {{destination}}으로 상품 동기화"}, "inventory": {"title": "재고", "description": "재고 수량을 지속적으로 동기화"}, "order": {"title": "주문", "description": "{{destination}}의 주문을 {{source}}로 가져오기"}, "buttonTitle": "{{destination}}에 연결"}}, "syncRecords": {"title": "동기화 목록", "filters": {"search": {"placeholder": "반품 주문 검색..."}, "status": "상태", "recordType": "레코드 유형", "channel": "채널", "connectionId": "연결 ID", "fetchEventId": "이벤트 가져오기 ID"}, "columns": {"channel": "채널", "header": "레코드 유형", "fetchEventId": "이벤트 가져오기 ID", "connectionId": "연결 ID", "lastUpdated": "마지막 업데이트", "fetchedAt": "가져온 시각", "finishedAt": "완료 시각", "publishedAt": "게시 시각", "transformedAt": "변환 시각"}}, "fetchEvents": {"title": "Fetch 이벤트", "filters": {"search": {"placeholder": "Fetch 이벤트 검색..."}, "status": "상태", "actionType": "작업 유형", "actionGroup": "작업 그룹", "eventTime": "이벤트 시각", "eventSource": "이벤트 소스", "fetchEventId": "Fetch 이벤트 ID"}, "columns": {"channel": "채널", "header": "레코드 유형", "fetchEventId": "Fetch 이벤트 ID", "connectionId": "연결 ID", "lastUpdated": "마지막 업데이트", "fetchedAt": "가져온 시각", "finishedAt": "완료 시각", "publishedAt": "게시 시각", "transformedAt": "변환 시각"}, "headers": {"channel": "채널", "actionType": "작업 유형", "actionGroup": "작업 그룹", "eventSource": "이벤트 소스", "eventTime": "이벤트 시각", "status": "상태", "actions": "작업"}}, "fetchEventDetail": {"title": "{{source}}의 Fetch 이벤트 상세", "actionGroup": "액션 그룹", "connectionId": "연결 ID", "actionType": "액션 타입", "eventSource": "이벤트 소스", "retryCount": "재시도 횟수", "status": "상태", "continuationToken": "연속 토큰", "objectId": "오브젝트 ID", "eventTime": "이벤트 시간", "createdAt": "생성일", "updatedAt": "업데이트 시간", "eventNumber": "{{number}}. ID 없음"}, "channel": {"title": "연결 목록", "filters": {"search": {"placeholder": "연결 검색..."}, "status": "상태"}, "headers": {"channel": "채널", "status": "상태", "url": "URL", "createdAt": "생성일", "lastUpdated": "마지막 업데이트", "actions": "작업"}, "actions": {"install": "새 채널 설치", "configure": "설정", "activate": "활성화", "deactivate": "비활성화"}}, "supportedChannels": {"title": "채널 목록", "filters": {"search": {"placeholder": "채널 검색..."}}}, "installChannel": {"title": "채널 설치"}, "settings": {"passwordsDontMatch": "비밀번호가 일치하지 않습니다", "eachRoleAssignmentMustHaveABranchAndAtLeastOneRoleSelected": "각 역할 할당에는 지점과 최소 하나의 역할이 필요합니다", "cannotMixAllBranchesWithIndividualBranchSelections": "모든 지점과 개별 지점 선택을 혼합할 수 없습니다", "emailInvalid": "이메일 형식이 올바르지 않습니다", "emailRequired": "이메일은 필수입니다", "passwordRequired": "비밀번호는 필수입니다", "confirmPasswordRequired": "비밀번호 확인은 필수입니다", "employeeNameRequired": "직원 이름은 필수입니다", "usernameMinLength": "사용자 이름은 최소 3자 이상이어야 합니다", "usernameInvalid": "사용자 이름은 영문자, 숫자, 밑줄만 포함할 수 있습니다", "branchRequired": "지점은 필수입니다", "atLeastOneRoleRequired": "최소 하나의 역할이 필수입니다", "atLeastOneRoleAssignmentRequired": "최소 하나의 역할 할당이 필수입니다", "employeeAccountAlreadyExists": "사용자 계정이 이미 존재합니다", "employeeAccountExpired": "사용자 계정이 만료되었습니다. RESEND 작업을 통해 사용자 계정을 재설정하세요", "employeeAccountDoesNotExist": "사용자 계정이 존재하지 않습니다", "accessDenied": "접근이 거절되었습니다", "employeeUpdatedSuccess": "직원이 성공적으로 업데이트되었습니다", "employeeCreatedSuccess": "직원이 성공적으로 생성되었습니다", "employeeDeletedSuccess": "직원이 성공적으로 삭제되었습니다", "employeeDeletedError": "직원 삭제에 실패했습니다", "employeeUpdatedError": "직원 업데이트에 실패했습니다", "employeeCreatedError": "직원 생성에 실패했습니다", "emailAlreadyInUse": "이메일이 이미 존재합니다", "phoneNumberAlreadyInUse": "전화번호가 이미 존재합니다", "phone": "전화번호", "birthday": "생일", "address": "주소", "shopInfo": "스토어 정보", "profileSettings": "프로필 설정", "profileSettingsDescription": "프로필 이미지, 이름, 비밀번호", "storeInformation": "스토어 정보", "storeInformationDescription": "연락처 정보, URL", "appearance": "외관", "appearanceDescription": "로고, 색상, 테마", "languageCurrency": "언어 및 통화", "languageCurrencyDescription": "언어 설정, 통화 지원", "employeesPermissions": "직원 및 권한", "employeesPermissionsDescription": "역할 할당, 역할 관리", "themeSetting": "테마 설정", "saveSuccess": "색상이 저장되었습니다", "saveError": "색상을 저장할 수 없습니다", "colorSetting": "색상 설정", "lightMode": "라이트 모드", "darkMode": "다크 모드", "logoSetting": "로고 설정", "language": {"language": "언어", "addLanguage": "언어 추가", "remove": "삭제", "addCurrency": "통화 추가", "update": "업데이트", "currency": "통화", "confirmRemoveTitle": "정말 삭제하시겠습니까?", "confirmRemoveDescription": "이 작업은 취소할 수 없습니다. 이 언어는 시스템에서 영구적으로 삭제됩니다."}, "theme": {"title": "테마", "description": "앱의 테마를 커스터마이즈합니다.", "lightMode": "라이트 모드", "darkMode": "다크 모드"}, "logo": {"title": "로고", "lightTheme": "(라이트 모드)", "darkTheme": "(다크 모드)", "description": "스토어에 표시될 로고를 커스터마이즈합니다.", "lightModeLogo": "라이트 모드 로고", "darkModeLogo": "다크 모드 로고", "lightModeIcon": "라이트 모드 아이콘", "darkModeIcon": "다크 모드 아이콘", "favicon": "파비콘", "lightModeLogoDescription": "라이트 모드 로고 업로드 (권장 크기: 180x40px)", "darkModeLogoDescription": "다크 모드 로고 업로드 (권장 크기: 180x40px)", "lightModeIconDescription": "라이트 모드 아이콘 업로드 (권장 크기: 40x40px)", "darkModeIconDescription": "다크 모드 아이콘 업로드 (권장 크기: 40x40px)", "faviconDescription": "웹사이트용 파비콘 업로드 (권장 크기: 32x32px)", "saveSuccess": "로고가 저장되었습니다", "saveError": "로고를 저장할 수 없습니다", "noChangesToSave": "저장할 변경 사항이 없습니다", "resetSuccess": "로고가 초기화되었습니다", "resetError": "로고를 초기화할 수 없습니다"}, "colors": {"title": "테마 색상", "description": "앱의 테마 색상을 커스터마이즈합니다.", "brandColor": "브랜드 색상", "lightMode": "(라이트 모드)", "darkMode": "(다크 모드)"}, "color": {"saveSuccess": "테마 색상이 저장되었습니다", "saveError": "테마 색상을 저장할 수 없습니다", "resetSuccess": "테마 색상이 초기화되었습니다", "resetError": "테마 색상을 초기화할 수 없습니다"}}, "profile": {"title": "프로필", "contactInformation": "연락처 정보", "contactInformationDescription": "고객이 귀하에게 연락할 수 있는 정보입니다", "changePassword": "비밀번호 변경", "aspectRatio": "화면 비율", "formats": "포맷", "name": "이름", "username": "사용자 이름", "email": "이메일", "phone": "전화번호", "update": "업데이트", "avatar": "프로필 이미지", "changePasswordDiaglog": {"title": "비밀번호 변경", "oldPassword": "현재 비밀번호", "newPassword": "새 비밀번호", "confirmPassword": "비밀번호 확인", "enterOldPassword": "현재 비밀번호 입력", "enterNewPassword": "새 비밀번호 입력", "enterConfirmPassword": "확인용 비밀번호 입력", "passwordRequirements": "비밀번호는 8자 이상이어야 합니다", "passwordChangedSuccessfully": "비밀번호가 성공적으로 변경되었습니다", "passwordChangeFailed": "비밀번호를 변경할 수 없습니다", "passwordsDoNotMatch": "비밀번호가 일치하지 않습니다", "passwordTooShort": "비밀번호는 8자 이상이어야 합니다", "forgotPassword": "비밀번호를 잊으셨나요?", "allFieldsRequired": "모든 항목은 필수입니다", "currentPasswordRequired": "현재 비밀번호는 필수입니다", "confirmPasswordRequired": "비밀번호 확인은 필수입니다", "passwordComplexity": "비밀번호는 최소 하나의 소문자, 하나의 대문자, 하나의 숫자를 포함해야 합니다", "passwordAttemptsExceeded": "비밀번호 시도 횟수가 초과되었습니다", "passwordDoesNotMatch": "비밀번호가 올바르지 않습니다"}, "forgotPasswordDialog": {"emailRequired": "이메일 주소를 입력하세요", "codeRequired": "인증 코드를 입력하세요", "codeSentSuccessfully": "인증 코드가 성공적으로 전송되었습니다", "codeSendFailed": "인증 코드를 전송할 수 없습니다", "codeVerifiedSuccessfully": "인증 코드가 성공적으로 확인되었습니다", "codeVerificationFailed": "인증 코드 확인에 실패했습니다", "codeResentSuccessfully": "인증 코드가 재전송되었습니다", "codeResendFailed": "인증 코드를 재전송할 수 없습니다", "passwordChangedSuccessfully": "비밀번호가 성공적으로 변경되었습니다", "passwordChangeFailed": "비밀번호를 변경할 수 없습니다", "passwordsDoNotMatch": "비밀번호가 일치하지 않습니다", "passwordTooShort": "비밀번호는 8자 이상이어야 합니다", "allFieldsRequired": "모든 항목은 필수입니다", "emailStep": {"title": "인증 코드 전송", "description": "아래에 이메일 주소를 입력하고 '코드 전송'을 클릭하여 비밀번호 재설정 코드를 받으세요.", "emailLabel": "이메일 주소", "emailPlaceholder": "이메일 주소 입력", "sending": "전송 중...", "sendCode": "코드 전송"}, "verificationStep": {"title": "인증 코드 입력", "description": "{{email}}로 전송된 인증 코드를 입력하세요", "codeLabel": "인증 코드", "codePlaceholder": "코드", "yourEmail": "귀하의 이메일", "resend": "재전송", "verifying": "확인 중...", "continue": "계속"}, "resetPasswordStep": {"title": "비밀번호 변경", "description": "새 비밀번호를 입력하여 변경하세요", "newPasswordLabel": "새 비밀번호", "newPasswordPlaceholder": "새 비밀번호 입력", "confirmPasswordLabel": "비밀번호 확인", "confirmPasswordPlaceholder": "확인용 비밀번호 입력", "changing": "변경 중...", "continue": "계속"}, "successStep": {"title": "비밀번호가 변경되었습니다", "description": "새 비밀번호로 로그인할 수 있습니다.", "loginButton": "로그인"}}}, "storeInformation": {"success": "매장 정보가 성공적으로 업데이트되었습니다", "error": "매장 정보를 업데이트할 수 없습니다", "title": "매장 정보", "contactInformation": "연락처 정보", "contactInformationDescription": "연락처 정보를 관리합니다", "changePassword": "비밀번호 변경", "aspectRatio": "화면 비율", "formats": "형식", "name": "이름", "username": "사용자 이름", "email": "이메일", "phone": "전화번호", "update": "업데이트", "storeInformationDescription": "매장의 연락처 정보와 설정을 관리합니다"}, "languageCurrency": {"title": "언어 및 통화", "description": "앱의 언어와 통화 설정을 관리합니다", "language": "언어", "currency": "통화", "addLanguage": "언어 추가", "addCurrency": "통화 추가", "remove": "삭제", "update": "업데이트", "back": "뒤로", "languageDescription": "지원할 언어를 선택하세요", "currencyDescription": "지원할 통화를 선택하세요", "updating": "업데이트 중...", "confirmRemoveTitle": "정말 삭제하시겠습니까?", "confirmRemoveDescription": "이 작업은 취소할 수 없습니다. 이 언어는 시스템에서 영구적으로 삭제됩니다.", "currencySettingsUpdatedSuccessfully": "통화 설정이 업데이트되었습니다", "currencySettingsUpdatedFailed": "통화 설정 업데이트에 실패했습니다"}, "productMappingList": {"syncSuccess": "상품 동기화 요청이 정상적으로 접수되었습니다", "syncFail": "상품 연결 요청을 받는 데 실패했습니다", "noConnection": "연결이 없습니다", "title": "상품 연결", "description": "Shopify에서 TikTok Shop으로의 상품 연결을 관리합니다", "filters": {"search": {"placeholder": "검색"}, "status": {"all": "모든 상품", "synced": "동기화됨", "mapped": "연결됨", "unMapped": "미연결", "errors": "오류"}}, "alert": {"title": "상품을 동기화하시겠습니까?", "description": "모든 상품을 두 플랫폼 간에 동기화하려고 합니다. 이 작업에는 시간이 소요될 수 있습니다.", "note": "주의:", "noteDescription": "일부 상품만 동기화하려면 계속하기 전에 목록에서 선택하세요.", "confirm": "동기화", "cancel": "취소", "areYouSure": "정말로 계속하시겠습니까?", "unmapSuccess": "상품 연결이 해제되었습니다", "unmapFail": "상품 연결 해제에 실패했습니다"}, "groupButton": {"settingButton": "설정", "syncButton": "상품 동기화"}, "status": {"synced": "동기화됨", "mapped": "연결됨", "unmapped": "미연결", "error": "오류"}, "actions": {"unmap": "연결 해제", "map": "연결", "fix": "속성 수정"}, "headers": {"product": "상품 {{product}}", "price": "가격", "last_synced": "마지막 동기화", "status": "상태", "actions": "작업"}, "nomap": "{{destination}}에 연결되지 않음"}, "productMapping": {"advancedMapping": {"title": "고급 연결 설정", "description": "상품 데이터 연결에 대한 고급 규칙을 설정합니다.", "sourceField": "소스 필드", "transformationType": "변환 유형", "addTransformation": "변환 추가", "removeTransformation": "변환 삭제", "ruleConfiguration": "규칙 설정", "outputPreview": "출력 미리보기", "finalOutput": "최종 출력", "applyTransformations": "변환 적용", "transformationChain": "변환 체인", "sampleData": "샘플 데이터", "preview": "미리보기", "output": "출력", "singleValue": "단일 값", "transformationForm": "변환", "exampleUsage": "사용 예시", "selectFieldsPlaceholder": "필드를 선택하세요", "searchFieldsPlaceholder": "필드를 검색...", "source": "소스", "searchTransformationTypes": "변환 유형 검색...", "selectTransformationTypes": "변환 유형 선택..."}, "lastSynced": "마지막 동기화", "errorLoading": "상품 동기화 세부정보 로드 오류", "manualRetry": "수동 재시도", "cancelledMessage": "상품 동기화가 취소되었습니다", "mappingStatus": "동기화 상태"}, "staff": {"name": "이름", "phoneNumber": "전화번호", "howCanICallYou": "당신의 이름은 무엇입니까?", "enterYourPhoneNumber": "전화번호를 입력해주세요", "title": "직원 목록", "filters": {"department": "부서", "role": "역할", "search": {"placeholder": "직원 검색..."}}, "actionButton": {"create": "직원 생성"}, "columns": {"staff": "직원", "role": "역할", "skills": "기술", "task": "작업", "conversations": "대화", "actions": "작업"}, "actions": {"view": "보기", "edit": "편집", "delete": "삭제"}, "maxCharactersReached": "최대 글자 수에 도달했습니다", "online": "온라인", "noStaff": "직원이 없습니다.", "loading": "로딩 중...", "interact": "상호작용", "createStaff": "직원 생성", "staffName": "직원 이름", "enterStaffName": "직원 이름 입력", "staffNameRequired": "직원 이름을 입력해주세요", "maxCharacters": "최대 250자", "selectDepartment": "부서를 선택", "searchDepartments": "부서 검색...", "selectRole": "역할 선택", "searchRoles": "역할 검색...", "creating": "생성 중", "update": "업데이트", "role": "역할", "department": "부서", "expertise": "전문 분야", "knowledgeWarning": "현재 지식은 정확한 답변을 제공하기에 충분하지 않습니다. 성능 향상을 위해 세부 정보를 추가해주세요.", "score": "점수", "avatar": {"title": "아바타", "xbotAvatar": "XBot 아바타", "image": "이미지", "selectedAvatar": "선택된 아바타", "avatar": "아바타"}, "knowledge": {"tab": "지식", "baby": "초보자", "warning": "현재 지식은 정확한 답변을 제공하기에 충분하지 않습니다. 성능 향상을 위해 세부 정보를 추가해주세요."}, "interactionStyle": {"tab": "상호작용 스타일", "description": "직원이 고객과 상호작용하는 방법을 설정합니다", "communicationTone": "커뮤니케이션 톤", "languagePreferences": "언어 설정", "responseLength": "응답 길이", "personalityTraits": "성격 특성", "temper": "감정", "formal": "격식", "casual": "비격식", "detailed": "상세", "concise": "간결", "creative": "창의적", "analytical": "분석적", "ethicalConstraints": "윤리적 제약", "contentFiltering": "콘텐츠 필터링 활성화", "instruction": "지시", "instructionPlaceholder": "이 직원에 대한 사용자 정의 지시를 입력해주세요 (선택 사항)", "greeting": "인사말", "greetingPlaceholder": "안녕하세요! OneXBots의 가상 직원입니다. 언제든지 편하게 문의해주세요!", "welcomeMessage": "안녕하세요! 저는 {{botName}}입니다. 오늘 어떻게 도와드릴까요?"}, "skills": {"tab": "데이터 접근 설정", "description": "직원이 접근할 수 있는 데이터를 설정합니다", "products": "상품", "orders": "주문", "inventory": "재고"}, "staffInfo": {"tab": "직원 정보", "description": "직원의 기본 정보를 관리합니다"}, "task": {"tab": "작업", "description": "직원의 작업과 책임을 관리합니다", "noTasks": "작업이 없습니다"}, "editStaff": {"validation": {"nameRequired": "이름은 필수입니다", "roleRequired": "역할은 필수입니다", "departmentRequired": "부서는 필수입니다", "greetingMaxLength": "인사말은 100자 이내여야 합니다"}, "tabs": {"staffInfo": "직원 정보", "interactionStyle": "상호작용 스타일", "knowledge": "지식", "skills": "기술", "task": "작업"}, "staffInfo": "직원 정보", "interactionStyle": "상호작용 스타일", "knowledge": "지식", "skills": "기술", "task": "작업", "integration": "통합", "embedCodeInstructions": "가상 직원 위젯을 웹페이지에 임베드하는 방법", "embedCodeTitle": "가상 직원 위젯 임베드", "title": "직원 편집", "staffName": "직원 이름", "rolePurpose": "역할/목적", "department": "부서", "domainExpertise": "전문 분야", "customExpertisePlaceholder": "전문 분야를 입력하고 Enter 키를 누르세요", "noRoleFound": "역할을 찾을 수 없습니다", "noDepartmentFound": "부서를 찾을 수 없습니다", "selectRole": "역할 선택", "selectDepartment": "부서 선택", "searchRoles": "역할 검색...", "searchDepartments": "부서 검색...", "namePhoneRequirement": "이름과 전화번호 입력이 필요합니다", "roles": {"contentWriter": "콘텐츠 작성자", "seoSpecialist": "SEO 전문가", "socialMediaManager": "소셜 미디어 매니저", "marketingSpecialist": "마케팅 전문가"}, "embedVirtualStaffWidget": "가상 직원 위젯 임베드", "themesColor": "테마 색상", "embedCode": "임베드 코드", "greeting": "OneXBots 인사말", "departments": {"engineering": "엔지니어링", "marketing": "마케팅", "sales": "영업", "support": "지원"}, "expertise": {"contentWriting": "콘텐츠 작성", "customerSupport": "고객 지원", "dataAnalysis": "데이터 분석", "emailMarketing": "이메일 마케팅", "graphicDesign": "그래픽 디자인", "projectManagement": "프로젝트 관리", "seo": "SEO", "socialMedia": "소셜 미디어 관리"}}, "embed": {"instructions": {"title": "임베드 지침", "step1Title": "1단계: 스크립트 추가", "step1Description": "스크립트 태그를 복사하여 웹페이지의 <head> 태그 안이나 </body> 태그 바로 앞에 붙여넣습니다.", "step2Title": "2단계: 컨테이너 위젯 추가", "step2Description": "div 요소를 복사하여 가상 직원 위젯을 표시할 위치에 붙여넣습니다. 위젯은 자동으로 해당 위치에서 초기화됩니다.", "step3Title": "3단계: 위젯 사용자 정의 (선택 사항)", "step3Description": "웹페이지에 CSS를 추가하여 위젯 인터페이스를 사용자 정의할 수 있습니다. 컨테이너 위젯의 ID는 xbot-container입니다.", "step4Title": "4단계: 통합 확인", "step4Description": "스크립트를 추가한 후 페이지를 새로고침하여 가상 직원 위젯이 정확히 표시되는지 확인합니다. 위젯은 직원 정보를 표시하고 방문자가 상호작용할 수 있어야 합니다.", "troubleshootingTitle": "문제 해결", "troubleshooting1": "스크립트 URL이 웹페이지에서 접근 가능한지 확인하세요.", "troubleshooting2": "브라우저 개발자 도구를 사용하여 오류 메시지를 확인하세요.", "troubleshooting3": "ID와 직원 이름이 정확한지 확인하세요.", "troubleshooting4": "웹페이지가 외부 스크립트 로드를 허용하는지 확인하세요."}, "script": {"title": "임베드 코드", "copy": "복사", "copied": "복사 완료!", "containerInstructions": "1. 위젯을 표시할 위치에 컨테이너 추가", "scriptInstructions": "2. 위젯을 표시할 위치에 스크립트 추가"}}, "promptExperiment": {"title": "프롬프트 테스트", "datasetName": "데이터셋 이름", "datasetNamePlaceholder": "데이터셋 이름 입력", "datasetNameRequired": "데이터셋 이름은 필수입니다", "runName": "실행 이름", "runNamePlaceholder": "실행 이름 입력", "runNameRequired": "실행 이름은 필수입니다", "cancel": "취소", "runExperiment": "실험 실행", "success": "프롬프트 테스트가 성공적으로 실행되었습니다", "error": "프롬프트 테스트를 실행할 수 없습니다"}}, "department": {"subscriptionNotFound": "부서를 생성하기 위한 구독이 없습니다", "deleteDepartmentTitle": "정말 삭제하시겠습니까?", "deleteDepartmentDescription": "부서를 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.", "updateDepartmentSuccess": "부서가 성공적으로 업데이트되었습니다", "updateDepartmentError": "부서를 업데이트할 수 없습니다", "deleteDepartmentSuccess": "부서가 성공적으로 삭제되었습니다", "deleteDepartmentError": "부서를 삭제할 수 없습니다", "createDepartmentSuccess": "부서가 성공적으로 생성되었습니다", "departmentNameAlreadyExists": "부서명이 이미 존재합니다", "createDepartmentFail": "부서를 생성할 수 없습니다.", "title": "부서", "editDepartment": "부서 편집", "createDepartment": "부서 생성", "createStaff": "직원 생성", "departmentName": "부서명", "enterDepartmentName": "부서명을 입력하세요", "description": "설명", "enterDescription": "설명을 입력하세요...", "departmentNameRequired": "부서명을 입력해주세요", "viewStaff": "직원 보기", "staffCount": "{{count}} 명의 직원", "additionalStaff": "{{count}} 명의 직원", "interact": "상호작용", "upload": "업로드", "deleteKnowledge": "지식 삭제", "deleteKnowledgeDescription": "지식을 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.", "deleteKnowledgeSuccess": "지식이 성공적으로 삭제되었습니다", "deleteKnowledgeError": "지식을 삭제할 수 없습니다", "deleteKnowledgeConfirm": "삭제", "deleteKnowledgeCancel": "취소", "knowledgeWarning": "현재 지식은 정확한 답변을 제공하기에 충분하지 않습니다. 성능 향상을 위해 세부 정보를 추가해주세요.", "knowledge": {"tab": "지식", "baby": "초보자", "warning": "현재 지식은 정확한 답변을 제공하기에 충분하지 않습니다. 성능 향상을 위해 세부 정보를 추가해주세요.", "status": {"error": "오류", "pending": "처리 중", "success": "성공"}}}, "knowledge": {"title": "지식", "headers": {"file": "파일", "status": "상태", "size": "크기", "updatedAt": "업데이트 날짜"}, "filters": {"search": {"placeholder": "파일 검색..."}, "fileType": "파일 유형", "status": "상태", "url": "URL", "file": "파일", "text": "텍스트"}, "actions": {"upload": "새 파일 업로드"}, "upload": {"dragAndDrop": "파일을 여기에 드래그 앤 드롭하거나,", "uploadButton": "업로드", "supportedFiles": "PDF, DOCX, TXT 또는 CSV", "totalSize": "총 크기", "noFileSelected": "선택된 파일이 없습니다", "uploadSuccess": "업로드가 성공했습니다", "fileTooLarge": "파일이 최대 크기 {{max}}MB를 초과했습니다", "file": "파일", "url": "웹 페이지 입력", "text": "텍스트 입력", "title": "지식 업로드", "invalidUrl": "유효하지 않은 URL입니다", "urlAlreadyAdded": "URL이 이미 존재합니다", "noUrlsToUpload": "최소 1개의 URL을 입력해주세요", "uploadError": "업로드 중 오류가 발생했습니다", "uploaded": "지식이 업로드되었습니다", "knowledgeNameRequired": "지식명은 필수입니다", "knowledgeNameTooLong": "지식명은 250자 이내여야 합니다", "textRequired": "텍스트는 필수입니다", "textTooLong": "텍스트는 20000자 이내여야 합니다", "search": "지식 검색", "textTitle": "지식명", "fileTitle": "파일에서 가져온 지식", "urlTitle": "URL에서 가져온 지식", "allTitle": "모든 지식", "deleteTitle": "지식 삭제", "deleteDescription": "지식을 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.", "deleteSuccess": "지식이 성공적으로 삭제되었습니다", "deleteError": "지식을 삭제할 수 없습니다", "deleteConfirm": "삭제", "deleteCancel": "취소", "leaveTitle": "저장하지 않고 나가기", "leaveDescription": "변경 사항은 저장되지 않습니다.", "leaveConfirm": "나가기", "leaveCancel": "돌아가기", "textInput": "직접 입력", "textInputPlaceholder": "여기에 지식 텍스트를 입력하세요...", "textInputTitle": "텍스트에서 가져온 지식", "textTitlePlaceholder": "지식 제목을 입력하세요", "pleaseSelectAtLeastOneFile": "최소 1개의 파일을 선택해주세요", "pleaseEnterAtLeastOneURL": "최소 1개의 URL을 입력해주세요", "pleaseEnterAtLeastOneTextFile": "최소 1개의 텍스트 파일을 입력해주세요", "pleaseEnterAllFields": "제목과 내용을 모두 입력해주세요", "newest": "최신", "oldest": "오래된", "noKnowledge": "지식이 없습니다", "urlImport": "URL에서 가져온 지식", "urlImportDescription": "웹 페이지에서 지식 가져오기", "deleteKnowledge": "지식 삭제", "deleteKnowledgeDescription": "지식을 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.", "deleteKnowledgeSuccess": "지식이 성공적으로 삭제되었습니다", "deleteKnowledgeError": "지식을 삭제할 수 없습니다", "deleteKnowledgeConfirm": "삭제", "deleteKnowledgeCancel": "취소", "fileImport": "파일에서 가져온 지식", "fileImportDescription": "파일에서 지식 가져오기", "fileImportSuccess": "지식이 성공적으로 업로드되었습니다", "fileImportError": "지식을 업로드할 수 없습니다", "fileImportConfirm": "업로드", "fileImportCancel": "취소", "textImport": "텍스트에서 가져온 지식", "textImportDescription": "텍스트에서 지식 가져오기", "textImportSuccess": "지식이 성공적으로 업로드되었습니다", "textImportError": "지식을 업로드할 수 없습니다", "textImportConfirm": "업로드", "textImportCancel": "취소", "urlImportSuccess": "지식이 성공적으로 업로드되었습니다", "urlImportError": "지식을 업로드할 수 없습니다", "urlImportConfirm": "업로드", "urlImportCancel": "취소", "deleteKnowledgeTitle": "지식 삭제"}, "status": {"error": "오류", "pending": "처리 중", "processing": "처리 중", "ready": "준비 완료"}}, "customer": {"details": {"customerDetails": "고객 상세", "name": "이름", "birthday": "생일", "gender": "성별", "phone": "전화번호", "email": "이메일", "shippingAddress": "배송지 주소", "billingAddress": "청구지 주소", "groupName": "그룹 이름", "totalLoyalPoints": "총 포인트", "totalRedeemPoints": "총 포인트", "tags": "태그", "noTags": "---"}, "purchase": {"purchaseInfo": "구매 정보", "totalSpent": "총 지출", "totalProductsPurchased": "구매한 상품 총합", "purchasedOrder": "구매한 주문", "totalProductsReturned": "반품한 상품 총합", "lastOrderAt": "마지막 주문일"}, "sales": {"suggestionInfo": "제안 정보", "defaultPriceGroup": "기본 가격 그룹", "defaultPaymentMethod": "기본 결제 방법", "discountPercent": "할인율"}, "order": {"orderHistory": "주문 내역"}}, "conversation": {"title": "대화", "whatCanIHelpWith": "무엇을 도와드릴까요?", "saySomething": "무언가 말씀해주세요...", "filters": {"search": {"placeholder": "검색..."}, "source": "소스", "unread": "읽지 않음", "read": "읽음", "assignee": "담당자"}}, "tasks": {"deleteDescription": "정말 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.", "promptContent": "프롬프트 내용", "shortDescription": "짧은 설명", "shortDescriptionPlaceholder": "여기에 지식 텍스트를 입력하세요...", "namePlaceholder": "작업 이름을 입력하세요", "promptContentPlaceholder": "여기에 지식 텍스트를 입력하세요...", "editTask": "작업 편집", "addTask": "작업 추가", "save": "저장", "add": "추가", "promptHelper": "프롬프트에 [변수]를 사용하여 필드를 삽입할 수 있습니다. 각 필드는 고유한 이름을 가져야 합니다. 동일한 변수가 여러 번 나타나면 사용자는 한 번만 입력하면 됩니다."}, "activities": {"title": "활동", "unknown": "알 수 없음", "opportunity_created": "기회를 생성함", "field_labels": {"order_changed": "주문 변경", "title": "제목", "status": "상태", "stage_changed": "열 변경", "assignee": "담당자", "assignee_changed": "담당자 변경", "customer_id": "고객", "customer_changed": "고객 변경", "expected_revenue": "예상 수익", "probability": "확률", "priority": "우선순위", "expected_closing": "예상 마감", "position": "위치", "position_changed": "위치 변경", "color": "색상", "tags": "태그", "user": "사용자", "note": "메모", "note_changed": "메모 변경", "schedule_activities": "활동 일정", "schedule_activities_changed": "활동 일정 추가", "schedule_activities_updated_at": "활동 일정 업데이트 날짜", "schedule_activities_created_at": "활동 일정 생성 날짜", "schedule_activities_due_date": "활동 일정 마감일", "schedule_activities_status": "활동 일정 상태", "schedule_activities_summary": "활동 일정 요약", "schedule_activities_note": "활동 일정 메모", "schedule_activities_assignee": "활동 일정 담당자", "schedule_activities_user": "활동 일정 사용자", "schedule_activities_count": "활동 일정 수", "representative_phone": "전화번호", "representative_contact": "연락처", "representative_email": "이메일", "colors": {"neutral": "회색", "blue": "파랑", "green": "초록", "yellow": "노랑", "red": "빨강", "teal": "청록", "pink": "분홍", "orange": "주황", "purple": "보라", "sky": "하늘색"}, "priorities": {"very_low": "매우 낮음", "low": "낮음", "medium": "중간", "high": "높음"}, "schedule_activity_status": {"status": "활동 일정 상태", "pending": "진행 중", "completed": "완료", "cancelled": "취소됨", "done": "완료"}, "opportunity_status": {"status": "상태", "ongoing": "진행 중", "won": "성공", "lost": "실패"}}, "scheduleActivity": {"groupButton": {"markAsDone": "완료", "edit": "편집", "cancel": "취소"}}, "history": {"tabs": {"history": "기록", "comments": "댓글", "placeholder": "댓글을 입력하세요..."}}, "headers": {"activity": "활동", "assignedTo": "담당자", "summary": "요약", "dueDate": "마감일", "status": "상태", "updatedAt": "업데이트 날짜"}, "filters": {"search": {"placeholder": "검색..."}, "status": "상태", "assignee": "담당자"}, "status": {"overdue": "기한 초과", "today": "오늘", "upcoming": "예정", "noduedate": "마감일 없음", "no_activity": "활동 없음"}, "updateSuccess": "활동이 업데이트되었습니다", "updateError": "활동을 업데이트할 수 없습니다", "deleteSuccess": "활동이 삭제되었습니다", "deleteError": "활동을 삭제할 수 없습니다", "error": "활동 유형 생성 오류", "errorActivityTypeExists": "같은 이름의 활동 유형이 이미 존재합니다", "createSuccess": "활동 유형이 생성되었습니다", "createError": "활동 유형을 생성할 수 없습니다"}, "opportunityDetail": {"updateStatusSuccess": {"title": "성공", "description": "기회의 상태가 성공적으로 업데이트되었습니다."}, "updateStatusError": {"title": "오류", "description": "기회의 상태를 업데이트할 수 없습니다. 다시 시도해주세요."}, "customerInfo": {"name": "이름", "company": "회사", "country": "국가", "address": "주소", "billingAddress": "(청구지)", "province": "도/현", "ward": "구", "phone": "전화번호", "job": "직책", "website": "웹사이트", "district": "구", "placeholder": "고객 선택"}, "updateError": {"title": "오류", "description": "기회를 업데이트할 수 없습니다. 다시 시도해주세요.", "probability": "확률은 0 이상 100 이하이어야 합니다.", "representativeEmail": "이메일 형식이 올바르지 않습니다", "customer": "고객은 필수 항목입니다"}, "updateSuccess": {"title": "성공", "description": "기회가 성공적으로 업데이트되었습니다."}, "updateCustomerError": {"title": "오류", "description": "고객을 업데이트할 수 없습니다. 다시 시도해주세요."}, "updateCustomerSuccess": {"title": "성공", "description": "고객이 성공적으로 업데이트되었습니다."}, "createError": {"title": "오류", "description": "기회를 생성할 수 없습니다. 다시 시도해주세요."}, "createSuccess": {"title": "성공", "description": "기회가 성공적으로 생성되었습니다."}}, "subscription": {"currentPlan": {"title": "현재 요금제:", "expiresOn": "만료일", "cancelSubscription": "취소", "upgrade": "업그레이드", "expired": "만료됨"}, "expired": {"title": "제한에 도달함", "description": "현재 요금제에서는 AI Staff 수에 제한이 있습니다. 업그레이드하여 더 많은 AI Staff를 생성하세요.", "yourUsage": "사용량:", "upgradeButton": "업그레이드", "seeAllPlans": "모든 요금제 보기", "planTitle": "요금제 만료됨", "planDescription": "현재 요금제가 만료되었습니다. 서비스를 계속 이용하려면 요금제를 업그레이드해주세요.", "expiredPlan": "만료일:", "upgrade": "업그레이드", "logout": "로그아웃"}, "exceedQuota": {"title": "제한에 도달함", "description": "현재 요금제에서는 {{quotaType}} 수에 제한이 있습니다. 업그레이드하여 더 많은 {{quotaType}}를 생성하세요.", "yourUsage": "사용량:", "upgradeButton": "업그레이드", "seeAllPlans": "모든 요금제 보기", "quotaTypes": {"staff": "AI Staff", "knowledge_capacity": "지식", "message": "메시지", "product": "상품", "order": "주문"}}, "usageStats": {"title": "사용량", "messages": "메시지", "staff": "AI Staff", "storage": "저장소", "unlimited": "무제한"}, "pricing": {"save": "절약", "annually": "연간", "title": "가격", "description": "개인과 기업을 위한 유연한 가격 요금제를 확인하세요.", "mostPopular": "인기", "upgrade": "업그레이드", "more": "더보기", "showLess": "숨기기"}, "billing": {"annualPlan": "연간 요금제", "savings": "15% 절약"}, "customPlan": {"title": "맞춤 요금제", "description": "귀하의 필요에 맞게 맞춤 제작합니다. 기능, 용량, 비용을 유연하게 조정할 수 있어 특수 요구사항이 있는 기업에 최적입니다.", "contactInfo": "연락처", "companyName": "회사명", "companyNamePlaceholder": "회사명을 입력하세요", "contactEmail": "연락처 이메일", "emailPlaceholder": "연락처 이메일을 입력하세요", "requirements": "요구사항", "messages": "메시지", "staff": "AI Staff", "storage": "지식 용량 (MB)", "currentMessages": "현재", "currentStaff": "현재", "currentStorage": "현재", "assistants": "AI Staff", "additionalRequirements": "추가 요구사항", "additionalRequirementsPlaceholder": "필요한 기능이나 요청사항을 입력해주세요...", "included": "항상 포함", "multilingualSupport": "다국어 지원", "prioritySupport": "우선 지원", "customIntegrations": "맞춤 통합", "submit": "맞춤 요금제 요청", "contact": "24시간 이내에 연락드립니다", "success": "맞춤 요금제 요청이 전송되었습니다!", "error": "맞춤 요금제 요청을 전송할 수 없습니다. 다시 시도해주세요.", "emailRequired": "연락처 이메일을 입력해주세요", "companyRequired": "회사명을 입력해주세요", "features": {"productManagement": "상품 관리", "orderManagement": "주문 관리", "knowledgeManagement": "지식 관리", "departmentManagement": "부서 관리", "employeeManagement": "직원 관리", "crm": "CRM"}, "configuration": {"virtualAssistants": "AI Staff", "messages": "메시지", "knowledgeCapacity": "지식 용량 (MB)", "aiModel": "AI 모델", "multilingual": "다국어", "scheduleCustomerCare": "고객 지원 일정", "customIntegration": "맞춤 통합"}, "button": "문의하기"}, "faq": {"title": "자주 묻는 질문", "description": "원하는 답변을 찾지 못하셨나요?", "description2": "문의해주세요.", "q1": "접근할 수 있나요?", "a1": "네. WAI-ARIA 디자인 템플릿을 따릅니다.", "q2": "스타일을 만들 수 있나요?", "a2": "네. 다른 컴포넌트에 맞춰 기본 스타일이 제공됩니다.", "q3": "애니메이션이 있나요?", "a3": "네. 기본적으로 애니메이션이 적용되며 필요에 따라 끌 수 있습니다.", "q4": "내 웹사이트에서 사용할 수 있나요?", "a4": "네. 이 컴포넌트는 모든 React 애플리케이션에서 사용할 수 있도록 설계되었습니다.", "q5": "어떻게 시작하나요?", "a5": "컴포넌트를 불러오고 JSX에서 사용하시면 됩니다."}}, "layouts": {"title": "レイアウト管理", "description": "不動産のフロアプランと単位マッピングを管理します", "totalLayouts": "合計レイアウト", "mappedUnits": "マッピングされた単位", "mapped": "マッピングされた", "unmapped": "未マッピング", "mappingStats": "マッピング統計", "selectLayout": "レイアウトを選択", "openMapping": "マッピングを開く", "floorPlans": "フロアプラン", "addLayout": "レイアウトを追加", "createLayout": "レイアウトを作成", "createLayoutDescription": "不動産の新しいフロアプランを作成します", "layoutNamePlaceholder": "例: 1階, 地下1階", "layoutDescriptionPlaceholder": "このレイアウトの説明", "searchLayouts": "レイアウトを検索...", "noLayouts": "レイアウトが見つかりません", "noLayoutsFound": "検索条件に一致するレイアウトが見つかりません", "createFirstLayout": "最初のレイアウトを作成して開始します", "tryDifferentSearch": "検索キーワードを変更してお試しください", "selectPropertyFirst": "不動産を選択してフロアプランを表示および管理します", "deleteLayout": "レイアウトを削除", "deleteLayoutConfirmation": "このレイアウトを削除してもよろしいですか？この操作は元に戻すことができません。", "saveSuccess": "レイアウトを保存しました", "deleteSuccess": "レイアウトを削除しました", "createSuccess": "レイアウトを作成しました", "dimensions": "寸法", "uploadImage": "フロアプランをアップロード", "uploadImageDescription": "フロアプランの画像をアップロードして単位のマッピングを開始します", "noLayoutSelected": "レイアウトが選択されていません", "selectLayoutToStart": "レイアウトを選択して単位のマッピングを開始します", "mappedToLayout": "レイアウトにマッピングされた", "assignUnit": "単位を割り当て", "selectUnit": "単位を選択", "mappingProgress": "マッピングの進行状況", "systemHealth": "システムの状態", "weeklyProgress": "週次の進行状況", "recentChanges": "最近の変更", "complete": "完了", "incomplete": "未完了", "withIssues": "問題がある", "duplicateAll": "すべてを複製", "exportAll": "すべてをエクスポート", "importAll": "すべてをインポート", "shareAll": "すべてを共有", "generateReport": "レポートを生成", "saveAsTemplate": "テンプレートとして保存", "viewReport": "レポートを表示", "lastActivity": "最終アクティビティ", "totalUnits": "合計単位", "exportSuccess": "データのエクスポートに成功しました", "exportError": "データのエクスポートに失敗しました", "importSuccess": "<PERSON><PERSON><PERSON><PERSON> thành công {{count}} b<PERSON> cục", "importError": "Lỗi nhập dữ liệu bố cục", "importParseError": "<PERSON><PERSON><PERSON> dạng tệp không hợp lệ. <PERSON><PERSON> lòng chọn tệp xuất bố cục hợp lệ.", "saveError": "Lỗi lưu bố cục: {{error}}", "deleteError": "Lỗi x<PERSON>a bố cục: {{error}}", "createError": "Lỗi tạo bố cục: {{error}}", "templateCreated": "Tạo mẫu thành công", "templateCreateError": "Lỗi tạo mẫu", "createTemplate": "Tạo mẫu", "createTemplateDescription": "<PERSON><PERSON><PERSON> bố cục này thành mẫu để sử dụng lại cho các bất động sản khác", "templateName": "<PERSON><PERSON>n mẫu", "templateNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên mẫu", "templateDescription": "<PERSON><PERSON>", "templateDescriptionPlaceholder": "<PERSON><PERSON> tả tùy chọn cho mẫu này", "templateCategory": "<PERSON><PERSON>", "categories": {"custom": "<PERSON><PERSON><PERSON> chỉnh", "residential": "<PERSON><PERSON> d<PERSON> c<PERSON>", "commercial": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại", "mixed": "Hỗn hợp"}, "overlapWarning": "<PERSON><PERSON><PERSON> b<PERSON>o chồng lấp", "overlapDetected": "<PERSON><PERSON><PERSON> {{count}} hình chồng lấp", "floorNumber": "<PERSON><PERSON> tầng", "floorNumberDescription": "Số tầng tùy chọn cho bố cục này", "dropImageHere": "<PERSON><PERSON><PERSON> hình ảnh mặt bằng vào đây hoặc nhấp để chọn", "supportedFormats": "Hỗ trợ JPG, PNG, SVG, WebP (tối đa 10MB)", "chooseFile": "<PERSON><PERSON><PERSON>", "imageUploaded": "<PERSON><PERSON><PERSON> hình <PERSON>nh thành công", "uploading": "<PERSON><PERSON> t<PERSON> lên", "invalidFileType": "<PERSON><PERSON> lòng chọn tệp hình <PERSON>nh hợp lệ", "fileTooLarge": "<PERSON><PERSON><PERSON> thư<PERSON><PERSON> tệp quá lớn. T<PERSON><PERSON> đa cho phép 10MB", "uploadFailed": "Lỗi tải lên hình <PERSON>nh", "dropUnitHere": "<PERSON>h<PERSON> đơn vị vào đây để đặt trên bố cục"}, "properties": {"title": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "add": "<PERSON><PERSON><PERSON><PERSON> bất động sản", "selectProperty": "<PERSON><PERSON><PERSON> bất động sản", "createProperty": "<PERSON><PERSON><PERSON> b<PERSON>t độ<PERSON> sản", "editProperty": "Chỉnh sửa bất động sản", "basicInformation": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "addressInformation": "Thông tin địa chỉ", "ownerInformation": "Thông tin chủ sở hữu", "purchaseInformation": "Thông tin mua", "images": "<PERSON><PERSON><PERSON> bất động sản", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t bất động sản thành công", "createSuccess": "<PERSON><PERSON><PERSON> b<PERSON>t động sản thành công", "updateError": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật bất động sản", "createError": "<PERSON><PERSON><PERSON><PERSON> thể tạo bất động sản", "headers": {"propertyInfo": "Thông tin BĐS", "name": "<PERSON><PERSON><PERSON> b<PERSON>t động sản", "address": "Địa chỉ", "type": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>", "totalUnits": "Tổng đơn vị", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "updatedAt": "<PERSON><PERSON><PERSON> n<PERSON> l<PERSON>c", "actions": "<PERSON><PERSON>"}, "address": {"street": "Địa chỉ đường", "city": "<PERSON><PERSON><PERSON><PERSON> phố", "state": "Tỉnh/Thành phố", "zipCode": "<PERSON><PERSON> b<PERSON>u đi<PERSON>n", "country": "Quốc gia"}, "owner": {"name": "<PERSON>ên chủ sở hữu", "email": "<PERSON>ail chủ sở hữu", "phone": "<PERSON><PERSON><PERSON><PERSON> thoại chủ sở hữu"}, "purchase": {"price": "<PERSON><PERSON><PERSON> mua", "date": "<PERSON><PERSON><PERSON> mua"}, "types": {"residential": "<PERSON><PERSON> c<PERSON>", "commercial": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại", "mixed": "Hỗn hợp"}, "status": {"active": "활성", "inactive": "비활성", "maintenance": "유지보수", "pending": "대기 중"}, "placeholders": {"name": "<PERSON><PERSON><PERSON><PERSON> tên bất động sản", "type": "<PERSON><PERSON><PERSON> lo<PERSON>i bất động sản", "description": "<PERSON><PERSON><PERSON><PERSON> mô tả bất động sản", "street": "<PERSON><PERSON><PERSON><PERSON> địa chỉ đường", "city": "<PERSON><PERSON><PERSON><PERSON> thành phố", "state": "Nhập tỉnh/thành phố", "zipCode": "<PERSON><PERSON><PERSON><PERSON> mã b<PERSON>u đi<PERSON>n", "country": "<PERSON><PERSON><PERSON><PERSON> quốc gia", "ownerName": "<PERSON><PERSON><PERSON><PERSON> tên chủ sở hữu", "ownerEmail": "<PERSON><PERSON>ập email chủ sở hữu", "ownerPhone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại chủ sở hữu", "purchasePrice": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> mua", "uploadImages": "<PERSON><PERSON><PERSON><PERSON> để tải lên hình ảnh hoặc kéo thả"}, "filters": {"search": {"placeholder": "<PERSON><PERSON><PERSON> kiếm bất động sản..."}, "type": "<PERSON><PERSON><PERSON> bất động sản", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>"}, "actions": {"addManual": "<PERSON><PERSON><PERSON><PERSON> bất động sản", "view": "Xem", "edit": "Chỉnh sửa", "delete": "Xóa"}, "deleteSuccess": "<PERSON><PERSON><PERSON> bất động sản thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa bất động sản", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải thông tin bất động sản", "deleteProperty": "<PERSON><PERSON><PERSON>", "deleteConfirmation": "Bạn có chắc chắn muốn xóa {{name}}? Hành động này không thể hoàn tác.", "noImages": "<PERSON><PERSON><PERSON> c<PERSON> hình <PERSON>nh", "unitStatus": "Tổng quan trạng thái căn hộ", "stats": {"totalUnits": "<PERSON><PERSON>ng số căn hộ", "occupancyRate": "Tỷ lệ thuê", "occupiedUnits": "<PERSON><PERSON><PERSON> hộ đã thuê", "availableUnits": "<PERSON><PERSON><PERSON> hộ còn trống"}, "components": {"propertyBasicInfo": {"type": "유형", "totalUnits": "총 유닛 수", "description": "설명"}, "propertyImages": {"noImages": "이미지가 없습니다", "propertyImage": "부동산 이미지"}, "propertyOwnerInfo": {"ownerInformation": "소유자 정보", "ownerName": "소유자 이름", "ownerEmail": "소유자 이메일", "ownerPhone": "소유자 전화번호", "purchasePrice": "구매 가격", "purchaseDate": "구매 날짜"}, "propertyHeader": {"edit": "편집", "delete": "삭제"}, "propertyStatsCards": {"totalUnits": "총 유닛 수", "occupiedUnits": "점유된 유닛", "availableUnits": "사용 가능한 유닛", "maintenance": "유지보수", "occupancyRate": "점유율"}}}, "contracts": {"title": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> đồng", "search": "<PERSON><PERSON><PERSON> kiếm hợp đồng...", "searchPlaceholder": "<PERSON><PERSON><PERSON> kiếm theo số hợp đồng", "export": "<PERSON><PERSON><PERSON> dữ liệu", "confirmDelete": "Bạn có chắc chắn muốn xóa hợp đồng nà<PERSON>?", "viewContracts": "<PERSON><PERSON> đ<PERSON>", "headers": {"contractInfo": "<PERSON><PERSON><PERSON><PERSON> tin hợp đồng", "tenant": "<PERSON><PERSON><PERSON><PERSON> thuê", "property": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "duration": "<PERSON><PERSON><PERSON><PERSON> hạn", "rentAmount": "<PERSON><PERSON><PERSON> thu<PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>"}, "filters": {"status": "<PERSON><PERSON><PERSON><PERSON> thái", "contractType": "<PERSON><PERSON><PERSON> h<PERSON> đ<PERSON>ng", "property": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "startDate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "endDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "clear": "Xóa bộ lọc"}, "status": {"draft": "<PERSON><PERSON><PERSON>", "active": "<PERSON><PERSON> l<PERSON>", "expired": "<PERSON><PERSON><PERSON>", "terminated": "<PERSON><PERSON> hủy", "pending": "<PERSON>ờ <PERSON>"}, "types": {"monthly": "<PERSON>", "annual": "<PERSON>", "profitSharing": "<PERSON><PERSON> sẻ lợi nhuận", "revenueSharing": "<PERSON>a sẻ doanh thu"}, "actions": {"view": "Xem", "edit": "Chỉnh sửa", "delete": "Xóa"}, "createContract": "<PERSON><PERSON><PERSON> đ<PERSON>", "editContract": "Chỉnh sửa hợp đồng", "createDescription": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> đồng bất động sản mới với các điều khoản linh hoạt", "editDescription": "<PERSON><PERSON><PERSON> nhật chi tiết và điều kho<PERSON>n hợp đồng", "sections": {"propertyUnit": "Chọn Bất động sản & <PERSON><PERSON><PERSON> hộ", "contractDetails": "<PERSON> tiết hợp đồng", "financialTerms": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>n tài ch<PERSON>h", "termsConditions": "<PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON> ki<PERSON>n"}, "fields": {"property": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "unit": "<PERSON><PERSON><PERSON>", "tenant": "<PERSON><PERSON><PERSON><PERSON> thuê", "contractType": "<PERSON><PERSON><PERSON> h<PERSON> đ<PERSON>ng", "startDate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "endDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "rentDueDay": "<PERSON><PERSON><PERSON> ti<PERSON>n", "rentAmount": "<PERSON><PERSON> tiền thuê", "depositAmount": "Tiền đặt cọc", "lateFeeAmount": "<PERSON><PERSON> chậm trễ", "noticePeriodDays": "<PERSON><PERSON>ờ<PERSON> gian b<PERSON><PERSON> tr<PERSON> (ngày)", "autoRenewal": "<PERSON><PERSON> hạn tự động", "profitSharingPercentage": "<PERSON><PERSON><PERSON> trăm chia sẻ lợi nhuận", "revenueSharingPercentage": "<PERSON><PERSON><PERSON> tr<PERSON>m chia sẻ doanh thu", "termsConditions": "<PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON> ki<PERSON>n"}, "placeholders": {"property": "<PERSON><PERSON><PERSON> bất động sản", "unit": "<PERSON><PERSON><PERSON> c<PERSON>n hộ", "tenant": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> thuê", "contractType": "<PERSON><PERSON><PERSON> lo<PERSON>i hợp đồng", "rentDueDay": "<PERSON><PERSON><PERSON><PERSON><PERSON> (1-31)", "rentAmount": "<PERSON><PERSON><PERSON><PERSON> số tiền thuê hàng tháng", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> tiền đặt cọc", "lateFeeAmount": "<PERSON><PERSON><PERSON><PERSON> phí chậm tr<PERSON> (t<PERSON><PERSON> ch<PERSON>n)", "noticePeriodDays": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>i gian b<PERSON> tr<PERSON> (ngày)", "profitSharingPercentage": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> trăm (0-100)", "revenueSharingPercentage": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> trăm (0-100)", "termsConditions": "<PERSON><PERSON><PERSON><PERSON> điều khoản và điều kiện hợp đồng"}, "contractTypes": {"monthly": "<PERSON><PERSON><PERSON> theo tháng", "annual": "<PERSON><PERSON><PERSON> the<PERSON> n<PERSON>", "profitSharing": "<PERSON><PERSON> sẻ lợi nhuận", "revenueSharing": "<PERSON>a sẻ doanh thu"}, "descriptions": {"autoRenewal": "Tự động gia hạn hợp đồng khi hết hạn"}, "messages": {"createSuccess": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> đồng thành công", "createError": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> đồng thất bại", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t hợp đồng thành công", "updateError": "<PERSON><PERSON><PERSON> nhật hợp đồng thất bại"}, "contractDetails": "<PERSON>", "overview": "<PERSON><PERSON><PERSON>", "paymentHistory": "<PERSON><PERSON><PERSON>"}, "reports": {"title": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> B<PERSON><PERSON> động sản", "description": "<PERSON><PERSON><PERSON> báo cáo toàn diện và phân tích hiệu suất bất động sản", "generateReport": "Tạo Báo cáo", "downloadReport": "<PERSON><PERSON><PERSON>", "generating": "<PERSON><PERSON> tạo...", "categories": {"financial": {"title": "Báo c<PERSON>o T<PERSON> ch<PERSON>", "description": "<PERSON><PERSON> tích doanh thu, chi phí và lợi nhuận", "generate": "Tạo"}, "occupancy": {"title": "<PERSON><PERSON><PERSON> cáo Tỷ lệ <PERSON>p đ<PERSON>y", "description": "Tỷ lệ trống và giữ chân khách thuê", "generate": "Tạo"}, "performance": {"title": "<PERSON><PERSON> tích <PERSON>", "description": "Chỉ số hiệu suất bất động sản và đơn vị", "generate": "Tạo"}, "maintenance": {"title": "Báo c<PERSON>o <PERSON> trì", "description": "Chi phí bảo trì và xu hướng", "generate": "Tạo"}, "tenant": {"title": "<PERSON><PERSON> tích <PERSON>ch thuê", "description": "Thông tin nhân khẩu học và hành vi khách thuê", "generate": "Tạo"}, "custom": {"title": "Báo cáo Tù<PERSON> chỉnh", "description": "<PERSON>â<PERSON> dựng báo cáo tùy chỉnh với bộ lọc", "create": "Tạo"}}, "comingSoon": {"title": "<PERSON><PERSON> thống <PERSON> c<PERSON>o <PERSON> cao - Sắp ra mắt", "description": "<PERSON><PERSON> tích toà<PERSON>, báo cáo tùy chỉnh và tính năng xuất dữ liệu sẽ có sẵn tại đây."}}, "dashboard": {"propertyAssets": {"title": "<PERSON>ảng điều khiển tài sản bất động sản", "description": "Tổng quan hiệu suất danh mục bất động sản và các chỉ số chính"}, "metrics": {"totalProperties": "<PERSON>ổng số bất động sản", "totalUnits": "<PERSON><PERSON>ng số căn hộ", "activeContracts": "<PERSON><PERSON><PERSON> đồng đang hoạt động", "monthlyRevenue": "<PERSON><PERSON><PERSON> thu hàng tháng"}, "charts": {"occupancyOverview": "T<PERSON>ng quan tỷ lệ lấp đầy"}, "occupancy": {"occupied": "<PERSON><PERSON> thuê", "vacant": "<PERSON><PERSON><PERSON><PERSON>", "rate": "Tỷ lệ lấp đầy", "tenants": "<PERSON>ổng số khách thuê"}, "quickActions": {"title": "<PERSON><PERSON> t<PERSON> n<PERSON>h", "addProperty": "<PERSON><PERSON><PERSON><PERSON> bất động sản", "addUnit": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>n hộ", "createContract": "<PERSON><PERSON><PERSON> đ<PERSON>", "addTenant": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>ch thuê"}, "recentActivities": {"title": "<PERSON><PERSON><PERSON> động gần đây", "viewAll": "<PERSON><PERSON> tất cả hoạt động"}, "portfolio": {"title": "<PERSON><PERSON><PERSON> t<PERSON>t danh mục", "properties": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "units": "<PERSON><PERSON><PERSON>", "contracts": "<PERSON><PERSON><PERSON>", "revenue": "<PERSON><PERSON>h thu", "performance": "<PERSON><PERSON><PERSON> su<PERSON>t tổng thể"}}, "units": {"title": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "addUnit": "<PERSON><PERSON><PERSON><PERSON>", "viewUnits": "<PERSON><PERSON>", "createUnit": "<PERSON><PERSON><PERSON>", "editUnit": "Chỉnh <PERSON><PERSON><PERSON>", "createDescription": "<PERSON><PERSON><PERSON> c<PERSON>n hộ cho thuê mới", "editDescription": "<PERSON><PERSON><PERSON> nhật thông tin căn hộ", "deleteUnit": "<PERSON><PERSON><PERSON>", "deleteConfirmation": "Bạn có chắc chắn muốn xóa căn hộ {{unit}}? Hành động này không thể hoàn tác.", "deleteSuccess": "<PERSON><PERSON><PERSON> căn hộ thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa căn hộ", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t căn hộ thành công", "updateError": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật căn hộ: {{error}}", "createSuccess": "<PERSON><PERSON><PERSON> c<PERSON>n hộ thành công", "createError": "<PERSON><PERSON><PERSON><PERSON> thể tạo căn hộ", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải căn hộ", "unitNumber": "<PERSON><PERSON><PERSON>", "noImages": "<PERSON><PERSON><PERSON><PERSON> có hình <PERSON>nh", "noUnitsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy căn hộ", "noPropertiesAvailable": "<PERSON><PERSON><PERSON><PERSON> có bất động sản", "searchUnits": "<PERSON><PERSON><PERSON> kiếm căn hộ...", "unitList": "<PERSON><PERSON>", "assignUnit": "<PERSON><PERSON>", "selectUnit": "<PERSON><PERSON><PERSON> c<PERSON>n hộ", "basicInformation": "Thông Tin Cơ Bản", "specifications": "Thông Số K<PERSON>hu<PERSON>", "financialInformation": "Thông Tin Tài <PERSON>", "amenities": "<PERSON><PERSON><PERSON><PERSON>", "images": "<PERSON><PERSON><PERSON> Ảnh", "headers": {"unitInfo": "<PERSON><PERSON>ông Tin <PERSON>", "property": "<PERSON><PERSON><PERSON>", "unitNumber": "Số Căn Hộ", "type": "<PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON>", "rent": "<PERSON><PERSON><PERSON><PERSON>", "status": "Trạng <PERSON>", "tenant": "<PERSON><PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>", "floor": "<PERSON><PERSON><PERSON>", "squareFootage": "<PERSON><PERSON><PERSON> (m²)", "bedrooms": "<PERSON><PERSON>ng <PERSON>", "bathrooms": "Phòng Tắm", "rentAmount": "<PERSON><PERSON>", "depositAmount": "<PERSON><PERSON><PERSON><PERSON>"}, "filters": {"type": "<PERSON><PERSON><PERSON>", "status": "Trạng <PERSON>", "property": "<PERSON><PERSON><PERSON>", "rentRange": "Khoảng G<PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "search": {"placeholder": "<PERSON><PERSON><PERSON> kiếm căn hộ..."}}, "actions": {"addManual": "<PERSON><PERSON><PERSON><PERSON> Cô<PERSON>"}, "status": {"available": "<PERSON><PERSON> Sẵn", "occupied": "<PERSON><PERSON>", "maintenance": "Bảo Trì", "inactive": "Không Hoạt Động"}, "types": {"studio": "Studio", "1br": "1 Phòng Ngủ", "2br": "2 Phòng Ngủ", "3br": "3 Phòng Ngủ", "commercial": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "office": "<PERSON><PERSON><PERSON>", "retail": "B<PERSON> Lẻ"}, "placeholders": {"property": "<PERSON><PERSON><PERSON> bất động sản", "unitNumber": "<PERSON><PERSON><PERSON><PERSON> số căn hộ", "type": "<PERSON><PERSON><PERSON> lo<PERSON>i căn hộ", "description": "<PERSON><PERSON><PERSON><PERSON> mô tả căn hộ", "floor": "<PERSON><PERSON><PERSON><PERSON> số tầng", "squareFootage": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>ch", "bedrooms": "Số phòng ngủ", "bathrooms": "Số phòng tắm", "rentAmount": "<PERSON><PERSON><PERSON><PERSON> số tiền thuê", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> ti<PERSON> c<PERSON>c", "uploadImages": "<PERSON><PERSON><PERSON><PERSON> để tải lên hình <PERSON>nh căn hộ"}}, "tenants": {"title": "<PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "editTenant": "Chỉnh <PERSON><PERSON><PERSON>", "createTenant": "<PERSON><PERSON><PERSON>", "editDescription": "<PERSON><PERSON><PERSON> nhật thông tin người thuê", "createDescription": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i thuê mới", "searchPlaceholder": "<PERSON><PERSON><PERSON> kiếm người thuê...", "viewTenant": "<PERSON><PERSON>", "noTenant": "<PERSON><PERSON><PERSON><PERSON>", "sections": {"personalInfo": "Thông Tin Cá Nhân", "identification": "<PERSON><PERSON><PERSON><PERSON>ờ Tùy Thân", "employment": "<PERSON><PERSON><PERSON>", "emergencyContact": "<PERSON><PERSON><PERSON>"}, "fields": {"fullName": "<PERSON><PERSON>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Họ", "email": "Email", "phone": "<PERSON><PERSON><PERSON><PERSON>", "dateOfBirth": "<PERSON><PERSON><PERSON>", "status": "Trạng <PERSON>", "identificationType": "Loại Giấ<PERSON>", "identificationNumber": "Số Giấy Tờ", "employmentStatus": "Tình Trạng Công <PERSON>", "employerName": "<PERSON><PERSON><PERSON>", "monthlyIncome": "<PERSON><PERSON><PERSON>", "emergencyContactName": "<PERSON><PERSON><PERSON>", "emergencyContactPhone": "Số Đ<PERSON>"}, "headers": {"tenantInfo": "T<PERSON>ông Tin Ngườ<PERSON>", "contact": "<PERSON><PERSON><PERSON>", "employmentStatus": "Tình Trạng Công <PERSON>", "status": "Trạng <PERSON>", "emergencyContact": "<PERSON><PERSON><PERSON>", "joinDate": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>"}, "filters": {"leaseStatus": "<PERSON>rạng <PERSON>", "property": "<PERSON><PERSON><PERSON>"}, "status": {"active": "Ho<PERSON><PERSON>", "inactive": "Không Hoạt Động", "expired": "<PERSON><PERSON><PERSON>", "pending": "Chờ <PERSON>"}, "employmentStatus": {"employed": "<PERSON><PERSON>", "unemployed": "<PERSON><PERSON><PERSON><PERSON>", "self_employed": "<PERSON><PERSON>", "student": "<PERSON><PERSON> Viê<PERSON>"}, "actions": {"addManual": "<PERSON><PERSON><PERSON><PERSON>"}, "stats": {"totalContracts": "Tổng Số H<PERSON>", "activeContracts": "<PERSON><PERSON><PERSON>", "joinDate": "<PERSON><PERSON><PERSON>"}, "quickStats": "<PERSON><PERSON><PERSON><PERSON>", "activeContracts": "<PERSON><PERSON><PERSON>", "identificationTypes": {"passport": "<PERSON><PERSON>", "national_id": "CMND/CCCD", "driver_license": "Bằng Lái Xe"}, "errors": {"notFound": "<PERSON><PERSON><PERSON><PERSON>", "notFoundDescription": "<PERSON>ười thuê mà bạn đang tìm không tồn tại hoặc đã bị xóa."}, "documents": "<PERSON><PERSON><PERSON>", "deleteConfirmation": {"title": "<PERSON><PERSON><PERSON>", "description": "Bạn có chắc chắn muốn xóa người thuê này? Hành động này không thể hoàn tác.", "hasActiveContracts": "<PERSON><PERSON><PERSON>i thuê này có hợp đồng đang hoạt động. Bạn có chắc chắn muốn xóa?"}, "messages": {"deleteSuccess": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i thuê thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa người thuê"}}, "maintenance": {"title": "Bảo Trì", "add": "<PERSON><PERSON><PERSON>", "search": "T<PERSON><PERSON> kiếm bảo trì...", "searchPlaceholder": "T<PERSON><PERSON> kiếm theo tiêu đề yêu cầu", "export": "<PERSON><PERSON><PERSON>", "confirmDelete": "Bạn có chắc chắn muốn xóa yêu cầu bảo trì này?", "headers": {"requestInfo": "<PERSON><PERSON><PERSON><PERSON>", "property": "<PERSON><PERSON><PERSON>", "requester": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "priority": "Đ<PERSON> Ưu Tiên", "status": "Trạng <PERSON>", "assignedTo": "<PERSON><PERSON>", "estimatedCost": "Chi Phí Ước Tính", "dueDate": "<PERSON><PERSON><PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>"}, "filters": {"status": "Trạng <PERSON>", "priority": "Đ<PERSON> Ưu Tiên", "category": "<PERSON><PERSON>", "property": "<PERSON><PERSON><PERSON>"}, "priorities": {"low": "<PERSON><PERSON><PERSON><PERSON>", "medium": "<PERSON><PERSON>", "high": "<PERSON>", "urgent": "<PERSON><PERSON><PERSON><PERSON>"}, "categories": {"plumbing": "Ống Nước", "electrical": "<PERSON><PERSON><PERSON><PERSON>", "hvac": "<PERSON><PERSON><PERSON><PERSON>", "structural": "<PERSON><PERSON><PERSON>", "appliance": "<PERSON><PERSON><PERSON>t Bị", "cosmetic": "<PERSON><PERSON><PERSON><PERSON>", "cleaning": "<PERSON><PERSON>", "security": "<PERSON>", "general": "<PERSON>", "other": "K<PERSON><PERSON><PERSON>"}, "actions": {"addManual": "<PERSON><PERSON><PERSON><PERSON>", "view": "Xem", "edit": "Chỉnh Sửa", "delete": "Xóa"}, "createRequest": "<PERSON><PERSON><PERSON>", "editRequest": "Chỉnh <PERSON><PERSON><PERSON>", "createDescription": "<PERSON><PERSON><PERSON> yêu cầu bảo trì mới cho bất động sản của bạn", "editDescription": "<PERSON><PERSON><PERSON> nhật chi tiết yêu cầu bảo trì", "sections": {"propertyUnit": "Lựa Chọn Bất Động Sản & Căn Hộ", "requestDetails": "<PERSON>", "scheduling": "Lập L<PERSON> & Nhà Thầu", "costInfo": "Thông Tin Chi Phí", "additional": "T<PERSON>ông Tin Bổ Sung"}, "fields": {"property": "<PERSON><PERSON><PERSON>", "unit": "<PERSON><PERSON><PERSON>", "tenant": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON>i<PERSON><PERSON>", "description": "<PERSON><PERSON>", "priority": "Đ<PERSON> Ưu Tiên", "category": "<PERSON><PERSON>", "status": "Trạng <PERSON>", "scheduledDate": "<PERSON><PERSON><PERSON>", "completedDate": "<PERSON><PERSON><PERSON>", "contractorName": "<PERSON><PERSON><PERSON>", "contractorPhone": "Số Điện Thoại Nhà <PERSON>hầ<PERSON>", "estimatedCost": "Chi Phí Ước Tính ($)", "actualCost": "<PERSON> Phí <PERSON> ($)", "notes": "<PERSON><PERSON>", "images": "<PERSON><PERSON><PERSON>"}, "placeholders": {"property": "<PERSON><PERSON><PERSON> bất động sản", "unit": "<PERSON><PERSON><PERSON> c<PERSON>n hộ", "tenant": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> thuê", "title": "<PERSON><PERSON> tả ngắn gọn về vấn đề", "description": "<PERSON><PERSON> tả chi tiết về vấn đề bảo trì", "priority": "<PERSON><PERSON><PERSON> mức độ ưu tiên", "category": "<PERSON><PERSON><PERSON> danh mục", "status": "<PERSON><PERSON><PERSON> trạng thái", "contractorName": "<PERSON><PERSON><PERSON><PERSON> tên nhà thầu", "contractorPhone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại nhà thầu", "estimatedCost": "0.00", "actualCost": "0.00", "notes": "<PERSON><PERSON> chú hoặc hướng dẫn bổ sung"}, "options": {"propertyWide": "<PERSON><PERSON><PERSON> trì toàn bộ bất động sản", "noTenant": "<PERSON><PERSON><PERSON><PERSON> có người thuê cụ thể"}, "status": {"open": "Mở", "in_progress": "<PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON>", "cancelled": "Đã <PERSON>", "inProgress": "<PERSON><PERSON>"}, "messages": {"createSuccess": "<PERSON><PERSON><PERSON> y<PERSON>u cầu bảo trì thành công", "createError": "<PERSON><PERSON><PERSON><PERSON> thể tạo yêu cầu bảo trì", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t yêu cầu bảo trì thành công", "updateError": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật yêu cầu bảo trì"}, "viewRequests": "<PERSON><PERSON>"}, "payments": {"headers": {"paymentInfo": "Thông Tin Thanh Toán", "amount": "Số Tiền", "contract": "<PERSON><PERSON><PERSON>", "property": "<PERSON><PERSON><PERSON>", "status": "Trạng <PERSON>", "paymentDate": "<PERSON><PERSON><PERSON>", "createdAt": "Tạo Lú<PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>"}, "status": {"pending": "<PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON><PERSON>", "refunded": "<PERSON><PERSON><PERSON>"}}, "pipelines": {"headers": {"activities": "<PERSON><PERSON><PERSON> đ<PERSON>", "amount": "Số lượng", "assignee": "Ngườ<PERSON> phụ trách", "closeDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c  ", "createdAt": "<PERSON><PERSON><PERSON>", "customer": "<PERSON><PERSON><PERSON><PERSON>", "expectedClosingDate": "<PERSON><PERSON><PERSON> kết thúc dự kiến", "expectedRevenue": "<PERSON><PERSON><PERSON> thu dự kiến", "opportunity": "<PERSON><PERSON> hội", "pipeline": "<PERSON><PERSON><PERSON> đo<PERSON>n xử lý", "probability": "<PERSON><PERSON><PERSON>", "stage": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>"}}}, "table": {"pagination": {"rowsPerPage": "페이지당 행 수", "description": "{{start}} ~ {{end}} / 총 {{total}} 행", "next": "다음", "previous": "이전"}, "selected": {"title": "선택됨", "delete": "{{count}}개 선택 삭제"}, "export": {"title": "데이터 내보내기", "description": "데이터 내보내기", "confirm": "데이터 내보내기", "cancel": "취소"}, "filter": {"clearFilter": "필터 지우기", "loadMore": "더 불러오기"}, "savedFilters": {"FilterTypeRequired": "필터 유형은 필수입니다", "NoFiltersToSave": "저장할 필터가 없습니다", "CreateFilterSuccess": "필터가 성공적으로 저장되었습니다", "CreateFilterFail": "필터를 저장할 수 없습니다", "UpdateFilterSuccess": "필터가 성공적으로 업데이트되었습니다", "DeleteFilterSuccess": "필터가 성공적으로 삭제되었습니다", "DeleteFilterFail": "필터를 삭제할 수 없습니다", "UpdateFilterFail": "필터를 업데이트할 수 없습니다", "UpdateFilter": "필터가 업데이트되었습니다", "settings": {"settings": "설정", "title": "탭 설정", "description": "첫 6개의 탭이 메인 페이지에서 우선적으로 표시됩니다. 필요에 맞게 커스터마이즈하세요.", "placeholder": "탭 이름 입력", "delete": {"title": "저장된 필터 삭제", "description": "이 저장된 필터를 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.", "confirm": "삭제"}}}}, "validation": {"titleRequired": "제목은 필수입니다", "required": "이 필드는 필수입니다", "invalidEmail": "유효한 이메일 주소를 입력하세요", "minLength": "{{count}}자 이상이어야 합니다", "maxLength": "{{count}}자 이내여야 합니다", "passwordMismatch": "비밀번호가 일치하지 않습니다", "invalidUsername": "사용자 이름은 3자 이상이어야 합니다", "emailRequired": "이메일을 입력하세요", "usernameRequired": "사용자 이름을 입력하세요", "passwordRequired": "비밀번호를 입력하세요", "confirmPasswordRequired": "비밀번호를 확인하세요", "invalidPassword": "비밀번호는 8자 이상이어야 합니다", "passwordsDoNotMatch": "비밀번호가 일치하지 않습니다", "verificationCodeRequired": "인증 코드를 입력하세요", "verificationCodeLength": "인증 코드는 6자리여야 합니다", "sessionRequired": "이 필드는 필수입니다", "usernameSpecialCharacters": "사용자 이름은 영문, 숫자, 밑줄만 사용할 수 있습니다", "skuFormat": "SKU는 영문, 숫자, 밑줄만 사용할 수 있습니다", "skuRequired": "SKU는 필수입니다", "nameRequired": "상품명을 입력하여 표시 효율을 최적화하세요", "nameTooLong": "상품명은 250자 이내여야 합니다", "titleTooLong": "제목은 100자 이내여야 합니다", "priceRequired": "가격은 필수입니다", "imageRequired": "이미지를 최소 1장 추가하세요", "imageFormat": "이미지 형식이 잘못되었습니다", "priceMustBePositive": "양수의 가격을 입력하세요", "invalidPrice": "가격이 유효하지 않습니다", "wrongUsernameOrPassword": "사용자 이름 또는 비밀번호가 잘못되었습니다", "phoneRequired": "전화번호는 필수입니다", "phoneNumberAlreadyExists": "전화번호가 이미 존재합니다", "contactRequired": "연락처는 필수입니다", "customerRequired": "고객은 필수입니다", "emailOrPhoneRequired": "이메일 또는 전화번호는 필수입니다", "activityTypeRequired": "활동 유형은 필수입니다", "summaryRequired": "개요는 필수입니다", "propertyRequired": "부동산은 필수입니다", "unitRequired": "단위는 필수입니다", "tenantRequired": "고객은 필수입니다", "contractTypeRequired": "계약 유형은 필수입니다", "startDateRequired": "시작일은 필수입니다", "rentAmountMustBePositive": "임대료는 양수여야 합니다", "depositAmountMustBePositive": "보증금은 양수여야 합니다", "lateFeeAmountMustBePositive": "연체료는 양수여야 합니다", "rentDueDayMin": "지불일은 최소 1일이어야 합니다", "rentDueDayMax": "지불일은 31일 이하여야 합니다", "percentageMax": "비율은 100% 이하여야 합니다", "noticePeriodMustBePositive": "통지 기간은 양수여야 합니다", "sharingPercentageRequired": "이 계약 유형에서는 공유 비율이 필수입니다", "idRequired": "ID는 필수입니다"}, "footer": {"crafted": "OneXAPIs에서 제작", "by": "by", "team": "OneXAPIs 팀", "heart": "heart"}, "install": {"installing": "설치 중...", "pleaseWait": "잠시 기다려 주세요", "error": {"backToHome": "홈으로 돌아가기", "notFound": "찾고 있는 페이지를 찾을 수 없습니다", "installationFailed": "설치 실패", "missingSourceChannel": "소스 채널을 찾을 수 없습니다", "authorizeDestination": "{channel_name}의 인증을 진행하세요"}}, "error": {"backToHome": "홈으로 돌아가기", "notFound": "페이지를 찾을 수 없습니다", "notFoundDescription": "찾고 있는 페이지를 찾을 수 없습니다."}, "socialIntegration": {"authorize": "인증", "reAuthorize": "재인증", "newAuthorize": "새 인증", "authorized": "인증됨", "sessionExpired": "세션이 만료되었습니다", "failedToSwitchStatus": "연결 상태 변경 실패", "failedToSetup": "연결 설정 실패", "facebookSetup": "Facebook 연동 설정", "zaloSetup": "Zalo OA 연동 설정", "defaultSetup": "연동 설정"}, "filter": {"allTime": "전체 기간", "today": "오늘", "yesterday": "어제", "lastWeek": "지난주", "lastMonth": "지난달", "last7Days": "지난 7일", "last30Days": "지난 30일", "last90Days": "지난 90일", "thisWeek": "이번 주", "thisMonth": "이번 달", "thisYear": "올해", "customize": "맞춤 설정", "reset": "초기화", "apply": "적용"}, "financial": {"dashboard": {"title": "財務ダッシュボード", "description": "不動産の財務パフォーマンスと指標の概要"}, "timeRange": {"month": "月", "quarter": "四半期", "year": "年"}, "exportReport": "レポートをエクスポート", "metrics": {"totalRevenue": "総収入", "netIncome": "純収入", "occupancyRate": "稼働率", "totalExpenses": "総コスト"}, "charts": {"revenueTrend": "収益のトレンド", "expenseBreakdown": "コスト分析", "occupancyTrend": "稼働率のトレンド", "revenueAnalysis": "収益分析"}, "revenue": "<PERSON><PERSON>h thu", "netIncome": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> ròng", "performance": "<PERSON><PERSON><PERSON>", "profitMargin": "Tỷ suất lợi n<PERSON>n", "totalProperties": "<PERSON>ổng số bất động sản", "totalUnits": "<PERSON><PERSON>ng số căn hộ", "occupiedUnits": "<PERSON><PERSON><PERSON> hộ đã thuê", "vacancyRate": "Tỷ lệ trống", "ytdPerformance": "<PERSON><PERSON><PERSON> suất từ đầu năm", "topProperties": "<PERSON><PERSON><PERSON> động sản hiệu suất cao", "monthlyRevenue": "doanh thu hàng tháng", "expenses": "Chi phí", "alerts": {"title": "<PERSON><PERSON><PERSON> b<PERSON>o tài ch<PERSON>h", "rentDue": "<PERSON><PERSON><PERSON> đến hạn thu tiền thuê", "highExpenses": "<PERSON><PERSON><PERSON> báo chi phí cao", "leaseExpiring": "<PERSON><PERSON><PERSON> đồng sắ<PERSON> hết hạn"}, "revenueAnalytics": {"title": "<PERSON><PERSON> tích doanh thu", "description": "<PERSON><PERSON> tích doanh thu chi tiết với biểu đồ tương tác và xu hướng"}, "expenseAnalysis": {"title": "<PERSON>ân tích chi phí", "description": "<PERSON><PERSON> tích chi phí toàn diện và thông tin tối ưu hóa chi phí"}, "profitAnalysis": {"title": "<PERSON><PERSON> tích lợi n<PERSON>n", "description": "<PERSON> hướng lợi nhuận, biên lợi nhuận và theo dõi hiệu suất ROI"}}, "contracts": {"contractDetails": "<PERSON>", "contractId": "<PERSON><PERSON>", "overview": "<PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON><PERSON>", "progress": "<PERSON>i<PERSON><PERSON>", "daysRemaining": "ng<PERSON><PERSON> còn lại", "openEnded": "<PERSON><PERSON><PERSON>ng giới hạn thời gian", "autoRenewalEnabled": "<PERSON><PERSON> hạn tự động đ<PERSON><PERSON><PERSON> bật", "perMonth": "mỗi tháng", "dayOfMonth": " của mỗi tháng", "sharingTerms": "<PERSON><PERSON><PERSON><PERSON> Sẻ", "paymentSummary": "<PERSON><PERSON>ng <PERSON>", "revenueProjections": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON>ông Tin <PERSON>", "days": "ng<PERSON>y", "quickActions": "<PERSON><PERSON>", "fields": {"property": "<PERSON><PERSON><PERSON>", "unit": "<PERSON><PERSON><PERSON>", "tenant": "<PERSON><PERSON><PERSON><PERSON>", "startDate": "<PERSON><PERSON><PERSON>", "endDate": "<PERSON><PERSON><PERSON>", "rentAmount": "<PERSON><PERSON>", "depositAmount": "Tiền Đặt Cọc", "lateFeeAmount": "<PERSON><PERSON>", "rentDueDay": "<PERSON><PERSON><PERSON>", "profitSharingPercentage": "Phần Tr<PERSON><PERSON> Sẻ <PERSON>ợ<PERSON>n", "revenueSharingPercentage": "<PERSON>ần <PERSON>r<PERSON><PERSON> Sẻ <PERSON>h <PERSON>hu", "noticePeriod": "Thời G<PERSON>"}, "sections": {"financialTerms": "<PERSON><PERSON><PERSON><PERSON>", "termsConditions": "<PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON>"}, "stats": {"totalPayments": "Tổng Thanh Toán", "completedPayments": "<PERSON><PERSON>", "pendingPayments": "Chờ Thanh To<PERSON>", "totalCollected": "<PERSON><PERSON><PERSON>"}, "projections": {"expectedMonthly": "<PERSON><PERSON><PERSON>", "expectedAnnual": "<PERSON><PERSON><PERSON>"}, "actions": {"recordPayment": "<PERSON><PERSON> <PERSON>", "viewPayments": "<PERSON><PERSON>"}, "errors": {"notFound": "<PERSON><PERSON><PERSON><PERSON>", "notFoundDescription": "<PERSON><PERSON><PERSON> đồng bạn đang tìm không tồn tại hoặc đã bị xóa."}, "deleteConfirmation": {"title": "<PERSON><PERSON><PERSON>", "description": "Bạn có chắc chắn muốn xóa hợp đồng này? Hành động này không thể hoàn tác.", "activeContract": "<PERSON><PERSON><PERSON> là hợp đồng đang hoạt động. Việ<PERSON> xóa có thể ảnh hưởng đến các thanh toán và ghi chép hiện tại."}, "messages": {"deleteSuccess": "<PERSON><PERSON><PERSON> hợp đồng thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa hợp đồng"}}, "unit": {"title": "<PERSON><PERSON><PERSON>", "type": {"apartment": "<PERSON><PERSON><PERSON>", "studio": "Studio", "1br": "1 Phòng Ngủ", "2br": "2 Phòng Ngủ", "3br": "3 Phòng Ngủ", "4br": "4 Phòng Ngủ", "penthouse": "Penthouse", "duplex": "Duplex", "commercial": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "office": "<PERSON><PERSON><PERSON>", "retail": "B<PERSON> Lẻ", "warehouse": "<PERSON><PERSON>"}}, "numbers": {"abbreviations": {"thousand": "K", "million": "tr", "billion": "tỷ", "trillion": "nghìn tỷ"}, "currency": {"symbol": "₫", "code": "VND"}}, "payments": {"createPayment": "<PERSON><PERSON><PERSON>", "editPayment": "Chỉnh <PERSON><PERSON><PERSON>", "createDescription": "<PERSON><PERSON><PERSON> bản ghi <PERSON>h toán mới cho tiền thuê, tiền đặt cọc và các khoản phí khác", "editDescription": "<PERSON><PERSON><PERSON> nhật thông tin thanh toán và trạng thái", "sections": {"contract": "<PERSON><PERSON>ông Tin <PERSON>", "paymentDetails": "<PERSON>"}, "fields": {"contract": "<PERSON><PERSON><PERSON>", "amount": "Số Tiền", "paymentDate": "<PERSON><PERSON><PERSON>", "paymentType": "<PERSON><PERSON><PERSON>", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON>", "referenceNumber": "<PERSON><PERSON>", "status": "Trạng <PERSON>", "description": "<PERSON><PERSON>"}, "placeholders": {"contract": "<PERSON><PERSON><PERSON> h<PERSON> đồng", "amount": "0.00", "paymentType": "<PERSON><PERSON><PERSON> lo<PERSON>i thanh toán", "paymentMethod": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "referenceNumber": "<PERSON><PERSON><PERSON><PERSON> số tham chiếu", "status": "<PERSON><PERSON><PERSON> trạng thái", "description": "<PERSON><PERSON><PERSON><PERSON> mô tả hoặc ghi chú"}, "paymentTypes": {"rent": "<PERSON><PERSON><PERSON><PERSON>", "deposit": "Tiền Đặt Cọc", "lateFee": "<PERSON><PERSON> <PERSON>", "maintenance": "Bảo Trì", "other": "K<PERSON><PERSON><PERSON>"}, "paymentMethods": {"cash": "Tiền Mặt", "bankTransfer": "<PERSON><PERSON><PERSON><PERSON>", "creditCard": "Thẻ <PERSON>", "check": "Séc"}, "status": {"pending": "<PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON><PERSON>", "refunded": "<PERSON><PERSON><PERSON>"}, "contractInfo": {"rent": "<PERSON><PERSON><PERSON><PERSON>", "deposit": "Tiền Đặt Cọc", "dueDay": "<PERSON><PERSON><PERSON>"}, "quickFill": {"rent": "<PERSON><PERSON><PERSON>n <PERSON> T<PERSON>ền <PERSON>", "deposit": "<PERSON><PERSON><PERSON><PERSON>"}, "messages": {"createSuccess": "支払いを作成しました", "updateSuccess": "支払いを更新しました", "createError": "支払いを作成できませんでした", "updateError": "支払いを更新できませんでした", "deleteSuccess": "支払いを削除しました", "deleteError": "支払いを削除できませんでした"}}, "permissions": {"staff": {"CREATE_STAFF": "직원 생성", "CREATE_STAFF_TASK": "직원 작업 생성", "CREATE_STAFF_KNOWLEDGE": "직원 지식 생성", "GET_STAFF": "직원 조회", "LIST_STAFF": "직원 목록", "UPDATE_STAFF": "직원 업데이트", "UPDATE_STAFF_ROLE": "직원 역할 업데이트", "UPDATE_STAFF_INTERACT_INFO": "직원 상호작용 정보 업데이트", "DELETE_STAFF_KNOWLEDGE": "직원 지식 삭제", "DELETE_STAFF_TASK": "직원 작업 삭제", "INTEGRATE_STAFF_TO_SOCIAL_PLATFORM": "소셜 플랫폼에 직원 통합"}, "account": {"CREATE_STAFF_ACCOUNT": "직원 계정 생성", "GET_STAFF_ACCOUNT": "직원 계정 조회", "LIST_STAFFS_ACCOUNT": "직원 계정 목록", "UPDATE_STAFF_ACCOUNT": "직원 계정 업데이트", "UPDATE_STAFF_ACCOUNT_AUTHORIZATION": "직원 계정 권한 업데이트", "DISABLE_STAFF_ACCOUNT": "직원 계정 비활성화", "DELETE_STAFF_ACCOUNT": "직원 계정 삭제"}, "activity": {"CREATE_ACTIVITIES": "활동 생성", "LIST_ACTIVITIES": "활동 목록", "LIST_ACTIVITIES_IN_CHARGE": "담당 활동 목록", "UPDATE_ACTIVITIES": "활동 업데이트", "DELETE_ACTIVITIES": "활동 삭제", "ASSIGN_STAFF_TO_ACTIVITIES": "활동에 직원 할당"}, "branch": {"CREATE_BRAND": "브랜드 생성", "GET_BRAND": "브랜드 조회", "LIST_BRAND": "브랜드 목록", "UPDATE_BRAND": "브랜드 업데이트", "DELETE_BRAND": "브랜드 삭제"}, "brand": {"BRAND_LIST_EXPORT_FILE": "브랜드 목록 내보내기 파일"}, "conversation": {"LIST_MESSAGE": "메시지 목록", "LIST_MESSAGE_IN_CHARGE": "담당 메시지 목록", "ASSIGN_STAFF_TO_CONVERSATION": "대화에 직원 할당", "REPLY_MESSAGE": "메시지 답장"}, "customer": {"CREATE_CUSTOMER": "고객 생성", "GET_CUSTOMER": "고객 조회", "LIST_CUSTOMER": "고객 목록", "LIST_CUSTOMER_IN_CHARGE": "담당 고객 목록", "UPDATE_CUSTOMER": "고객 업데이트", "DELETE_CUSTOMER": "고객 삭제", "SHOW_CUSTOMER_PHONE": "고객 전화번호 표시", "CUSTOMER_LIST_EXPORT_FILE": "고객 목록 내보내기 파일", "SHOW_CUSTOMER_GROUP": "고객 그룹 표시"}, "department": {"CREATE_DEPARTMENT": "부서 생성", "GET_DEPARTMENT": "부서 조회", "UPDATE_DEPARTMENT": "부서 업데이트", "UPDATE_DEPARTMENT_DESCRIPTION": "부서 설명 업데이트", "DELETE_DEPARTMENT": "부서 삭제"}, "knowledge": {"CREATE_KNOWLEDGE": "지식 생성", "GET_KNOWLEDGE": "지식 조회", "UPDATE_KNOWLEDGE": "지식 업데이트", "DELETE_KNOWLEDGE": "지식 삭제"}, "opportunity": {"CREATE_OPPORTUNITY": "기회 생성", "GET_OPPORTUNITY": "기회 조회", "LIST_OPPORTUNITY": "기회 목록", "LIST_OPPORTUNITY_IN_CHARGE": "담당 기회 목록", "LIST_OPPORTUNITY_HISTORY": "기회 이력 목록", "LIST_OPPORTUNITY_ORDER_HISTORY": "기회 주문 이력 목록", "UPDATE_OPPORTUNITY": "기회 업데이트", "UPDATE_OPPORTUNITY_EXPECTED_CLOSING_DATE": "기회 예상 종료일 업데이트", "UPDATE_OPPORTUNITY_PRIORITY": "기회 우선순위 업데이트", "UPDATE_OPPORTUNITY_NOTE": "기회 메모 업데이트", "UPDATE_OPPORTUNITY_EXPECTED_REVENUE": "기회 예상 수익 업데이트", "DELETE_OPPORTUNITY": "기회 삭제", "ASSIGN_STAFF_TO_OPPORTUNITY": "기회에 직원 할당", "MARK_OPPORTUNITY_WON_LOST": "기회 승패 표시"}, "order": {"CREATE_ORDER": "주문 생성", "GET_ORDER": "주문 조회", "LIST_ORDER": "주문 목록", "LIST_ORDER_IN_CHARGE": "담당 주문 목록", "UPDATE_ORDER": "주문 업데이트", "UPDATE_ORDER_NOTE": "주문 메모 업데이트", "DELETE_ORDER": "주문 삭제", "ORDER_CONFIRM_STATUS": "주문 확인 상태", "SAVE_DRAFT_ORDER": "초안 주문 저장", "ORDER_CONFIRM_PAID_STATUS": "주문 결제 완료 확인 상태", "ORDER_CONFIRM_COMPLETED_STATUS": "주문 완료 확인 상태", "ORDER_CONFIRM_CANCELED_STATUS": "주문 취소 확인 상태", "ORDER_CONFIRM_PACKING_STATUS": "주문 포장 확인 상태", "ORDER_IMPORT": "주문 가져오기", "ORDER_LIST_EXPORT_FILE": "주문 목록 내보내기 파일"}, "product": {"CREATE_PRODUCT": "상품 생성", "CREATE_PRODUCT_QUICKLY": "상품 빠른 생성", "GET_PRODUCT": "상품 조회", "GET_PRODUCT_PRICE_GROUP": "상품 가격 그룹 조회", "LIST_PRODUCT": "상품 목록", "LIST_PUBLISHED_PRODUCT": "게시된 상품 목록", "UPDATE_PRODUCT": "상품 업데이트", "UPDATE_PRODUCT_FILES": "상품 파일 업데이트", "DELETE_PRODUCT": "상품 삭제", "PRODUCT_LIST_EXPORT_FILE": "상품 목록 내보내기 파일"}, "role": {"CREATE_ROLE": "역할 생성", "LIST_ROLE": "역할 목록", "UPDATE_ROLE": "역할 업데이트", "DELETE_ROLE": "역할 삭제"}, "stage": {"CREATE_STAGE": "단계 생성", "GET_STAGE_TOTAL_EXPECTED_REVENUE": "단계 총 예상 수익 조회", "UPDATE_STAGE": "단계 업데이트", "DELETE_STAGE": "단계 삭제"}, "task": {"CREATE_TASK": "작업 생성", "GET_TASK": "작업 조회", "LIST_TASK": "작업 목록", "UPDATE_TASK": "작업 업데이트", "UPDATE_TASK_PROMPT": "작업 프롬프트 업데이트", "DELETE_TASK": "작업 삭제"}, "variant": {"VARIANT_LIST_EXPORT_FILE": "변형 목록 내보내기 파일"}}}