"use client";

import { Suspense } from "react";

// Components

export default function LayoutDemoPage() {
  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="mx-auto max-w-7xl">
        <div className="mb-6">
          <h1 className="mb-2 text-3xl font-bold text-gray-900">Layout Management System Demo</h1>
          <p className="text-gray-600">
            Visual property layout management with unit mapping capabilities
          </p>
        </div>

        <Suspense
          fallback={
            <div className="flex h-96 items-center justify-center">
              <div className="size-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
            </div>
          }>
          {/* <LayoutManagementComponent /> */}
        </Suspense>
      </div>
    </div>
  );
}
