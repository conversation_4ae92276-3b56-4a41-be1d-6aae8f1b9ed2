import { useCallback, useState } from "react";

import type { Unit } from "../../types";
import type { LayoutData, UnitPosition } from "../components/LayoutViewer";

interface UnitMapping {
  unitId: string;
  layoutId: string;
  positionId: string;
  x: number;
  y: number;
  width: number;
  height: number;
}

export type ViewMode = "grid" | "list";
export type FilterStatus = "all" | "mapped" | "unmapped";

interface UseUnitMappingReturn {
  // State
  selectedLayoutId: string;
  viewMode: ViewMode;
  searchTerm: string;
  filterStatus: FilterStatus;
  selectedUnitId: string | null;
  showDeleteDialog: boolean;
  layoutToDelete: string | null;
  unitMappings: UnitMapping[];
  isEditing: boolean;
  isLoading: boolean;
  error: string | null;

  // Computed values
  currentLayout: LayoutData | undefined;
  filteredUnits: Unit[];
  totalUnits: number;
  mappedUnits: number;
  unmappedUnits: number;
  mappingProgress: number;

  // Actions
  setSelectedLayoutId: (id: string) => void;
  setViewMode: (mode: ViewMode) => void;
  setSearchTerm: (term: string) => void;
  setFilterStatus: (status: FilterStatus) => void;
  setSelectedUnitId: (id: string | null) => void;
  setShowDeleteDialog: (show: boolean) => void;
  setLayoutToDelete: (id: string | null) => void;
  setIsEditing: (editing: boolean) => void;
  setIsLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // Event handlers
  handleLayoutSelect: (layoutId: string) => void;
  handleUnitSelect: (unit: Unit) => void;
  handleUnitPositionChange: (unitPosition: UnitPosition) => void;
  handleSaveLayout: () => Promise<void>;
  handleDeleteLayout: () => void;
  handleShapesChange: (shapes: any[]) => void;

  // Utility functions
  getUnitStatusColor: (status: Unit["status"]) => string;
  getUnitStatusVariant: (
    status: Unit["status"]
  ) => "secondary" | "default" | "destructive" | "outline";
}

interface UseUnitMappingProps {
  property: any; // TODO: Define Property type
  units: Unit[];
  layouts: LayoutData[];
  onLayoutSave?: (layout: LayoutData) => void;
  onLayoutDelete?: (layoutId: string) => void;
  onUnitUpdate?: (unit: Unit) => void;
}

export function useUnitMapping({
  property,
  units,
  layouts,
  onLayoutSave,
  onLayoutDelete,
  onUnitUpdate,
}: UseUnitMappingProps): UseUnitMappingReturn {
  // State
  const [selectedLayoutId, setSelectedLayoutId] = useState<string>(layouts[0]?.id || "");
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState<FilterStatus>("all");
  const [selectedUnitId, setSelectedUnitId] = useState<string | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [layoutToDelete, setLayoutToDelete] = useState<string | null>(null);
  const [unitMappings, setUnitMappings] = useState<UnitMapping[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get current layout
  const currentLayout = layouts.find((l) => l.id === selectedLayoutId);

  // Filter units based on search and status
  const filteredUnits = units.filter((unit) => {
    const matchesSearch =
      unit.unit_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      unit.unit_type.toLowerCase().includes(searchTerm.toLowerCase());

    if (!matchesSearch) return false;

    if (filterStatus === "all") return true;

    const isMapped = unitMappings.some(
      (mapping) => mapping.unitId === unit.id && mapping.layoutId === selectedLayoutId
    );

    return filterStatus === "mapped" ? isMapped : !isMapped;
  });

  // Calculate stats
  const totalUnits = units.length;
  const mappedUnits = unitMappings.filter((m) => m.layoutId === selectedLayoutId).length;
  const unmappedUnits = totalUnits - mappedUnits;
  const mappingProgress = totalUnits > 0 ? (mappedUnits / totalUnits) * 100 : 0;

  // Handle layout selection
  const handleLayoutSelect = useCallback((layoutId: string) => {
    setSelectedLayoutId(layoutId);
    setSelectedUnitId(null);
  }, []);

  // Handle unit selection
  const handleUnitSelect = useCallback((unit: Unit) => {
    setSelectedUnitId(unit.id);
  }, []);

  // Handle unit position change
  const handleUnitPositionChange = useCallback(
    (unitPosition: UnitPosition) => {
      setUnitMappings((prev) => {
        const existing = prev.find(
          (m) => m.unitId === unitPosition.unitId && m.layoutId === selectedLayoutId
        );

        if (existing) {
          return prev.map((m) =>
            m.unitId === unitPosition.unitId && m.layoutId === selectedLayoutId
              ? {
                  ...m,
                  x: unitPosition.x,
                  y: unitPosition.y,
                  width: unitPosition.width,
                  height: unitPosition.height,
                }
              : m
          );
        } else {
          return [
            ...prev,
            {
              unitId: unitPosition.unitId,
              layoutId: selectedLayoutId,
              positionId: unitPosition.id,
              x: unitPosition.x,
              y: unitPosition.y,
              width: unitPosition.width,
              height: unitPosition.height,
            },
          ];
        }
      });
    },
    [selectedLayoutId]
  );

  // Handle save layout
  const handleSaveLayout = useCallback(async () => {
    if (!currentLayout) return;

    setIsLoading(true);
    setError(null);

    try {
      const updatedLayout: LayoutData = {
        ...currentLayout,
        unitPositions: unitMappings
          .filter((m) => m.layoutId === selectedLayoutId)
          .map((m) => {
            const unit = units.find((u) => u.id === m.unitId);
            return {
              id: m.positionId,
              unitId: m.unitId,
              x: m.x,
              y: m.y,
              width: m.width,
              height: m.height,
            };
          }),
      };

      await onLayoutSave?.(updatedLayout);
      setIsEditing(false);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to save layout";
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [currentLayout, unitMappings, selectedLayoutId, units, onLayoutSave]);

  // Handle delete layout
  const handleDeleteLayout = useCallback(() => {
    if (layoutToDelete) {
      onLayoutDelete?.(layoutToDelete);
      setLayoutToDelete(null);
      setShowDeleteDialog(false);

      // Select first available layout
      const remainingLayouts = layouts.filter((l) => l.id !== layoutToDelete);
      if (remainingLayouts.length > 0) {
        setSelectedLayoutId(remainingLayouts[0].id);
      }
    }
  }, [layoutToDelete, onLayoutDelete, layouts]);

  // Get unit status color
  const getUnitStatusColor = useCallback((status: Unit["status"]) => {
    switch (status) {
      case "available":
        return "bg-success";
      case "occupied":
        return "bg-primary";
      case "maintenance":
        return "bg-destructive";
      case "inactive":
        return "bg-muted-foreground";
      default:
        return "bg-muted-foreground";
    }
  }, []);

  // Get unit status badge variant
  const getUnitStatusVariant = useCallback((status: Unit["status"]) => {
    switch (status) {
      case "available":
        return "secondary" as const;
      case "occupied":
        return "default" as const;
      case "maintenance":
        return "destructive" as const;
      case "inactive":
        return "outline" as const;
      default:
        return "outline" as const;
    }
  }, []);

  // Handle shapes change callback
  const handleShapesChange = useCallback(
    (shapes: any[]) => {
      const newMappings = shapes.map((shape: any) => ({
        unitId: shape.unitId,
        layoutId: selectedLayoutId,
        positionId: shape.id,
        x: shape.x,
        y: shape.y,
        width: shape.width,
        height: shape.height,
      }));
      setUnitMappings((prev) => [
        ...prev.filter((m) => m.layoutId !== selectedLayoutId),
        ...newMappings,
      ]);
    },
    [selectedLayoutId]
  );

  return {
    // State
    selectedLayoutId,
    viewMode,
    searchTerm,
    filterStatus,
    selectedUnitId,
    showDeleteDialog,
    layoutToDelete,
    unitMappings,
    isEditing,
    isLoading,
    error,

    // Computed values
    currentLayout,
    filteredUnits,
    totalUnits,
    mappedUnits,
    unmappedUnits,
    mappingProgress,

    // Actions
    setSelectedLayoutId,
    setViewMode,
    setSearchTerm,
    setFilterStatus,
    setSelectedUnitId,
    setShowDeleteDialog,
    setLayoutToDelete,
    setIsEditing,
    setIsLoading,
    setError,

    // Event handlers
    handleLayoutSelect,
    handleUnitSelect,
    handleUnitPositionChange,
    handleSaveLayout,
    handleDeleteLayout,
    handleShapesChange,

    // Utility functions
    getUnitStatusColor,
    getUnitStatusVariant,
  };
}
