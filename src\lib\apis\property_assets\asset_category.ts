import { PROPERTY_ASSETS_ENDPOINTS } from "../../../constants/endpoints";
import { privateApi } from "../../api_helper";
import { ResponseAxiosDetail, ResponseList } from "../types/common";
import type {
  AssetCategory,
  CreateAssetCategory,
  UpdateAssetCategory,
} from "../types/property_assets/asset_category";

// Asset Category APIs
export const assetCategoryApi = {
  // Get all asset categories with optional filtering and pagination
  list: async (params?: Record<string, unknown>) => {
    return await privateApi.get<ResponseList<AssetCategory>>(
      PROPERTY_ASSETS_ENDPOINTS.ASSET_CATEGORIES,
      {
        params,
      }
    );
  },

  // Create a new asset category
  create: async (data: CreateAssetCategory) => {
    return await privateApi.post<ResponseAxiosDetail<AssetCategory>>(
      PROPERTY_ASSETS_ENDPOINTS.CREATE_ASSET_CATEGORY,
      data
    );
  },

  // Update an existing asset category
  update: async (id: string, data: UpdateAssetCategory) => {
    return await privateApi.put<ResponseAxiosDetail<AssetCategory>>(
      PROPERTY_ASSETS_ENDPOINTS.UPDATE_ASSET_CATEGORY.replace(":id", id),
      data
    );
  },

  // Delete an asset category
  delete: async (id: string) => {
    return await privateApi.delete<{ success: boolean; message?: string }>(
      PROPERTY_ASSETS_ENDPOINTS.DELETE_ASSET_CATEGORY.replace(":id", id)
    );
  },

  // Get asset category by ID
  getById: async (id: string) => {
    return await privateApi.get<ResponseAxiosDetail<AssetCategory>>(
      PROPERTY_ASSETS_ENDPOINTS.GET_ASSET_CATEGORY.replace(":id", id)
    );
  },

  // Get asset categories by parent category ID
  getByParentCategory: async (
    parentCategoryId: string,
    params?: {
      page?: number;
      limit?: number;
      search?: string;
    }
  ) => {
    return await privateApi.get<ResponseList<AssetCategory>>(
      PROPERTY_ASSETS_ENDPOINTS.ASSET_CATEGORIES,
      {
        params: {
          ...params,
          parent_category_id: parentCategoryId,
        },
      }
    );
  },

  // Get root asset categories (no parent)
  getRootCategories: async (params?: { page?: number; limit?: number; search?: string }) => {
    return await privateApi.get<ResponseList<AssetCategory>>(
      PROPERTY_ASSETS_ENDPOINTS.ASSET_CATEGORIES,
      {
        params: {
          ...params,
          parent_category_id: "null",
        },
      }
    );
  },
};
