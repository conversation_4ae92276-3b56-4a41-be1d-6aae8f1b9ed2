"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { useSubscriptionByIdPolling } from "@/features/subscription/hooks/subscription";

import { subscriptionApi } from "@/lib/apis/crm_plan/subscription";
import { Plan } from "@/lib/apis/crm_plan/types";

import { usePlan } from "../../hooks";

export enum RegistrationTime {
  MONTHLY = "MONTHLY",
  YEARLY = "YEARLY",
}

export interface RegistrationTimeOption {
  value: RegistrationTime;
  label: string;
  multiplier: number;
}

export interface CompanyInformation {
  companyName: string;
  taxId: string;
  address: string;
  email: string;
}

export interface CheckoutFormData {
  planId: string;
  duration: RegistrationTime;
  invoice: {
    companyName: string;
    taxId: string;
    address: string;
    email: string;
  };
}

export interface CheckoutData {
  // Order Information
  subscriptionPlan: Plan;
  registrationTime: RegistrationTime;
  price: number;
  currency: string;
  creationDate: string;
  paymentMethod: string;
  companyInformation: CompanyInformation;

  totalAmount: number;

  // QR Payment - these will be populated from subscriptionDetails.qr_info
  accountName?: string;
  accountNumber?: string;
  bankName?: string;
  transferContent?: string;
}

export const useCheckout = ({
  planId,
  initialRegistrationTime = RegistrationTime.YEARLY,
  subscriptionId,
}: {
  planId: string;
  initialRegistrationTime?: RegistrationTime;
  subscriptionId?: string;
}) => {
  const { t } = useTranslation();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [showCompanyInfo, setShowCompanyInfo] = useState(false);
  const [isCompanyInfoCompleted, setIsCompanyInfoCompleted] = useState(false);
  const [existingSubscription, setExistingSubscription] = useState<any>(null);
  const [isCheckingSubscription, setIsCheckingSubscription] = useState(true);
  const [isPaymentMode, setIsPaymentMode] = useState(false);
  const [subscriptionDetails, setSubscriptionDetails] = useState<any>(null);

  useEffect(() => {
    if (subscriptionDetails) {
      setIsPaymentMode(true);
    }
  }, [subscriptionDetails]);
  const { data: subscriptionPlan, isLoading: isLoadingPlan } = usePlan(planId);

  // Poll subscription status if subscriptionId is provided
  const { data: polledSubscription } = useSubscriptionByIdPolling(
    subscriptionId || "",
    !!subscriptionId,
    3000
  );

  useEffect(() => {
    if (polledSubscription) {
      setSubscriptionDetails(polledSubscription);
    }
  }, [polledSubscription]);

  // Form setup
  const form = useForm<CheckoutFormData>({
    defaultValues: {
      planId: planId,
      duration: initialRegistrationTime,
      invoice: {
        companyName: "",
        taxId: "",
        address: "",
        email: "",
      },
    },
  });

  const {
    watch,
    setValue,
    handleSubmit,
    formState: { errors },
  } = form;
  const formData = watch();

  // Fetch subscription details if subscriptionId is provided
  useEffect(() => {
    const fetchSubscriptionDetails = async () => {
      if (!subscriptionId) return;
      if (subscriptionDetails) return;
      setIsCheckingSubscription(true);
      try {
        const response = await subscriptionApi.getById(subscriptionId);
        const subscription = response.data;

        setSubscriptionDetails(subscription);
        setIsPaymentMode(true);
      } catch (error) {
        console.error("Failed to fetch subscription details:", error);
        toast.error("Failed to fetch subscription details");
      } finally {
        setIsCheckingSubscription(false);
      }
    };

    fetchSubscriptionDetails();
  }, [subscriptionId, subscriptionDetails]);

  const registrationTimeOptions: RegistrationTimeOption[] = [
    { value: RegistrationTime.MONTHLY, label: "1 month", multiplier: 1 },
    { value: RegistrationTime.YEARLY, label: "12 months", multiplier: 1 },
  ];

  // Get price directly from plan based on registration time
  const currentPrice =
    formData.duration === RegistrationTime.YEARLY
      ? subscriptionPlan?.duration.YEARLY.sale_price || 0
      : subscriptionPlan?.duration.MONTHLY.sale_price || 0;

  // Calculate total based on company info completion
  const calculateTotal = () => {
    const basePrice = currentPrice;
    return basePrice;
  };

  const totalAmount = calculateTotal();

  // Mock data - in real app this would come from API or props
  const checkoutData: CheckoutData = {
    // Order Information
    subscriptionPlan: subscriptionPlan as Plan,
    registrationTime: formData.duration,
    price: currentPrice,
    currency: subscriptionPlan?.service?.currency || "VND", // Default currency for Vietnamese market
    creationDate: new Date().toLocaleDateString("vi-VN"),
    paymentMethod: "Bank transfer",
    companyInformation: formData.invoice,
    totalAmount: totalAmount,

    // QR Payment - these will be populated from subscriptionDetails.qr_info
    accountName: "",
    accountNumber: "",
    bankName: "",
    transferContent: "",
  };

  const handleIssueInvoice = async () => {
    setShowCompanyInfo(!showCompanyInfo);
  };
  const handleCancelOrder = () => {
    router.push("/subscription");
    setIsPaymentMode(false);
  };

  const handleConfirmCompanyInfo = () => {
    const hasInvoiceInfo = Boolean(
      formData.invoice.companyName &&
        formData.invoice.taxId &&
        formData.invoice.address &&
        formData.invoice.email
    );
    setIsCompanyInfoCompleted(hasInvoiceInfo);
  };

  const handleRemoveCompanyInfo = () => {
    setIsCompanyInfoCompleted(false);
    setValue("invoice", {
      companyName: "",
      taxId: "",
      address: "",
      email: "",
    } as CompanyInformation);
  };

  const handleUpdateCompanyInfo = (field: keyof CompanyInformation, value: string) => {
    setValue(`invoice.${field}`, value);
  };

  const handleRegistrationTimeChange = (value: RegistrationTime) => {
    setValue("duration", value);

    // Update URL with new time parameter - only on client side
    if (typeof window !== "undefined" && typeof document !== "undefined") {
      const url = new URL(window.location.href);
      url.searchParams.set("time", value.toString());

      // Update browser history without page reload
      window.history.pushState({}, "", url.toString());
    }
  };

  const onSubmit = async (data: CheckoutFormData) => {
    setIsLoading(true);
    try {
      const subscriptionPayload: any = {
        plan_id: data.planId,
        billing_cycle: data.duration,
      };
      const hasInvoiceInfo =
        data.invoice.companyName &&
        data.invoice.taxId &&
        data.invoice.address &&
        data.invoice.email;
      if (hasInvoiceInfo) {
        subscriptionPayload.invoice = {
          company_name: data.invoice.companyName,
          tax_number: data.invoice.taxId,
          address: data.invoice.address,
          company_email: data.invoice.email,
        };
      }

      const response = await subscriptionApi.create(subscriptionPayload);
      setSubscriptionDetails(response.data);
      const subscriptionId = response.data.id;
      toast.success("Subscription created successfully");
      // Update only the sub-id param, keep all other params
      if (typeof window !== "undefined") {
        const url = new URL(window.location.href);
        url.searchParams.set("sub-id", subscriptionId);
        router.push((url.pathname + "?" + url.searchParams.toString()) as any);
      }
    } catch (error) {
      console.error("Failed to create subscription:", error);
      toast.error("Failed to create subscription");
    } finally {
      setIsLoading(false);
    }
  };
  return {
    form,
    formData,
    errors,
    checkoutData,
    isLoading,
    isLoadingPlan,
    isCheckingSubscription,
    existingSubscription,
    showCompanyInfo,
    isCompanyInfoCompleted,
    registrationTimeOptions,
    currentPrice,
    isPaymentMode,
    subscriptionDetails,
    totalAmount,
    handleIssueInvoice,
    handleConfirmCompanyInfo,
    handleRemoveCompanyInfo,
    handleUpdateCompanyInfo,
    handleRegistrationTimeChange,
    handleSubmit: handleSubmit(onSubmit),
    handleCancelOrder,
  };
};
