import { useQuery } from "@tanstack/react-query";

import { permissionsApi, type PermissionGroup } from "@/lib/apis/permission";

// Query keys for permissions
export const permissionKeys = {
  all: ["permissions"] as const,
  lists: () => [...permissionKeys.all, "list"] as const,
  list: () => [...permissionKeys.lists()] as const,
  details: () => [...permissionKeys.all, "detail"] as const,
  detail: (id: string) => [...permissionKeys.details(), id] as const,
};

// Hook for fetching permissions using TanStack Query
export const usePermissions = () => {
  return useQuery<PermissionGroup[]>({
    queryKey: permissionKeys.list(),
    queryFn: async () => {
      const response = await permissionsApi.list();
      return response;
    },
  });
};

// Helper function to reset all permissions to unchecked
export const resetPermissions = (groups: PermissionGroup[]): PermissionGroup[] => {
  return groups.map((group) => ({
    ...group,
    modules: group.modules.map((module) => ({
      ...module,
      permissions: module.permissions.map((permission) => ({
        ...permission,
        checked: false,
      })),
    })),
  }));
};

// Helper function to get selected permissions
export const getSelectedPermissions = (groups: PermissionGroup[]): string[] => {
  return groups
    .flatMap((group) => group.modules)
    .flatMap((module) => module.permissions)
    .filter((permission) => permission.checked)
    .map((permission) => permission.id);
};

// Helper function to set specific permissions as checked
export const setPermissionsChecked = (
  groups: PermissionGroup[],
  permissionIds: string[]
): PermissionGroup[] => {
  return groups.map((group) => ({
    ...group,
    modules: group.modules.map((module) => ({
      ...module,
      permissions: module.permissions.map((permission) => ({
        ...permission,
        checked: permissionIds.includes(permission.id),
      })),
    })),
  }));
};

// Helper function to toggle all permissions in a module
export const toggleModulePermissions = (
  groups: PermissionGroup[],
  groupId: string,
  moduleId: string
): PermissionGroup[] => {
  return groups.map((group) => {
    if (group.id === groupId) {
      return {
        ...group,
        modules: group.modules.map((module) => {
          if (module.id === moduleId) {
            const allChecked = module.permissions.every((p) => p.checked);
            return {
              ...module,
              permissions: module.permissions.map((permission) => ({
                ...permission,
                checked: !allChecked,
              })),
            };
          }
          return module;
        }),
      };
    }
    return group;
  });
};

// Helper function to toggle group permissions
export const toggleGroupPermissions = (
  groups: PermissionGroup[],
  groupId: string,
  moduleId: string,
  typeName: "CRUD" | "ACTION"
): PermissionGroup[] => {
  return groups.map((group) => {
    if (group.id === groupId) {
      return {
        ...group,
        modules: group.modules.map((module) => {
          if (module.id === moduleId) {
            const typePermissions = module.permissions.filter((p) => p.group === typeName);
            const allChecked = typePermissions.every((p) => p.checked);

            return {
              ...module,
              permissions: module.permissions.map((permission) => ({
                ...permission,
                checked: permission.group === typeName ? !allChecked : permission.checked,
              })),
            };
          }
          return module;
        }),
      };
    }
    return group;
  });
};

// Default expanded modules (first module of first group expanded by default)
export const getDefaultExpandedModules = (groups: PermissionGroup[]): Set<string> => {
  if (groups.length > 0 && groups[0].modules.length > 0) {
    return new Set([`${groups[0].id}_${groups[0].modules[0].id}`]);
  }
  return new Set();
};

// Default active tab (first group)
export const getDefaultActiveTab = (groups: PermissionGroup[]): string => {
  return groups.length > 0 ? groups[0].id : "";
};
