// Property type options
export const PROPERTY_TYPES = [
  { value: "residential", label: "Residential" },
  { value: "commercial", label: "Commercial" },
  { value: "mixed", label: "Mixed Use" },
];

// Property status options
export const PROPERTY_STATUSES = [
  { value: "active", label: "Active" },
  { value: "inactive", label: "Inactive" },
];

// Property filter options
export const PROPERTY_FILTER_OPTIONS = [
  { value: "all", label: "All Properties" },
  { value: "residential", label: "Residential" },
  { value: "commercial", label: "Commercial" },
  { value: "mixed", label: "Mixed Use" },
  { value: "active", label: "Active" },
  { value: "inactive", label: "Inactive" },
];

// Property validation rules
export const PROPERTY_VALIDATION_RULES = {
  name: {
    minLength: 2,
    maxLength: 100,
  },
  description: {
    maxLength: 500,
  },
  address: {
    street: { minLength: 5, maxLength: 200 },
    city: { minLength: 2, maxLength: 100 },
    state: { minLength: 2, maxLength: 100 },
    zipCode: { minLength: 5, maxLength: 10 },
    country: { minLength: 2, maxLength: 100 },
  },
  owner: {
    name: { minLength: 2, maxLength: 100 },
    email: { maxLength: 255 },
    phone: { minLength: 10, maxLength: 20 },
  },
  purchase: {
    price: { min: 0, max: 999999999 },
  },
};

// Property image settings
export const PROPERTY_IMAGE_SETTINGS = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: ["image/jpeg", "image/png", "image/webp"],
  maxImages: 20,
  thumbnailSize: { width: 300, height: 200 },
  fullSize: { width: 1920, height: 1080 },
};

// Property layout settings
export const PROPERTY_LAYOUT_SETTINGS = {
  maxFloorNumber: 100,
  maxScale: 5.0,
  minScale: 0.1,
  defaultScale: 1.0,
  maxOffset: 10000,
  minOffset: -10000,
};

// Property financial categories
export const PROPERTY_FINANCIAL_CATEGORIES = [
  { value: "rental_income", label: "Rental Income" },
  { value: "other_income", label: "Other Income" },
  { value: "maintenance_expenses", label: "Maintenance Expenses" },
  { value: "utility_expenses", label: "Utility Expenses" },
  { value: "insurance_expenses", label: "Insurance Expenses" },
  { value: "property_tax_expenses", label: "Property Tax" },
  { value: "management_expenses", label: "Management Expenses" },
  { value: "other_expenses", label: "Other Expenses" },
];

// Property amenities options
export const PROPERTY_AMENITIES_OPTIONS = [
  { value: "parking", label: "Parking" },
  { value: "elevator", label: "Elevator" },
  { value: "security", label: "Security System" },
  { value: "gym", label: "Gym" },
  { value: "pool", label: "Swimming Pool" },
  { value: "garden", label: "Garden" },
  { value: "balcony", label: "Balcony" },
  { value: "storage", label: "Storage" },
  { value: "laundry", label: "Laundry Room" },
  { value: "playground", label: "Playground" },
];

// Property document types
export const PROPERTY_DOCUMENT_TYPES = [
  { value: "deed", label: "Property Deed" },
  { value: "survey", label: "Property Survey" },
  { value: "inspection", label: "Inspection Report" },
  { value: "insurance", label: "Insurance Policy" },
  { value: "tax_assessment", label: "Tax Assessment" },
  { value: "floor_plan", label: "Floor Plan" },
  { value: "photos", label: "Property Photos" },
  { value: "contracts", label: "Contracts" },
  { value: "permits", label: "Building Permits" },
  { value: "other", label: "Other" },
];
