"use client";

import { useState } from "react";
import {
  Activity,
  AlertCircle,
  ArrowUpDown,
  Calendar,
  Check,
  CheckCircle,
  CheckSquare,
  ChevronDown,
  ChevronRight,
  Clock,
  Copy,
  DollarSign,
  Download,
  Edit,
  Eye,
  FileText,
  Filter,
  Home,
  Plus,
  RefreshCw,
  Save,
  Search,
  Settings,
  Square,
  Target,
  Trash2,
  TrendingUp,
  Undo,
  Upload,
  Users,
  X,
} from "lucide-react";
import { useTranslation } from "react-i18next";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";

interface BulkUnitOperationsProps {
  className?: string;
}

export function BulkUnitOperations({ className }: BulkUnitOperationsProps) {
  const { t } = useTranslation();
  const [selectedUnits, setSelectedUnits] = useState<string[]>([]);
  const [bulkAction, setBulkAction] = useState<string>("");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedProperty, setSelectedProperty] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [isEditMode, setIsEditMode] = useState(false);
  const [isBulkDialogOpen, setIsBulkDialogOpen] = useState(false);
  const [isProgressDialogOpen, setIsProgressDialogOpen] = useState(false);
  const [operationProgress, setOperationProgress] = useState(0);

  // Mock units data
  const units = [
    {
      id: "1",
      unitNumber: "101",
      property: "Sunset Apartments",
      type: "1-Bedroom",
      status: "Occupied",
      tenant: "John Smith",
      rent: 2200,
      leaseEnd: "2024-05-31",
      sqft: 750,
      floor: 1,
      balcony: true,
      pets: false,
      smoking: false,
      lastInspection: "2024-01-15",
      nextMaintenance: "2024-06-01",
      issues: [],
      revenue: 26400,
      occupancyRate: 100,
    },
    {
      id: "2",
      unitNumber: "205",
      property: "Downtown Lofts",
      type: "2-Bedroom",
      status: "Vacant",
      tenant: null,
      rent: 3500,
      leaseEnd: null,
      sqft: 1200,
      floor: 2,
      balcony: true,
      pets: true,
      smoking: false,
      lastInspection: "2024-01-10",
      nextMaintenance: "2024-07-10",
      issues: ["Paint touch-up needed"],
      revenue: 35000,
      occupancyRate: 83.3,
    },
    {
      id: "3",
      unitNumber: "103",
      property: "Garden View Complex",
      type: "Studio",
      status: "Maintenance",
      tenant: null,
      rent: 1800,
      leaseEnd: null,
      sqft: 625,
      floor: 1,
      balcony: false,
      pets: false,
      smoking: false,
      lastInspection: "2024-02-01",
      nextMaintenance: "2024-03-01",
      issues: ["Plumbing repair", "Flooring replacement"],
      revenue: 18000,
      occupancyRate: 75.0,
    },
    {
      id: "4",
      unitNumber: "301",
      property: "Sunset Apartments",
      type: "2-Bedroom",
      status: "Occupied",
      tenant: "Sarah Wilson",
      rent: 2800,
      leaseEnd: "2024-08-15",
      sqft: 1100,
      floor: 3,
      balcony: true,
      pets: true,
      smoking: false,
      lastInspection: "2024-01-20",
      nextMaintenance: "2024-04-20",
      issues: [],
      revenue: 33600,
      occupancyRate: 100,
    },
    {
      id: "5",
      unitNumber: "404",
      property: "Downtown Lofts",
      type: "3-Bedroom",
      status: "Occupied",
      tenant: "Mike Johnson",
      rent: 4200,
      leaseEnd: "2024-12-31",
      sqft: 1500,
      floor: 4,
      balcony: true,
      pets: false,
      smoking: false,
      lastInspection: "2024-01-05",
      nextMaintenance: "2024-05-05",
      issues: [],
      revenue: 50400,
      occupancyRate: 100,
    },
  ];

  const bulkActions = [
    {
      value: "rent-increase",
      label: "Apply Rent Increase",
      icon: DollarSign,
      description: "Increase rent for selected units",
    },
    {
      value: "status-change",
      label: "Change Status",
      icon: Activity,
      description: "Update status for multiple units",
    },
    {
      value: "maintenance-schedule",
      label: "Schedule Maintenance",
      icon: Settings,
      description: "Schedule maintenance for selected units",
    },
    {
      value: "inspection-schedule",
      label: "Schedule Inspection",
      icon: CheckCircle,
      description: "Schedule inspections for multiple units",
    },
    {
      value: "policy-update",
      label: "Update Policies",
      icon: FileText,
      description: "Update pet/smoking policies",
    },
    {
      value: "tenant-notice",
      label: "Send Notice",
      icon: AlertCircle,
      description: "Send bulk notices to tenants",
    },
    {
      value: "data-export",
      label: "Export Data",
      icon: Download,
      description: "Export unit data to CSV/Excel",
    },
    {
      value: "copy-settings",
      label: "Copy Settings",
      icon: Copy,
      description: "Copy settings from one unit to others",
    },
  ];

  const filteredUnits = units.filter((unit) => {
    const matchesSearch =
      searchTerm === "" ||
      unit.unitNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      unit.property.toLowerCase().includes(searchTerm.toLowerCase()) ||
      unit.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (unit.tenant && unit.tenant.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesProperty = selectedProperty === "all" || unit.property === selectedProperty;
    const matchesStatus = selectedStatus === "all" || unit.status === selectedStatus;

    return matchesSearch && matchesProperty && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Occupied":
        return "bg-success/10 text-success border-success/20";
      case "Vacant":
        return "bg-warning/10 text-warning border-warning/20";
      case "Maintenance":
        return "bg-destructive/10 text-destructive border-destructive/20";
      default:
        return "bg-muted text-muted-foreground border-muted-foreground/20";
    }
  };

  const handleSelectAll = () => {
    if (selectedUnits.length === filteredUnits.length) {
      setSelectedUnits([]);
    } else {
      setSelectedUnits(filteredUnits.map((unit) => unit.id));
    }
  };

  const handleSelectUnit = (unitId: string) => {
    setSelectedUnits((prev) =>
      prev.includes(unitId) ? prev.filter((id) => id !== unitId) : [...prev, unitId]
    );
  };

  const executeBulkAction = async () => {
    setIsProgressDialogOpen(true);
    setOperationProgress(0);

    // Simulate bulk operation progress
    for (let i = 0; i <= 100; i += 10) {
      await new Promise((resolve) => setTimeout(resolve, 200));
      setOperationProgress(i);
    }

    setIsProgressDialogOpen(false);
    setIsBulkDialogOpen(false);
    setSelectedUnits([]);
    setBulkAction("");
  };

  const selectedAction = bulkActions.find((action) => action.value === bulkAction);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-xl font-semibold text-foreground">Bulk Unit Operations</h2>
          <p className="text-sm text-muted-foreground">
            Manage multiple units efficiently with bulk operations and batch updates
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {selectedUnits.length > 0 && (
            <>
              <Badge variant="outline" className="mr-2">
                {selectedUnits.length} selected
              </Badge>
              <Dialog open={isBulkDialogOpen} onOpenChange={setIsBulkDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Settings className="mr-2 size-4" />
                    Bulk Actions
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Bulk Operations</DialogTitle>
                    <DialogDescription>
                      Apply actions to {selectedUnits.length} selected units
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label>Select Action</Label>
                      <Select value={bulkAction} onValueChange={setBulkAction}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose an action" />
                        </SelectTrigger>
                        <SelectContent>
                          {bulkActions.map((action) => (
                            <SelectItem key={action.value} value={action.value}>
                              <div className="flex items-center space-x-2">
                                <action.icon className="size-4" />
                                <div>
                                  <div>{action.label}</div>
                                  <div className="text-xs text-muted-foreground">
                                    {action.description}
                                  </div>
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Action-specific forms */}
                    {bulkAction === "rent-increase" && (
                      <div className="space-y-4 rounded-lg bg-primary/10 p-4">
                        <h4 className="font-medium">Rent Increase Details</h4>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label>Increase Type</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="Select type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="percentage">Percentage</SelectItem>
                                <SelectItem value="fixed">Fixed Amount</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <Label>Amount</Label>
                            <Input placeholder="5% or $100" />
                          </div>
                        </div>
                        <div>
                          <Label>Effective Date</Label>
                          <Input type="date" />
                        </div>
                        <div>
                          <Label>Notice Period (days)</Label>
                          <Input type="number" placeholder="30" />
                        </div>
                      </div>
                    )}

                    {bulkAction === "status-change" && (
                      <div className="space-y-4 rounded-lg bg-warning/10 p-4">
                        <h4 className="font-medium">Status Change</h4>
                        <div>
                          <Label>New Status</Label>
                          <Select>
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Available">Available</SelectItem>
                              <SelectItem value="Occupied">Occupied</SelectItem>
                              <SelectItem value="Maintenance">Maintenance</SelectItem>
                              <SelectItem value="Reserved">Reserved</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label>Reason/Notes</Label>
                          <Textarea placeholder="Reason for status change..." />
                        </div>
                      </div>
                    )}

                    {bulkAction === "maintenance-schedule" && (
                      <div className="space-y-4 rounded-lg bg-warning/10 p-4">
                        <h4 className="font-medium">Maintenance Scheduling</h4>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label>Maintenance Type</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="Select type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="routine">Routine</SelectItem>
                                <SelectItem value="preventive">Preventive</SelectItem>
                                <SelectItem value="emergency">Emergency</SelectItem>
                                <SelectItem value="upgrade">Upgrade</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <Label>Priority</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="Select priority" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="low">Low</SelectItem>
                                <SelectItem value="medium">Medium</SelectItem>
                                <SelectItem value="high">High</SelectItem>
                                <SelectItem value="urgent">Urgent</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <div>
                          <Label>Scheduled Date</Label>
                          <Input type="date" />
                        </div>
                        <div>
                          <Label>Description</Label>
                          <Textarea placeholder="Maintenance description..." />
                        </div>
                      </div>
                    )}

                    {bulkAction === "policy-update" && (
                      <div className="space-y-4 rounded-lg bg-secondary/10 p-4">
                        <h4 className="font-medium">Policy Updates</h4>
                        <div className="space-y-3">
                          <div className="flex items-center space-x-2">
                            <Checkbox id="pets" />
                            <Label htmlFor="pets">Update Pet Policy</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Checkbox id="smoking" />
                            <Label htmlFor="smoking">Update Smoking Policy</Label>
                          </div>
                          <div>
                            <Label>Pet Policy</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="Select policy" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="allowed">Pets Allowed</SelectItem>
                                <SelectItem value="cats-only">Cats Only</SelectItem>
                                <SelectItem value="small-pets">Small Pets Only</SelectItem>
                                <SelectItem value="no-pets">No Pets</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsBulkDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={executeBulkAction} disabled={!bulkAction}>
                      <Check className="mr-2 size-4" />
                      Execute Action
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </>
          )}
          <Button variant="outline">
            <Upload className="mr-2 size-4" />
            Import
          </Button>
          <Button variant="outline">
            <Download className="mr-2 size-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Filters & Search</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
            <div>
              <Label>Search Units</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Search by unit, tenant, or property"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div>
              <Label>Property</Label>
              <Select value={selectedProperty} onValueChange={setSelectedProperty}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Properties</SelectItem>
                  <SelectItem value="Sunset Apartments">Sunset Apartments</SelectItem>
                  <SelectItem value="Downtown Lofts">Downtown Lofts</SelectItem>
                  <SelectItem value="Garden View Complex">Garden View Complex</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Status</Label>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="Occupied">Occupied</SelectItem>
                  <SelectItem value="Vacant">Vacant</SelectItem>
                  <SelectItem value="Maintenance">Maintenance</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button variant="outline" className="w-full">
                <Filter className="mr-2 size-4" />
                Advanced Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Units Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Units ({filteredUnits.length})</CardTitle>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={handleSelectAll}>
                {selectedUnits.length === filteredUnits.length ? (
                  <CheckSquare className="mr-2 size-4" />
                ) : (
                  <Square className="mr-2 size-4" />
                )}
                Select All
              </Button>
              <Button variant="outline" size="sm">
                <RefreshCw className="mr-2 size-4" />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {filteredUnits.map((unit) => (
              <Card
                key={unit.id}
                className={`transition-colors ${
                  selectedUnits.includes(unit.id)
                    ? "border-primary/20 bg-primary/10"
                    : "hover:bg-muted/50"
                }`}>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-4">
                    {/* Selection Checkbox */}
                    <Checkbox
                      checked={selectedUnits.includes(unit.id)}
                      onCheckedChange={() => handleSelectUnit(unit.id)}
                    />

                    {/* Unit Info */}
                    <div className="grid flex-1 grid-cols-1 items-center gap-4 md:grid-cols-6">
                      <div>
                        <div className="font-medium">Unit {unit.unitNumber}</div>
                        <div className="text-sm text-muted-foreground">{unit.property}</div>
                      </div>

                      <div>
                        <div className="text-sm text-muted-foreground">Type</div>
                        <div className="font-medium">{unit.type}</div>
                      </div>

                      <div>
                        <Badge className={getStatusColor(unit.status)}>{unit.status}</Badge>
                        {unit.tenant && (
                          <div className="mt-1 text-xs text-muted-foreground">{unit.tenant}</div>
                        )}
                      </div>

                      <div>
                        <div className="text-sm text-muted-foreground">Rent</div>
                        <div className="font-medium">${unit.rent.toLocaleString()}</div>
                      </div>

                      <div>
                        <div className="text-sm text-muted-foreground">Sq Ft</div>
                        <div className="font-medium">{unit.sqft}</div>
                      </div>

                      <div className="flex items-center space-x-2">
                        {unit.issues.length > 0 && (
                          <Badge variant="outline" className="text-warning">
                            {unit.issues.length} Issues
                          </Badge>
                        )}
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm">
                              Actions
                              <ChevronDown className="ml-1 size-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <Eye className="mr-2 size-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 size-4" />
                              Edit Unit
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Calendar className="mr-2 size-4" />
                              Schedule Maintenance
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Copy className="mr-2 size-4" />
                              Duplicate Settings
                            </DropdownMenuItem>
                            <DropdownMenuItem className="text-destructive">
                              <Trash2 className="mr-2 size-4" />
                              Delete Unit
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </div>

                  {/* Expandable Details */}
                  <Accordion type="single" collapsible className="mt-4">
                    <AccordionItem value="details" className="border-none">
                      <AccordionTrigger className="py-2 hover:no-underline">
                        <span className="text-sm text-muted-foreground">View Details</span>
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="grid grid-cols-2 gap-4 pt-2 text-sm md:grid-cols-4">
                          <div>
                            <span className="text-muted-foreground">Floor: </span>
                            <span>{unit.floor}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Balcony: </span>
                            <span>{unit.balcony ? "Yes" : "No"}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Pets: </span>
                            <span>{unit.pets ? "Allowed" : "Not Allowed"}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Smoking: </span>
                            <span>{unit.smoking ? "Allowed" : "Not Allowed"}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Last Inspection: </span>
                            <span>{new Date(unit.lastInspection).toLocaleDateString()}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Next Maintenance: </span>
                            <span>{new Date(unit.nextMaintenance).toLocaleDateString()}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Annual Revenue: </span>
                            <span>${unit.revenue.toLocaleString()}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Occupancy Rate: </span>
                            <span>{unit.occupancyRate.toFixed(1)}%</span>
                          </div>
                        </div>
                        {unit.issues.length > 0 && (
                          <div className="mt-4">
                            <span className="text-sm text-muted-foreground">Current Issues: </span>
                            <div className="mt-1 flex flex-wrap gap-1">
                              {unit.issues.map((issue, index) => (
                                <Badge key={index} variant="outline" className="text-warning">
                                  {issue}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Quick Bulk Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
            {bulkActions.slice(0, 4).map((action) => (
              <Button
                key={action.value}
                variant="outline"
                className="flex h-auto flex-col items-center space-y-2 p-4"
                disabled={selectedUnits.length === 0}
                onClick={() => {
                  setBulkAction(action.value);
                  setIsBulkDialogOpen(true);
                }}>
                <action.icon className="size-6" />
                <div className="text-center">
                  <div className="font-medium">{action.label}</div>
                  <div className="text-xs text-muted-foreground">{action.description}</div>
                </div>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Operation Progress Dialog */}
      <Dialog open={isProgressDialogOpen} onOpenChange={setIsProgressDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Processing Bulk Operation</DialogTitle>
            <DialogDescription>
              {selectedAction?.label} for {selectedUnits.length} units
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{operationProgress}%</div>
              <p className="text-sm text-muted-foreground">Complete</p>
            </div>
            <Progress value={operationProgress} className="h-3" />
            <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
              <RefreshCw className="size-4 animate-spin" />
              <span>Processing units...</span>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold">{filteredUnits.length}</div>
            <p className="text-sm text-muted-foreground">Total Units</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-success">
              {filteredUnits.filter((u) => u.status === "Occupied").length}
            </div>
            <p className="text-sm text-muted-foreground">Occupied</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-warning">
              {filteredUnits.filter((u) => u.status === "Vacant").length}
            </div>
            <p className="text-sm text-muted-foreground">Vacant</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-destructive">
              {filteredUnits.filter((u) => u.status === "Maintenance").length}
            </div>
            <p className="text-sm text-muted-foreground">Maintenance</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
