import { useTranslation } from "react-i18next";

import { TableCell, TableRow } from "@/components/ui/table";

import AccessDenied from "../ui/access-denined";

interface AccessDeniedProps {
  columns: number;
  deniedMessage: string;
}

export function AccessDeniedRow({ columns, deniedMessage }: AccessDeniedProps) {
  const { t } = useTranslation();
  return (
    <TableRow>
      <TableCell colSpan={columns} className="h-24 text-center">
        <AccessDenied deniedMessage={deniedMessage} />
      </TableCell>
    </TableRow>
  );
}
