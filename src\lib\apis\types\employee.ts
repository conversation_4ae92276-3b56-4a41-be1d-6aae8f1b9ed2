import { SortDirection } from "@tanstack/react-table";

// Employee type based on the API response
export interface Employee {
  id: string;
  username: string;
  name: string;
  email: string;
  phone_number: string | null;
  birthdate: string | null;
  address: string | null;
  picture: string | null;
  role: string;
  status: "ENABLED" | "DISABLED";
  confirmation_status?:
    | "CONFIRMED"
    | "UNCONFIRMED"
    | "ARCHIVED"
    | "EXTERNAL_PROVIDER"
    | "UNKNOWN"
    | "RESET_REQUIRED"
    | "FORCE_CHANGE_PASSWORD";
  company_id: string;
  created_at: string;
  updated_at: string;
  role_list: Array<{
    id: string;
    name: string;
  }>;
  branch_list: Array<{
    id: string;
    name: string;
  }>;
  user_roles: UserRole[];
  metadata?: {
    plan: string;
    role: string;
    company_id: string;
  };
}

export interface UserRole {
  branch_id: string;
  role_ids: string[];
}

// API response type for employee list
export interface EmployeeListResponse {
  total: number;
  page: number;
  limit: string;
  items: Employee[];
}

// Filter parameters for employee list
export interface EmployeeListParams {
  page?: number;
  limit?: number;
  company_id?: string;
  role?: string;
  status?: string;
  location?: string;
  email?: string;
  phone_number?: string;
  created_at_from?: string;
  created_at_to?: string;
  ["sort_updated_at"]?: SortDirection;
  updated_at_from?: string;
  updated_at_to?: string;
  search?: string;
}

// Employee creation payload
export interface CreateEmployeePayload {
  username: string;
  password?: string;
  email: string;
  name: string;
  phone_number?: string;
  address?: string;
  birthdate?: string;
  user_roles: Record<string, string[]>; // branch_id -> [role_id1, role_id2, ...]
}

// Employee update payload
export interface UpdateEmployeePayload {
  name?: string;
  phone_number?: string;
  address?: string;
  birthdate?: string;
  user_roles?: Record<string, string[]>; // branch_id -> [role_id1, role_id2, ...]
}

// Employee status update payload
export interface UpdateEmployeeStatusPayload {
  id: string;
  status: Employee["status"];
}

// Employee role update payload
export interface UpdateEmployeeRolePayload {
  id: string;
  role: Employee["role"];
}
