import { useTranslation } from "react-i18next";

import { type VerificationStepProps } from "../components/ForgotPasswordDialog/VerificationStep.types";

export const useVerificationStep = ({
  email,
  onVerificationCodeChange,
  onVerifyCode,
  onResendCode,
  onClose,
}: VerificationStepProps) => {
  const { t } = useTranslation();

  /**
   * Creates masked email for privacy
   * Shows only last 3 characters of username + domain
   */
  const maskedEmail = email
    ? `••••••••••${email.split("@")[0].slice(-3)}@${email.split("@")[1]}`
    : t("pages.profile.forgotPasswordDialog.verificationStep.yourEmail");

  /**
   * <PERSON>les verification code input changes
   */
  const handleVerificationCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onVerificationCodeChange(e.target.value);
  };

  /**
   * <PERSON>les verify code button click
   */
  const handleVerifyCode = () => {
    onVerifyCode();
  };

  /**
   * <PERSON>les resend code button click
   */
  const handleResendCode = () => {
    onResendCode();
  };

  /**
   * <PERSON>les close button click
   */
  const handleClose = () => {
    if (onClose) {
      onClose();
    }
  };

  return {
    maskedEmail,
    handleVerificationCodeChange,
    handleVerifyCode,
    handleResendCode,
    handleClose,
  };
};
