import { Skeleton } from "@/components/ui/skeleton";

export function LayoutGridSkeleton() {
  return (
    <div className="space-y-4">
      {/* Layout Grid Skeleton */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Generate 6 skeleton cards to show loading state */}
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="rounded-lg border bg-card p-4 shadow-sm">
            {/* Layout Name Skeleton */}
            <div className="mb-3">
              <Skeleton className="h-5 w-3/4" />
            </div>

            {/* Description Skeleton */}
            <div className="mb-4 space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>

            {/* Status and Floor Info Skeleton */}
            <div className="mb-4 flex items-center justify-between">
              <Skeleton className="h-6 w-20 rounded-full" />
              <Skeleton className="h-4 w-16" />
            </div>

            {/* Unit Count Skeleton */}
            <div className="mb-4">
              <Skeleton className="h-4 w-24" />
            </div>

            {/* Action Buttons Skeleton */}
            <div className="flex gap-2">
              <Skeleton className="h-8 w-16" />
              <Skeleton className="h-8 w-16" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
