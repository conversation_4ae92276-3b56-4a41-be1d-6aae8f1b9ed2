"use client";

import { useTranslation } from "react-i18next";

import { SubscriptionPlan } from "./use-subscription";

export const usePricingCard = ({
  plan,
  isAnnualBilling,
  formatPrice,
}: {
  plan: SubscriptionPlan;
  isAnnualBilling: boolean;
  formatPrice: (price: number | string, currency: string) => string;
}) => {
  const { t } = useTranslation();

  // Get price based on billing cycle from the new duration structure
  const displayPrice = isAnnualBilling
    ? plan.duration?.YEARLY?.sale_price || "0"
    : plan.duration?.MONTHLY?.sale_price || "0";

  const billingCycle = isAnnualBilling ? "/year" : "/month";

  // Use semantic theme colors instead of hardcoded colors
  const cardClasses = `flex flex-col  ${
    plan.isPopular || plan.is_popular
      ? "border-2 border-primary bg-card rounded-b-lg"
      : "border-border bg-card rounded-lg border"
  }`;

  const buttonVariant: "default" | "secondary" =
    plan.isPopular || plan.is_popular ? "default" : "secondary";

  return {
    t,
    displayPrice,
    billingCycle,
    cardClasses,
    buttonVariant,
  };
};
