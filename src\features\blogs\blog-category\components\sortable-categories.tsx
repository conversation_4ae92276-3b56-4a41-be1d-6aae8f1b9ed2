"use client";

import React, { useCallback, useState } from "react";
import {
  closestCenter,
  DndContext,
  DragEndEvent,
  DragOverlay,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { Loader2 } from "lucide-react";
import { useInView } from "react-intersection-observer";

import { Category } from "@/lib/apis/types/category";

import { useBlogCategoryManager } from "../../hooks/use-blog-category";
import { CategoryItem } from "./category-item";
import { CategoryTreeItem } from "./category-tree-item";

interface SortableCategoriesProps {
  onLoadMore: () => void;
  hasNextPage: boolean;
  isFetchingNextPage: boolean;
  isLoading: boolean;
}

export function SortableCategories({
  onLoadMore,
  hasNextPage,
  isFetchingNextPage,
  isLoading,
}: SortableCategoriesProps) {
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [draggedCategory, setDraggedCategory] = useState<Category | null>(null);

  const {
    categoryTree,
    flattenedCategories,
    handleDragEnd: handleDragEndFromHook,
    handleLoadMore: handleLoadMoreFromHook,
  } = useBlogCategoryManager();

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const { ref: loadMoreRef, inView } = useInView();

  const handleDragStart = useCallback(
    (event: any) => {
      const draggedId = event.active.id;
      const draggedCat = flattenedCategories.find((cat) => cat.id === draggedId);
      setDraggedCategory(draggedCat || null);
    },
    [flattenedCategories]
  );

  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;
      setDraggedCategory(null);

      if (over && active.id !== over.id) {
        handleDragEndFromHook(active.id as string, over.id as string);
      }
    },
    [handleDragEndFromHook]
  );

  const handleEdit = (category: Category) => {
    setEditingCategory(category);
  };

  const handleCancelEdit = () => {
    setEditingCategory(null);
  };

  const handleDelete = (id: string) => {
    // This will be handled by the CategoryItem component
  };

  // Load more when the last item comes into view
  React.useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      handleLoadMoreFromHook();
    }
  }, [inView, hasNextPage, isFetchingNextPage, handleLoadMoreFromHook]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="size-6 animate-spin" />
      </div>
    );
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}>
      <SortableContext items={flattenedCategories} strategy={verticalListSortingStrategy}>
        <div className="space-y-3">
          {categoryTree.map((node) => (
            <CategoryTreeItem
              key={node.category.id}
              node={node}
              onEdit={handleEdit}
              onDelete={handleDelete}
              isEditing={editingCategory}
              onCancelEdit={handleCancelEdit}
            />
          ))}

          {/* Load more trigger */}
          {hasNextPage && (
            <div ref={loadMoreRef} className="flex justify-center py-4">
              {isFetchingNextPage && <Loader2 className="size-5 animate-spin" />}
            </div>
          )}
        </div>
      </SortableContext>

      <DragOverlay>
        {draggedCategory ? (
          <CategoryItem
            category={draggedCategory}
            onEdit={() => {}}
            onDelete={() => {}}
            level={0}
          />
        ) : null}
      </DragOverlay>
    </DndContext>
  );
}
