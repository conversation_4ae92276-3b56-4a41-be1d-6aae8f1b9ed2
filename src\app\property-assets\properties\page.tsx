"use client";

import { useMemo } from "react";
import { useRouter } from "next/navigation";
import { PlusIcon } from "lucide-react";
import { useTranslation } from "react-i18next";

import { columns } from "@/features/property-assets/property/components/PropertyList/column";
import { useProperties } from "@/features/property-assets/property/hooks/property";

import { TableContainer } from "@/components/custom-table/container/table-container";
import GroupButton, { GroupButtonProps } from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { EFilterType, FilterTableProps, FilterType } from "@/components/data-table/types";
import { authProtectedPaths } from "@/constants/paths";

export default function PropertiesPage() {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch } = useDatatable();
  const router = useRouter();
  const options = useMemo(
    () => ({ limit: Number(getInitialParams.limit), ...getInitialParams }),
    [getInitialParams]
  );

  const { properties, total, isLoading, isFetching, refetch, useDeletePropertyMutation } =
    useProperties(options);

  const deletePropertyMutation = useDeletePropertyMutation.mutate;

  const handleDeleteProperty = async (id: string) => {
    await deletePropertyMutation(id);
  };

  const isTableLoading = isLoading || isFetching;

  const listFilter: FilterType[] = useMemo(
    () => [
      {
        id: "type",
        type: EFilterType.SELECT_BOX,
        title: t("pages.properties.filters.type"),
        defaultValue: getInitialParams["type"] as string,
        dataOption: [
          { value: "RESIDENTIAL", label: t("pages.properties.types.residential") },
          { value: "COMMERCIAL", label: t("pages.properties.types.commercial") },
          { value: "MIXED", label: t("pages.properties.types.mixed") },
        ],
      },
      {
        id: "status",
        type: EFilterType.SELECT_BOX,
        title: t("pages.properties.filters.status"),
        defaultValue: getInitialParams["status"] as string,
        dataOption: [
          { value: "ACTIVE", label: t("common.status.active") },
          { value: "INACTIVE", label: t("common.status.inactive") },
        ],
      },
      {
        id: "created_at",
        type: EFilterType.DATE,
        title: t("pages.properties.filters.createdAt"),
        defaultValue: getInitialParams["created_at_from"] as string,
      },
      {
        id: "updated_at",
        type: EFilterType.DATE,
        title: t("pages.properties.filters.updatedAt"),
        defaultValue: getInitialParams["updated_at_from"] as string,
      },
    ],
    [t, getInitialParams]
  );

  const filterConfig: FilterTableProps = useMemo(
    () => ({
      showSearch: true,
      filterType: "properties",
      searchPlaceHolder: t("pages.properties.filters.search.placeholder"),
      initialValues: getInitialParams,
      listFilter,
      handleParamSearch,
      listLoading: isTableLoading,
    }),
    [t, getInitialParams, listFilter, handleParamSearch, isTableLoading]
  );

  const groupButtonConfig: GroupButtonProps = {
    buttons: [
      {
        type: "button" as const,
        title: t("pages.properties.add"),
        icon: PlusIcon,
        onClick: () => router.push(authProtectedPaths.PROPERTIES_NEW as any),
      },
    ],
    onRefresh: () => refetch(),
    isRefreshLoading: isFetching,
  };

  return (
    <TableCard>
      <TableHeader
        title={t("pages.properties.title")}
        filterType="properties"
        data={properties}
        filterProps={filterConfig as FilterTableProps}
        rightComponent={<GroupButton {...groupButtonConfig} />}
      />
      <TableContainer
        columns={columns(handleDeleteProperty, isFetching, t)}
        data={properties}
        loading={isTableLoading}
        total={total}
        pageSize={Number(getInitialParams.limit)}
        currentPage={Number(getInitialParams.page)}
        onHandleDelete={async (listIndexId: number[], handleRestRows) => {
          // TODO: Implement bulk delete functionality
          // For now, just reset the selected rows
          handleRestRows();
        }}
      />
    </TableCard>
  );
}
