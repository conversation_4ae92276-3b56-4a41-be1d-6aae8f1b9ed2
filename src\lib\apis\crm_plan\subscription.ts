import { CRM_ENDPOINTS } from "../../../constants/endpoints";
import { privateApi } from "../../api_helper";
import { ResponseAxiosDetail } from "../types/common";
import { CancelSubscriptionRequest, CreateSubscriptionRequest, Subscription } from "./types";

export const subscriptionApi = {
  create: async (subscription: CreateSubscriptionRequest) => {
    return await privateApi.post<ResponseAxiosDetail<Subscription>>(
      CRM_ENDPOINTS.SUBSCRIPTIONS,
      subscription
    );
  },
  getCurrent: async () => {
    return await privateApi.get<ResponseAxiosDetail<Subscription>>(
      CRM_ENDPOINTS.CURRENT_SUBSCRIPTIONS
    );
  },
  getById: async (id: string) => {
    return await privateApi.get<ResponseAxiosDetail<Subscription>>(CRM_ENDPOINTS.SUBSCRIPTION(id));
  },
  cancel: async (id: string, data: CancelSubscriptionRequest = {}) => {
    return await privateApi.post<ResponseAxiosDetail<Subscription>>(
      CRM_ENDPOINTS.CANCEL_SUBSCRIPTION(id),
      data
    );
  },
};
