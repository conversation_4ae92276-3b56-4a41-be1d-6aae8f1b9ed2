import type { AssetCategory } from "../types";

export const mockCategories: AssetCategory[] = [
  {
    id: "1",
    name: "Residential Units",
    description: "Individual apartment units and living spaces",
    icon: "Home",
    color: "#8884d8",
    parentId: null,
    assetCount: 48,
    totalValue: 3200000,
    avgValue: 66667,
    performanceScore: 85,
    trend: "up",
    trendValue: 12.5,
    isActive: true,
    createdDate: "2023-01-15",
    subcategories: ["Studio", "1-Bedroom", "2-Bedroom", "3-Bedroom"],
  },
  {
    id: "2",
    name: "Common Areas",
    description: "Shared spaces like lobbies, hallways, and amenities",
    icon: "Building",
    color: "#82ca9d",
    parentId: null,
    assetCount: 15,
    totalValue: 850000,
    avgValue: 56667,
    performanceScore: 78,
    trend: "up",
    trendValue: 8.2,
    isActive: true,
    createdDate: "2023-01-15",
    subcategories: ["Lobby", "Hallways", "Gym", "Rooftop"],
  },
  {
    id: "3",
    name: "Parking Spaces",
    description: "Vehicle parking areas and garages",
    icon: "Car",
    color: "#ffc658",
    parentId: null,
    assetCount: 62,
    totalValue: 620000,
    avgValue: 10000,
    performanceScore: 92,
    trend: "up",
    trendValue: 15.3,
    isActive: true,
    createdDate: "2023-02-01",
    subcategories: ["Covered", "Open", "Reserved", "Visitor"],
  },
  {
    id: "4",
    name: "HVAC Systems",
    description: "Heating, ventilation, and air conditioning equipment",
    icon: "Wrench",
    color: "#ff7300",
    parentId: null,
    assetCount: 25,
    totalValue: 450000,
    avgValue: 18000,
    performanceScore: 72,
    trend: "down",
    trendValue: -3.1,
    isActive: true,
    createdDate: "2023-01-20",
    subcategories: ["Central AC", "Heat Pumps", "Boilers", "Ventilation"],
  },
  {
    id: "5",
    name: "Electrical Systems",
    description: "Electrical infrastructure and equipment",
    icon: "Lightbulb",
    color: "#8dd1e1",
    parentId: null,
    assetCount: 18,
    totalValue: 320000,
    avgValue: 17778,
    performanceScore: 88,
    trend: "up",
    trendValue: 6.7,
    isActive: true,
    createdDate: "2023-01-25",
    subcategories: ["Main Panels", "Outlets", "Lighting", "Emergency Systems"],
  },
  {
    id: "6",
    name: "Security Systems",
    description: "Security and surveillance equipment",
    icon: "Shield",
    color: "#d084d0",
    parentId: null,
    assetCount: 12,
    totalValue: 180000,
    avgValue: 15000,
    performanceScore: 94,
    trend: "up",
    trendValue: 18.9,
    isActive: true,
    createdDate: "2023-02-10",
    subcategories: ["Cameras", "Access Control", "Alarms", "Intercoms"],
  },
  {
    id: "7",
    name: "Landscaping",
    description: "Outdoor spaces, gardens, and landscape features",
    icon: "TreePine",
    color: "#00C49F",
    parentId: null,
    assetCount: 8,
    totalValue: 120000,
    avgValue: 15000,
    performanceScore: 81,
    trend: "up",
    trendValue: 4.2,
    isActive: true,
    createdDate: "2023-03-01",
    subcategories: ["Gardens", "Irrigation", "Lighting", "Hardscaping"],
  },
  {
    id: "8",
    name: "Technology Infrastructure",
    description: "Internet, network, and smart building systems",
    icon: "Wifi",
    color: "#FF8042",
    parentId: null,
    assetCount: 22,
    totalValue: 280000,
    avgValue: 12727,
    performanceScore: 89,
    trend: "up",
    trendValue: 22.1,
    isActive: true,
    createdDate: "2023-02-15",
    subcategories: ["Network Equipment", "Smart Devices", "Fiber", "Wifi Systems"],
  },
];
