import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { settingApi } from "@/lib/apis/setting";
import { useVersion, versionApi } from "@/lib/apis/version";

import { LanguageSetting } from "../../hooks/language-setting";
import { AVAILABLE_LANGUAGES } from "../constants";

// Hook for managing language settings
export const useLanguage = () => {
  const { i18n, t } = useTranslation();
  const { getSettingValue } = useVersion();
  const [selectedLanguages, setSelectedLanguages] = useState<string[]>([i18n.language || "en"]);
  const [displayedLanguages, setDisplayedLanguages] = useState<string[]>([i18n.language || "en"]);
  const [isLoading, setIsLoading] = useState(false);
  const isInitialized = useRef(false);
  const originalLanguage = useRef<string>(i18n.language || "en");

  // Use React Query to fetch shop info data directly from the version query
  const { data: versionData, isLoading: isVersionLoading } = useQuery({
    queryKey: ["version"],
    queryFn: versionApi.getVersion,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  // Get language settings from shop_info
  const savedLanguages = useMemo(() => {
    const shopInfo = versionData?.data?.dict?.shop_info?.setting_value as Record<string, any>;
    const apiLanguages = (shopInfo?.language as LanguageSetting[]) || [];

    // Filter out unsupported language codes and set defaults
    const validLanguages = apiLanguages.filter((lang) =>
      AVAILABLE_LANGUAGES.some((available) => available.code === lang.code)
    );

    // If no valid languages or empty, set default languages
    if (validLanguages.length === 0) {
      return [
        { code: "en", name: "English" },
        { code: "vi", name: "Vietnamese" },
      ];
    }

    return validLanguages;
  }, [versionData]);

  // Update selected languages when i18n language changes or when saved languages are loaded
  useEffect(() => {
    const currentLanguage = i18n.language || "en";

    // If we have saved languages, use them; otherwise use the current i18n language
    if (savedLanguages.length > 0 && !isVersionLoading) {
      const savedLanguageCodes = savedLanguages.map((lang: LanguageSetting) => lang.code);

      // Only update if the values are actually different to prevent infinite loops
      setDisplayedLanguages((prev) => {
        if (JSON.stringify(prev) !== JSON.stringify(savedLanguageCodes)) {
          return savedLanguageCodes;
        }
        return prev;
      });

      // Always set selected language to current i18n language if it exists in saved languages
      // This ensures the selected language matches what the client is currently displaying
      if (savedLanguageCodes.includes(currentLanguage)) {
        setSelectedLanguages((prev) => {
          if (prev[0] !== currentLanguage) {
            return [currentLanguage];
          }
          return prev;
        });
      } else {
        // If current language is not in saved languages, use the first available one
        setSelectedLanguages((prev) => {
          if (prev[0] !== savedLanguageCodes[0]) {
            return [savedLanguageCodes[0]];
          }
          return prev;
        });
      }
    } else if (!isVersionLoading) {
      // No saved languages, use default languages
      const defaultLanguages = ["en", "vi"];
      setSelectedLanguages((prev) => {
        if (prev[0] !== "en") {
          return ["en"];
        }
        return prev;
      });
      setDisplayedLanguages((prev) => {
        if (JSON.stringify(prev) !== JSON.stringify(defaultLanguages)) {
          return defaultLanguages;
        }
        return prev;
      });
    }

    // Mark as initialized to prevent further updates
    if (!isVersionLoading) {
      isInitialized.current = true;
    }
  }, [i18n.language, isVersionLoading]); // Add isVersionLoading dependency

  // Handle savedLanguages changes after initialization
  useEffect(() => {
    if (!isInitialized.current || !savedLanguages.length || isVersionLoading) return;

    const savedLanguageCodes = savedLanguages.map((lang: LanguageSetting) => lang.code);
    const currentLanguage = i18n.language || "en";

    // Update displayed languages if they're different
    setDisplayedLanguages((prev) => {
      if (JSON.stringify(prev) !== JSON.stringify(savedLanguageCodes)) {
        return savedLanguageCodes;
      }
      return prev;
    });

    // Update selected language if current one is not in saved languages
    if (!savedLanguageCodes.includes(currentLanguage)) {
      setSelectedLanguages([savedLanguageCodes[0] || currentLanguage]);
    }
  }, [i18n.language, isVersionLoading]); // Add isVersionLoading dependency

  const handleLanguageToggle = useCallback((languageCode: string) => {
    // For language, we only allow one selection at a time (like radio buttons)
    // Don't change i18n language immediately - wait for update button
    setSelectedLanguages([languageCode]);
  }, []);

  const handleAddLanguage = useCallback(
    (languageCode: string | string[]) => {
      if (Array.isArray(languageCode)) {
        // Add multiple languages
        const newLanguages = languageCode.filter((code) => !displayedLanguages.includes(code));
        if (newLanguages.length > 0) {
          setDisplayedLanguages((prev) => [...prev, ...newLanguages]);
        }
      } else {
        // Add single language
        if (!displayedLanguages.includes(languageCode)) {
          setDisplayedLanguages((prev) => [...prev, languageCode]);
        }
      }
    },
    [displayedLanguages]
  );

  const handleRemoveLanguage = useCallback(
    (languageCode: string) => {
      if (displayedLanguages.length > 1) {
        setDisplayedLanguages((prev) => prev.filter((code) => code !== languageCode));
        // Also remove from selected if it was selected
        setSelectedLanguages((prev) => prev.filter((code) => code !== languageCode));
      }
    },
    [displayedLanguages]
  );

  // Get all languages for the add selector (displayed ones will be disabled)
  const getAvailableLanguagesForAdd = useCallback(() => {
    return AVAILABLE_LANGUAGES.map((lang) => ({
      ...lang,
      isDisabled: displayedLanguages.includes(lang.code), // Disable if already in the list
      isDirty:
        displayedLanguages.includes(lang.code) &&
        !savedLanguages.some((saved) => saved.code === lang.code), // Mark as dirty if newly added
    }));
  }, [displayedLanguages, savedLanguages]);

  const handleRemoveLanguageFromDisplay = useCallback(
    (languageCode: string) => {
      // Remove from displayed languages
      if (displayedLanguages.length > 1) {
        setDisplayedLanguages((prev) => prev.filter((code) => code !== languageCode));

        // If the removed language was selected, select another available language
        if (selectedLanguages.includes(languageCode)) {
          const remainingLanguages = displayedLanguages.filter((code) => code !== languageCode);
          setSelectedLanguages([remainingLanguages[0] || "en"]);
        }
      }
    },
    [displayedLanguages, selectedLanguages]
  );

  const queryClient = useQueryClient();

  const updateLanguageMutation = useMutation({
    mutationFn: async () => {
      // Get current shop_info settings
      const currentShopInfo = getSettingValue<Record<string, any>>("shop_info") || {};

      // Create language settings from displayed languages (without currency)
      const languageSettings = displayedLanguages.map((langCode) => {
        const langInfo = AVAILABLE_LANGUAGES.find((lang) => lang.code === langCode);
        return {
          name: langInfo?.name || langCode,
          code: langCode,
        };
      });

      // Update only language-related fields while preserving other fields
      const updatedShopInfo: Record<string, any> = {
        ...currentShopInfo,
        language: languageSettings,
      };

      await settingApi.updateLanguageSetting("shop_info", updatedShopInfo);
      const version = await versionApi.getVersion();

      return {
        ...version,
        data: {
          ...version.data,
          dict: {
            ...version.data.dict,
            shop_info: { ...version.data.dict.shop_info, setting_value: updatedShopInfo },
          },
        },
      };
    },
    onSuccess: (updatedVersion) => {
      queryClient.setQueryData(["version"], updatedVersion);

      // Invalidate the version query to refetch fresh data
      // queryClient.invalidateQueries({ queryKey: ["version"] });

      // Update i18n language if changed - this changes the UI language
      const newLanguage = selectedLanguages[0];
      if (newLanguage && newLanguage !== i18n.language) {
        i18n.changeLanguage(newLanguage);

        // Also update document language attribute for accessibility
        if (typeof document !== "undefined") {
          document.documentElement.lang = newLanguage;
        }

        // Update localStorage to persist the language choice
        if (typeof localStorage !== "undefined") {
          localStorage.setItem("i18nextLng", newLanguage);
        }
      }

      // Update the original language ref so future changes are detected correctly
      originalLanguage.current = selectedLanguages[0];

      toast.success(t("profile.updateSuccess"));
    },
    onError: (error: Error) => {
      toast.error(t("profile.updateError"));
    },
  });

  const handleUpdateLanguage = useCallback(async () => {
    setIsLoading(true);
    try {
      await updateLanguageMutation.mutateAsync();
    } catch (error) {
      console.error("Failed to update language settings:", error);
    } finally {
      setIsLoading(false);
    }
  }, [updateLanguageMutation]);

  const hasLanguageChanges = useCallback(() => {
    // Check if the displayed languages are different from what was originally loaded from shop_info
    if (savedLanguages.length > 0) {
      const savedLanguageCodes = savedLanguages.map((lang: LanguageSetting) => lang.code);
      const currentDisplayed = displayedLanguages;

      // Check if the currently displayed languages are different from what's saved
      if (savedLanguageCodes.length !== currentDisplayed.length) {
        return true;
      }

      // Check if any language codes are different
      const displayedLanguagesChanged =
        savedLanguageCodes.some((code: string) => !currentDisplayed.includes(code)) ||
        currentDisplayed.some((code: string) => !savedLanguageCodes.includes(code));

      // Check if the selected language has changed from what was originally loaded
      // The originally selected language is the one that was active when the component loaded
      const selectedLanguageChanged = selectedLanguages[0] !== originalLanguage.current;

      return displayedLanguagesChanged || selectedLanguageChanged;
    }

    // Fallback: check if selected language is different from original language
    return selectedLanguages[0] !== originalLanguage.current;
  }, [selectedLanguages, displayedLanguages, savedLanguages]);

  // Get all languages for the add selector (displayed ones will be disabled)
  const getAllLanguagesForSelection = useCallback(() => {
    return AVAILABLE_LANGUAGES.map((lang) => ({
      ...lang,
      isDisabled: displayedLanguages.includes(lang.code),
      isDirty:
        displayedLanguages.includes(lang.code) &&
        !savedLanguages.some((saved) => saved.code === lang.code), // Mark as dirty if newly added
    }));
  }, [displayedLanguages, savedLanguages]);

  // Get available languages that are not currently selected
  const getAvailableLanguagesForSelection = useCallback(() => {
    return AVAILABLE_LANGUAGES.filter((lang) => !selectedLanguages.includes(lang.code));
  }, [selectedLanguages]);

  return {
    selectedLanguages,
    displayedLanguages,
    isLoading,
    isVersionLoading,
    handleLanguageToggle,
    handleAddLanguage,
    handleRemoveLanguage,
    handleRemoveLanguageFromDisplay,
    handleUpdateLanguage,
    hasLanguageChanges,
    getAvailableLanguagesForSelection,
    getAllLanguagesForSelection,
    getAvailableLanguagesForAdd,
  };
};
