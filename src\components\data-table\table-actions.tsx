import { Trash } from "lucide-react";
import { useTranslation } from "react-i18next";

import { CustomPagination } from "@/components/custom-pagination";
import { But<PERSON> } from "@/components/ui/button";

interface TableActionsProps {
  formattedSelectedRows: number[];
  allowDelete: boolean;
  hasDataPermission: boolean;
  total: number;
  pageSize: number;
  currentPage: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  onDeleteDialogOpen: () => void;
}

export function TableActions({
  formattedSelectedRows,
  allowDelete,
  hasDataPermission,
  total,
  pageSize,
  currentPage,
  onPageChange,
  onPageSizeChange,
  onDeleteDialogOpen,
}: TableActionsProps) {
  const { t } = useTranslation();

  return (
    <>
      {formattedSelectedRows.length > 0 && allowDelete && hasDataPermission && (
        <div className="flex items-center justify-between gap-2 text-nowrap text-center">
          <Button
            leftIcon={<Trash size={16} />}
            variant="outlineDestructive"
            color="crimson"
            size="sm"
            onClick={onDeleteDialogOpen}>
            {t("table.selected.delete", { count: formattedSelectedRows.length })}
          </Button>
        </div>
      )}
      {formattedSelectedRows.length === 0 && hasDataPermission && (
        <CustomPagination
          total={total}
          pageSize={pageSize}
          currentPage={currentPage + 1}
          onPageSizeChange={onPageSizeChange}
          onPageChange={(page) => onPageChange(page - 1)}
        />
      )}
    </>
  );
}
