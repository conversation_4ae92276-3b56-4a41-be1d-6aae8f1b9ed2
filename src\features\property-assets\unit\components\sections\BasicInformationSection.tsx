import { UseFormReturn } from "react-hook-form";
import { useTranslation } from "react-i18next";

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { ImageUpload } from "@/components/ui/image-upload";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

interface BasicInformationSectionProps {
  form: UseFormReturn<any>;
}

export function BasicInformationSection({ form }: BasicInformationSectionProps) {
  const { t } = useTranslation();

  return (
    <div className="rounded-lg border bg-card p-4">
      <h3 className="mb-4 text-sm font-medium text-card-foreground">
        {t("pages.units.basicInformation") || "Basic Information"}
      </h3>
      <div className="space-y-4">
        {/* Image Upload */}
        <FormField
          control={form.control}
          name="images"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-foreground">
                {t("pages.units.images") || "Images"} <span className="text-destructive">*</span>
              </FormLabel>
              <FormControl>
                <ImageUpload
                  value={
                    field.value?.length > 0
                      ? JSON.stringify(field.value.map((img: { image: string }) => img.image))
                      : null
                  }
                  onChange={(base64) => {
                    if (!base64) {
                      field.onChange([]);
                      return;
                    }

                    try {
                      const images = JSON.parse(base64) as string[];
                      const processedImages = images.map((image) => {
                        const nameMatch = image.match(/;name=(.*?)(;|$)/);
                        const filename = nameMatch ? decodeURIComponent(nameMatch[1]) : "image.jpg";

                        return {
                          name: filename,
                          image: image,
                        };
                      });

                      field.onChange(processedImages);
                    } catch (error) {
                      console.error("Error processing images:", error);
                    }
                  }}
                  multiple
                />
              </FormControl>
              <FormMessage className="text-destructive" />
            </FormItem>
          )}
        />

        {/* Property Selection */}
        <FormField
          control={form.control}
          name="property_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-foreground">
                {t("pages.units.property") || "Property"}{" "}
                <span className="text-destructive">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="h-9 border-input focus:border-primary focus:ring-ring">
                    <SelectValue placeholder={t("common.select") || "Select..."} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="property-1">
                    {t("pages.units.sampleProperty1") || "Property 1"}
                  </SelectItem>
                  <SelectItem value="property-2">
                    {t("pages.units.sampleProperty2") || "Property 2"}
                  </SelectItem>
                </SelectContent>
              </Select>
              <FormMessage className="text-destructive" />
            </FormItem>
          )}
        />

        {/* Unit Number and Type */}
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="unit_number"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-foreground">
                  {t("pages.units.unitNumber") || "Unit Number"}{" "}
                  <span className="text-destructive">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={
                      t("pages.units.unitNumberPlaceholder") || "Enter unit (e.g. A-101)"
                    }
                    className="h-9 border-input focus:border-primary focus:ring-ring"
                    {...field}
                  />
                </FormControl>
                <FormMessage className="text-destructive" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-foreground">
                  {t("pages.units.unitType") || "Unit Type"}{" "}
                  <span className="text-destructive">*</span>
                </FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="h-9 border-input focus:border-primary focus:ring-ring">
                      <SelectValue placeholder={t("common.selectType") || "Select type"} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="studio">
                      {t("pages.units.types.studio") || "Studio"}
                    </SelectItem>
                    <SelectItem value="1br">{t("pages.units.types.1br") || "1 Bedroom"}</SelectItem>
                    <SelectItem value="2br">{t("pages.units.types.2br") || "2 Bedroom"}</SelectItem>
                    <SelectItem value="3br">{t("pages.units.types.3br") || "3 Bedroom"}</SelectItem>
                    <SelectItem value="commercial">
                      {t("pages.units.types.commercial") || "Commercial"}
                    </SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage className="text-destructive" />
              </FormItem>
            )}
          />
        </div>

        {/* Description */}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-foreground">
                {t("pages.units.description") || "Description"}
              </FormLabel>
              <FormControl>
                <Textarea
                  placeholder={t("pages.units.descriptionPlaceholder") || "Type your message here."}
                  rows={3}
                  className="resize-none border-input focus:border-primary focus:ring-ring"
                  {...field}
                />
              </FormControl>
              <FormMessage className="text-destructive" />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
