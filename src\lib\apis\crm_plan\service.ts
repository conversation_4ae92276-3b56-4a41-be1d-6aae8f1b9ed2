import { CRM_ENDPOINTS } from "../../../constants/endpoints";
import { privateApi } from "../../api_helper";
import { ResponseList } from "../types/common";
import { CreateServiceRequest, Service, UpdateServiceRequest } from "./types";

export const serviceApi = {
  list: async (params?: Record<string, unknown>) => {
    return await privateApi.get<ResponseList<Service>>(CRM_ENDPOINTS.SERVICES, { params });
  },
  create: async (service: CreateServiceRequest) => {
    return await privateApi.post<Service>(CRM_ENDPOINTS.SERVICES, service);
  },
  get: async (id: string) => {
    return await privateApi.get<Service>(CRM_ENDPOINTS.SERVICE(id));
  },
  update: async (id: string, service: UpdateServiceRequest) => {
    return await privateApi.put<Service>(CRM_ENDPOINTS.SERVICE(id), service);
  },
  delete: async (id: string) => {
    return await privateApi.delete(CRM_ENDPOINTS.SERVICE(id));
  },
};
