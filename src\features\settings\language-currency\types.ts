import { StaticImageData } from "next/image";

export interface Language {
  code: string;
  name: string;
  nativeName: string;
  flag: StaticImageData;
  isDefault: boolean;
  isActive: boolean;
}

export interface Currency {
  code: string;
  name: string;
  symbol: string;
  flag: StaticImageData;
  isDefault: boolean;
  isActive: boolean;
}

export interface LanguageCurrencySettings {
  languages: Language[];
  currencies: Currency[];
}

export interface LanguageCurrencyFormData {
  selectedLanguages: string[];
  selectedCurrencies: string[];
}
