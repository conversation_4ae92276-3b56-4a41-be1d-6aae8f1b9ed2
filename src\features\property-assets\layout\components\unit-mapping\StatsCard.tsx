import { Building2 } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { StatsCardSkeleton } from "../LoadingSkeleton";

interface StatsCardProps {
  mappedUnits: number;
  unmappedUnits: number;
  mappingProgress: number;
  isLoading?: boolean;
}

export function StatsCard({
  mappedUnits,
  unmappedUnits,
  mappingProgress,
  isLoading,
}: StatsCardProps) {
  const { t } = useTranslation();

  if (isLoading) {
    return <StatsCardSkeleton />;
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          <Building2 className="size-4" />
          {t("pages.layouts.mappingStats")}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid grid-cols-3 gap-2 text-center">
          <div>
            <div className="text-lg font-bold text-primary">{mappedUnits}</div>
            <div className="text-xs text-muted-foreground">{t("pages.layouts.mapped")}</div>
          </div>
          <div>
            <div className="text-lg font-bold text-warning">{unmappedUnits}</div>
            <div className="text-xs text-muted-foreground">{t("pages.layouts.unmapped")}</div>
          </div>
          <div>
            <div className="text-lg font-bold text-success">{mappingProgress.toFixed(0)}%</div>
            <div className="text-xs text-muted-foreground">{t("common.progress")}</div>
          </div>
        </div>
        <div className="h-2 w-full rounded-full bg-muted">
          <div
            className="h-2 rounded-full bg-success transition-all duration-300"
            style={{ width: `${mappingProgress}%` }}
          />
        </div>
      </CardContent>
    </Card>
  );
}
