import puppeteer from "puppeteer";

import { navigateWithAuth } from "./utils/auth.mjs";

async function validatePropertiesTab() {
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: { width: 1920, height: 1080 },
  });

  try {
    const page = await browser.newPage();

    console.log("🔐 Authenticating and navigating to property-assets-dashboard...");
    await navigateWithAuth(page, "http://localhost:3000/property-assets-dashboard");

    // Wait for page to load
    await new Promise((resolve) => setTimeout(resolve, 3000));

    console.log("📸 Taking initial dashboard screenshot...");
    await page.screenshot({
      path: "/Users/<USER>/Desktop/Projects/onex-erp/screenshots/properties-tab-dashboard-overview.png",
      fullPage: true,
    });

    // Check for tabs
    console.log("🔍 Looking for navigation tabs...");
    const tabs = await page.$$('[role="tablist"] button, .tabs button, button[data-state]');
    console.log(`📊 Found ${tabs.length} tab buttons`);

    // Get tab text content
    const tabTexts = [];
    for (let tab of tabs) {
      try {
        const text = await tab.evaluate((el) => el.textContent?.trim());
        if (text) tabTexts.push(text);
      } catch (e) {
        // Skip if error getting text
      }
    }
    console.log(`📝 Tab texts found: ${tabTexts.join(", ")}`);

    // Look specifically for Properties tab
    let propertiesTab = null;
    for (let tab of tabs) {
      try {
        const text = await tab.evaluate((el) => el.textContent?.trim().toLowerCase());
        if (text && text.includes("properties")) {
          propertiesTab = tab;
          break;
        }
      } catch (e) {
        // Continue searching
      }
    }

    if (propertiesTab) {
      console.log("✅ Properties tab found! Clicking on it...");
      await propertiesTab.click();

      // Wait for tab content to load
      await new Promise((resolve) => setTimeout(resolve, 2000));

      console.log("📸 Taking Properties tab screenshot...");
      await page.screenshot({
        path: "/Users/<USER>/Desktop/Projects/onex-erp/screenshots/properties-tab-active.png",
        fullPage: true,
      });

      // Check for PropertyGallery component
      const galleryComponent = await page.$(
        '[class*="gallery"], [class*="image"], .property-gallery'
      );
      console.log(`🖼️ PropertyGallery component found: ${galleryComponent ? "Yes" : "No"}`);

      // Check for property-related content
      const propertyContent = await page.$('[class*="property"], [class*="Property"]');
      console.log(`🏢 Property content found: ${propertyContent ? "Yes" : "No"}`);
    } else {
      console.log("❌ Properties tab not found");
    }

    // Test other tabs for comparison
    console.log("🔄 Testing other available tabs...");
    const availableTabs = tabs.slice(0, Math.min(6, tabs.length)); // Test first 6 tabs max

    for (let i = 0; i < availableTabs.length; i++) {
      try {
        const tab = availableTabs[i];
        const tabText = await tab.evaluate((el) => el.textContent?.trim());

        console.log(`🔘 Clicking tab: ${tabText}`);
        await tab.click();
        await new Promise((resolve) => setTimeout(resolve, 1500));

        await page.screenshot({
          path: `/Users/<USER>/Desktop/Projects/onex-erp/screenshots/properties-tab-${i}-${tabText?.toLowerCase().replace(/\s+/g, "-") || "unknown"}.png`,
          fullPage: true,
        });
      } catch (e) {
        console.log(`⚠️ Error testing tab ${i}: ${e.message}`);
      }
    }

    // Test responsive design
    console.log("📱 Testing mobile responsive design...");
    await page.setViewport({ width: 375, height: 667 });
    await new Promise((resolve) => setTimeout(resolve, 1000));
    await page.screenshot({
      path: "/Users/<USER>/Desktop/Projects/onex-erp/screenshots/properties-tab-mobile.png",
      fullPage: true,
    });

    console.log("✅ Properties tab validation completed!");
    console.log("📸 Screenshots saved:");
    console.log("   - properties-tab-dashboard-overview.png");
    console.log("   - properties-tab-active.png");
    console.log("   - properties-tab-mobile.png");
    console.log("   - properties-tab-[0-5]-[tab-name].png");

    return {
      success: true,
      tabsFound: tabs.length,
      tabTexts,
      propertiesTabFound: !!propertiesTab,
      screenshots: [
        "properties-tab-dashboard-overview.png",
        "properties-tab-active.png",
        "properties-tab-mobile.png",
      ],
    };
  } catch (error) {
    console.error("❌ Error validating properties tab:", error);
    return { success: false, error: error.message };
  } finally {
    await browser.close();
  }
}

// Run validation
validatePropertiesTab()
  .then((result) => {
    if (result.success) {
      console.log("\n🎉 Properties tab validation COMPLETED!");
      console.log(`✅ Total tabs found: ${result.tabsFound}`);
      console.log(`✅ Tab texts: ${result.tabTexts.join(", ")}`);
      console.log(`✅ Properties tab present: ${result.propertiesTabFound}`);
      console.log(`✅ Screenshots captured: ${result.screenshots.length}`);
    } else {
      console.log("\n❌ Properties tab validation FAILED!");
      console.log(`Error: ${result.error}`);
    }
  })
  .catch((error) => {
    console.error("❌ Script execution failed:", error);
  });
