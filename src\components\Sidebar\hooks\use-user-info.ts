import { useQuery } from "@tanstack/react-query";

import { authApi } from "@/lib/apis/auth";
import { getAuthToken } from "@/lib/auth";

export const useUserInfo = () => {
  const {
    data: userInfo,
    isLoading: isLoadingUser,
    error: userError,
  } = useQuery({
    queryKey: ["userInfo"],
    queryFn: async () => {
      const authData = getAuthToken();
      if (!authData?.Token?.AccessToken || !authData?.Token?.IdToken || !authData?.Username) {
        throw new Error("Authentication data not found");
      }

      const response = await authApi.getInfo(
        authData.Token.AccessToken,
        authData.Token.IdToken,
        authData.Username
      );

      return {
        Username: response.Username || "",
        UserAttributes: response.UserAttributes || [],
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!getAuthToken()?.Token?.AccessToken, // Only run query if we have auth data
  });

  return { userInfo, isLoadingUser, userError };
};
