import Image from "next/image";
import { useRouter } from "next/navigation";
import { Clock5, MoreHorizontal } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { StatusBadge } from "@/components/ui/status-badge";
import type { Layout } from "@/lib/apis/types/property_assets/layout";

import { useLayoutCard } from "./useLayoutCard";

interface LayoutCardProps {
  layout: Layout;
}

export function LayoutCard({ layout }: LayoutCardProps) {
  const { mappingProgress, totalUnits, progressPercentage } = useLayoutCard(layout);
  const router = useRouter();

  const handleView = () => {
    router.push(`/property-assets/properties/${layout.property_id}/layout/${layout.id}`);
  };

  return (
    <Card className="relative overflow-hidden">
      {/* Floor Plan Image */}
      <div className="h-[157px] w-full bg-muted">
        {layout.image?.url ? (
          <Image
            src={layout?.image?.url}
            alt={layout.name}
            width={0}
            height={0}
            sizes="100vw"
            className="size-full object-cover"
          />
        ) : (
          <div className="flex size-full items-center justify-center">
            <div className="text-center text-muted-foreground">
              <div className="mx-auto mb-2 h-24 w-32 rounded bg-muted/50"></div>
              <p className="text-xs">Floor Plan Preview</p>
            </div>
          </div>
        )}
      </div>

      {/* Content Section */}
      <div className="space-y-2 p-4">
        {/* Title */}
        <h3 className="text-base font-medium leading-6 text-foreground">{layout.name}</h3>

        {/* Progress Section */}
        <div className="space-y-1">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-1">
              <span className="text-muted-foreground">Mapping progress</span>
              <span className="text-foreground">
                {mappingProgress}/{totalUnits}
              </span>
            </div>
            <span className="text-right text-muted-foreground">
              {Math.round(progressPercentage)}%
            </span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
        </div>

        {/* Separator */}
        <Separator className="my-2" />

        {/* Last Activity */}
        <div className="flex items-center gap-1 text-sm text-muted-foreground">
          <Clock5 className="size-4" />
          <span>
            Last Activity:{" "}
            {layout.updated_at
              ? new Date(layout.updated_at).toLocaleDateString()
              : "Recently updated"}
          </span>
        </div>
      </div>
      <div className="absolute left-2 top-2 flex flex-col gap-1">
        <StatusBadge status={layout.status} />
        <Badge variant="secondary">
          {layout.floor_number ? `${layout.floor_number} Floor` : "N/A"}
        </Badge>
      </div>

      {/* Actions Dropdown - Top Right */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-2.5 top-2.5 size-8 p-0 hover:bg-muted/50">
            <MoreHorizontal className="size-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" sideOffset={4}>
          <DropdownMenuItem onClick={handleView}>View</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </Card>
  );
}
