import { authProtectedPaths } from "@/constants/paths";

export const PRODUCT_PERMISSIONS = {
  // Products
  [authProtectedPaths.PRODUCTS]: ["LIST_PRODUCT"],
  [authProtectedPaths.PRODUCTS_NEW]: ["CREATE_PRODUCT"],
  [authProtectedPaths.PRODUCTS_ID]: ["GET_PRODUCT"],
  [authProtectedPaths.PRODUCTS_ID_EDIT]: ["UPDATE_PRODUCT"],
  [`${authProtectedPaths.PRODUCTS}/:id/delete`]: ["DELETE_PRODUCT"],
  [`${authProtectedPaths.PRODUCTS}/:id/price-group`]: ["GET_PRODUCT_PRICE_GROUP"],
  [`${authProtectedPaths.PRODUCTS}/:id/files`]: ["UPDATE_PRODUCT_FILES"],
  [`${authProtectedPaths.PRODUCTS}/quick-create`]: ["CREATE_PRODUCT_QUICKLY"],
  [`${authProtectedPaths.PRODUCTS}/published`]: ["LIST_PUBLISHED_PRODUCT"],
  [`${authProtectedPaths.PRODUCTS}/export`]: ["PRODUCT_LIST_EXPORT_FILE"],

  // Variants
  [authProtectedPaths.VARIANTS]: ["LIST_VARIANT"],
  [authProtectedPaths.VARIANTS_ID]: ["GET_VARIANT"],
  [authProtectedPaths.VARIANTS_ID_EDIT]: ["UPDATE_VARIANT"],
  [`${authProtectedPaths.VARIANTS}/:id/delete`]: ["DELETE_VARIANT"],
  [`${authProtectedPaths.VARIANTS}/export`]: ["VARIANT_LIST_EXPORT_FILE"],

  // Brands
  [authProtectedPaths.BRANDS]: ["GET_BRAND", "LIST_BRAND"],
  [`${authProtectedPaths.BRANDS}/new`]: ["CREATE_BRAND"],
  [`${authProtectedPaths.BRANDS}/:id`]: ["GET_BRAND"],
  [`${authProtectedPaths.BRANDS}/:id/edit`]: ["UPDATE_BRAND"],
  [`${authProtectedPaths.BRANDS}/:id/delete`]: ["DELETE_BRAND"],
  [`${authProtectedPaths.BRANDS}/export`]: ["BRAND_LIST_EXPORT_FILE"],

  // Categories
  [authProtectedPaths.CATEGORIES]: ["LIST_CATEGORY"],
  [`${authProtectedPaths.CATEGORIES}/new`]: ["CREATE_CATEGORY"],
  [`${authProtectedPaths.CATEGORIES}/:id`]: ["GET_CATEGORY"],
  [`${authProtectedPaths.CATEGORIES}/:id/edit`]: ["UPDATE_CATEGORY"],
  [`${authProtectedPaths.CATEGORIES}/:id/delete`]: ["DELETE_CATEGORY"],
} as const;

export type ProductPermissionKey = keyof typeof PRODUCT_PERMISSIONS;
export type ProductPermissionValue = (typeof PRODUCT_PERMISSIONS)[ProductPermissionKey];
