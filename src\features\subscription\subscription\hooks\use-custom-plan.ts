"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { contactApi } from "@/lib/apis/contact";
import { getSimpleUserFromLocalStorage } from "@/lib/apis/users";

export interface CustomPlanRequirements {
  messages: number;
  staff: number;
  storage: number;
  contactEmail?: string;
  companyName?: string;
  // Configuration options
  multilingual: boolean;
  scheduleCustomerCare: boolean;
  customIntegration: boolean;
  aiModel: string;
}

interface UseCustomPlanOptions {
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
}

export function useCustomPlan(options: UseCustomPlanOptions = {}) {
  const { t } = useTranslation();
  const { onSuccess, onError } = options;
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get user info from localStorage
  const user = getSimpleUserFromLocalStorage();

  const form = useForm<CustomPlanRequirements>({
    defaultValues: {
      messages: 10000,
      staff: 10,
      storage: 500,
      contactEmail: user.email || "",
      companyName: user.company_id || "",
      // Configuration options with proper defaults
      multilingual: true,
      scheduleCustomerCare: true,
      customIntegration: true,
      aiModel: "gpt-4o",
    },
  });

  const {
    watch,
    setValue,
    handleSubmit,
    formState: { errors },
  } = form;
  const requirements = watch();

  const handleRequirementChange = (field: keyof CustomPlanRequirements, value: string | number) => {
    const newValue = typeof value === "number" ? Math.max(0, value) : value;
    setValue(field, newValue);
  };

  const handleQuantityChange = (field: "staff" | "messages" | "storage", increment: boolean) => {
    const currentValue = requirements[field];
    const step = field === "staff" ? 1 : field === "messages" ? 1000 : 100;
    const newValue = increment ? currentValue + step : Math.max(0, currentValue - step);
    setValue(field, newValue);
  };

  const onSubmit = async (data: CustomPlanRequirements) => {
    if (!data.contactEmail) {
      toast.error(
        t("pages.subscription.customPlan.emailRequired", "Please provide your contact email")
      );
      return;
    }

    if (!data.companyName) {
      toast.error(
        t("pages.subscription.customPlan.companyRequired", "Please provide your company name")
      );
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare the message payload for contact API with user info
      const messagePayload = {
        name: user.name || user.username || data.contactEmail.split("@")[0] || "Custom Plan User",
        phone: "", // Phone not available from user data
        email: data.contactEmail,
        organization_name: data.companyName,
        message: `Custom Plan Request:
        
Requirements:
- Virtual Assistants: ${data.staff}
- Messages: ${data.messages}
- Storage: ${data.storage} GB

Configuration Options:
- AI Model: ${data.aiModel}
- Multilingual Support: ${data.multilingual ? "Yes" : "No"}
- Schedule Customer Care: ${data.scheduleCustomerCare ? "Yes" : "No"}
- Custom Integration: ${data.customIntegration ? "Yes" : "No"}

User Details:
- Name: ${user.name || user.username || "Not provided"}
- Email: ${data.contactEmail}
- Company: ${data.companyName}
- Role: ${user.role || "Not provided"}
- User ID: ${user.id || "Not provided"}`,
        category: "Custom Plan Request",
        subject: "New Custom Plan Request from OneXBots",
        source: "OneXBots Custom Plan",
        enabled_notification: true,
      };

      // Send the message using contact API
      await contactApi.sendMessage(messagePayload);
      // Show success dialog
      setShowSuccessDialog(true);
      onSuccess?.(data);
    } catch (error) {
      console.error("Error submitting custom plan:", error);
      toast.error(
        t(
          "pages.subscription.customPlan.error",
          "Failed to submit custom plan request. Please try again."
        )
      );
      onError?.(error as Error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("en-US").format(num);
  };

  const resetRequirements = () => {
    form.reset({
      messages: 10000,
      staff: 10,
      storage: 500,
      contactEmail: user.email || "",
      companyName: user.company_id || "",
      // Reset configuration options
      multilingual: true,
      scheduleCustomerCare: true,
      customIntegration: true,
      aiModel: "gpt-4o",
    });
  };

  const closeSuccessDialog = () => {
    setShowSuccessDialog(false);
  };

  return {
    form,
    requirements,
    errors,
    isSubmitting,
    handleRequirementChange,
    handleQuantityChange,
    handleSubmit: handleSubmit(onSubmit),
    formatNumber,
    resetRequirements,
    showSuccessDialog,
    closeSuccessDialog,
    user,
  };
}
