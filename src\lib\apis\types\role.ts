import { ApiPermissionsResponse } from "../permission";

// Role type based on the API response
export interface Role {
  id: string;
  name: string;
  permissions: ApiPermissionsResponse;
  note?: string;
  memberCount?: number;
  deactivatedCount?: number;
  created_at?: string;
  updated_at?: string;
  total_disabled_user?: number;
  total_enabled_user?: number;
  total_user?: number;
}

// Role creation payload
export interface CreateRolePayload {
  name: string;
  note: string;
  permissions: Record<string, boolean>;
}

// Role update payload
export interface UpdateRolePayload {
  name?: string;
  permissions?: Record<string, boolean>;
  note?: string;
}
