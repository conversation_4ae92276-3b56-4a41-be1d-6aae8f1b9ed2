"use client";

import { cn } from "@/lib/utils";

interface RightSidePanelProps {
  isOpen: boolean;
  children: React.ReactNode;
  onClose: () => void;
  className?: string;
}

export const RightSidePanel = ({ isOpen, children, onClose, className }: RightSidePanelProps) => {
  return (
    <div
      className={cn(
        "hidden bg-card shadow-xl transition-all duration-300 ease-in-out md:block",
        isOpen ? "w-1/2 flex-1 translate-x-0 opacity-100" : "w-0 translate-x-full opacity-0",
        className
      )}>
      {isOpen && (
        <div className="flex h-full flex-col">
          <div className="flex-1 overflow-auto p-4">{children}</div>
        </div>
      )}
    </div>
  );
};
