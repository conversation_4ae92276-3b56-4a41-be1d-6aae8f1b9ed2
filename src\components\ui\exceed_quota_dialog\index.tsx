"use client";

import { CircleX, Zap } from "lucide-react";
import { useTranslation } from "react-i18next";

import { convertByte } from "@/lib/utils/convert_byte";

import { Button } from "../button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "../dialog";
import type { ExceedQuotaDialogProps } from "./exceed-quota-dialog.types";

/**
 * ExceedQuotaDialog - A modal dialog that cannot be closed by user interaction
 * Users must either upgrade or see all plans to proceed when they exceed their quota
 */
export function ExceedQuotaDialog({
  open,
  onOpenChange,
  currentPlan = "Advance",
  expiresOn = "June 25, 2025",
  usage = { used: "10", max: "10", quotaKey: "staff" },
  quotaKey = "staff",
  onUpgrade,
  onSeeAllPlans,
}: ExceedQuotaDialogProps) {
  const { t } = useTranslation();

  // Helper function to format usage value based on quota key
  const formatUsageValue = (
    usageValue: string | { used: string; max: string; quotaKey: string },
    key: string
  ) => {
    // If usageValue is an object, extract the values
    if (typeof usageValue === "object" && usageValue !== null) {
      const { used, max } = usageValue;

      // Check if the key contains "capacity" to apply byte conversion
      if (key.toLowerCase().includes("capacity")) {
        const usedValue = parseInt(used, 10);
        const maxValue = parseInt(max, 10);

        if (!isNaN(usedValue) && !isNaN(maxValue)) {
          return `${convertByte(usedValue)}/${convertByte(maxValue)}`;
        }
      }

      // For non-capacity quotas, return the formatted string
      return `${used}/${max}`;
    }

    // Handle string format (legacy support)
    if (typeof usageValue === "string") {
      if (key.toLowerCase().includes("capacity")) {
        const parts = usageValue.split("/");
        if (parts.length === 2) {
          const usedValue = parseInt(parts[0], 10);
          const totalValue = parseInt(parts[1], 10);

          if (!isNaN(usedValue) && !isNaN(totalValue)) {
            return `${convertByte(usedValue)}/${convertByte(totalValue)}`;
          }
        }
      }
      return usageValue;
    }

    return String(usageValue);
  };

  const handleUpgrade = () => {
    onUpgrade?.();
    onOpenChange(false);
  };

  const handleSeeAllPlans = () => {
    onSeeAllPlans?.();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[500px] p-6" isShowClose={true}>
        <div className="relative">
          {/* Icon Wrapper */}
          <div className="mb-4 flex justify-center">
            <div className="flex size-12 items-center justify-center rounded-full bg-destructive/10">
              <CircleX className="size-6 text-destructive" />
            </div>
          </div>

          {/* Header */}
          <DialogHeader className="text-center">
            <DialogTitle className="text-center text-2xl font-semibold leading-none tracking-tight">
              {t("pages.subscription.exceedQuota.title")}
            </DialogTitle>
            <DialogDescription className="mt-2 text-center text-sm text-muted-foreground">
              {t("pages.subscription.exceedQuota.description", {
                quotaType: t(`pages.subscription.exceedQuota.quotaTypes.${quotaKey}`),
              })}
            </DialogDescription>
          </DialogHeader>

          {/* Plan Information */}
          <div className="mt-6 grid grid-cols-1 gap-1.5 sm:grid-cols-2">
            {/* Current Plan */}
            <div className="flex items-center gap-2">
              <div className="size-1 rounded-full bg-muted-foreground" />
              <span className="text-sm font-medium">
                {t("pages.subscription.currentPlan.title")}
              </span>
              <span className="text-sm">{currentPlan}</span>
            </div>

            {/* Expires On */}
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <span className=" ">{t("pages.subscription.currentPlan.expiresOn")}</span>
              <span className=" ">{expiresOn}</span>
            </div>
          </div>
          {/* Usage */}
          <div className="flex items-center gap-2">
            <div className="size-1 rounded-full bg-muted-foreground" />
            <span className="flex-none text-sm font-medium">
              {t("pages.subscription.exceedQuota.yourUsage")}
            </span>
            <span className="text-sm text-destructive">{formatUsageValue(usage, quotaKey)}</span>
            <span className="text-sm text-destructive">({t(`quota.${quotaKey}`)})</span>
          </div>
          {/* Action Buttons */}
          <div className="mt-6 flex flex-col gap-2">
            <Button
              onClick={handleUpgrade}
              className="w-full bg-primary text-primary-foreground hover:bg-primary/90"
              leftIcon={<Zap className="size-4" />}>
              {t("pages.subscription.exceedQuota.upgradeButton")}
            </Button>
            <Button variant="outline" onClick={handleSeeAllPlans} className="w-full">
              {t("pages.subscription.exceedQuota.seeAllPlans")}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Export the hook for external use
export { useExceedQuotaDialog } from "./use-exceed-quota-dialog";
export type {
  ExceedQuotaDialogProps,
  UseExceedQuotaDialogProps,
} from "./exceed-quota-dialog.types";
