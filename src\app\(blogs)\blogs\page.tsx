"use client";

import { useMemo } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Download, Plus } from "lucide-react";
import { useTranslation } from "react-i18next";

import { columns } from "@/features/blogs/blog-list/column";
import { useBlogList } from "@/features/blogs/hooks/blog";

import { TableContainer } from "@/components/custom-table/container/table-container";
import GroupButton from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { EFilterType, FilterTableProps, FilterType } from "@/components/data-table/types";
import { authProtectedPaths } from "@/constants/paths";

export default function BlogListPage() {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch } = useDatatable();
  const queryClient = useQueryClient();
  const options = useMemo(
    () => ({ limit: Number(getInitialParams.limit), ...getInitialParams }),
    [getInitialParams]
  );

  const { data, isLoading, isFetching, refetch } = useBlogList(options);

  const useDeleteBlogMutation = useMutation({
    mutationFn: async (id: string) => {
      // Implement delete API if available
      // await blogsApi.delete(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["blogs"] });
    },
  });

  const blogs = data?.items ?? [];
  const total = data?.total ?? 0;
  const isTableLoading = isLoading || isFetching;

  // Filter configuration
  const filterConfig: FilterTableProps = useMemo(
    () => ({
      showSearch: true,
      filterType: "blogs",
      searchPlaceHolder: t("pages.blogList.filters.search.placeholder"),
      initialValues: getInitialParams,
      listFilter: [
        {
          id: "category.id",
          type: EFilterType.SELECT_BOX,
          title: t("pages.blogList.filters.category"),
          remote: true,
          pathUrlLoad: "blog/categories",
          defaultValue: getInitialParams["category.id"],
        },
        {
          id: "created_at",
          type: EFilterType.DATE,
          title: t("pages.blogList.filters.createdAt"),
          defaultValue: {
            from: getInitialParams["created_at_from"],
            to: getInitialParams["created_at_to"],
          },
        },
        {
          id: "updated_at",
          type: EFilterType.DATE,
          title: t("pages.blogList.filters.updatedAt"),
          defaultValue: {
            from: getInitialParams["updated_at_from"],
            to: getInitialParams["updated_at_to"],
          },
        },
      ] as FilterType[],
      handleParamSearch,
      listLoading: isTableLoading,
    }),
    [t, getInitialParams, handleParamSearch, isTableLoading]
  );

  // Group button configuration
  const groupButtonConfig = {
    buttons: [
      {
        type: "button" as const,
        title: t("pages.blogList.actions.import"),
        icon: Download,
        variant: "outline" as const,
        onClick: () => {
          // Implement import functionality
          console.log("Import blogs");
        },
      },
      {
        type: "button" as const,
        title: t("pages.blogList.actions.addBlog"),
        icon: Plus,
        onClick: () => {
          // Navigate to add blog page
          window.location.href = authProtectedPaths.BLOGS_NEW;
        },
      },
    ],
    onRefresh: () => refetch(),
    isRefreshLoading: isFetching,
  };

  return (
    <TableCard className="border-none">
      <TableHeader
        title={t("pages.blogList.title")}
        filterType="blogs"
        data={blogs}
        filterProps={filterConfig}
        rightComponent={<GroupButton {...groupButtonConfig} />}
      />
      <TableContainer
        columns={columns(useDeleteBlogMutation, isFetching, t)}
        data={blogs}
        loading={isTableLoading}
        total={total}
        pageSize={Number(getInitialParams.limit)}
        currentPage={Number(getInitialParams.page)}
        onHandleDelete={async (listIndexId: number[], handleRestRows) => {
          // TODO: Implement bulk delete functionality
          // For now, just reset the selected rows
          handleRestRows();
        }}
      />
    </TableCard>
  );
}
