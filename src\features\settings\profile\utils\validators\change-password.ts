import { z } from "zod";

export const changePasswordSchema = z
  .object({
    currentPassword: z
      .string()
      .min(1, "pages.profile.changePasswordDiaglog.currentPasswordRequired"),
    newPassword: z
      .string()
      .min(8, "pages.profile.changePasswordDiaglog.passwordTooShort")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        "pages.profile.changePasswordDiaglog.passwordComplexity"
      ),
    confirmPassword: z
      .string()
      .min(1, "pages.profile.changePasswordDiaglog.confirmPasswordRequired"),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "pages.profile.changePasswordDiaglog.passwordsDoNotMatch",
    path: ["confirmPassword"],
  });

export type ChangePasswordFormData = z.infer<typeof changePasswordSchema>;
