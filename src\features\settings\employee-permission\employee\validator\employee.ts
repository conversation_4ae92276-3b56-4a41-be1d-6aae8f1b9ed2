import { z } from "zod";

// Base schema for employee data
const baseEmployeeSchema = z.object({
  employeeName: z.string().min(1, "pages.settings.employeeNameRequired"),
  username: z
    .string()
    .min(3, "pages.settings.usernameMinLength")
    .regex(/^[a-zA-Z0-9_]+$/, "pages.settings.usernameInvalid"),
  email: z.string().min(1, "pages.settings.emailRequired").email("pages.settings.emailInvalid"),
  phone_number: z.string().regex(/^\d+$/, "Only digits allowed.").optional().or(z.literal("")),
  birthdate: z.date().optional(),
  address: z.string().optional(),
  roles: z
    .array(
      z.object({
        branch: z.string().min(1, "pages.settings.branchRequired"),
        roles: z.array(z.string()).min(1, "pages.settings.atLeastOneRoleRequired"),
      })
    )
    .min(1, "pages.settings.atLeastOneRoleAssignmentRequired"),
});

// Form validation schema for adding employees (with password validation)
export const addEmployeeSchema = baseEmployeeSchema
  .extend({
    password: z.string().min(1, "pages.settings.passwordRequired"),
    confirmPassword: z.string().min(1, "pages.settings.confirmPasswordRequired"),
  })
  .refine(
    (data) => {
      // Only validate password match if both password fields are provided
      if (data.password && data.confirmPassword) {
        return data.password === data.confirmPassword;
      }
      return true;
    },
    {
      message: "pages.settings.passwordsDontMatch",
      path: ["confirmPassword"],
    }
  )
  .refine(
    (data) => {
      // Validate that each role assignment has both branch and roles
      if (data.roles && data.roles.length > 0) {
        return data.roles.every(
          (role) => role.branch && role.branch.trim() !== "" && role.roles && role.roles.length > 0
        );
      }
      return false; // At least one role assignment is required
    },
    {
      message: "pages.settings.eachRoleAssignmentMustHaveABranchAndAtLeastOneRoleSelected",
      path: ["roles"],
    }
  )
  .refine(
    (data) => {
      // Validate that "All" branches is not mixed with individual branches
      if (data.roles && data.roles.length > 0) {
        const hasAllBranches = data.roles.some((role) => role.branch === "all");
        const hasIndividualBranches = data.roles.some(
          (role) => role.branch !== "all" && role.branch.trim() !== ""
        );

        // If "All" is selected, there should be no individual branches
        if (hasAllBranches && hasIndividualBranches) {
          return false;
        }
      }
      return true;
    },
    {
      message: "pages.settings.cannotMixAllBranchesWithIndividualBranchSelections",
      path: ["roles"],
    }
  );

// Form validation schema for editing employees (without password validation)
export const editEmployeeSchema = baseEmployeeSchema
  .extend({
    username: z.string().optional(),
    email: z.string().optional(),
  })
  .refine(
    (data) => {
      // Validate that each role assignment has both branch and roles
      if (data.roles && data.roles.length > 0) {
        return data.roles.every(
          (role) => role.branch && role.branch.trim() !== "" && role.roles && role.roles.length > 0
        );
      }
      return false; // At least one role assignment is required
    },
    {
      message: "pages.settings.eachRoleAssignmentMustHaveABranchAndAtLeastOneRoleSelected",
      path: ["roles"],
    }
  )
  .refine(
    (data) => {
      // Validate that "All" branches is not mixed with individual branches
      if (data.roles && data.roles.length > 0) {
        const hasAllBranches = data.roles.some((role) => role.branch === "all");
        const hasIndividualBranches = data.roles.some(
          (role) => role.branch !== "all" && role.branch.trim() !== ""
        );

        // If "All" is selected, there should be no individual branches
        if (hasAllBranches && hasIndividualBranches) {
          return false;
        }
      }
      return true;
    },
    {
      message: "pages.settings.cannotMixAllBranchesWithIndividualBranchSelections",
      path: ["roles"],
    }
  );

// Type for the form data
export type AddEmployeeFormData = z.infer<typeof addEmployeeSchema>;
export type EditEmployeeFormData = z.infer<typeof editEmployeeSchema>;

// Interface for role assignments
export interface RoleAssignment {
  id: string;
  branch: string;
  roles: string[];
}

// Employee interface for the component props
export interface Employee {
  id: string;
  name: string;
  username: string;
  email: string;
  phone_number: string | null;
  dob: string | null;
  address: string | null;
  user_roles?: Array<{
    branch_id: string;
    role_ids: string[];
  }>;
  role?: string;
}
