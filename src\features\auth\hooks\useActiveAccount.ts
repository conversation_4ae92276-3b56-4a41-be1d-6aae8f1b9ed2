import { useRouter } from "next/navigation";
import { useMutation } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { authApi } from "@/lib/apis/auth";

import { useLogin } from "./useLogin";

export interface ChangePasswordPayload {
  username: string;
  password: string;
  new_password: string;
}

export const useActiveAccount = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const { directLogin, loading: isDirectLoginLoading } = useLogin();

  const changePasswordMutation = useMutation({
    mutationFn: async (payload: Omit<ChangePasswordPayload, "username">) => {
      const tempUsername = localStorage.getItem("tempUsername");

      if (!tempUsername) {
        throw new Error("Authentication data not found");
      }

      const fullPayload: ChangePasswordPayload = {
        username: tempUsername,
        password: payload.password,
        new_password: payload.new_password,
      };

      const response = await authApi.changePassword(fullPayload);
      return response;
    },
    onSuccess: async (data, variables) => {
      toast.success(t("common.success"), {
        description: t("pages.profile.changePasswordDiaglog.passwordChangedSuccessfully"),
      });

      // Get the stored username and new password for automatic login
      const tempUsername = localStorage.getItem("tempUsername");
      const newPassword = variables.new_password; // Use the new password from the mutation variables

      // Clear the temporary username
      localStorage.removeItem("tempUsername");

      try {
        // Automatically log in the user with their new password
        await directLogin(tempUsername!, newPassword);
        // The useLogin hook will handle the redirect to dashboard based on user role/onboarding status
      } catch (error) {
        // If auto-login fails, redirect to login page as fallback
        console.error("Auto-login failed:", error);
        toast.error(t("common.error"), {
          description: t("auth.autoLoginFailed"),
        });
        router.push("/login");
      }
    },
    onError: (error: any) => {
      console.log(error);
      if (error === "Password attempts exceeded") {
        toast.error(t("common.error"), {
          description: t("pages.profile.changePasswordDiaglog.passwordAttemptsExceeded"),
        });
        return;
      }

      if (error === "Incorrect username or password.") {
        toast.error(t("common.error"), {
          description: t("pages.profile.changePasswordDiaglog.passwordDoesNotMatch"),
        });
        return;
      }

      const errorMessage = t("pages.profile.changePasswordDiaglog.passwordChangeFailed");

      toast.error(t("common.error"), {
        description: errorMessage || error,
      });
    },
  });

  const changePassword = async (password: string, new_password: string) => {
    return changePasswordMutation.mutateAsync({ password, new_password });
  };

  return {
    changePassword,
    isLoading: changePasswordMutation.isPending || isDirectLoginLoading,
    error: changePasswordMutation.error,
    reset: changePasswordMutation.reset,
  };
};
