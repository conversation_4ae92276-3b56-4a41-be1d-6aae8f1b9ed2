"use client";

import { useMemo } from "react";
import { PlusIcon } from "lucide-react";
import { useTranslation } from "react-i18next";

import { columns, useDeleteUnit, useUnits } from "@/features/property-assets/unit";

import { TableContainer } from "@/components/custom-table/container/table-container";
import GroupButton, { GroupButtonProps } from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { EFilterType, FilterTableProps, FilterType } from "@/components/data-table/types";
import { authProtectedPaths } from "@/constants/paths";

export default function UnitsPage() {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch } = useDatatable();

  const options = useMemo(
    () => ({ limit: Number(getInitialParams.limit), ...getInitialParams }),
    [getInitialParams]
  );

  const { data: unitsData, isLoading, isFetching, refetch } = useUnits(undefined, options);

  const deleteUnitMutation = useDeleteUnit();

  const units = unitsData?.items || [];
  const total = unitsData?.total || 0;

  const isTableLoading = isLoading || isFetching;

  const listFilter: FilterType[] = useMemo(
    () => [
      {
        id: "type",
        type: EFilterType.SELECT_BOX,
        title: t("pages.units.filters.type"),
        defaultValue: getInitialParams["type"] as string,
        dataOption: [
          { value: "studio", label: "Studio" },
          { value: "1br", label: "1 Bedroom" },
          { value: "2br", label: "2 Bedroom" },
          { value: "3br", label: "3 Bedroom" },
          { value: "commercial", label: "Commercial" },
        ],
      },
      {
        id: "status",
        type: EFilterType.SELECT_BOX,
        title: t("pages.units.filters.status"),
        defaultValue: getInitialParams["status"] as string,
        dataOption: [
          { value: "available", label: "Available" },
          { value: "occupied", label: "Occupied" },
          { value: "maintenance", label: "Maintenance" },
          { value: "unavailable", label: "Unavailable" },
        ],
      },
      {
        id: "property_id",
        type: EFilterType.SELECT_BOX,
        title: t("pages.units.filters.property"),
        defaultValue: getInitialParams["property_id"] as string,
        dataOption: [
          // This would typically be populated from a properties list
          { value: "1", label: "Property 1" },
          { value: "2", label: "Property 2" },
        ],
      },
      {
        id: "rent_range",
        type: EFilterType.SELECT_BOX,
        title: t("pages.units.filters.rentRange"),
        defaultValue: getInitialParams["rent_range"] as string,
        dataOption: [
          { value: "0-1000", label: "$0 - $1,000" },
          { value: "1000-2000", label: "$1,000 - $2,000" },
          { value: "2000-3000", label: "$2,000 - $3,000" },
          { value: "3000+", label: "$3,000+" },
        ],
      },
      {
        id: "created_at",
        type: EFilterType.DATE,
        title: t("pages.units.filters.createdAt"),
        defaultValue: getInitialParams["created_at_from"] as string,
      },
      {
        id: "updated_at",
        type: EFilterType.DATE,
        title: t("pages.units.filters.updatedAt"),
        defaultValue: getInitialParams["updated_at_from"] as string,
      },
    ],
    [t, getInitialParams]
  );

  const filterConfig: FilterTableProps = useMemo(
    () => ({
      showSearch: true,
      filterType: "units",
      searchPlaceHolder: t("pages.units.filters.search.placeholder"),
      initialValues: getInitialParams,
      listFilter,
      handleParamSearch,
      listLoading: isTableLoading,
    }),
    [t, getInitialParams, listFilter, handleParamSearch, isTableLoading]
  );

  const groupButtonConfig: GroupButtonProps = {
    buttons: [
      {
        type: "dropdown" as const,
        title: t("pages.units.add"),
        icon: PlusIcon,
        items: [
          {
            type: "add_manual",
            title: t("pages.units.actions.addManual"),
            icon: PlusIcon,
            href: authProtectedPaths.UNITS_NEW,
          },
        ],
      },
    ],
    onRefresh: () => refetch(),
    isRefreshLoading: isFetching,
  };

  return (
    <TableCard>
      <TableHeader
        title={t("pages.units.title")}
        filterType="units"
        data={units}
        filterProps={filterConfig as FilterTableProps}
        rightComponent={<GroupButton {...groupButtonConfig} />}
      />
      <TableContainer
        columns={columns(deleteUnitMutation, isFetching, t)}
        data={units}
        loading={isTableLoading}
        total={total}
        pageSize={Number(getInitialParams.limit)}
        currentPage={Number(getInitialParams.page)}
        onHandleDelete={async (listIndexId: number[], handleRestRows) => {
          // Bulk delete functionality can be added later
          console.log("Bulk delete selected:", listIndexId);
          handleRestRows();
        }}
      />
    </TableCard>
  );
}
