"use client";

import { useRouter } from "next/navigation";

import { queryClient } from "@/components/providers";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { clearAuth } from "@/lib/auth";

import { useForgotPassword } from "../hooks/useForgotPassword";
import { EmailStep } from "./ForgotPasswordDialog/EmailStep";
import { ResetPasswordStep } from "./ForgotPasswordDialog/ResetPasswordStep";
import { SuccessStep } from "./ForgotPasswordDialog/SuccessStep";
import { VerificationStep } from "./ForgotPasswordDialog/VerificationStep";

interface ForgotPasswordDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const ForgotPasswordDialog = ({ open, onOpenChange }: ForgotPasswordDialogProps) => {
  const {
    currentStep,
    email,
    verificationCode,
    newPassword,
    confirmPassword,
    showNewPassword,
    showConfirmPassword,
    isLoading,
    countdown,
    isCheckingDelay,
    session,
    sendCodeError,
    verifyCodeError,
    resetPasswordError,
    setEmail,
    setVerificationCode,
    setNewPassword,
    setConfirmPassword,
    setShowNewPassword,
    setShowConfirmPassword,
    setCurrentStep,
    resetState,
    handleSendCode,
    handleVerifyCode,
    handleResetPassword,
    handleResendCode,
    checkCountdown,
  } = useForgotPassword();

  const handleClose = () => {
    resetState();
    onOpenChange(false);
  };

  const router = useRouter();

  const handleBackToLogin = () => {
    // Perform logout when leaving success step
    try {
      // Clear all TanStack Query cache to prevent data from previous sessions
      queryClient.clear();

      // Clear authentication
      clearAuth();

      // Close dialog and redirect to login
      handleClose();

      // Redirect to login page
      router.push("/login");
    } catch (error) {
      console.error("Logout failed:", error);
      // Fallback: just close the dialog
      handleClose();
    }
  };

  const handleBackToEmail = () => {
    setCurrentStep("email");
  };

  const handleFocus = () => {
    // Reset any errors when user focuses on input
    if (sendCodeError) {
      // Reset send code error
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case "email":
        return (
          <EmailStep
            email={email}
            isLoading={isLoading}
            countdown={countdown}
            isCheckingDelay={isCheckingDelay}
            error={sendCodeError}
            onEmailChange={setEmail}
            onSendCode={handleSendCode}
            onCancel={handleClose}
            onFocus={handleFocus}
          />
        );

      case "verification":
        return (
          <VerificationStep
            email={email}
            verificationCode={verificationCode}
            isLoading={isLoading}
            countdown={countdown}
            isCheckingDelay={isCheckingDelay}
            error={verifyCodeError}
            onVerificationCodeChange={setVerificationCode}
            onVerifyCode={handleVerifyCode}
            onResendCode={handleResendCode}
            onBack={handleBackToEmail}
          />
        );

      case "reset":
        return (
          <ResetPasswordStep
            newPassword={newPassword}
            confirmPassword={confirmPassword}
            showNewPassword={showNewPassword}
            showConfirmPassword={showConfirmPassword}
            isLoading={isLoading}
            error={resetPasswordError}
            onNewPasswordChange={setNewPassword}
            onConfirmPasswordChange={setConfirmPassword}
            onToggleNewPasswordVisibility={() => setShowNewPassword(!showNewPassword)}
            onToggleConfirmPasswordVisibility={() => setShowConfirmPassword(!showConfirmPassword)}
            onResetPassword={handleResetPassword}
            onBack={handleBackToEmail}
          />
        );

      case "success":
        return <SuccessStep onBackToLogin={handleBackToLogin} />;

      default:
        return null;
    }
  };

  // Custom onOpenChange handler to prevent closing on success step
  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen && (currentStep === "success" || currentStep === "reset")) {
      // Don't allow closing on success step - just ignore the close attempt
      return;
    }
    onOpenChange(newOpen);
  };

  // Determine if close button should be shown
  const shouldShowClose: boolean = currentStep === "email" || currentStep === "verification";

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="w-[600px] max-w-[90vw] p-0" isShowClose={shouldShowClose}>
        <div>{renderStepContent()}</div>
      </DialogContent>
    </Dialog>
  );
};
