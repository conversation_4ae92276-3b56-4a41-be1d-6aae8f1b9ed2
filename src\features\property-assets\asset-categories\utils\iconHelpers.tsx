import {
  Building,
  Car,
  FolderOpen,
  Home,
  Lightbulb,
  Shield,
  TreePine,
  Wifi,
  Wrench,
} from "lucide-react";

export const getIconComponent = (iconName: string) => {
  const iconMap = {
    Home,
    Building,
    Car,
    Wrench,
    Lightbulb,
    Shield,
    TreePine,
    Wifi,
  };
  const IconComponent = iconMap[iconName as keyof typeof iconMap] || FolderOpen;
  return <IconComponent className="size-8" />;
};

export const getSmallIconComponent = (iconName: string) => {
  const iconMap = {
    Home,
    Building,
    Car,
    Wrench,
    Lightbulb,
    Shield,
    TreePine,
    Wifi,
  };
  const IconComponent = iconMap[iconName as keyof typeof iconMap] || FolderOpen;
  return <IconComponent className="size-4" />;
};
