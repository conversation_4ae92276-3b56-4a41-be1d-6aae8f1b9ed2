import { useRouter } from "next/navigation";
import { useMutation } from "@tanstack/react-query";
import { format } from "date-fns";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import { authApi } from "@/lib/apis/auth";
import { createI18nResolver } from "@/lib/utils";

import { SignupFormValues, signupSchema } from "../utils/validators/signup";
import { authKeys } from "./keys";

export const useSignup = () => {
  const router = useRouter();
  const { t } = useTranslation();

  const form = useForm<SignupFormValues>({
    resolver: createI18nResolver(signupSchema, t),
    defaultValues: {
      email: "",
      username: "",
      password: "",
      confirmPassword: "",
      gender: "",
      birthdate: undefined,
    },
  });

  const {
    mutate: onSubmit,
    isPending: loading,
    error,
    isSuccess: success,
    reset: resetMutation,
  } = useMutation({
    mutationKey: authKeys.signup(),
    mutationFn: async (data: SignupFormValues) => {
      try {
        if (!data.username || !data.password || !data.email) {
          throw new Error(t("validation.required"));
        }

        const response = await authApi.signup({
          ...data,
          birthdate: data.birthdate ? format(data.birthdate, "yyyy-MM-dd") : undefined,
        } as any);

        return response;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (data, variables) => {
      const expiryTimestamp = Date.now() + 60 * 1000;
      localStorage.setItem(`resend_countdown_${variables.username}`, expiryTimestamp.toString());
      localStorage.setItem(`register_email_${variables.username}`, variables.email);
      router.push(`/verify-confirmation-code?username=${encodeURIComponent(variables.username)}`);
    },
  });

  const handleFocus = () => {
    resetMutation();
  };

  const signupSubmit = (formData: SignupFormValues) => {
    onSubmit(formData);
  };

  return {
    signupSubmit,
    loading,
    form,
    error,
    success,
    handleFocus,
  };
};
