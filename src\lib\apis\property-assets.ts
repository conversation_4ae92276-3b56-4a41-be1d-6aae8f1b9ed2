import axios from "axios";

import type {
  ApiResponse,
  Contract,
  CreateContract,
  CreateMaintenanceRequest,
  CreatePayment,
  CreateProperty,
  CreateTenant,
  CreateUnit,
  FinancialSummary,
  MaintenanceRequest,
  Payment,
  ProfitLossReport,
  Tenant,
  Transaction,
  Unit,
} from "@/features/property-assets/types";

import { ENDPOINTS } from "@/constants/endpoints";

import { ResponseList } from "./types/common";
import { Property } from "./types/property_assets/property";

// Create a dedicated API client for property-assets that uses local Next.js routes
const propertyAssetsApi = axios.create({
  baseURL: "", // Empty baseURL to use relative paths for local Next.js API routes
  headers: {
    "Content-Type": "application/json",
  },
});

// Add response interceptor to match propertyAssetsApi behavior
propertyAssetsApi.interceptors.response.use(
  (response) => response.data,
  (error) => Promise.reject(error.response?.data?.message || error.message)
);

// Property APIs
export const propertyApi = {
  list: async (params?: Record<string, unknown>) => {
    return await propertyAssetsApi.get<ResponseList<Property>>(
      ENDPOINTS.PROPERTY_ASSETS.PROPERTIES,
      {
        params,
      }
    );
  },

  create: async (data: CreateProperty) => {
    return await propertyAssetsApi.post<ApiResponse<Property>>(
      ENDPOINTS.PROPERTY_ASSETS.CREATE_PROPERTY,
      data
    );
  },

  update: async (id: string, data: Partial<CreateProperty>) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.UPDATE_PROPERTY.replace(":id", id);
    return await propertyAssetsApi.put<ApiResponse<Property>>(url, data);
  },

  delete: async (id: string) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.DELETE_PROPERTY.replace(":id", id);
    return await propertyAssetsApi.delete<ApiResponse<void>>(url);
  },

  getById: async (id: string) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.GET_PROPERTY.replace(":id", id);
    return await propertyAssetsApi.get<ApiResponse<Property>>(url);
  },
};

// Unit APIs
export const unitApi = {
  list: async (params?: Record<string, unknown>) => {
    console.log("unitApi.list called with params:", params);
    console.log("Using endpoint:", ENDPOINTS.PROPERTY_ASSETS.UNITS);
    return await propertyAssetsApi.get<ResponseList<Unit>>(ENDPOINTS.PROPERTY_ASSETS.UNITS, {
      params,
    });
  },

  create: async (data: CreateUnit) => {
    return await propertyAssetsApi.post<ApiResponse<Unit>>(
      ENDPOINTS.PROPERTY_ASSETS.CREATE_UNIT,
      data
    );
  },

  update: async (id: string, data: Partial<CreateUnit>) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.UPDATE_UNIT.replace(":id", id);
    return await propertyAssetsApi.put<ApiResponse<Unit>>(url, data);
  },

  delete: async (id: string) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.DELETE_UNIT.replace(":id", id);
    return await propertyAssetsApi.delete<ApiResponse<void>>(url);
  },

  getById: async (id: string) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.GET_UNIT.replace(":id", id);
    return await propertyAssetsApi.get<ApiResponse<Unit>>(url);
  },

  getByProperty: async (propertyId: string, params?: Record<string, unknown>) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.PROPERTY_UNITS.replace(":propertyId", propertyId);
    return await propertyAssetsApi.get<ResponseList<Unit>>(url, { params });
  },
};

// Tenant APIs
export const tenantApi = {
  list: async (params?: Record<string, unknown>) => {
    return await propertyAssetsApi.get<ResponseList<Tenant>>(ENDPOINTS.PROPERTY_ASSETS.TENANTS, {
      params,
    });
  },

  create: async (data: CreateTenant) => {
    return await propertyAssetsApi.post<ApiResponse<Tenant>>(
      ENDPOINTS.PROPERTY_ASSETS.CREATE_TENANT,
      data
    );
  },

  update: async (id: string, data: Partial<CreateTenant>) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.UPDATE_TENANT.replace(":id", id);
    return await propertyAssetsApi.put<ApiResponse<Tenant>>(url, data);
  },

  delete: async (id: string) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.DELETE_TENANT.replace(":id", id);
    return await propertyAssetsApi.delete<ApiResponse<void>>(url);
  },

  getById: async (id: string) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.GET_TENANT.replace(":id", id);
    return await propertyAssetsApi.get<ApiResponse<Tenant>>(url);
  },
};

// Contract APIs
export const contractApi = {
  list: async (params?: Record<string, unknown>) => {
    return await propertyAssetsApi.get<ResponseList<Contract>>(
      ENDPOINTS.PROPERTY_ASSETS.CONTRACTS,
      {
        params,
      }
    );
  },

  create: async (data: CreateContract) => {
    return await propertyAssetsApi.post<ApiResponse<Contract>>(
      ENDPOINTS.PROPERTY_ASSETS.CREATE_CONTRACT,
      data
    );
  },

  update: async (id: string, data: Partial<CreateContract>) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.UPDATE_CONTRACT.replace(":id", id);
    return await propertyAssetsApi.put<ApiResponse<Contract>>(url, data);
  },

  delete: async (id: string) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.DELETE_CONTRACT.replace(":id", id);
    return await propertyAssetsApi.delete<ApiResponse<void>>(url);
  },

  getById: async (id: string) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.GET_CONTRACT.replace(":id", id);
    return await propertyAssetsApi.get<ApiResponse<Contract>>(url);
  },

  getByTenant: async (tenantId: string, params?: Record<string, unknown>) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.TENANT_CONTRACTS.replace(":tenantId", tenantId);
    return await propertyAssetsApi.get<ResponseList<Contract>>(url, { params });
  },

  getByUnit: async (unitId: string, params?: Record<string, unknown>) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.UNIT_CONTRACTS.replace(":unitId", unitId);
    return await propertyAssetsApi.get<ResponseList<Contract>>(url, { params });
  },
};

// Payment APIs
export const paymentApi = {
  list: async (params?: Record<string, unknown>) => {
    return await propertyAssetsApi.get<ResponseList<Payment>>(ENDPOINTS.PROPERTY_ASSETS.PAYMENTS, {
      params,
    });
  },

  create: async (data: CreatePayment) => {
    return await propertyAssetsApi.post<ApiResponse<Payment>>(
      ENDPOINTS.PROPERTY_ASSETS.CREATE_PAYMENT,
      data
    );
  },

  update: async (id: string, data: Partial<CreatePayment>) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.UPDATE_PAYMENT.replace(":id", id);
    return await propertyAssetsApi.put<ApiResponse<Payment>>(url, data);
  },

  delete: async (id: string) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.DELETE_PAYMENT.replace(":id", id);
    return await propertyAssetsApi.delete<ApiResponse<void>>(url);
  },

  getById: async (id: string) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.GET_PAYMENT.replace(":id", id);
    return await propertyAssetsApi.get<ApiResponse<Payment>>(url);
  },

  getByContract: async (contractId: string, params?: Record<string, unknown>) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.CONTRACT_PAYMENTS.replace(":contractId", contractId);
    return await propertyAssetsApi.get<ResponseList<Payment>>(url, { params });
  },
};

// Maintenance APIs
export const maintenanceApi = {
  list: async (params?: Record<string, unknown>) => {
    return await propertyAssetsApi.get<ResponseList<MaintenanceRequest>>(
      ENDPOINTS.PROPERTY_ASSETS.MAINTENANCE,
      {
        params,
      }
    );
  },

  create: async (data: CreateMaintenanceRequest) => {
    return await propertyAssetsApi.post<ApiResponse<MaintenanceRequest>>(
      ENDPOINTS.PROPERTY_ASSETS.CREATE_MAINTENANCE,
      data
    );
  },

  update: async (id: string, data: Partial<CreateMaintenanceRequest>) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.UPDATE_MAINTENANCE.replace(":id", id);
    return await propertyAssetsApi.put<ApiResponse<MaintenanceRequest>>(url, data);
  },

  delete: async (id: string) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.DELETE_MAINTENANCE.replace(":id", id);
    return await propertyAssetsApi.delete<ApiResponse<void>>(url);
  },

  getById: async (id: string) => {
    const url = ENDPOINTS.PROPERTY_ASSETS.GET_MAINTENANCE.replace(":id", id);
    return await propertyAssetsApi.get<ApiResponse<MaintenanceRequest>>(url);
  },
};

// Financial APIs
export const financialApi = {
  getSummary: async (params?: Record<string, unknown>) => {
    return await propertyAssetsApi.get<ApiResponse<FinancialSummary>>(
      ENDPOINTS.PROPERTY_ASSETS.FINANCIAL_SUMMARY,
      {
        params,
      }
    );
  },

  getProfitLossReport: async (params?: {
    start_date?: string;
    end_date?: string;
    property_id?: string;
    time_range?: string;
  }) => {
    return await propertyAssetsApi.get<ApiResponse<ProfitLossReport>>(
      ENDPOINTS.PROPERTY_ASSETS.PROFIT_LOSS_REPORT,
      {
        params,
      }
    );
  },

  getRevenueAnalytics: async (params?: {
    start_date?: string;
    end_date?: string;
    property_id?: string;
    time_range?: string;
  }) => {
    return await propertyAssetsApi.get<ApiResponse<any>>(
      ENDPOINTS.PROPERTY_ASSETS.REVENUE_ANALYTICS,
      {
        params,
      }
    );
  },

  getTransactions: async (params?: Record<string, unknown>) => {
    return await propertyAssetsApi.get<ResponseList<Transaction>>(
      ENDPOINTS.PROPERTY_ASSETS.TRANSACTIONS,
      {
        params,
      }
    );
  },
};

// Dashboard APIs
export const dashboardApi = {
  getStats: async (params?: Record<string, unknown>) => {
    return await propertyAssetsApi.get<ApiResponse<FinancialSummary>>(
      ENDPOINTS.PROPERTY_ASSETS.DASHBOARD_STATS,
      {
        params,
      }
    );
  },
};
