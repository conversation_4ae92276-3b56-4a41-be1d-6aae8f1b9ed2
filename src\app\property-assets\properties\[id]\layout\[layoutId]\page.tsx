"use client";

import { useCallback, useMemo, useState } from "react";
import { useParams, usePathname, useRouter, useSearchParams } from "next/navigation";

import { LayoutArea, UnitList } from "@/features/property-assets/layout/components/unit-mapping";
import { useLayoutsByProperty } from "@/features/property-assets/layout/hooks/use-layouts";
import { useUnits } from "@/features/property-assets/unit/hooks/use-units";

import { Card } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";

// This page now uses data hooks filtered by property_id

function useLayoutDetailPage() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const params = useParams<{ id: string; layoutId: string }>();

  const mode = (searchParams.get("mode") || "view") as "view" | "edit";

  const setMode = useCallback(
    (nextMode: "view" | "edit") => {
      const paramsSP = new URLSearchParams(searchParams.toString());
      paramsSP.set("mode", nextMode);
      router.replace(`${pathname}?${paramsSP.toString()}` as any);
    },
    [pathname, router, searchParams]
  );

  const isView = useMemo(() => mode === "view", [mode]);

  const propertyId = params.id;
  const { data: layoutsResp, isLoading: isLoadingLayouts } = useLayoutsByProperty(propertyId);
  const { data: unitsResp, isLoading: isLoadingUnits } = useUnits(propertyId);

  const layouts: Array<{ id: string; name?: string; title?: string }> =
    (layoutsResp as any)?.items ?? (layoutsResp as any) ?? [];
  const units: Array<{
    id: string;
    unit_number?: string;
    name?: string;
    type?: string;
    size?: number;
    area_sqft?: number;
    rent?: number;
    monthly_rent?: number;
    status?: string;
  }> = (unitsResp as any)?.items ?? (unitsResp as any) ?? [];

  const handleLayoutChange = (nextLayoutId: string) => {
    const paramsSP = new URLSearchParams(searchParams.toString());
    router.replace(
      `/property-assets/properties/${propertyId}/layout/${nextLayoutId}?${paramsSP.toString()}` as any
    );
  };

  const formatCurrency = (v: number) => new Intl.NumberFormat("vi-VN").format(v);

  return {
    params,
    isView,
    setMode,
    handleLayoutChange,
    isLoadingLayouts,
    isLoadingUnits,
    layouts,
    units,
    formatCurrency,
  };
}

export default function LayoutDetailPage() {
  const {
    params,
    isView,
    setMode,
    handleLayoutChange,
    isLoadingLayouts,
    isLoadingUnits,
    layouts,
    units,
    formatCurrency,
  } = useLayoutDetailPage();

  // Local UI state for selection
  const [selectedUnitId, setSelectedUnitId] = useState<string | null>(null);

  // Adapt units to UnitList props shape
  const adaptedUnits = useMemo(
    () =>
      units.map((u: any) => ({
        id: u.id,
        unit_number: u.unit_number ?? u.name ?? u.id,
        unit_type: u.type ?? u.unit_type ?? "",
        square_footage: u.size ?? u.area_sqft ?? 0,
        status: (u.status ?? "available") as any,
        rent_amount: u.rent ?? u.monthly_rent ?? undefined,
      })),
    [units]
  );

  const getUnitStatusColor = (status: any) => {
    switch (status) {
      case "occupied":
        return "bg-warning";
      case "maintenance":
        return "bg-destructive";
      case "inactive":
        return "bg-muted";
      default:
        return "bg-success";
    }
  };

  const getUnitStatusVariant = (status: any) => {
    switch (status) {
      case "occupied":
        return "default" as const;
      case "maintenance":
        return "destructive" as const;
      case "inactive":
        return "outline" as const;
      default:
        return "secondary" as const;
    }
  };

  const unitMappings: Array<{ unitId: string; layoutId: string }> = [];

  const currentLayout = useMemo(() => {
    const found: any = (layouts as any[]).find((l) => l.id === params.layoutId);
    if (!found) return undefined;
    return {
      id: found.id,
      name: found.name || found.title || found.id,
      imageUrl: found.image_url || found.imageUrl || "",
      unitPositions: [],
    };
  }, [layouts, params.layoutId]);

  // const statusBadge: Record<
  //   string,
  //   { label: string; className: string; variant?: "secondary" | "default" }
  // > = {
  //   available: { label: "Available", className: "", variant: "secondary" },
  //   occupied: {
  //     label: "Occupied",
  //     className: "bg-warning/10 text-warning border-warning/20",
  //     variant: "default",
  //   },
  //   maintenance: {
  //     label: "Under Maintenance",
  //     className: "bg-destructive/10 text-destructive border-destructive/20",
  //     variant: "default",
  //   },
  //   inactive: { label: "Inactive", className: "", variant: "secondary" },
  // };

  return (
    <div className="grid grid-cols-12 gap-4 p-4 pt-0">
      {/* Left Sidebar */}
      <Card className="col-span-12 h-[calc(100vh-80px)] space-y-3 overflow-hidden border-none p-4 shadow-none md:col-span-3">
        {/* Layout selector */}
        <Select value={params.layoutId} onValueChange={handleLayoutChange}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder={"Select layout"} />
          </SelectTrigger>
          <SelectContent>
            {isLoadingLayouts ? (
              <div className="p-2">
                <Skeleton className="h-6 w-full" />
              </div>
            ) : (
              layouts.map((l) => (
                <SelectItem key={l.id} value={l.id}>
                  {l.name || l.title || l.id}
                </SelectItem>
              ))
            )}
          </SelectContent>
        </Select>

        {/* Units list (reused component) */}
        <UnitList
          units={adaptedUnits as any}
          selectedUnitId={selectedUnitId}
          selectedLayoutId={params.layoutId as string}
          unitMappings={unitMappings}
          isLoading={!!isLoadingUnits}
          onUnitSelect={(u: any) => setSelectedUnitId(u.id)}
          getUnitStatusColor={getUnitStatusColor as any}
          getUnitStatusVariant={getUnitStatusVariant as any}
        />
      </Card>

      {/* Right Canvas */}
      <div className="col-span-12 h-[calc(100vh-80px)] overflow-hidden md:col-span-9">
        <LayoutArea
          currentLayout={currentLayout as any}
          units={adaptedUnits as any}
          isEditing={!isView}
          isLoading={false}
          onSave={() => {}}
          onShapesChange={() => {}}
          onUnitClick={() => {}}
          onUnitPositionChange={() => {}}
          onToggleMode={() => setMode(isView ? "edit" : "view")}
        />
      </div>
    </div>
  );
}
