export const QUERY_KEYS = {
  BLOGS: "blogs",
  BLOG_CATEGORIES: "blog-categories",
  CATEGORIES: "categories",
} as const;

export const blogKeys = {
  all: [QUERY_KEYS.BLOGS] as const,
  lists: () => [...blogKeys.all, "list"] as const,
  list: (filters: Record<string, unknown>) => [...blogKeys.lists(), filters] as const,
  details: () => [...blogKeys.all, "detail"] as const,
  detail: (id: string) => [...blogKeys.details(), id] as const,
  categories: () => [QUERY_KEYS.BLOG_CATEGORIES] as const,
};

export const categoryKeys = {
  all: [QUERY_KEYS.CATEGORIES] as const,
  lists: () => [...categoryKeys.all, "list"] as const,
  list: (filters: Record<string, unknown>) => [...categoryKeys.lists(), filters] as const,
  details: () => [...categoryKeys.all, "detail"] as const,
  detail: (id: string) => [...categoryKeys.details(), id] as const,
};
