import { usePermission } from "@/components/provider/permission-provider";

// Types for sidebar items
interface SidebarItem {
  title: string;
  url?: string;
  icon?: any;
  items?: SidebarItem[];
}

interface SidebarSection {
  title: string;
  icon?: any;
  items?: SidebarItem[];
  url?: string;
}

export const useSidebarPermissions = () => {
  const {
    filterSidebarItems,
    isModuleAccessible,
    getModulePermissions,
    hasPermission,
    isPermissionBlocked,
  } = usePermission();

  // Filter sidebar sections based on permissions
  const filterSidebarSections = (sections: SidebarSection[]): SidebarSection[] => {
    return sections.filter((section) => {
      // If section has a direct URL, check route permission
      if (section.url) {
        return true; // Let the route permission check handle this
      }

      // If section has sub-items, check if any are accessible
      if (section.items && Array.isArray(section.items)) {
        const accessibleItems = filterSidebarItems(section.items);
        if (accessibleItems.length > 0) {
          section.items = accessibleItems;
          return true;
        }
        return false;
      }

      // Default to showing section if no specific checks
      return true;
    });
  };

  // Check if a specific module should be hidden (all permissions disabled)
  const shouldHideModule = (moduleName: string): boolean => {
    const modulePermissions = getModulePermissions(moduleName);

    // If no permissions found for this module, don't hide it
    if (modulePermissions.length === 0) return false;

    // Hide module if ALL permissions are disabled
    return modulePermissions.every((permission) => isPermissionBlocked(permission));
  };

  // Check if a specific module should be shown (at least one permission enabled)
  const shouldShowModule = (moduleName: string): boolean => {
    return !shouldHideModule(moduleName);
  };

  // Get filtered sidebar data with permissions applied
  const getFilteredSidebarData = (sidebarData: any) => {
    const filtered = { ...sidebarData };

    // Filter main navigation
    if (filtered.navMain) {
      filtered.navMain = filterSidebarSections(filtered.navMain);
    }

    // Filter operations
    if (filtered.operations) {
      filtered.operations = filterSidebarSections(filtered.operations);
    }

    // Filter virtual staff (if exists)
    if (filtered.virtual_staff) {
      filtered.virtual_staff = filterSidebarSections(filtered.virtual_staff);
    }

    // Filter CRM
    if (filtered.crm) {
      filtered.crm = filterSidebarSections(filtered.crm);
    }

    // Filter property assets
    if (filtered.property_assets) {
      filtered.property_assets = filterSidebarSections(filtered.property_assets);
    }

    return filtered;
  };

  return {
    filterSidebarItems,
    filterSidebarSections,
    shouldHideModule,
    shouldShowModule,
    getFilteredSidebarData,
    isModuleAccessible,
    getModulePermissions,
    hasPermission,
    isPermissionBlocked,
  };
};
