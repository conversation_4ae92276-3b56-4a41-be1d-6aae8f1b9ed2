"use client";

import * as React from "react";
import * as DropdownMenuPrimitive from "@radix-ui/react-dropdown-menu";
import { useTranslation } from "react-i18next";

import { usePermission } from "@/components/provider/permission-provider";

import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from "./dropdown-menu";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "./tooltip";

// Permission check interface
interface PermissionCheck {
  /** Permission key to check (e.g., 'create_user', 'edit_product') */
  permission?: string;
  /** Route to check permission for (e.g., '/users/create') */
  route?: string;
  /** Module name to check access for */
  module?: string;
}

// Extended props for permission-aware dropdown items
interface PermissionDropdownItemProps
  extends React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item>,
    PermissionCheck {
  /** Custom message to show when permission is denied */
  permissionDeniedMessage?: string;
  /** Whether to show tooltip on hover when disabled */
  showTooltip?: boolean;
  /** Whether to hide the item when permission is denied */
  hideOnNoPermission?: boolean;
  /** Children to render when permission is denied (alternative to hiding) */
  fallbackChildren?: React.ReactNode;
}

interface PermissionDropdownCheckboxItemProps
  extends React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>,
    PermissionCheck {
  /** Custom message to show when permission is denied */
  permissionDeniedMessage?: string;
  /** Whether to show tooltip on hover when disabled */
  showTooltip?: boolean;
  /** Whether to hide the item when permission is denied */
  hideOnNoPermission?: boolean;
  /** Children to render when permission is denied (alternative to hiding) */
  fallbackChildren?: React.ReactNode;
}

interface PermissionDropdownRadioItemProps
  extends React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>,
    PermissionCheck {
  /** Custom message to show when permission is denied */
  permissionDeniedMessage?: string;
  /** Whether to show tooltip on hover when disabled */
  showTooltip?: boolean;
  /** Whether to hide the item when permission is denied */
  hideOnNoPermission?: boolean;
  /** Children to render when permission is denied (alternative to hiding) */
  fallbackChildren?: React.ReactNode;
}

interface PermissionDropdownSubTriggerProps
  extends React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger>,
    PermissionCheck {
  /** Custom message to show when permission is denied */
  permissionDeniedMessage?: string;
  /** Whether to show tooltip on hover when disabled */
  showTooltip?: boolean;
  /** Whether to hide the item when permission is denied */
  hideOnNoPermission?: boolean;
  /** Children to render when permission is denied (alternative to hiding) */
  fallbackChildren?: React.ReactNode;
}

// Permission-aware dropdown item component
const PermissionDropdownItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.Item>,
  PermissionDropdownItemProps
>(
  (
    {
      permission,
      route,
      module,
      permissionDeniedMessage,
      showTooltip = true,
      hideOnNoPermission = false,
      fallbackChildren,
      children,
      className,
      ...props
    },
    ref
  ) => {
    const { t } = useTranslation();
    const { hasPermission, hasRoutePermission, isModuleAccessible } = usePermission();
    // Determine if item should be disabled
    const isDisabled = React.useMemo(() => {
      // If no permission checks specified, item is enabled
      if (!permission && !route && !module) {
        return false;
      }

      // Check specific permission
      if (permission && !hasPermission(permission)) {
        return true;
      }

      // Check route permission
      if (route && !hasRoutePermission(route)) {
        return true;
      }

      // Check module access
      if (module && !isModuleAccessible(module)) {
        return true;
      }

      return false;
    }, [permission, route, module, hasPermission, hasRoutePermission, isModuleAccessible]);

    // Generate permission denied message
    const deniedMessage = React.useMemo(() => {
      if (permissionDeniedMessage) {
        return permissionDeniedMessage;
      }

      if (permission) {
        return t("common.noPermission", {
          permission: t(`permissions.${permission}`) || permission,
        });
      }

      if (route) {
        return t("common.noRoutePermission", { route });
      }

      if (module) {
        return t("common.noModuleAccess", { module: t(`modules.${module}`) || module });
      }

      return t("common.noPermission");
    }, [permissionDeniedMessage, permission, route, module, t]);
    if (!hideOnNoPermission && isDisabled && fallbackChildren) {
      return <>{fallbackChildren}</>;
    }
    if (hideOnNoPermission && isDisabled) {
      return null;
    }
    const dropdownItem = (
      <DropdownMenuItem disabled={isDisabled} className={className} {...props}>
        {children}
      </DropdownMenuItem>
    );
    if (showTooltip && isDisabled) {
      // Use DropdownMenuItem with disabled state when permissions are denied
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="inline-block">{dropdownItem}</div>
            </TooltipTrigger>
            <TooltipContent>
              <p>{deniedMessage}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }
    return dropdownItem;
  }
);

PermissionDropdownItem.displayName = "PermissionDropdownItem";

// Permission-aware dropdown checkbox item component
const PermissionDropdownCheckboxItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,
  PermissionDropdownCheckboxItemProps
>(
  (
    {
      permission,
      route,
      module,
      permissionDeniedMessage,
      showTooltip = true,
      hideOnNoPermission = true,
      fallbackChildren,
      children,
      className,
      ...props
    },
    ref
  ) => {
    const { t } = useTranslation();
    const { hasPermission, hasRoutePermission, isModuleAccessible } = usePermission();

    // Determine if item should be disabled
    const isDisabled = React.useMemo(() => {
      if (!permission && !route && !module) {
        return false;
      }

      if (permission && !hasPermission(permission)) {
        return true;
      }

      if (route && !hasRoutePermission(route)) {
        return true;
      }

      if (module && !isModuleAccessible(module)) {
        return true;
      }

      return false;
    }, [permission, route, module, hasPermission, hasRoutePermission, isModuleAccessible]);

    // Generate permission denied message
    const deniedMessage = React.useMemo(() => {
      if (permissionDeniedMessage) {
        return permissionDeniedMessage;
      }

      if (permission) {
        return t("common.noPermission", {
          permission: t(`permissions.${permission}`) || permission,
        });
      }

      if (route) {
        return t("common.noRoutePermission", { route });
      }

      if (module) {
        return t("common.noModuleAccess", { module: t(`modules.${module}`) || module });
      }

      return t("common.noPermission");
    }, [permissionDeniedMessage, permission, route, module, t]);

    // If hiding and permission denied, show fallback content
    if (!hideOnNoPermission && isDisabled && fallbackChildren) {
      return <>{fallbackChildren}</>;
    }

    // If hiding and permission denied, render nothing
    if (hideOnNoPermission && isDisabled) {
      return null;
    }

    // Use DropdownMenuCheckboxItem with disabled state when permissions are denied
    if (isDisabled) {
      return (
        <DropdownMenuCheckboxItem disabled className={className} {...props}>
          {children}
        </DropdownMenuCheckboxItem>
      );
    }

    // Return normal PermissionDropdownCheckboxItem when permissions are available
    return (
      <DropdownMenuPrimitive.CheckboxItem ref={ref} className={className} {...props}>
        {children}
      </DropdownMenuPrimitive.CheckboxItem>
    );
  }
);

PermissionDropdownCheckboxItem.displayName = "PermissionDropdownCheckboxItem";

// Permission-aware dropdown radio item component
const PermissionDropdownRadioItem = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,
  PermissionDropdownRadioItemProps
>(
  (
    {
      permission,
      route,
      module,
      permissionDeniedMessage,
      showTooltip = true,
      hideOnNoPermission = true,
      fallbackChildren,
      children,
      className,
      ...props
    },
    ref
  ) => {
    const { t } = useTranslation();
    const { hasPermission, hasRoutePermission, isModuleAccessible } = usePermission();

    // Determine if item should be disabled
    const isDisabled = React.useMemo(() => {
      if (!permission && !route && !module) {
        return false;
      }

      if (permission && !hasPermission(permission)) {
        return true;
      }

      if (route && !hasRoutePermission(route)) {
        return true;
      }

      if (module && !isModuleAccessible(module)) {
        return true;
      }

      return false;
    }, [permission, route, module, hasPermission, hasRoutePermission, isModuleAccessible]);

    // Generate permission denied message
    const deniedMessage = React.useMemo(() => {
      if (permissionDeniedMessage) {
        return permissionDeniedMessage;
      }

      if (permission) {
        return t("common.noPermission", {
          permission: t(`permissions.${permission}`) || permission,
        });
      }

      if (route) {
        return t("common.noRoutePermission", { route });
      }

      if (module) {
        return t("common.noModuleAccess", { module: t(`modules.${module}`) || module });
      }

      return t("common.noPermission");
    }, [permissionDeniedMessage, permission, route, module, t]);

    // If hiding and permission denied, show fallback content
    if (!hideOnNoPermission && isDisabled && fallbackChildren) {
      return <>{fallbackChildren}</>;
    }

    // If hiding and permission denied, render nothing
    if (hideOnNoPermission && isDisabled) {
      return null;
    }

    // Use DropdownMenuRadioItem with disabled state when permissions are denied
    if (isDisabled) {
      return (
        <DropdownMenuRadioItem disabled className={className} {...props}>
          {children}
        </DropdownMenuRadioItem>
      );
    }

    // Return normal PermissionDropdownRadioItem when permissions are available
    return (
      <DropdownMenuPrimitive.RadioItem ref={ref} className={className} {...props}>
        {children}
      </DropdownMenuPrimitive.RadioItem>
    );
  }
);

PermissionDropdownRadioItem.displayName = "PermissionDropdownRadioItem";

// Permission-aware dropdown sub-trigger component
const PermissionDropdownSubTrigger = React.forwardRef<
  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,
  PermissionDropdownSubTriggerProps
>(
  (
    {
      permission,
      route,
      module,
      permissionDeniedMessage,
      showTooltip = true,
      hideOnNoPermission = true,
      fallbackChildren,
      children,
      className,
      ...props
    },
    ref
  ) => {
    const { t } = useTranslation();
    const { hasPermission, hasRoutePermission, isModuleAccessible } = usePermission();

    // Determine if item should be disabled
    const isDisabled = React.useMemo(() => {
      if (!permission && !route && !module) {
        return false;
      }

      if (permission && !hasPermission(permission)) {
        return true;
      }

      if (route && !hasRoutePermission(route)) {
        return true;
      }

      if (module && !isModuleAccessible(module)) {
        return true;
      }

      return false;
    }, [permission, route, module, hasPermission, hasRoutePermission, isModuleAccessible]);

    // Generate permission denied message
    const deniedMessage = React.useMemo(() => {
      if (permissionDeniedMessage) {
        return permissionDeniedMessage;
      }

      if (permission) {
        return t("common.noPermission", {
          permission: t(`permissions.${permission}`) || permission,
        });
      }

      if (route) {
        return t("common.noRoutePermission", { route });
      }

      if (module) {
        return t("common.noModuleAccess", { module: t(`modules.${module}`) || module });
      }

      return t("common.noPermission");
    }, [permissionDeniedMessage, permission, route, module, t]);

    // If hiding and permission denied, show fallback content
    if (!hideOnNoPermission && isDisabled && fallbackChildren) {
      return <>{fallbackChildren}</>;
    }

    // If hiding and permission denied, render nothing
    if (hideOnNoPermission && isDisabled) {
      return null;
    }

    // Use DropdownMenuSubTrigger with disabled state when permissions are denied
    if (isDisabled) {
      return (
        <DropdownMenuSubTrigger disabled className={className} {...props}>
          {children}
        </DropdownMenuSubTrigger>
      );
    }

    // Return normal PermissionDropdownSubTrigger when permissions are available
    return (
      <DropdownMenuPrimitive.SubTrigger ref={ref} className={className} {...props}>
        {children}
      </DropdownMenuPrimitive.SubTrigger>
    );
  }
);

PermissionDropdownSubTrigger.displayName = "PermissionDropdownSubTrigger";

// Export all components
export {
  // Base dropdown components (without permission checks)
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuGroup,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuRadioGroup,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioItem,
  DropdownMenuSubTrigger,
  // Permission-aware dropdown components
  PermissionDropdownItem,
  PermissionDropdownCheckboxItem,
  PermissionDropdownRadioItem,
  PermissionDropdownSubTrigger,
  // Permission types
  type PermissionCheck,
  type PermissionDropdownItemProps,
  type PermissionDropdownCheckboxItemProps,
  type PermissionDropdownRadioItemProps,
  type PermissionDropdownSubTriggerProps,
};
