"use client";

import { Check, Minus, Plus } from "lucide-react";
import { useTranslation } from "react-i18next";

import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Switch,
} from "@/components/ui";

import { useCustomPlan } from "../hooks";
import { CustomPlanSuccessDialog } from "./custom-plan-success-dialog";

// Types for internal components
interface FeatureItemProps {
  children: React.ReactNode;
}

interface QuantityControlProps {
  label: string;
  value: number;
  onValueChange: (value: number) => void;
}

interface ConfigurationItemProps {
  label: string;
  children: React.ReactNode;
}

// Data arrays
const FEATURES = [
  "productManagement",
  "orderManagement",
  "knowledgeManagement",
  "departmentManagement",
  "employeeManagement",
  "crm",
];

const AI_MODELS = [
  { value: "gpt-4o", label: "~GPT-4o" },
  { value: "gpt-4", label: "~GPT-4" },
  { value: "gpt-3.5", label: "~GPT-3.5" },
];

const SWITCH_OPTIONS = [
  { label: "multilingual", defaultChecked: true },
  { label: "scheduleCustomerCare", defaultChecked: true },
  { label: "customIntegration", defaultChecked: true },
];

// Reusable sub-components
const FeatureItem = ({ children }: FeatureItemProps) => (
  <div className="flex items-center gap-2 py-1">
    <Check className="size-4 text-primary" />
    <span className="text-base text-foreground">{children}</span>
  </div>
);

const QuantityControl = ({ label, value, onValueChange }: QuantityControlProps) => (
  <div className="flex w-full items-center justify-between gap-3">
    <span className="flex-auto text-base text-foreground">{label}</span>
    <div className="flex w-48 flex-none items-center justify-center gap-1.5 rounded-lg border border-border p-1">
      <Button
        variant="secondary"
        type="button"
        size="sm"
        className="size-8 flex-none p-0"
        onClick={() => onValueChange(value - 1)}>
        <Minus className="size-4" />
      </Button>
      <Input
        type="number"
        value={value}
        onChange={(e) => {
          const newValue = parseInt(e.target.value) || 0;
          onValueChange(newValue);
        }}
        className="!focus-visible:ring-0 flex-auto border-none bg-transparent text-center text-sm text-foreground outline-none ring-0"
      />
      <Button
        variant="secondary"
        size="sm"
        type="button"
        className="size-8 flex-none p-0"
        onClick={() => onValueChange(value + 1)}>
        <Plus className="size-4" />
      </Button>
    </div>
  </div>
);

const ConfigurationItem = ({ label, children }: ConfigurationItemProps) => (
  <div className="flex h-10 w-full items-center justify-between gap-3">
    <span className="flex-auto text-base text-foreground">{label}</span>
    {children}
  </div>
);

const PlanHeader = ({ title, description }: { title: string; description: string }) => (
  <div className="flex flex-col gap-2">
    <h3 className="text-xl font-semibold text-foreground">{title}</h3>
    <p className="text-sm text-muted-foreground">{description}</p>
  </div>
);

const FeaturesList = () => {
  const { t } = useTranslation();

  return (
    <div className="flex flex-col">
      {FEATURES.map((feature) => (
        <FeatureItem key={feature}>
          {t(`pages.subscription.customPlan.features.${feature}`)}
        </FeatureItem>
      ))}
    </div>
  );
};

// Main component
export default function CustomPlan() {
  const { t } = useTranslation();
  const {
    form,
    requirements,
    isSubmitting,
    handleRequirementChange,
    handleSubmit,
    showSuccessDialog,
    closeSuccessDialog,
  } = useCustomPlan({
    onSuccess: (data) => {
      console.log("Custom plan submitted successfully:", data);
      // Success dialog will be shown automatically
    },
  });

  // Quantity controls configuration
  const quantityControls = [
    {
      label: t("pages.subscription.customPlan.configuration.virtualAssistants"),
      field: "staff" as const,
    },
    {
      label: t("pages.subscription.customPlan.configuration.messages"),
      field: "messages" as const,
    },
    {
      label: t("pages.subscription.customPlan.configuration.knowledgeCapacity"),
      field: "storage" as const,
    },
  ];

  return (
    <>
      <form onSubmit={handleSubmit}>
        <Card className="relative overflow-hidden border border-border bg-card">
          <CardContent className="flex flex-col gap-4 p-6">
            {/* Pricing Header */}
            <div className="grid grid-cols-1 gap-4 px-2 md:grid-cols-2">
              {/* Left Column - Features */}
              <div className="flex flex-1 flex-col gap-3">
                <PlanHeader
                  title={t("pages.subscription.customPlan.title")}
                  description={t("pages.subscription.customPlan.description")}
                />
                <FeaturesList />
              </div>

              {/* Right Column - Configuration */}
              <div className="flex flex-1 flex-col items-end justify-center gap-2">
                {/* Quantity Controls */}
                {quantityControls.map(({ label, field }) => (
                  <QuantityControl
                    key={field}
                    label={label}
                    value={requirements[field]}
                    onValueChange={(newValue) => handleRequirementChange(field, newValue)}
                  />
                ))}

                {/* AI Model Select */}
                <ConfigurationItem label={t("pages.subscription.customPlan.configuration.aiModel")}>
                  <Select
                    value={requirements.aiModel}
                    onValueChange={(value) => form.setValue("aiModel", value)}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="~GPT-4o" />
                    </SelectTrigger>
                    <SelectContent>
                      {AI_MODELS.map((model) => (
                        <SelectItem key={model.value} value={model.value}>
                          {model.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </ConfigurationItem>

                {/* Switch Options */}
                {SWITCH_OPTIONS.map(({ label, defaultChecked }) => (
                  <ConfigurationItem
                    key={label}
                    label={t(`pages.subscription.customPlan.configuration.${label}`)}>
                    <Switch
                      checked={
                        requirements[
                          label as "multilingual" | "scheduleCustomerCare" | "customIntegration"
                        ] as boolean
                      }
                      onCheckedChange={(checked) =>
                        form.setValue(
                          label as "multilingual" | "scheduleCustomerCare" | "customIntegration",
                          checked
                        )
                      }
                      className="ml-auto"
                    />
                  </ConfigurationItem>
                ))}
              </div>
            </div>

            {/* Contact Button */}
            <Button
              type="submit"
              variant="secondary"
              loading={isSubmitting}
              className="w-full bg-neutral-800 text-primary-foreground"
              size="lg">
              {t("pages.subscription.customPlan.button")}
            </Button>
          </CardContent>
        </Card>
      </form>

      {/* Success Dialog */}
      <CustomPlanSuccessDialog isOpen={showSuccessDialog} onClose={closeSuccessDialog} />
    </>
  );
}
