import { useInfiniteQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { categoryApi } from "@/lib/apis/category";
import { Category } from "@/lib/apis/types/category";

import { categoryKeys } from "./keys";

interface UseBlogCategoriesOptions {
  limit?: number;
  search?: string;
  parent_id?: string | null;
}

export function useBlogCategories(options: UseBlogCategoriesOptions = {}) {
  const { limit = 20, search, parent_id } = options;

  const query = useInfiniteQuery({
    queryKey: categoryKeys.list({ limit, search, parent_id }),
    queryFn: ({ pageParam = 0 }) =>
      categoryApi.list({
        page: pageParam,
        limit,
        search,
        parent_id,
      }),
    getNextPageParam: (lastPage, allPages) => {
      const currentPage = allPages.length;
      const totalPages = Math.ceil(lastPage.total / limit);
      return currentPage < totalPages ? currentPage : undefined;
    },
    initialPageParam: 0,
  });

  const categories = query.data?.pages.flatMap((page) => page.items) ?? [];
  const hasNextPage = query.hasNextPage;
  const isFetchingNextPage = query.isFetchingNextPage;
  const fetchNextPage = query.fetchNextPage;

  return {
    ...query,
    categories,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  };
}

export function useCreateCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Partial<Category>) => categoryApi.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: categoryKeys.lists() });
      toast.success("Category created successfully");
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || "Failed to create category");
    },
  });
}

export function useUpdateCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Category> }) =>
      categoryApi.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: categoryKeys.lists() });
      toast.success("Category updated successfully");
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || "Failed to update category");
    },
  });
}

export function useDeleteCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => categoryApi.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: categoryKeys.lists() });
      toast.success("Category deleted successfully");
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || "Failed to delete category");
    },
  });
}

export function useUpdateCategoryParent() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, parentId }: { id: string; parentId: string | null }) =>
      categoryApi.updateParent(id, parentId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: categoryKeys.lists() });
      toast.success("Category parent updated successfully");
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || "Failed to update category parent");
    },
  });
}
