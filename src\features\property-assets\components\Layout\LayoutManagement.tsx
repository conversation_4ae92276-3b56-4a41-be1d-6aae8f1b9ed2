"use client";

import { use<PERSON><PERSON>back, useMemo, useState } from "react";
import { useRouter } from "next/navigation";
import {
  Activity,
  AlertCircle,
  AlertTriangle,
  BarChart3,
  Building2,
  CheckCircle,
  Clock,
  Copy,
  Download,
  Edit,
  Eye,
  FileText,
  Grid3X3,
  Layout,
  Loader2,
  MapPin,
  Plus,
  Search,
  Settings,
  Share,
  Target,
  Trash2,
  TrendingDown,
  TrendingUp,
  Upload,
  X,
  Zap,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
// UI Components
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

// Hook imports
import { useCreateLayout, useDeleteLayout, useLayouts, useProperties, useUnits } from "../../hooks";
// Types
import type { PropertyLayout, Unit } from "../../types";
import type { LayoutData } from "./LayoutViewer";
import { AnalyticsCardSkeleton, LayoutCardSkeleton } from "./LoadingSkeleton";
// Components
import { UnitMappingInterface } from "./UnitMappingInterface";

// No mock data needed - using real Vietnamese property data from API

// Template Form Component
interface TemplateFormProps {
  layout: PropertyLayout | null;
  onSubmit: (data: { name: string; description?: string; category?: string }) => void;
  onCancel: () => void;
}

function TemplateFormComponent({ layout, onSubmit, onCancel }: TemplateFormProps) {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    name: layout?.name ? `${layout.name} Template` : "",
    description: "",
    category: "custom",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.name.trim()) {
      onSubmit(formData);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="mb-2 block text-sm font-medium text-foreground">
          {t("pages.layouts.templateName")}
        </label>
        <Input
          value={formData.name}
          onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
          placeholder={t("pages.layouts.templateNamePlaceholder")}
          required
        />
      </div>

      <div>
        <label className="mb-2 block text-sm font-medium text-foreground">
          {t("pages.layouts.templateDescription")}
        </label>
        <Textarea
          value={formData.description}
          onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
          placeholder={t("pages.layouts.templateDescriptionPlaceholder")}
          rows={3}
        />
      </div>

      <div>
        <label className="mb-2 block text-sm font-medium text-foreground">
          {t("pages.layouts.templateCategory")}
        </label>
        <Select
          value={formData.category}
          onValueChange={(value) => setFormData((prev) => ({ ...prev, category: value }))}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="custom">{t("pages.layouts.categories.custom")}</SelectItem>
            <SelectItem value="residential">{t("pages.layouts.categories.residential")}</SelectItem>
            <SelectItem value="commercial">{t("pages.layouts.categories.commercial")}</SelectItem>
            <SelectItem value="mixed">{t("pages.layouts.categories.mixed")}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          {t("common.cancel")}
        </Button>
        <Button type="submit">{t("pages.layouts.createTemplate")}</Button>
      </div>
    </form>
  );
}

const mockUnits: Unit[] = [
  {
    id: "unit1",
    property_id: "prop1",
    unit_number: "101",
    unit_type: "1br",
    floor: 1,
    square_footage: 750,
    bedrooms: 1,
    bathrooms: 1,
    rent_amount: 2500,
    deposit_amount: 2500,
    status: "available",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },
  {
    id: "unit2",
    property_id: "prop1",
    unit_number: "102",
    unit_type: "2br",
    floor: 1,
    square_footage: 1100,
    bedrooms: 2,
    bathrooms: 2,
    rent_amount: 3200,
    deposit_amount: 3200,
    status: "occupied",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },
  {
    id: "unit3",
    property_id: "prop1",
    unit_number: "201",
    unit_type: "studio",
    floor: 2,
    square_footage: 500,
    bedrooms: 0,
    bathrooms: 1,
    rent_amount: 1800,
    deposit_amount: 1800,
    status: "maintenance",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },
];

interface LayoutManagementComponentProps {
  className?: string;
}

export function LayoutManagementComponent({ className = "" }: LayoutManagementComponentProps) {
  const { t } = useTranslation();
  const router = useRouter();

  // State
  const [selectedPropertyId, setSelectedPropertyId] = useState<string>("prop1"); // Default to PRIME COMPLEX
  const [viewMode, setViewMode] = useState<"overview" | "mapping">("overview");
  const [searchTerm, setSearchTerm] = useState("");
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [layoutToDelete, setLayoutToDelete] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<"all" | "complete" | "incomplete" | "issues">(
    "all"
  );
  const [sortBy, setSortBy] = useState<"name" | "progress" | "lastUpdated">("name");
  const [error, setError] = useState<string | null>(null);
  const [localLoading, setLocalLoading] = useState(false);

  // Data from hooks
  const { data: propertiesData, isLoading: propertiesLoading } = useProperties();
  const { data: unitsData, isLoading: unitsLoading } = useUnits(
    selectedPropertyId,
    {},
    !!selectedPropertyId
  );
  const { data: layoutsData, isLoading: layoutsLoading } = useLayouts(
    selectedPropertyId,
    {},
    !!selectedPropertyId
  );

  const createLayoutMutation = useCreateLayout();
  const deleteLayoutMutation = useDeleteLayout();

  const isLoading =
    propertiesLoading ||
    unitsLoading ||
    layoutsLoading ||
    localLoading ||
    createLayoutMutation.isPending ||
    deleteLayoutMutation.isPending;

  const properties = propertiesData?.items || [];
  const selectedProperty = properties.find((p) => p.id === selectedPropertyId);
  const units = unitsData?.items || [];
  const layouts = layoutsData?.items || [];

  // Handlers
  const handlePropertySelect = useCallback((propertyId: string) => {
    setSelectedPropertyId(propertyId);
    setViewMode("overview");
  }, []);

  const handleLayoutSave = useCallback(
    async (layout: LayoutData) => {
      setLocalLoading(true);
      setError(null);
      try {
        // TODO: Replace with actual API call
        await new Promise((resolve) => setTimeout(resolve, 1000)); // Simulate API call

        toast.success(t("pages.layouts.saveSuccess"));
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : t("common.error.unknown");
        setError(errorMessage);
        toast.error(t("pages.layouts.saveError", { error: errorMessage }));
      } finally {
        setLocalLoading(false);
      }
    },
    [t]
  );

  const handleLayoutDelete = useCallback(
    async (layoutId: string) => {
      try {
        await deleteLayoutMutation.mutateAsync(layoutId);
        setLayoutToDelete(null);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : t("common.error.unknown");
        setError(errorMessage);
      }
    },
    [deleteLayoutMutation, t]
  );

  const handleUnitUpdate = useCallback(
    async (unit: Unit) => {
      setLocalLoading(true);
      setError(null);
      try {
        // TODO: Replace with actual API call
        await new Promise((resolve) => setTimeout(resolve, 1000)); // Simulate API call

        toast.success(t("pages.units.updateSuccess"));
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : t("common.error.unknown");
        setError(errorMessage);
        toast.error(t("pages.units.updateError", { error: errorMessage }));
      } finally {
        setLocalLoading(false);
      }
    },
    [t]
  );

  const handleCreateLayout = useCallback(
    async (data: {
      name: string;
      description?: string;
      floorNumber?: number;
      imageUrl?: string;
      imageWidth: number;
      imageHeight: number;
    }) => {
      if (!selectedPropertyId) return;

      try {
        await createLayoutMutation.mutateAsync({
          propertyId: selectedPropertyId,
          data,
        });
        setShowCreateDialog(false);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : t("common.error.unknown");
        setError(errorMessage);
      }
    },
    [createLayoutMutation, selectedPropertyId, t]
  );

  // Enhanced filtering and sorting logic
  const filteredLayouts = layouts
    .filter((layout) => {
      // Search filter
      const matchesSearch = layout.name.toLowerCase().includes(searchTerm.toLowerCase());
      if (!matchesSearch) return false;

      // Status filter
      if (statusFilter === "all") return true;

      const mappedUnitsCount = layout.unitPositions?.length || 0;
      const totalUnitsForLayout = units.filter((u) => u.property_id === selectedPropertyId).length;
      const layoutProgress =
        totalUnitsForLayout > 0 ? (mappedUnitsCount / totalUnitsForLayout) * 100 : 0;
      const hasIssues = mappedUnitsCount === 0 && totalUnitsForLayout > 0;

      switch (statusFilter) {
        case "complete":
          return layoutProgress >= 100;
        case "incomplete":
          return layoutProgress > 0 && layoutProgress < 100;
        case "issues":
          return hasIssues;
        default:
          return true;
      }
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "progress":
          const aProgress = a.unitPositions?.length || 0;
          const bProgress = b.unitPositions?.length || 0;
          return bProgress - aProgress;
        case "lastUpdated":
          // Simulate last updated sorting (would use actual timestamps in real app)
          return (b.unitPositions?.length || 0) - (a.unitPositions?.length || 0);
        case "name":
        default:
          return a.name.localeCompare(b.name);
      }
    });

  // Calculate stats with enhanced analytics and real-time updates
  const stats = useMemo(() => {
    const totalLayouts = layouts.length;
    const activeLayouts = layouts.filter((l) => l.id).length;
    const totalUnits = units.length;

    // Calculate actual mapping progress from unit positions
    const mappedUnits = layouts.reduce((total, layout) => {
      return total + (layout.unitPositions?.length || 0);
    }, 0);
    const unmappedUnits = totalUnits - mappedUnits;
    const mappingProgress = totalUnits > 0 ? (mappedUnits / totalUnits) * 100 : 0;

    // Health indicators based on layout completeness
    const layoutsWithIssues = layouts.filter((layout) => {
      const layoutUnits = layout.unitPositions?.length || 0;
      const totalUnitsForLayout = units.filter((u) => u.property_id === selectedPropertyId).length;
      return layoutUnits === 0 && totalUnitsForLayout > 0;
    }).length;

    const healthScore =
      layouts.length > 0 ? ((layouts.length - layoutsWithIssues) / layouts.length) * 100 : 100;

    // Simulate recent activity (would come from real activity logs)
    const recentActivity = Math.floor(Math.random() * 10) + 1;

    // Calculate weekly progress (simulated)
    const weeklyProgress = mappingProgress * 0.23; // Simulated weekly growth

    return {
      totalLayouts,
      activeLayouts,
      totalUnits,
      mappedUnits,
      unmappedUnits,
      mappingProgress,
      layoutsWithIssues,
      healthScore,
      recentActivity,
      weeklyProgress: Number(weeklyProgress.toFixed(1)),
    };
  }, [layouts, units, selectedPropertyId]);

  // Export functionality
  const handleExportLayouts = useCallback(async () => {
    try {
      const exportData = {
        property: selectedProperty,
        layouts: filteredLayouts,
        units: units,
        exportDate: new Date().toISOString(),
        stats,
      };

      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: "application/json",
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `layouts-${selectedProperty?.name || "export"}-${new Date().toISOString().split("T")[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success(t("pages.layouts.exportSuccess"));
    } catch (error) {
      toast.error(t("pages.layouts.exportError"));
    }
  }, [selectedProperty, filteredLayouts, units, stats, t]);

  // Import functionality
  const handleImportLayouts = useCallback(async () => {
    try {
      const input = document.createElement("input");
      input.type = "file";
      input.accept = ".json";
      input.onchange = async (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = async (event) => {
          try {
            const importData = JSON.parse(event.target?.result as string);

            // Validate import data structure
            if (!importData.layouts || !Array.isArray(importData.layouts)) {
              throw new Error("Invalid layout data format");
            }

            // TODO: Implement actual import logic with API calls
            // For now, just show success message
            toast.success(t("pages.layouts.importSuccess", { count: importData.layouts.length }));

            // Refresh the layouts data
            // queryClient.invalidateQueries(['layouts', selectedPropertyId]);
          } catch (parseError) {
            // TODO: Implement proper error logging for import parse errors
            toast.error(t("pages.layouts.importParseError"));
          }
        };
        reader.readAsText(file);
      };
      input.click();
    } catch (error) {
      // TODO: Implement proper error logging for import errors
      toast.error(t("pages.layouts.importError"));
    }
  }, [selectedPropertyId, t]);

  // Template creation from existing layout
  const [templateDialog, setTemplateDialog] = useState<{
    open: boolean;
    layout: PropertyLayout | null;
  }>({ open: false, layout: null });

  const handleCreateTemplate = useCallback(
    (layoutId: string) => {
      const layout = layouts.find((l) => l.id === layoutId);
      if (layout) {
        setTemplateDialog({ open: true, layout: layout as any });
      }
    },
    [layouts]
  );

  const handleSaveAsTemplate = useCallback(
    async (templateData: { name: string; description?: string; category?: string }) => {
      if (!templateDialog.layout) return;

      try {
        const templatePayload = {
          name: templateData.name,
          description: templateData.description || "",
          category: templateData.category || "custom",
          sourceLayoutId: templateDialog.layout.id,
          propertyType: selectedProperty?.property_type || "mixed",
          dimensions: {
            width: (templateDialog.layout as any).width,
            height: (templateDialog.layout as any).height,
          },
          unitPositions: (templateDialog.layout as any).unitPositions || [],
          imageUrl: (templateDialog.layout as any).imageUrl,
          createdAt: new Date().toISOString(),
          isTemplate: true,
        };

        // TODO: Implement actual template saving API call
        // await templateApi.createTemplate(templatePayload);

        // TODO: Add proper logging for template save operations
        toast.success(t("pages.layouts.templateCreated"));
        setTemplateDialog({ open: false, layout: null });
      } catch (error) {
        // TODO: Implement proper error logging for template creation
        toast.error(t("pages.layouts.templateCreateError"));
      }
    },
    [templateDialog.layout, selectedProperty, t]
  );

  if (viewMode === "mapping" && selectedProperty) {
    return (
      <div className={`space-y-6 ${className}`}>
        {/* Back to Overview */}
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => setViewMode("overview")}
            className="flex items-center gap-2">
            <Grid3X3 className="size-4" />
            {t("common.backToOverview")}
          </Button>
          <div className="flex items-center gap-2 text-muted-foreground">
            <Building2 className="size-4" />
            <span className="text-sm">{selectedProperty.name}</span>
          </div>
        </div>

        {/* Unit Mapping Interface */}
        <UnitMappingInterface
          property={selectedProperty}
          units={units}
          layouts={layouts}
          onLayoutSave={handleLayoutSave}
          onLayoutDelete={handleLayoutDelete}
          onUnitUpdate={handleUnitUpdate}
        />
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className={`space-y-6 ${className} relative`}>
        {/* Loading Overlay */}
        {isLoading && (
          <div className="absolute inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm">
            <div className="flex flex-col items-center gap-2">
              <Loader2 className="size-8 animate-spin text-primary" />
              <p className="text-sm text-muted-foreground">{t("common.loading")}</p>
            </div>
          </div>
        )}

        {/* Error Alert */}
        {error && (
          <div className="flex items-start gap-2 rounded-lg border border-destructive/20 bg-destructive/10 p-4">
            <AlertCircle className="mt-0.5 size-5 text-destructive" />
            <div className="flex-1">
              <p className="text-sm font-medium text-destructive">{t("common.error.title")}</p>
              <p className="mt-1 text-sm text-muted-foreground">{error}</p>
            </div>
            <Button variant="ghost" size="sm" onClick={() => setError(null)} className="h-auto p-1">
              <X className="size-4" />
            </Button>
          </div>
        )}
        {/* Enhanced Analytics Dashboard */}
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
          {isLoading ? (
            <>
              <AnalyticsCardSkeleton />
              <AnalyticsCardSkeleton />
              <AnalyticsCardSkeleton />
              <AnalyticsCardSkeleton />
            </>
          ) : (
            <>
              {/* Primary Metrics */}
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        {t("pages.layouts.totalLayouts")}
                      </p>
                      <div className="mt-2 flex items-center space-x-2">
                        <p className="text-2xl font-bold text-foreground">{stats.totalLayouts}</p>
                        <Badge className="border-primary/20 bg-primary/10 text-primary">
                          <Building2 className="mr-1 size-3" />
                          {properties.length} {t("pages.properties.title")}
                        </Badge>
                      </div>
                    </div>
                    <div className="rounded-full bg-primary/10 p-3">
                      <Layout className="size-6 text-primary" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        {t("pages.layouts.mappingProgress")}
                      </p>
                      <div className="mt-2 flex items-center space-x-2">
                        <p className="text-2xl font-bold text-foreground">
                          {stats.mappingProgress.toFixed(1)}%
                        </p>
                        <Badge
                          className={
                            stats.mappingProgress >= 80
                              ? "border-success/20 bg-success/10 text-success"
                              : "border-warning/20 bg-warning/10 text-warning"
                          }>
                          {stats.mappingProgress >= 80 ? (
                            <TrendingUp className="mr-1 size-3" />
                          ) : (
                            <TrendingDown className="mr-1 size-3" />
                          )}
                          {stats.mappedUnits}/{stats.totalUnits}
                        </Badge>
                      </div>
                      <div className="mt-3 h-2 w-full rounded-full bg-muted">
                        <div
                          className="h-2 rounded-full bg-primary transition-all duration-300"
                          style={{ width: `${stats.mappingProgress}%` }}
                        />
                      </div>
                    </div>
                    <div className="rounded-full bg-success/10 p-3">
                      <Target className="size-6 text-success" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        {t("pages.layouts.systemHealth")}
                      </p>
                      <div className="mt-2 flex items-center space-x-2">
                        <p className="text-2xl font-bold text-foreground">
                          {stats.healthScore.toFixed(0)}%
                        </p>
                        <Badge
                          className={
                            stats.healthScore >= 90
                              ? "border-success/20 bg-success/10 text-success"
                              : stats.healthScore >= 70
                                ? "border-warning/20 bg-warning/10 text-warning"
                                : "border-destructive/20 bg-destructive/10 text-destructive"
                          }>
                          {stats.healthScore >= 90 ? (
                            <CheckCircle className="mr-1 size-3" />
                          ) : stats.healthScore >= 70 ? (
                            <AlertTriangle className="mr-1 size-3" />
                          ) : (
                            <AlertTriangle className="mr-1 size-3" />
                          )}
                          {stats.layoutsWithIssues === 0
                            ? t("common.status.healthy")
                            : `${stats.layoutsWithIssues} ${t("common.issues")}`}
                        </Badge>
                      </div>
                    </div>
                    <div className="rounded-full bg-secondary/10 p-3">
                      <Activity className="size-6 text-secondary-foreground" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        {t("pages.layouts.weeklyProgress")}
                      </p>
                      <div className="mt-2 flex items-center space-x-2">
                        <p className="text-2xl font-bold text-foreground">
                          +{stats.weeklyProgress}%
                        </p>
                        <Badge className="border-warning/20 bg-warning/10 text-warning">
                          <Zap className="mr-1 size-3" />
                          {stats.recentActivity} {t("pages.layouts.recentChanges")}
                        </Badge>
                      </div>
                    </div>
                    <div className="rounded-full bg-warning/10 p-3">
                      <BarChart3 className="size-6 text-warning" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </div>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
          {/* Property Selector */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Building2 className="size-4" />
                  {t("pages.properties.selectProperty")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Select value={selectedPropertyId} onValueChange={handlePropertySelect}>
                  <SelectTrigger className="property-selector">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {properties.map((property) => (
                      <SelectItem key={property.id} value={property.id}>
                        <div className="flex items-center gap-2">
                          <Building2 className="size-4" />
                          <div>
                            <div className="font-medium">{property.name}</div>
                            <div className="text-xs text-muted-foreground">
                              {property.total_units} units
                            </div>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {selectedProperty && (
                  <div className="space-y-2 rounded-lg bg-muted p-3">
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-muted-foreground">
                        {t("pages.properties.headers.type")}
                      </span>
                      <Badge variant="secondary" className="text-xs">
                        {selectedProperty.property_type}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-muted-foreground">
                        {t("pages.properties.headers.totalUnits")}
                      </span>
                      <span className="text-xs font-medium">{selectedProperty.total_units}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-muted-foreground">
                        {t("common.status.status")}
                      </span>
                      <Badge
                        variant={selectedProperty.status === "active" ? "default" : "secondary"}
                        className="text-xs">
                        {t(`common.status.${selectedProperty.status}`)}
                      </Badge>
                    </div>
                  </div>
                )}

                <Separator />

                <Button
                  className="w-full"
                  onClick={() => setViewMode("mapping")}
                  disabled={!selectedProperty}>
                  <MapPin className="mr-2 size-4" />
                  {t("pages.layouts.openMapping")}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Layouts Grid */}
          <div className="lg:col-span-3">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Layout className="size-5" />
                    {t("pages.layouts.floorPlans")}
                    {selectedProperty && (
                      <span className="text-sm font-normal text-muted-foreground">
                        - {selectedProperty.name}
                      </span>
                    )}
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowCreateDialog(true)}
                      disabled={!selectedProperty || isLoading}>
                      <Plus className="mr-2 size-4" />
                      {t("pages.layouts.addLayout")}
                    </Button>

                    {/* Bulk Actions */}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm" disabled={!selectedProperty}>
                          <Settings className="mr-2 size-4" />
                          {t("common.bulkActions")}
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem>
                          <Copy className="mr-2 size-4" />
                          {t("pages.layouts.duplicateAll")}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={handleExportLayouts}>
                          <Download className="mr-2 size-4" />
                          {t("pages.layouts.exportAll")}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={handleImportLayouts}>
                          <Upload className="mr-2 size-4" />
                          {t("pages.layouts.importAll")}
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Share className="mr-2 size-4" />
                          {t("pages.layouts.shareAll")}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          <FileText className="mr-2 size-4" />
                          {t("pages.layouts.generateReport")}
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Layout className="mr-2 size-4" />
                          {t("pages.layouts.createTemplate")}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {/* Enhanced Search and Filter */}
                <div className="mb-6 flex items-center gap-4">
                  <div className="relative max-w-sm flex-1">
                    <Search className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      placeholder={t("pages.layouts.searchLayouts")}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-9"
                    />
                  </div>

                  <Select
                    value={statusFilter}
                    onValueChange={(value: typeof statusFilter) => setStatusFilter(value)}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder={t("common.filter")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">{t("common.all")}</SelectItem>
                      <SelectItem value="complete">{t("pages.layouts.complete")}</SelectItem>
                      <SelectItem value="incomplete">{t("pages.layouts.incomplete")}</SelectItem>
                      <SelectItem value="issues">{t("pages.layouts.withIssues")}</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={sortBy} onValueChange={(value: typeof sortBy) => setSortBy(value)}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder={t("common.sortBy")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="name">{t("common.name")}</SelectItem>
                      <SelectItem value="progress">{t("common.progress")}</SelectItem>
                      <SelectItem value="lastUpdated">{t("common.lastUpdated")}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Layouts Grid */}
                {selectedProperty ? (
                  <ScrollArea className="h-[500px]">
                    {isLoading ? (
                      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                        <LayoutCardSkeleton />
                        <LayoutCardSkeleton />
                        <LayoutCardSkeleton />
                        <LayoutCardSkeleton />
                        <LayoutCardSkeleton />
                        <LayoutCardSkeleton />
                      </div>
                    ) : filteredLayouts.length > 0 ? (
                      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                        {filteredLayouts.map((layout) => {
                          // Calculate layout health and metrics with real-time data
                          const mappedUnitsCount = layout.unitPositions?.length || 0;
                          const totalUnitsForLayout = units.filter(
                            (u) => u.property_id === selectedPropertyId
                          ).length;
                          const layoutProgress =
                            totalUnitsForLayout > 0
                              ? (mappedUnitsCount / totalUnitsForLayout) * 100
                              : 0;
                          const hasIssues = mappedUnitsCount === 0 && totalUnitsForLayout > 0;
                          const lastActivity =
                            mappedUnitsCount > 0 ? "Recently updated" : "No activity";

                          return (
                            <Card
                              key={layout.id}
                              className="overflow-hidden transition-all duration-200 hover:shadow-lg">
                              <div className="relative h-48 bg-muted">
                                <img
                                  src={layout.imageUrl}
                                  alt={layout.name}
                                  className="size-full object-cover"
                                  onError={(e) => {
                                    (e.target as HTMLImageElement).src =
                                      "https://via.placeholder.com/400x300/f0f0f0/666?text=Floor+Plan";
                                  }}
                                />

                                {/* Health Indicator */}
                                <div className="absolute left-2 top-2">
                                  <Badge
                                    className={`${hasIssues ? "border-destructive/20 bg-destructive/10 text-destructive" : layoutProgress >= 80 ? "border-success/20 bg-success/10 text-success" : "border-warning/20 bg-warning/10 text-warning"}`}>
                                    {hasIssues ? (
                                      <AlertTriangle className="mr-1 size-3" />
                                    ) : layoutProgress >= 80 ? (
                                      <CheckCircle className="mr-1 size-3" />
                                    ) : (
                                      <Clock className="mr-1 size-3" />
                                    )}
                                    {hasIssues
                                      ? t("common.status.issues")
                                      : layoutProgress >= 80
                                        ? t("common.status.complete")
                                        : t("common.status.inProgress")}
                                  </Badge>
                                </div>

                                {/* Quick Actions */}
                                <div className="absolute right-2 top-2 flex gap-1">
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                        variant="secondary"
                                        size="sm"
                                        onClick={() => setViewMode("mapping")}>
                                        <Eye className="size-4" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>{t("common.view")}</TooltipContent>
                                  </Tooltip>

                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button variant="secondary" size="sm">
                                        <Settings className="size-4" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent>
                                      <DropdownMenuItem onClick={() => setViewMode("mapping")}>
                                        <Edit className="mr-2 size-4" />
                                        {t("common.edit")}
                                      </DropdownMenuItem>
                                      <DropdownMenuItem>
                                        <Copy className="mr-2 size-4" />
                                        {t("common.duplicate")}
                                      </DropdownMenuItem>
                                      <DropdownMenuItem>
                                        <Download className="mr-2 size-4" />
                                        {t("common.export")}
                                      </DropdownMenuItem>
                                      <DropdownMenuItem>
                                        <Share className="mr-2 size-4" />
                                        {t("common.share")}
                                      </DropdownMenuItem>
                                      <DropdownMenuSeparator />
                                      <DropdownMenuItem>
                                        <FileText className="mr-2 size-4" />
                                        {t("pages.layouts.viewReport")}
                                      </DropdownMenuItem>
                                      <DropdownMenuItem
                                        onClick={() => handleCreateTemplate(layout.id)}>
                                        <Layout className="mr-2 size-4" />
                                        {t("pages.layouts.saveAsTemplate")}
                                      </DropdownMenuItem>
                                      <DropdownMenuSeparator />
                                      <DropdownMenuItem
                                        onClick={() => setLayoutToDelete(layout.id)}
                                        className="text-destructive">
                                        <Trash2 className="mr-2 size-4" />
                                        {t("common.delete")}
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </div>
                              </div>
                              <CardContent className="p-4">
                                <div className="space-y-3">
                                  <div className="flex items-center justify-between">
                                    <h3 className="truncate font-semibold text-foreground">
                                      {layout.name}
                                    </h3>
                                    <Badge variant="outline" className="text-xs">
                                      {layout.width} × {layout.height}
                                    </Badge>
                                  </div>

                                  {/* Progress Bar */}
                                  <div className="space-y-1">
                                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                                      <span>{t("pages.layouts.mappingProgress")}</span>
                                      <span>{layoutProgress.toFixed(0)}%</span>
                                    </div>
                                    <div className="h-1.5 w-full rounded-full bg-muted">
                                      <div
                                        className={`h-1.5 rounded-full transition-all duration-300 ${layoutProgress >= 80 ? "bg-success" : layoutProgress >= 50 ? "bg-warning" : "bg-destructive"}`}
                                        style={{ width: `${layoutProgress}%` }}
                                      />
                                    </div>
                                  </div>

                                  {/* Metrics */}
                                  <div className="grid grid-cols-2 gap-2 text-xs">
                                    <div className="flex items-center justify-between">
                                      <span className="text-muted-foreground">
                                        {t("pages.layouts.mappedUnits")}
                                      </span>
                                      <span className="font-medium">{mappedUnitsCount}</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                      <span className="text-muted-foreground">
                                        {t("pages.layouts.totalUnits")}
                                      </span>
                                      <span className="font-medium">{totalUnitsForLayout}</span>
                                    </div>
                                  </div>

                                  {/* Last Activity */}
                                  <div className="mt-2 flex items-center border-t pt-2 text-xs text-muted-foreground">
                                    <Clock className="mr-1 size-3" />
                                    <span>
                                      {t("pages.layouts.lastActivity")}: {lastActivity}
                                    </span>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          );
                        })}
                      </div>
                    ) : (
                      <div className="py-12 text-center">
                        <Layout className="mx-auto mb-4 size-16 text-muted-foreground" />
                        <h3 className="mb-2 text-lg font-semibold text-foreground">
                          {searchTerm
                            ? t("pages.layouts.noLayoutsFound")
                            : t("pages.layouts.noLayouts")}
                        </h3>
                        <p className="mb-4 text-sm text-muted-foreground">
                          {searchTerm
                            ? t("pages.layouts.tryDifferentSearch")
                            : t("pages.layouts.createFirstLayout")}
                        </p>
                        {!searchTerm && (
                          <Button onClick={() => setShowCreateDialog(true)}>
                            <Plus className="mr-2 size-4" />
                            {t("pages.layouts.addLayout")}
                          </Button>
                        )}
                      </div>
                    )}
                  </ScrollArea>
                ) : (
                  <div className="py-12 text-center">
                    <Building2 className="mx-auto mb-4 size-16 text-muted-foreground" />
                    <h3 className="mb-2 text-lg font-semibold text-foreground">
                      {t("pages.properties.selectProperty")}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {t("pages.layouts.selectPropertyFirst")}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Create Layout Dialog */}
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
            {selectedProperty && (
              <CreateLayoutForm
                property={selectedProperty}
                onSubmit={handleCreateLayout}
                onCancel={() => setShowCreateDialog(false)}
                isLoading={createLayoutMutation.isPending}
              />
            )}
          </DialogContent>
        </Dialog>

        {/* Delete Layout Dialog */}
        <Dialog open={!!layoutToDelete} onOpenChange={() => setLayoutToDelete(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t("pages.layouts.deleteLayout")}</DialogTitle>
              <DialogDescription>{t("pages.layouts.deleteLayoutConfirmation")}</DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setLayoutToDelete(null)}>
                {t("common.cancel")}
              </Button>
              <Button
                variant="destructive"
                onClick={() => layoutToDelete && handleLayoutDelete(layoutToDelete)}
                disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 size-4 animate-spin" />
                    {t("common.deleting")}
                  </>
                ) : (
                  t("common.delete")
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Create Template Dialog */}
        <Dialog
          open={templateDialog.open}
          onOpenChange={() => setTemplateDialog({ open: false, layout: null })}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>{t("pages.layouts.createTemplate")}</DialogTitle>
              <DialogDescription>{t("pages.layouts.createTemplateDescription")}</DialogDescription>
            </DialogHeader>
            <TemplateFormComponent
              layout={templateDialog.layout}
              onSubmit={handleSaveAsTemplate}
              onCancel={() => setTemplateDialog({ open: false, layout: null })}
            />
          </DialogContent>
        </Dialog>
      </div>
    </TooltipProvider>
  );
}
