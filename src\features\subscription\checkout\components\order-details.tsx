"use client";

import { useTranslation } from "react-i18next";

import { InfoRow } from "./info-row";

interface OrderDetailsProps {
  creationDate: string;
  paymentMethod: string;
}

export const OrderDetails: React.FC<OrderDetailsProps> = ({ creationDate, paymentMethod }) => {
  const { t } = useTranslation();

  const orderInfoFields = [
    {
      key: "creationDate",
      label: t("pages.checkout.orderInformation.creationDate"),
      value: creationDate,
    },
    {
      key: "paymentMethod",
      label: t("pages.checkout.orderInformation.paymentMethod"),
      value: paymentMethod,
    },
  ] as const;

  return (
    <div className="flex flex-col gap-2">
      {orderInfoFields.map(({ key, label, value }) => (
        <InfoRow key={key} label={label} value={value} />
      ))}
    </div>
  );
};
