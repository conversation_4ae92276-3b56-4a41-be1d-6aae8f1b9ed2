"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
  Cell,
  Pie,
  <PERSON>hart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import type { AssetCategory } from "../types";

interface AnalyticsSidebarProps {
  categories: AssetCategory[];
}

export function AnalyticsSidebar({ categories }: AnalyticsSidebarProps) {
  // Data for charts
  const pieChartData = categories
    .filter(
      (cat) => typeof cat.assetCount === "number" && !isNaN(cat.assetCount) && cat.assetCount > 0
    )
    .map((cat) => ({
      name: cat.name,
      value: cat.assetCount,
      color: cat.color || "#000000",
    }));

  const performanceData = categories
    .filter((cat) => typeof cat.performanceScore === "number" && !isNaN(cat.performanceScore))
    .map((cat) => ({
      name: cat.name.length > 15 ? cat.name.substring(0, 15) + "..." : cat.name,
      performance: cat.performanceScore,
      assets: cat.assetCount || 0,
    }));

  // Calculate statistics
  const highestPerformance =
    categories.length > 0
      ? categories.reduce((max, cat) => (cat.performanceScore > max.performanceScore ? cat : max))
      : null;

  const mostAssets =
    categories.length > 0
      ? categories.reduce((max, cat) => (cat.assetCount > max.assetCount ? cat : max))
      : null;

  const highestValue =
    categories.length > 0
      ? categories.reduce((max, cat) => (cat.totalValue > max.totalValue ? cat : max))
      : null;

  return (
    <div className="space-y-4">
      {/* Asset Distribution Chart */}
      <Card className="border-border">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-card-foreground">
            Asset Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[200px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={pieChartData}
                  cx="50%"
                  cy="50%"
                  outerRadius={60}
                  dataKey="value"
                  label={({ name, percent }) => `${(percent * 100).toFixed(0)}%`}
                  labelLine={false}>
                  {pieChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: "hsl(var(--card))",
                    border: "1px solid hsl(var(--border))",
                    borderRadius: "6px",
                    color: "hsl(var(--foreground))",
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Performance Chart */}
      <Card className="border-border">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-card-foreground">
            Performance Scores
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[250px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={performanceData} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.3} />
                <XAxis
                  type="number"
                  domain={[0, 100]}
                  stroke="hsl(var(--muted-foreground))"
                  fontSize={12}
                />
                <YAxis
                  dataKey="name"
                  type="category"
                  width={80}
                  stroke="hsl(var(--muted-foreground))"
                  fontSize={12}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: "hsl(var(--card))",
                    border: "1px solid hsl(var(--border))",
                    borderRadius: "6px",
                    color: "hsl(var(--foreground))",
                  }}
                />
                <Bar dataKey="performance" fill="hsl(var(--primary))" radius={[0, 4, 4, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Category Statistics */}
      <Card className="border-border">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium text-card-foreground">
            Category Statistics
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Highest Performance</span>
            <div className="flex items-center gap-2">
              {highestPerformance ? (
                <>
                  <Badge variant="secondary" className="text-xs">
                    {highestPerformance.performanceScore}%
                  </Badge>
                  <span className="text-sm font-medium text-card-foreground">
                    {highestPerformance.name}
                  </span>
                </>
              ) : (
                <span className="text-sm text-muted-foreground">No data</span>
              )}
            </div>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Most Assets</span>
            <div className="flex items-center gap-2">
              {mostAssets ? (
                <>
                  <Badge variant="secondary" className="text-xs">
                    {mostAssets.assetCount}
                  </Badge>
                  <span className="text-sm font-medium text-card-foreground">
                    {mostAssets.name}
                  </span>
                </>
              ) : (
                <span className="text-sm text-muted-foreground">No data</span>
              )}
            </div>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Highest Value</span>
            <div className="flex items-center gap-2">
              {highestValue ? (
                <>
                  <Badge variant="secondary" className="text-xs">
                    ${(highestValue.totalValue / 1000000).toFixed(1)}M
                  </Badge>
                  <span className="text-sm font-medium text-card-foreground">
                    {highestValue.name}
                  </span>
                </>
              ) : (
                <span className="text-sm text-muted-foreground">No data</span>
              )}
            </div>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Active Categories</span>
            <div className="flex items-center gap-2">
              <Badge variant="default" className="text-xs">
                {categories.filter((cat) => cat.isActive).length}
              </Badge>
              <span className="text-sm font-medium text-card-foreground">Active</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
