"use client";

import { useRouter } from "next/navigation";
import { BadgeCheck } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { queryClient } from "@/components/providers";
import { Button } from "@/components/ui/button";
import { clearAuth } from "@/lib/auth";

interface SuccessStepProps {
  onBackToLogin: () => void;
}

export const SuccessStep = ({ onBackToLogin }: SuccessStepProps) => {
  const { t } = useTranslation();
  const router = useRouter();
  const handleLogout = () => {
    try {
      // Clear all TanStack Query cache to prevent data from previous sessions
      queryClient.clear();

      clearAuth();
      router.push("/login");
    } catch {
      toast.error(t("auth.logoutError"));
    }
  };
  return (
    <>
      <div className="space-y-6 px-6 pb-8 pt-6 text-center">
        <div className="flex justify-center">
          <div className="rounded-full bg-green-100 p-5">
            <BadgeCheck className="size-20 text-green-600" />
          </div>
        </div>

        <div className="space-y-2">
          <h2 className="text-2xl font-semibold">
            {t("pages.profile.forgotPasswordDialog.successStep.title")}
          </h2>
          <p className="text-sm text-muted-foreground">
            {t("pages.profile.forgotPasswordDialog.successStep.description")}
          </p>
        </div>
      </div>

      <div className="flex items-center justify-center px-6 pb-6 pt-0">
        <Button onClick={handleLogout} className="w-fit">
          {t("pages.profile.forgotPasswordDialog.successStep.loginButton")}
        </Button>
      </div>
    </>
  );
};
