#!/usr/bin/env node
import fs from "fs";
import path from "path";
import puppeteer from "puppeteer";

// Configuration
const CONFIG = {
  BASE_URL: "http://localhost:3000",
  LOGIN_URL: "http://localhost:3000/login",
  FORM_URL: "http://localhost:3000/property-assets/properties/new",
  LIST_URL: "http://localhost:3000/property-assets/properties",
  LOGIN_CREDENTIALS: {
    username: "onexapis_admin",
    password: "Admin@123",
  },
  LOAD_TIMEOUT: 30000,
  VIEWPORTS: {
    mobile: { width: 375, height: 667 },
    tablet: { width: 768, height: 1024 },
    desktop: { width: 1200, height: 800 },
  },
  SCREENSHOT_DIR: "./screenshots/property-form-validation",
};

// Ensure screenshot directory exists
if (!fs.existsSync(CONFIG.SCREENSHOT_DIR)) {
  fs.mkdirSync(CONFIG.SCREENSHOT_DIR, { recursive: true });
}

async function performLogin(page) {
  console.log("🔐 Performing authentication...");

  try {
    await page.goto(CONFIG.LOGIN_URL, { waitUntil: "networkidle2", timeout: CONFIG.LOAD_TIMEOUT });

    // Wait for page to load and find login elements
    await page.waitForSelector(
      'input[name="email"], input[type="email"], input[placeholder*="email" i]',
      { timeout: 10000 }
    );

    // Find username/email input
    const usernameInput =
      (await page.$('input[name="email"]')) ||
      (await page.$('input[type="email"]')) ||
      (await page.$('input[placeholder*="email" i]'));

    if (!usernameInput) {
      throw new Error("Username input field not found");
    }

    // Clear and type username
    await usernameInput.click({ clickCount: 3 });
    await usernameInput.type(CONFIG.LOGIN_CREDENTIALS.username);

    // Find and fill password
    const passwordInput =
      (await page.$('input[name="password"]')) || (await page.$('input[type="password"]'));
    if (!passwordInput) {
      throw new Error("Password input field not found");
    }

    await passwordInput.click({ clickCount: 3 });
    await passwordInput.type(CONFIG.LOGIN_CREDENTIALS.password);

    // Find and click login button
    const loginButton =
      (await page.$('button[type="submit"]')) ||
      (await page.$('button:has-text("Login")')) ||
      (await page.$('button:has-text("Sign in")')) ||
      (await page.$('input[type="submit"]'));

    if (!loginButton) {
      throw new Error("Login button not found");
    }

    await loginButton.click();

    // Wait for navigation after login
    await page.waitForNavigation({ waitUntil: "networkidle2", timeout: 15000 });

    console.log("   ✅ Authentication successful");
    return true;
  } catch (error) {
    console.log(`   ❌ Authentication failed: ${error.message}`);
    return false;
  }
}

async function testPropertyList(page) {
  console.log("📋 Testing Property List page...");
  const results = [];

  try {
    await page.goto(CONFIG.LIST_URL, { waitUntil: "networkidle2", timeout: CONFIG.LOAD_TIMEOUT });

    // Test 1: Page loads successfully
    const title = await page.title();
    results.push({
      test: "Page Load",
      passed: title.includes("Properties") || title.includes("OneX"),
      details: `Page title: ${title}`,
    });

    // Test 2: Table structure exists
    const tableExists = (await page.$('table, [role="table"], .custom-table')) !== null;
    results.push({
      test: "Table Structure",
      passed: tableExists,
      details: tableExists ? "Table found" : "Table not found",
    });

    // Test 3: Add Property button exists and works
    const addButtonTest = await page.evaluate(() => {
      // Look for the dropdown button with "Add Property" text
      const button = Array.from(document.querySelectorAll("button")).find(
        (el) => el.textContent?.includes("Add Property") || el.textContent?.includes("Add")
      );

      // Debug: let's see all buttons on the page
      const allButtons = Array.from(document.querySelectorAll("button")).map((btn) => ({
        text: btn.textContent?.trim(),
        classes: btn.className,
        attributes: {
          "data-state": btn.getAttribute("data-state"),
          "aria-haspopup": btn.getAttribute("aria-haspopup"),
          "aria-expanded": btn.getAttribute("aria-expanded"),
        },
      }));

      return {
        exists: !!button,
        text: button?.textContent || null,
        isDropdown:
          button?.getAttribute("data-state") !== null ||
          button?.getAttribute("aria-haspopup") === "true",
        allButtons: allButtons,
      };
    });

    results.push({
      test: "Add Property Button",
      passed: addButtonTest.exists,
      details: addButtonTest.exists
        ? `Add button found: "${addButtonTest.text}" (dropdown: ${addButtonTest.isDropdown})`
        : `Add button not found. All buttons: ${JSON.stringify(addButtonTest.allButtons, null, 2)}`,
    });

    // Test 4: Test button functionality (click and check dropdown)
    if (addButtonTest.exists) {
      try {
        // Wait for the button to be clickable and click it
        await page.waitForSelector("button", { visible: true });

        // Click the Add Property button using page.click for better reliability
        const buttonClicked = await page.evaluate(() => {
          const button = Array.from(document.querySelectorAll("button")).find(
            (el) => el.textContent?.includes("Add Property") || el.textContent?.includes("Add")
          );
          if (button) {
            button.click();
            return true;
          }
          return false;
        });

        if (!buttonClicked) {
          results.push({
            test: "Button Functionality",
            passed: false,
            details: "Could not find or click the Add Property button",
          });
        } else {
          // Wait longer for dropdown to appear
          await new Promise((resolve) => setTimeout(resolve, 1000));

          // Check if dropdown menu appeared or navigation happened
          const dropdownOrNavigation = await page.evaluate(() => {
            // More comprehensive dropdown selectors
            const dropdown = document.querySelector(
              '[role="menu"], [data-radix-popper-content-wrapper], [data-radix-dropdown-menu-content], .dropdown-content'
            );

            // Check if URL changed (navigation)
            const urlChanged = window.location.pathname.includes("/properties/new");

            // Also check for visible dropdown items
            const dropdownItems = document.querySelectorAll('[role="menuitem"], .dropdown-item');

            return {
              dropdownVisible: !!dropdown,
              navigated: urlChanged,
              currentUrl: window.location.pathname,
              dropdownItemsCount: dropdownItems.length,
              dropdownHTML: dropdown ? dropdown.outerHTML.substring(0, 200) : null,
            };
          });

          results.push({
            test: "Button Functionality",
            passed: dropdownOrNavigation.dropdownVisible || dropdownOrNavigation.navigated,
            details: dropdownOrNavigation.navigated
              ? `Navigation successful to ${dropdownOrNavigation.currentUrl}`
              : dropdownOrNavigation.dropdownVisible
                ? `Dropdown menu opened successfully (${dropdownOrNavigation.dropdownItemsCount} items)`
                : "Button click had no visible effect",
          });

          // If dropdown appeared, try clicking the actual "Add Property" item
          if (dropdownOrNavigation.dropdownVisible) {
            const menuItemClicked = await page.evaluate(() => {
              // Look for menu items with more specific selectors
              const menuItem = Array.from(
                document.querySelectorAll('a[role="menuitem"], [role="menuitem"], .dropdown-item a')
              ).find(
                (el) =>
                  el.textContent?.includes("Add Manual") ||
                  el.textContent?.includes("Add Property") ||
                  (el.href && el.href.includes("/properties/new"))
              );

              if (menuItem) {
                menuItem.click();
                return true;
              }
              return false;
            });

            await new Promise((resolve) => setTimeout(resolve, 1500));

            const finalNavigation = await page.evaluate(() => ({
              currentUrl: window.location.pathname,
              navigated: window.location.pathname.includes("/properties/new"),
            }));

            results.push({
              test: "Dropdown Navigation",
              passed: finalNavigation.navigated,
              details: finalNavigation.navigated
                ? `Successfully navigated to ${finalNavigation.currentUrl}`
                : menuItemClicked
                  ? "Menu item clicked but navigation failed"
                  : "Could not find or click dropdown menu item",
            });
          }
        }
      } catch (error) {
        results.push({
          test: "Button Functionality",
          passed: false,
          details: `Button click test failed: ${error.message}`,
        });
      }
    }

    // Test 4: Search functionality
    const searchInput =
      (await page.$('input[type="search"], input[placeholder*="search" i]')) !== null;
    results.push({
      test: "Search Functionality",
      passed: searchInput,
      details: searchInput ? "Search input found" : "Search input not found",
    });
  } catch (error) {
    results.push({
      test: "Property List Access",
      passed: false,
      details: `Error: ${error.message}`,
    });
  }

  return results;
}

async function testPropertyForm(page) {
  console.log("📝 Testing Property Form page...");
  const results = [];

  try {
    await page.goto(CONFIG.FORM_URL, { waitUntil: "networkidle2", timeout: CONFIG.LOAD_TIMEOUT });

    // Test 1: Form page loads successfully
    const title = await page.title();
    results.push({
      test: "Form Page Load",
      passed: title.includes("Property") || title.includes("OneX"),
      details: `Page title: ${title}`,
    });

    // Test 2: Property name input exists
    const nameInput =
      (await page.$('input[name="name"], input[placeholder*="property name" i]')) !== null;
    results.push({
      test: "Property Name Input",
      passed: nameInput,
      details: nameInput ? "Name input found" : "Name input not found",
    });

    // Test 3: Property type select exists (Radix UI Select)
    const typeSelect =
      (await page.$(
        'select[name="property_type"], [name="property_type"], button[role="combobox"], [data-radix-select-trigger]'
      )) !== null;
    results.push({
      test: "Property Type Select",
      passed: typeSelect,
      details: typeSelect ? "Type select found" : "Type select not found",
    });

    // Test 4: Address fields exist
    const streetInput =
      (await page.$('input[name="address.street"], input[placeholder*="street" i]')) !== null;
    results.push({
      test: "Address Fields",
      passed: streetInput,
      details: streetInput ? "Address fields found" : "Address fields not found",
    });

    // Test 5: Owner information fields
    const ownerNameInput =
      (await page.$('input[name="owner_name"], input[placeholder*="owner name" i]')) !== null;
    results.push({
      test: "Owner Information",
      passed: ownerNameInput,
      details: ownerNameInput ? "Owner fields found" : "Owner fields not found",
    });

    // Test 6: Submit button exists
    const submitButton = await page.evaluate(() => {
      const button = Array.from(document.querySelectorAll("button")).find(
        (el) =>
          el.type === "submit" ||
          el.textContent?.includes("Create") ||
          el.textContent?.includes("Save")
      );
      return !!button;
    });
    results.push({
      test: "Submit Button",
      passed: submitButton,
      details: submitButton ? "Submit button found" : "Submit button not found",
    });

    // Test 7: Form validation (try to submit empty form)
    if (submitButton) {
      try {
        await page.evaluate(() => {
          const button = Array.from(document.querySelectorAll("button")).find(
            (el) =>
              el.type === "submit" ||
              el.textContent?.includes("Create") ||
              el.textContent?.includes("Save")
          );
          if (button) button.click();
        });
        (await page.waitForTimeout)
          ? page.waitForTimeout(1000)
          : new Promise((resolve) => setTimeout(resolve, 1000));

        // Check for validation messages
        const validationMessage =
          (await page.$('.error, [role="alert"], .text-red, .text-destructive')) !== null;
        results.push({
          test: "Form Validation",
          passed: validationMessage,
          details: validationMessage ? "Validation messages shown" : "No validation messages",
        });
      } catch (error) {
        results.push({
          test: "Form Validation",
          passed: false,
          details: `Validation test error: ${error.message}`,
        });
      }
    }
  } catch (error) {
    results.push({
      test: "Property Form Access",
      passed: false,
      details: `Error: ${error.message}`,
    });
  }

  return results;
}

async function captureScreenshots(page, testName) {
  console.log("📸 Capturing screenshots...");
  const screenshots = {};

  for (const [device, viewport] of Object.entries(CONFIG.VIEWPORTS)) {
    try {
      await page.setViewport(viewport);
      await new Promise((resolve) => setTimeout(resolve, 1000)); // Let the page adjust

      const screenshotPath = path.join(CONFIG.SCREENSHOT_DIR, `${testName}-${device}.png`);
      await page.screenshot({
        path: screenshotPath,
        fullPage: true,
      });

      screenshots[device] = {
        success: true,
        path: screenshotPath,
      };
      console.log(`   ✅ ${device} screenshot captured`);
    } catch (error) {
      screenshots[device] = {
        success: false,
        error: error.message,
      };
      console.log(`   ❌ ${device} screenshot failed: ${error.message}`);
    }
  }

  return screenshots;
}

async function generateReport(listResults, formResults, listScreenshots, formScreenshots) {
  const totalTests = listResults.length + formResults.length;
  const passedTests = [...listResults, ...formResults].filter((result) => result.passed).length;
  const passRate = ((passedTests / totalTests) * 100).toFixed(1);

  console.log("\n📊 Property Form Validation Report");
  console.log("================================");
  console.log(`Overall Pass Rate: ${passRate}% (${passedTests}/${totalTests} tests passed)`);
  console.log("");

  console.log("📋 Property List Tests:");
  listResults.forEach((result) => {
    const status = result.passed ? "✅" : "❌";
    console.log(`   ${status} ${result.test}: ${result.details}`);
  });

  console.log("\n📝 Property Form Tests:");
  formResults.forEach((result) => {
    const status = result.passed ? "✅" : "❌";
    console.log(`   ${status} ${result.test}: ${result.details}`);
  });

  console.log("\n📸 Screenshots:");
  console.log("Property List:");
  Object.entries(listScreenshots).forEach(([device, result]) => {
    const status = result.success ? "✅" : "❌";
    console.log(`   ${status} ${device}: ${result.success ? result.path : result.error}`);
  });

  console.log("Property Form:");
  Object.entries(formScreenshots).forEach(([device, result]) => {
    const status = result.success ? "✅" : "❌";
    console.log(`   ${status} ${device}: ${result.success ? result.path : result.error}`);
  });

  return { passRate: parseFloat(passRate), totalTests, passedTests };
}

async function main() {
  console.log("🚀 Starting Property Form validation...");

  const browser = await puppeteer.launch({
    headless: true,
    args: ["--no-sandbox", "--disable-setuid-sandbox"],
  });

  try {
    const page = await browser.newPage();

    // Perform authentication
    const authSuccess = await performLogin(page);
    if (!authSuccess) {
      console.log("❌ Validation failed: Authentication unsuccessful");
      process.exit(1);
    }

    // Test Property List
    const listResults = await testPropertyList(page);
    const listScreenshots = await captureScreenshots(page, "property-list");

    // Test Property Form
    const formResults = await testPropertyForm(page);
    const formScreenshots = await captureScreenshots(page, "property-form");

    // Generate report
    const report = await generateReport(listResults, formResults, listScreenshots, formScreenshots);

    console.log("\n🎯 Validation Complete!");
    console.log(`Pass Rate: ${report.passRate}%`);

    if (report.passRate >= 80) {
      console.log("✅ Property Form validation PASSED (≥80% pass rate)");
      process.exit(0);
    } else {
      console.log("❌ Property Form validation FAILED (<80% pass rate)");
      process.exit(1);
    }
  } catch (error) {
    console.error("❌ Validation failed with error:", error);
    process.exit(1);
  } finally {
    await browser.close();
  }
}

main().catch(console.error);
