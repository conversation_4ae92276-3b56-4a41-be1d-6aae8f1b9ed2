import {
  InfiniteData,
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { SortDirection } from "@/components/custom-table/hooks/use-data-table";
import { employeeApi } from "@/lib/apis/employee";
import { ResponseAxiosDetail } from "@/lib/apis/types/common";
import { Employee as ApiEmployee } from "@/lib/apis/types/employee";

import { Employee, EmployeeListParams, EmployeeListResponse } from "../types/employee";
import { employeeKeys } from "./keys";

interface UseEmployeesOptions extends Partial<EmployeeListParams> {
  enabled?: boolean;
  onCreateSuccess?: (data: any) => void;
  onDeleteSuccess?: () => void;
  onToggleStatusSuccess?: (data: any) => void;
}

export function useEmployees(options: UseEmployeesOptions = {}) {
  const { t } = useTranslation();
  const {
    limit = 20,
    enabled = true,
    onCreateSuccess,
    onDeleteSuccess,
    onToggleStatusSuccess,
    ...restOptions
  } = options;
  const queryClient = useQueryClient();

  const query = useInfiniteQuery({
    queryKey: employeeKeys.list({ limit, ...restOptions }),
    queryFn: ({ pageParam = 0 }) =>
      employeeApi.list({
        limit,
        ["sort_updated_at"]: SortDirection.DESC,
        page: pageParam as number,
        ...restOptions,
      }),
    getNextPageParam: (lastPage) => {
      const totalPages = Math.ceil(lastPage.total / Number(lastPage.limit));
      if (lastPage.page < totalPages) {
        return lastPage.page + 1;
      }
      return undefined;
    },
    initialPageParam: 0,
    enabled,
  });

  const employees = query.data?.pages.flatMap((page) => page.items) ?? [];
  const total = query.data?.pages[0]?.total ?? 0;

  const useDeleteEmployeeMutation = useMutation({
    mutationFn: async (userId: string) => {
      const response = await employeeApi.delete(userId);
      return response;
    },
    onSuccess: (_, deletedUserId) => {
      queryClient.setQueryData<InfiniteData<EmployeeListResponse>>(
        employeeKeys.list({ limit, ...restOptions }),
        (oldData) => {
          if (!oldData) return oldData;
          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.filter((item) => item.id !== deletedUserId),
            total: page.total - 1,
          }));
          return {
            ...oldData,
            pages: newPages,
          };
        }
      );
      toast.success(t("pages.employeePermission.employeeDeletedSuccess"));
      onDeleteSuccess?.();
    },
    onError: (error) => {
      toast.error(t("pages.employeePermission.employeeDeletedError"));
    },
  });

  // Loading states
  const isLoading = query.isLoading;
  const isFetching = query.isFetching;
  const isFetchingNextPage = query.isFetchingNextPage;
  const hasNextPage = query.hasNextPage;

  const useCreateEmployeeMutation = useMutation<ResponseAxiosDetail<ApiEmployee>, Error, any>({
    mutationFn: async (data: any) => {
      const transformedData = {
        username: data.username,
        password: data.password,
        email: data.email || "",
        name: data.employeeName,
        phone_number: data.phone_number || "",
        address: data.address || "",
        birthdate: data.birthdate ? data.birthdate.toISOString().split("T")[0] : undefined,
        gender: "MALE",
        user_roles: data.roles.reduce((acc: Record<string, string[]>, role: any) => {
          if (role.branch && role.roles && role.roles.length > 0) {
            // Handle "all" branch option - send "all": [] instead of expanding
            if (role.branch === "all") {
              acc["all"] = role.roles;
            } else {
              acc[role.branch] = role.roles;
            }
          }
          return acc;
        }, {}),
      };
      const response = await employeeApi.create(transformedData);
      return response;
    },
    onSuccess: (response: ResponseAxiosDetail<Employee>) => {
      queryClient.setQueryData<InfiniteData<EmployeeListResponse>>(
        employeeKeys.list({ limit, ...restOptions }),
        (oldData) => {
          if (!oldData) return oldData;
          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: [...page.items, response.data],
            total: page.total + 1,
          }));
          return {
            ...oldData,
            pages: newPages,
          };
        }
      );
      // Call success callback if provided
      onCreateSuccess?.(response.data);
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const useToggleEmployeeStatusMutation = useMutation({
    mutationFn: async (data: { userId: string; enabled: boolean }) => {
      if (data.enabled) {
        const response = await employeeApi.deactivate(data.userId);
        return response;
      } else {
        const response = await employeeApi.activate(data.userId);
        return response;
      }
    },
    onSuccess: (response) => {
      const data = response.data;
      queryClient.setQueryData<InfiniteData<EmployeeListResponse>>(
        employeeKeys.list({ limit, ...restOptions }),
        (oldData) => {
          if (!oldData) return oldData;

          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.map((item) =>
              item.id === data.id ? { ...item, status: data.status } : item
            ),
          }));
          return { ...oldData, pages: newPages };
        }
      );
      // Call success callback if provided
      onToggleStatusSuccess?.(data);
    },
  });

  const useUpdateEmployeeMutation = useMutation<ApiEmployee, Error, { id: string; data: any }>({
    mutationFn: async (data: { id: string; data: any }) => {
      const transformedData = {
        name: data.data.name,
        phone_number: data.data.phone_number
          ? data.data.phone_number.startsWith("+84")
            ? data.data.phone_number
            : `+84${data.data.phone_number}`
          : "",
        address: data.data.address || "",
        birthdate: data.data.birthdate || undefined,
        user_roles: data.data.user_roles || {},
      };
      const response = await employeeApi.update(data.id, transformedData);
      return response;
    },
    onSuccess: (response) => {
      // Update the employee in the cache
      queryClient.setQueryData<InfiniteData<EmployeeListResponse>>(
        employeeKeys.list({ limit, ...restOptions }),
        (oldData) => {
          if (!oldData) return oldData;
          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.map((item) =>
              item.id === response.id ? { ...item, ...response } : item
            ),
          }));
          return { ...oldData, pages: newPages };
        }
      );
      // Also update the individual employee query if it exists
      queryClient.setQueryData(employeeKeys.detail(response.id), response);
      toast.success(t("pages.employeePermission.employeeUpdatedSuccess"));
    },
    onError: (error) => {
      toast.error(t("pages.employeePermission.employeeUpdatedError"));
    },
  });

  return {
    ...query,
    employees,
    total,
    // Loading states
    isLoading,
    isFetching,
    isFetchingNextPage,
    hasNextPage,
    // Mutations
    deleteEmployeeMutation: useDeleteEmployeeMutation,
    createEmployeeMutation: useCreateEmployeeMutation,
    toggleEmployeeStatusMutation: useToggleEmployeeStatusMutation,
    updateEmployeeMutation: useUpdateEmployeeMutation,
  };
}

interface UseEmployeeOptions {
  enabled?: boolean;
}

export function useEmployee(id: string, options: UseEmployeeOptions = {}) {
  const { enabled = true } = options;

  return useQuery<ApiEmployee>({
    queryKey: employeeKeys.detail(id),
    queryFn: async () => {
      const response = await employeeApi.getEmployeeById(id);
      // Transform the API response to match our Employee interface
      return {
        ...response,
        // Ensure user_roles is always an array
        user_roles: response.user_roles || [],
        // Map API fields to our interface
        birthdate: response.birthdate || null,
        phone_number: response.phone_number || null,
        address: response.address || null,
        picture: response.picture || null,
      };
    },
    enabled: !!id && enabled,
  });
}
export function useResetPassword() {
  const queryClient = useQueryClient();

  const mutation = useMutation<ApiEmployee, Error, { username: string; password: string }>({
    mutationFn: async ({ username, password }: { username: string; password: string }) => {
      const response = await employeeApi.setPassword(username, password);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: employeeKeys.lists() });
    },
  });

  return {
    ...mutation,
    isLoading: mutation.isPending,
    isSuccess: mutation.isSuccess,
    isError: mutation.isError,
  };
}
