"use client";

import { useLayoutEffect } from "react";
import { useRouter } from "next/navigation";

import { useLayout } from "@/components/providers";
import { Form } from "@/components/ui/form";
import { authProtectedPaths } from "@/constants/paths";

import { AmenitiesSection } from "./sections/AmenitiesSection";
import { BasicInformationSection } from "./sections/BasicInformationSection";
import { FinancialInformationSection } from "./sections/FinancialInformationSection";
import { SpecificationsSection } from "./sections/SpecificationsSection";
import { UnitFormFooter } from "./UnitFormFooter";
import { useUnitForm } from "./useUnitForm";

interface UnitFormProps {
  initialData?: any;
  isEditing?: boolean;
}

export function UnitForm({ initialData, isEditing = false }: UnitFormProps) {
  const router = useRouter();
  const { setFooter } = useLayout();

  const { form, isSubmitting, onSubmit, isFormValid } = useUnitForm({ initialData, isEditing });

  // Set footer using useLayoutEffect to avoid infinite loops
  useLayoutEffect(() => {
    const footerComponent = (
      <UnitFormFooter
        isSubmitting={isSubmitting}
        isEditing={isEditing}
        isFormValid={isFormValid}
        onCancel={() => router.push(authProtectedPaths.UNITS as any)}
      />
    );

    setFooter(footerComponent);

    return () => {
      setFooter(null);
    };
  }, [setFooter, isSubmitting, isEditing, isFormValid, router]);

  return (
    <div className="flex h-fit flex-auto flex-col gap-4 overflow-y-auto p-4 pt-0">
      <div className="flex-1">
        <Form {...form}>
          <form id="unit-form" onSubmit={form.handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
              {/* Left Column - Main Content */}
              <div className="col-span-1 space-y-4 lg:col-span-2">
                <BasicInformationSection form={form} />
                <SpecificationsSection form={form} />
              </div>

              {/* Right Column - Sidebar */}
              <div className="col-span-1 space-y-4">
                <FinancialInformationSection form={form} />
                <AmenitiesSection form={form} />
              </div>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
