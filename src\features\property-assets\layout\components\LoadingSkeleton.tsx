"use client";

import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface LoadingSkeletonProps {
  className?: string;
}

export function LayoutCardSkeleton({ className = "" }: LoadingSkeletonProps) {
  return (
    <Card className={`overflow-hidden ${className}`}>
      <div className="relative h-48 animate-pulse bg-muted" />
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="h-5 w-24 animate-pulse rounded bg-muted" />
            <div className="h-5 w-16 animate-pulse rounded bg-muted" />
          </div>

          <div className="space-y-1">
            <div className="flex items-center justify-between text-xs">
              <div className="h-3 w-20 animate-pulse rounded bg-muted" />
              <div className="h-3 w-8 animate-pulse rounded bg-muted" />
            </div>
            <div className="h-1.5 w-full animate-pulse rounded-full bg-muted" />
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div className="flex items-center justify-between">
              <div className="h-3 w-12 animate-pulse rounded bg-muted" />
              <div className="h-3 w-6 animate-pulse rounded bg-muted" />
            </div>
            <div className="flex items-center justify-between">
              <div className="h-3 w-12 animate-pulse rounded bg-muted" />
              <div className="h-3 w-6 animate-pulse rounded bg-muted" />
            </div>
          </div>

          <div className="mt-2 flex items-center border-t pt-2">
            <div className="h-3 w-20 animate-pulse rounded bg-muted" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function AnalyticsCardSkeleton({ className = "" }: LoadingSkeletonProps) {
  return (
    <Card className={className}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="h-4 w-24 animate-pulse rounded bg-muted" />
            <div className="mt-2 flex items-center space-x-2">
              <div className="h-8 w-16 animate-pulse rounded bg-muted" />
              <div className="h-6 w-20 animate-pulse rounded bg-muted" />
            </div>
          </div>
          <div className="size-12 animate-pulse rounded-full bg-muted" />
        </div>
      </CardContent>
    </Card>
  );
}

export function UnitListSkeleton({ className = "" }: LoadingSkeletonProps) {
  return (
    <div className={`space-y-2 ${className}`}>
      {Array.from({ length: 6 }).map((_, index) => (
        <div key={index} className="rounded-lg border p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Skeleton className="size-3 rounded-full" />
              <div className="space-y-1">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-3 w-24" />
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-16" />
              <Skeleton className="size-4" />
            </div>
          </div>

          <div className="mt-2">
            <Skeleton className="h-3 w-20" />
          </div>
        </div>
      ))}
    </div>
  );
}

export function StatsCardSkeleton({ className = "" }: LoadingSkeletonProps) {
  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="h-4 w-20 animate-pulse rounded bg-muted" />
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid grid-cols-3 gap-2 text-center">
          <div className="space-y-1">
            <div className="mx-auto h-6 w-8 animate-pulse rounded bg-muted" />
            <div className="mx-auto h-3 w-12 animate-pulse rounded bg-muted" />
          </div>
          <div className="space-y-1">
            <div className="mx-auto h-6 w-8 animate-pulse rounded bg-muted" />
            <div className="mx-auto h-3 w-12 animate-pulse rounded bg-muted" />
          </div>
          <div className="space-y-1">
            <div className="mx-auto h-6 w-8 animate-pulse rounded bg-muted" />
            <div className="mx-auto h-3 w-12 animate-pulse rounded bg-muted" />
          </div>
        </div>
        <div className="h-2 w-full animate-pulse rounded-full bg-muted" />
      </CardContent>
    </Card>
  );
}
