import { ROLE_ENDPOINTS } from "@/constants/endpoints";

import { privateApi } from "../api_helper";
import { ResponseAxiosDetail, ResponseList, SuccessResponse } from "./types/common";
import { CreateRolePayload, Role, UpdateRolePayload } from "./types/role";

export const roleApi = {
  // Get list of roles
  list: async (params?: Record<string, unknown>): Promise<ResponseList<Role>> => {
    const response = await privateApi.get<ResponseList<Role>>(ROLE_ENDPOINTS.LIST, { params });
    return response;
  },

  // Get role by ID
  getById: async (id: string): Promise<ResponseAxiosDetail<Role>> => {
    const response = await privateApi.get<ResponseAxiosDetail<Role>>(
      `${ROLE_ENDPOINTS.LIST}/${id}`
    );
    return response;
  },

  // Create new role
  create: async (data: CreateRolePayload): Promise<ResponseAxiosDetail<Role>> => {
    const response = await privateApi.post<ResponseAxiosDetail<Role>>(ROLE_ENDPOINTS.CREATE, data);
    return response;
  },

  // Update role
  update: async (id: string, data: UpdateRolePayload): Promise<ResponseAxiosDetail<Role>> => {
    const url = ROLE_ENDPOINTS.UPDATE.replace(":id", id);
    const response = await privateApi.put<ResponseAxiosDetail<Role>>(url, data);
    return response;
  },

  // Delete role
  delete: async (id: string): Promise<SuccessResponse> => {
    const url = ROLE_ENDPOINTS.DELETE.replace(":id", id);
    const response = await privateApi.delete<SuccessResponse>(url);
    return response;
  },
};
