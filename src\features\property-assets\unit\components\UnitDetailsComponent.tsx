"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  AlertCircle,
  Bath,
  Bed,
  Building2,
  Calendar,
  CheckCircle,
  DollarSign,
  Edit,
  Home,
  ImageIcon,
  Layers,
  MapPin,
  Ruler,
  Settings,
  Trash2,
  User,
  XCircle,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
// UI Components
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { authProtectedPaths } from "@/constants/paths";

// Property Assets imports
import { useDeleteUnit, useUnit } from "../../hooks/useUnits";
import type { Unit } from "../../types";

interface UnitDetailsComponentProps {
  unitId: string;
}

export function UnitDetailsComponent({ unitId }: UnitDetailsComponentProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Data fetching
  const { data: unit, isLoading: unitLoading, error: unitError } = useUnit(unitId);

  // Mutations
  const deleteUnitMutation = useDeleteUnit();

  const handleEdit = () => {
    router.push(`${authProtectedPaths.UNITS}/${unitId}/edit` as any);
  };

  const handleDelete = async () => {
    try {
      await deleteUnitMutation.mutateAsync(unitId);
      toast.success(t("pages.units.deleteSuccess"));
      router.push(authProtectedPaths.UNITS as any);
    } catch (error) {
      toast.error(t("pages.units.deleteError"));
    }
  };

  // Loading state
  if (unitLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <div className="mx-auto size-8 animate-spin rounded-full border-b-2 border-primary"></div>
          <p className="mt-2 text-sm text-muted-foreground">{t("common.loading")}</p>
        </div>
      </div>
    );
  }

  // Error state
  if (unitError || !unit) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <AlertCircle className="mx-auto mb-4 size-12 text-destructive" />
          <p className="text-sm text-destructive">{t("pages.units.loadError")}</p>
          <Button
            variant="outline"
            onClick={() => router.push(authProtectedPaths.UNITS as any)}
            className="mt-4">
            {t("common.goBack")}
          </Button>
        </div>
      </div>
    );
  }

  const getUnitTypeLabel = (type: Unit["unit_type"]) => {
    const types = {
      studio: t("pages.units.types.studio"),
      "1br": t("pages.units.types.1br"),
      "2br": t("pages.units.types.2br"),
      "3br": t("pages.units.types.3br"),
      commercial: t("pages.units.types.commercial"),
      office: t("pages.units.types.office"),
      retail: t("pages.units.types.retail"),
    };
    return types[type] || type;
  };

  const getStatusBadge = (status: Unit["status"]) => {
    const variants = {
      available: "secondary" as const,
      occupied: "default" as const,
      maintenance: "destructive" as const,
      inactive: "outline" as const,
    };

    const labels = {
      available: t("common.status.available"),
      occupied: t("common.status.occupied"),
      maintenance: t("common.status.maintenance"),
      inactive: t("common.status.inactive"),
    };

    const icons = {
      available: <CheckCircle className="mr-1 size-3" />,
      occupied: <User className="mr-1 size-3" />,
      maintenance: <Settings className="mr-1 size-3" />,
      inactive: <XCircle className="mr-1 size-3" />,
    };

    return (
      <Badge variant={variants[status]} className="flex items-center">
        {icons[status]}
        {labels[status]}
      </Badge>
    );
  };

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="flex items-start justify-between">
        <div className="space-y-1">
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold text-foreground">
              {t("pages.units.unitNumber")} {unit.unit_number}
            </h1>
            {getStatusBadge(unit.status)}
          </div>
          {unit.property && (
            <div className="flex items-center text-muted-foreground">
              <Building2 className="mr-2 size-4" />
              <span className="text-sm">{unit.property.name}</span>
              <span className="mx-2">•</span>
              <MapPin className="mr-1 size-4" />
              <span className="text-sm">
                {unit.property.address.street}, {unit.property.address.city}
              </span>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleEdit}>
            <Edit className="mr-2 size-4" />
            {t("common.edit")}
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowDeleteDialog(true)}
            className="text-destructive hover:text-destructive">
            <Trash2 className="mr-2 size-4" />
            {t("common.delete")}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
        {/* Main Content */}
        <div className="space-y-8 lg:col-span-2">
          {/* Unit Specifications */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Home className="size-5" />
                {t("pages.units.specifications")}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-6 md:grid-cols-3">
                <div className="rounded-lg bg-muted/50 p-4 text-center">
                  <div className="text-2xl font-bold text-foreground">
                    {getUnitTypeLabel(unit.unit_type)}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {t("pages.units.headers.type")}
                  </div>
                </div>

                <div className="rounded-lg bg-muted/50 p-4 text-center">
                  <div className="mb-1 flex items-center justify-center">
                    <Ruler className="mr-1 size-4" />
                    <span className="text-2xl font-bold text-foreground">
                      {unit.square_footage}
                    </span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {t("pages.units.headers.squareFootage")}
                  </div>
                </div>

                <div className="rounded-lg bg-muted/50 p-4 text-center">
                  <div className="mb-1 flex items-center justify-center">
                    <Layers className="mr-1 size-4" />
                    <span className="text-2xl font-bold text-foreground">{unit.floor}</span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {t("pages.units.headers.floor")}
                  </div>
                </div>
              </div>

              {(unit.bedrooms || unit.bathrooms) && (
                <div className="grid grid-cols-2 gap-6">
                  {unit.bedrooms && (
                    <div className="flex items-center justify-center rounded-lg bg-muted/50 p-4">
                      <Bed className="mr-2 size-5 text-primary" />
                      <div>
                        <div className="text-lg font-semibold text-foreground">{unit.bedrooms}</div>
                        <div className="text-sm text-muted-foreground">
                          {t("pages.units.headers.bedrooms")}
                        </div>
                      </div>
                    </div>
                  )}

                  {unit.bathrooms && (
                    <div className="flex items-center justify-center rounded-lg bg-muted/50 p-4">
                      <Bath className="mr-2 size-5 text-primary" />
                      <div>
                        <div className="text-lg font-semibold text-foreground">
                          {unit.bathrooms}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {t("pages.units.headers.bathrooms")}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {unit.description && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("pages.units.headers.description")}
                  </label>
                  <p className="mt-1 text-foreground">{unit.description}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Financial Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="size-5" />
                {t("pages.units.financialInformation")}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div className="rounded-lg bg-muted/50 p-6 text-center">
                  <div className="mb-2 text-3xl font-bold text-foreground">
                    ${unit.rent_amount.toLocaleString()}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {t("pages.units.headers.rentAmount")} / {t("common.month")}
                  </div>
                </div>

                <div className="rounded-lg bg-muted/50 p-6 text-center">
                  <div className="mb-2 text-3xl font-bold text-foreground">
                    ${unit.deposit_amount.toLocaleString()}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {t("pages.units.headers.depositAmount")}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Amenities */}
          {unit.amenities && unit.amenities.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="size-5" />
                  {t("pages.units.amenities")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-2 md:grid-cols-3">
                  {unit.amenities.map((amenity, index) => (
                    <div key={index} className="flex items-center rounded bg-muted/50 p-2">
                      <CheckCircle className="mr-2 size-4 text-success" />
                      <span className="text-sm text-foreground">{amenity}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-8">
          {/* Unit Images */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ImageIcon className="size-5" />
                {t("pages.units.images")}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {unit.images && unit.images.length > 0 ? (
                <div className="grid grid-cols-1 gap-3">
                  {unit.images.map((image) => (
                    <div key={image.id} className="relative">
                      <img
                        src={image.url}
                        alt={image.name}
                        className="h-32 w-full rounded-lg border border-border object-cover"
                      />
                      {image.is_primary && (
                        <Badge className="absolute left-2 top-2 text-xs">
                          {t("common.primary")}
                        </Badge>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="py-8 text-center text-muted-foreground">
                  <ImageIcon className="mx-auto mb-2 size-12 opacity-50" />
                  <p className="text-sm">{t("pages.units.noImages")}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Property Information */}
          {unit.property && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="size-5" />
                  {t("pages.properties.propertyInfo")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("pages.properties.headers.name")}
                  </label>
                  <p className="text-foreground">{unit.property.name}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("pages.properties.headers.type")}
                  </label>
                  <p className="capitalize text-foreground">{unit.property.property_type}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("pages.properties.addressInformation")}
                  </label>
                  <p className="text-sm text-foreground">
                    {unit.property.address.street}
                    <br />
                    {unit.property.address.city}, {unit.property.address.state}{" "}
                    {unit.property.address.zip_code}
                  </p>
                </div>

                <Separator />

                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() =>
                    router.push(`${authProtectedPaths.PROPERTIES}/${unit.property_id}` as any)
                  }>
                  <Building2 className="mr-2 size-4" />
                  {t("pages.properties.viewProperty")}
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>{t("common.quickActions")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => router.push(`${authProtectedPaths.TENANTS}?unit=${unitId}` as any)}
                disabled={unit.status !== "occupied"}>
                <User className="mr-2 size-4" />
                {unit.status === "occupied"
                  ? t("pages.tenants.viewTenant")
                  : t("pages.tenants.noTenant")}
              </Button>

              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() =>
                  router.push(`${authProtectedPaths.CONTRACTS}?unit=${unitId}` as any)
                }>
                <Calendar className="mr-2 size-4" />
                {t("pages.contracts.viewContracts")}
              </Button>

              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() =>
                  router.push(`${authProtectedPaths.MAINTENANCE}?unit=${unitId}` as any)
                }>
                <Settings className="mr-2 size-4" />
                {t("pages.maintenance.viewRequests")}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("pages.units.deleteUnit")}</DialogTitle>
            <DialogDescription>
              {t("pages.units.deleteConfirmation", { unit: unit.unit_number })}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              {t("common.cancel")}
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={deleteUnitMutation.isPending}>
              {deleteUnitMutation.isPending ? t("common.deleting") : t("common.delete")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
