"use client";

import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";

import { AddOrder } from "@/features/orders/components/add_order";
import { Note } from "@/features/orders/components/note";
import { Payment } from "@/features/orders/components/payment";
import { OrderItem } from "@/features/orders/hooks/types";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Location } from "@/lib/apis/location";

interface AddOrderDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  customer?: any;
  opportunityId?: string;
  onFieldChange?: (field: string, value: any, shouldDirty?: boolean) => void;
  onBranchSelect?: (branch: Location) => void;
  onOrderCreated?: (order: any) => void;
  onOrderAdjusted?: (order: any) => void;
  initialOrder?: any;
  isEditing?: boolean;
}

export function AddOrderDialog({
  open,
  onOpenChange,
  customer,
  opportunityId,
  onFieldChange,
  onBranchSelect,
  onOrderCreated,
  onOrderAdjusted,
  initialOrder,
  isEditing,
}: AddOrderDialogProps) {
  const { t } = useTranslation();
  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);
  const [orderTotal, setOrderTotal] = useState(0);
  const [orderItemsCount, setOrderItemsCount] = useState(0);
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null);
  const [note, setNote] = useState("");
  const [tags, setTags] = useState<string[]>([]);
  const [initialOrderItemsForAddOrder, setInitialOrderItemsForAddOrder] = useState<OrderItem[]>([]);
  const initializedRef = useRef(false);
  const isInitializingRef = useRef(false);
  const isSettingInitialDataRef = useRef(false);
  const originalOrderItemsRef = useRef<OrderItem[]>([]);

  // Initialize form with existing order data when editing - only once when dialog opens
  useEffect(() => {
    if (open && !initializedRef.current) {
      initializedRef.current = true;
      isInitializingRef.current = true;
      isSettingInitialDataRef.current = true;

      if (isEditing && initialOrder) {
        // Pre-fill form with existing order data for editing
        if (initialOrder.order_line_items) {
          // Convert order line items back to OrderItem format for the form
          const items: OrderItem[] = initialOrder.order_line_items.map((item: any) => {
            // Special handling for custom items
            if (item.custom) {
              const customItem: OrderItem = {
                id: item.id || "",
                variant_id: item.variant_id || "",
                name: item.name || "",
                sku: item.sku || "",
                image: item.image_url || "",
                price: item.unit_price || item.sale_price || 0,
                sale_price: item.sale_price || item.unit_price || 0,
                unit_price: item.unit_price || item.sale_price || 0,
                quantity: item.quantity || 0,
                total: (item.unit_price || item.sale_price || 0) * (item.quantity || 0),
                note: item.note || "",
                custom: true,
                // For custom items, create a minimal variant object
                variant: {
                  id: item.variant_id || "",
                  name: item.name || "",
                  sku: item.sku || "",
                  price: item.unit_price || item.sale_price || 0,
                  unit_price: item.unit_price || item.sale_price || 0,
                  sale_price: item.sale_price || item.unit_price || 0,
                  custom: true,
                  // Custom items typically don't have inventories
                  inventories: [],
                } as any,
              };

              return customItem;
            }

            // Regular item mapping
            const mappedItem: OrderItem = {
              id: item.id || "",
              variant_id: item.variant_id || "",
              name: item.name || "",
              sku: item.sku || "",
              image: item.image_url || "",
              price: item.unit_price || item.sale_price || 0,
              sale_price: item.sale_price || item.unit_price || 0,
              unit_price: item.unit_price || item.sale_price || 0,
              quantity: item.quantity || 0,
              total: (item.unit_price || item.sale_price || 0) * (item.quantity || 0),
              note: item.note || "",
              custom: false,
              // Map to OrderVariant interface
              variant: {
                id: item.variant_id || "",
                name: item.variant_name || "",
                sku: item.sku || "",
                price: item.unit_price || item.sale_price || 0,
                unit_price: item.unit_price || item.sale_price || 0,
                sale_price: item.sale_price || item.unit_price || 0,
                custom: false,
                // Preserve inventory information if available
                inventories: item.inventories || item.variant?.inventories || [],
                // Preserve any other variant properties
                ...(item.variant || {}),
              } as any,
            };

            return mappedItem;
          });

          // Set order items directly without triggering the update function
          setOrderItems(items);
          setInitialOrderItemsForAddOrder(items);
          originalOrderItemsRef.current = items;

          // Calculate totals
          const total = items.reduce((sum, item) => {
            const quantity =
              typeof item.quantity === "string" ? parseInt(item.quantity, 10) : item.quantity || 0;
            const price =
              typeof item.sale_price === "string"
                ? parseFloat(item.sale_price)
                : item.sale_price || 0;
            return sum + quantity * price;
          }, 0);
          setOrderTotal(total);

          const count = items.reduce((sum, item) => {
            const quantity =
              typeof item.quantity === "string" ? parseInt(item.quantity, 10) : item.quantity || 0;
            return sum + quantity;
          }, 0);
          setOrderItemsCount(count);
        }

        // Set location if available
        if (initialOrder.location) {
          setSelectedLocation({
            id: initialOrder.location.id,
            name: initialOrder.location.name,
          } as Location);
        }

        // Set note and tags
        if (initialOrder.note) {
          setNote(initialOrder.note);
        }
        if (initialOrder.tags) {
          setTags(
            typeof initialOrder.tags === "string"
              ? initialOrder.tags.split(",").filter((tag: string) => tag.trim())
              : initialOrder.tags
          );
        }
      } else {
        // Reset form for new order
        setOrderItems([]);
        setInitialOrderItemsForAddOrder([]);
        setOrderTotal(0);
        setOrderItemsCount(0);
        setSelectedLocation(null);
        setNote("");
        setTags([]);
        originalOrderItemsRef.current = [];
      }

      // Mark initialization as complete
      setTimeout(() => {
        isInitializingRef.current = false;
        isSettingInitialDataRef.current = false;
      }, 200);
    }

    // Reset initialization flag when dialog closes
    if (!open) {
      initializedRef.current = false;
      isInitializingRef.current = false;
      isSettingInitialDataRef.current = false;
    }
  }, [open, isEditing, initialOrder]);

  // Update order items when they change in the AddOrder component
  const updateOrderLineItems = (items: OrderItem[]) => {
    if (!isInitializingRef.current && items.length !== orderItems.length) {
      setOrderItems(items);

      // Only update item count, don't call onFieldChange yet
      const count = items.reduce((sum, item) => {
        const quantity =
          typeof item.quantity === "string" ? parseInt(item.quantity, 10) : item.quantity || 0;
        return sum + quantity;
      }, 0);
      setOrderItemsCount(count);
    } else if (!isInitializingRef.current) {
      // Check if individual items changed
      const hasChanges = items.some((item, index) => {
        const existingItem = orderItems[index];
        if (!existingItem) return true;

        // Special handling for custom items
        if (item.custom || existingItem.custom) {
          return (
            item.name !== existingItem.name ||
            item.quantity !== existingItem.quantity ||
            item.price !== existingItem.price ||
            item.sale_price !== existingItem.sale_price ||
            item.note !== existingItem.note ||
            item.sku !== existingItem.sku
          );
        }

        // For regular items, check standard properties
        return (
          item.quantity !== existingItem.quantity ||
          item.price !== existingItem.price ||
          item.sale_price !== existingItem.sale_price ||
          item.note !== existingItem.note
        );
      });

      if (hasChanges) {
        setOrderItems(items);

        // Update item count
        const count = items.reduce((sum, item) => {
          const quantity =
            typeof item.quantity === "string" ? parseInt(item.quantity, 10) : item.quantity || 0;
          return sum + quantity;
        }, 0);
        setOrderItemsCount(count);
      }
    }
  };

  // Custom handler for order items that prevents updates during initialization
  const handleOrderItemsChange = (items: OrderItem[]) => {
    // if (isInitializingRef.current || isSettingInitialDataRef.current) {
    //   return;
    // }

    // Additional safeguard for custom items - prevent them from being overridden
    const hasCustomItems = items.some((item) => item.custom);
    if (hasCustomItems) {
      // Check if this is a valid update (not empty data from initialization)
      const isValidUpdate = items.every((item) => {
        if (item.custom) {
          return item.name && item.name.trim() !== "" && item.quantity > 0;
        }
        return true;
      });

      if (!isValidUpdate) {
        return;
      }

      // For custom items, ensure we preserve their properties
      const updatedItems = items.map((item, index) => {
        if (item.custom) {
          const originalItem = originalOrderItemsRef.current[index];
          const existingItem = orderItems[index];
          const referenceItem = originalItem || existingItem;

          if (referenceItem && referenceItem.custom) {
            // Preserve original custom item properties
            return {
              ...item,
              name: item.name || referenceItem.name || "",
              quantity: item.quantity || referenceItem.quantity || 0,
              price: item.price || referenceItem.price || 0,
              sale_price: item.sale_price || referenceItem.sale_price || 0,
              note: item.note || referenceItem.note || "",
            };
          }
        }
        return item;
      });

      updateOrderLineItems(updatedItems);
    } else {
      updateOrderLineItems(items);
    }
  };

  // Custom handler for total changes that prevents updates during initialization
  const handleTotalChange = (total: number) => {
    setOrderTotal(total);
  };

  // Handler for branch selection
  const handleBranchSelect = (branch: Location) => {
    setSelectedLocation(branch);
    if (onBranchSelect) {
      onBranchSelect(branch);
    }
  };

  // Handler for note changes
  const handleNoteChange = (note: string) => {
    setNote(note);
  };

  // Handler for tags changes
  const handleTagsChange = (tags: string[]) => {
    setTags(tags);
  };

  const handleClose = () => {
    // Reset state when closing
    setOrderItems([]);
    setInitialOrderItemsForAddOrder([]);
    setOrderTotal(0);
    setOrderItemsCount(0);
    setSelectedLocation(null);
    setNote("");
    setTags([]);
    initializedRef.current = false;
    isInitializingRef.current = false;
    isSettingInitialDataRef.current = false;
    originalOrderItemsRef.current = [];
    onOpenChange(false);
  };

  const handleSave = () => {
    // Only call onFieldChange when actually saving and there's data
    if (onFieldChange && orderItems.length > 0) {
      const orderLineItems = orderItems.map((item) => {
        // Create order line item with only necessary fields
        const orderLineItem: any = {
          unit_price: item.price,
          sale_price: item.sale_price,
          sku: item.sku,
          name: item.name,
          variant_name: item.variant?.name || "",
          image_url: item.image || null,
          variant_id: item.variant_id,
          category: (item.variant as any)?.category || null,
          brand: (item.variant as any)?.brand || null,
          quantity: item.quantity,
          note: item.note || "",
          custom: item.custom || false,
          // Preserve variant information for editing
          variant: {
            id: item.variant_id,
            name: item.variant?.name || "",
            category: (item.variant as any)?.category || null,
            brand: (item.variant as any)?.brand || null,
            product_id: (item.variant as any)?.product_id || null,
            sku: item.sku,
            price: item.price,
            sale_price: item.sale_price,
            // Preserve inventory information if available
            inventories: (item.variant as any)?.inventories || [],
          },
        };

        // Only add product_id if it's not a custom item
        if (!item.custom && (item.variant as any)?.product_id) {
          orderLineItem.product_id = (item.variant as any).product_id;
        }

        // Only add id if it exists and is not empty
        if (item.id && item.id.trim() !== "") {
          orderLineItem.id = item.id;
        }

        return orderLineItem;
      });

      // Create clean order data with only necessary fields
      const orderData = {
        order_line_items: orderLineItems,
        total: orderTotal,
        sub_total: orderTotal,
        location: selectedLocation
          ? {
              id: selectedLocation.id,
              name: selectedLocation.name,
            }
          : null,
        customer: customer,
        note: note,
        tags: tags.join(","),
      };

      // Update the opportunity with the order data
      onFieldChange("order", orderData, true);

      // Notify parent component based on whether we're creating or adjusting
      if (isEditing && onOrderAdjusted) {
        onOrderAdjusted(orderData);
      } else if (onOrderCreated) {
        onOrderCreated(orderData);
      }
    }

    // Reset state and close
    setOrderItems([]);
    setInitialOrderItemsForAddOrder([]);
    setOrderTotal(0);
    setOrderItemsCount(0);
    setSelectedLocation(null);
    setNote("");
    setTags([]);
    initializedRef.current = false;
    isInitializingRef.current = false;
    isSettingInitialDataRef.current = false;
    originalOrderItemsRef.current = [];
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="h-[700px] max-w-5xl gap-0 p-0">
        <DialogHeader className="px-6 pb-4 pt-6">
          <DialogTitle>{t("pages.orders.addOrder")}</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 overflow-y-auto px-6 pb-6 pt-0">
          {/* Order Items Section */}
          <Card>
            <CardContent className="p-4">
              <AddOrder
                selectedLocationId={selectedLocation?.id}
                onTotalChange={handleTotalChange}
                onOrderItemsChange={handleOrderItemsChange}
                disabled={false}
                readOnly={false}
                showAddServiceButton={true}
                isOpportunityView={true}
                onBranchSelect={handleBranchSelect}
                initialSelectedBranch={selectedLocation}
                initialOrderItems={initialOrderItemsForAddOrder}
              />
            </CardContent>
          </Card>

          {/* Payment Info */}
          <div className="grid grid-cols-2 gap-4">
            {/* Notes and Tags */}
            <Card>
              <CardContent className="grid grid-cols-1 p-4">
                <Note
                  initialNote={note}
                  initialTags={tags}
                  onNoteChange={handleNoteChange}
                  onTagsChange={handleTagsChange}
                  disabled={false}
                  readOnly={false}
                />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <Payment
                  subtotal={orderTotal}
                  totalItems={orderItemsCount}
                  disabled={false}
                  readOnly={false}
                />
              </CardContent>
            </Card>
          </div>
        </div>
        <DialogFooter className="flex justify-end gap-2 border-t p-4">
          <Button variant="outline" onClick={handleClose}>
            {t("common.cancel")}
          </Button>
          <Button onClick={handleSave} disabled={orderItems.length === 0}>
            {t("common.save")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
