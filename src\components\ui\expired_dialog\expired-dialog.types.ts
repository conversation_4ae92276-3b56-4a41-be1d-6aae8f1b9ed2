export interface ExpiredDialogProps {
  /** Controls whether the dialog is open */
  open: boolean;
  /** Callback when dialog open state changes */
  onOpenChange: (open: boolean) => void;
  /** Current subscription plan name */
  currentPlan?: string;
  /** Callback when upgrade button is clicked */
  onUpgrade?: () => void;
  /** Callback when logout button is clicked */
  onLogout?: () => void;
  /** Callback when dialog is closed programmatically */
  onClose?: () => void;
}

export interface UseExpiredDialogProps {
  currentPlan?: string;
  onUpgrade?: () => void;
  onLogout?: () => void;
  onClose?: () => void;
}

export interface UseExpiredDialogReturn {
  isOpen: boolean;
  openDialog: () => void;
  closeDialog: () => void;
  dialogProps: ExpiredDialogProps;
}
