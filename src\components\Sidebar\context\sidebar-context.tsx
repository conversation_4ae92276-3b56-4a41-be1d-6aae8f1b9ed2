"use client";

import { createContext, ReactNode, useContext, useEffect, useState } from "react";
import { usePathname } from "next/navigation";

import { settingsPaths } from "@/constants/paths";

interface SidebarContextType {
  isSettingsSidebarOpen: boolean;
  previousPage: string;
  openSettingsSidebar: (fromPage?: string) => void;
  closeSettingsSidebar: () => void;
  currentSidebar: "main" | "settings";
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

export function useSidebarContext() {
  const context = useContext(SidebarContext);
  if (context === undefined) {
    throw new Error("useSidebarContext must be used within a SidebarProvider");
  }
  return context;
}

interface SidebarProviderProps {
  children: ReactNode;
}

export function SidebarProvider({ children }: SidebarProviderProps) {
  const pathname = usePathname();
  const [isSettingsSidebarOpen, setIsSettingsSidebarOpen] = useState(false);
  const [previousPage, setPreviousPage] = useState("/dashboard");

  // Check if current path is a settings path
  const isSettingsPath = Object.values(settingsPaths).some((path) => {
    const routePattern = path.split(":")[0].replace(/\/$/, "");
    return pathname.startsWith(routePattern);
  });

  // Automatically open settings sidebar when on settings pages
  useEffect(() => {
    if (isSettingsPath && !isSettingsSidebarOpen) {
      setIsSettingsSidebarOpen(true);
    } else if (!isSettingsPath && isSettingsSidebarOpen) {
      setIsSettingsSidebarOpen(false);
    }
  }, [pathname, isSettingsPath, isSettingsSidebarOpen]);

  const openSettingsSidebar = (fromPage?: string) => {
    if (fromPage) {
      setPreviousPage(fromPage);
    }
    setIsSettingsSidebarOpen(true);
  };

  const closeSettingsSidebar = () => {
    setIsSettingsSidebarOpen(false);
  };

  const currentSidebar: "main" | "settings" = isSettingsSidebarOpen ? "settings" : "main";

  const value: SidebarContextType = {
    isSettingsSidebarOpen,
    previousPage,
    openSettingsSidebar,
    closeSettingsSidebar,
    currentSidebar,
  };

  return <SidebarContext.Provider value={value}>{children}</SidebarContext.Provider>;
}
