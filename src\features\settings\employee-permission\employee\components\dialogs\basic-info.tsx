"use client";

import { useTranslation } from "react-i18next";

import { DatePicker } from "@/components/ui/date-picker";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { cleanPhoneNumber, formatPhoneNumber } from "@/utils/helpers/number-formatter";

// Basic Information Component
export function BasicInfo({ mode = "add" }: { mode?: "add" | "edit" | "duplicate" }) {
  const { t } = useTranslation();

  return (
    <div className="space-y-2">
      {/* Employee Name and Username */}
      <div className="grid grid-cols-2 gap-x-4 gap-y-2">
        <FormField
          name="employeeName"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>{t("pages.settings.employeeName", "Employee name")}</FormLabel>
              <FormControl>
                <Input
                  placeholder={t("pages.settings.employeeNamePlaceholder", "<PERSON><PERSON>g. <PERSON>")}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>{t("pages.settings.username", "Username")}</FormLabel>
              <FormControl>
                <Input
                  placeholder={t("pages.settings.usernamePlaceholder", "johnroger123")}
                  disabled={mode === "edit"}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Email and Phone */}
      <div className="grid grid-cols-2 gap-x-4 gap-y-2">
        <FormField
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>{t("pages.settings.email", "Email")}</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder={t("pages.settings.emailPlaceholder", "<EMAIL>")}
                  disabled={mode === "edit"}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          name="phone_number"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("pages.settings.phone", "Phone")}</FormLabel>
              <FormControl>
                <Input
                  type="tel"
                  placeholder={t("pages.settings.phonePlaceholder", "0123456789")}
                  value={field.value ? formatPhoneNumber(field.value) : ""}
                  onChange={(e) => {
                    // Clean and limit to 10 digits
                    const cleanValue = cleanPhoneNumber(e.target.value);
                    field.onChange(cleanValue);
                  }}
                  onBlur={(e) => {
                    // Format on blur for better UX
                    if (field.value) {
                      e.target.value = formatPhoneNumber(field.value);
                    }
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Birthday and Address */}
      <div className="grid grid-cols-2 gap-4">
        <FormField
          name="birthdate"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("pages.settings.birthday")}</FormLabel>
              <FormControl>
                <DatePicker
                  value={field.value}
                  onChange={field.onChange}
                  placeholder={t("pages.settings.birthdayPlaceholder")}
                  allowFutureYears={false}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          name="address"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("pages.settings.address")}</FormLabel>
              <FormControl>
                <Input
                  placeholder={t("pages.settings.addressPlaceholder", "123 Main Street")}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
