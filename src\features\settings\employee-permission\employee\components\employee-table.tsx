"use client";

import { useCallback, useMemo, useState } from "react";
import { Plus } from "lucide-react";
import { useTranslation } from "react-i18next";

import {
  AddEmployeeDialog,
  columns,
} from "@/features/settings/employee-permission/employee/components";

import { TableContainer } from "@/components/custom-table/container/table-container";
import GroupButton, { GroupButtonProps } from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { FilterTableProps, FilterType } from "@/components/data-table/types";
import { usePermission } from "@/components/provider/permission-provider";

import { useBranches } from "../../hook/use-branch";
import { useEmployees } from "../hooks/use-employee";
import { Employee } from "../types/employee";

export default function EmployeeTable() {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch } = useDatatable();
  const { hasPermission } = usePermission();
  const hasListEmployee = hasPermission("LIST_EMPLOYEE");
  const hasCreateEmployee = hasPermission("CREATE_EMPLOYEE");

  const options = useMemo(
    () => ({
      limit: Number(getInitialParams.limit),
      enabled: hasListEmployee,
      ...getInitialParams,
    }),
    [getInitialParams, hasListEmployee]
  );

  const {
    employees,
    total,
    isLoading,
    isFetching,
    deleteEmployeeMutation,
    toggleEmployeeStatusMutation,
  } = useEmployees({
    ...options,
    onCreateSuccess: () => {
      handleCloseDialog();
    },
    onDeleteSuccess: () => {
      // Delete dialog will be closed by the mutation itself
    },
    onToggleStatusSuccess: () => {
      handleCloseDialog();
      // Toggle status dialog will be closed by the mutation itself
    },
  });

  const [showAddEmployee, setShowAddEmployee] = useState(false);
  const [selectedBranch, setSelectedBranch] = useState<string>("all");
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
  const [dialogMode, setDialogMode] = useState<"add" | "edit" | "duplicate">("add");

  const isTableLoading = isLoading || isFetching;

  // Handle branch tab change and update search params
  const handleBranchTabChange = useCallback(
    (branchId: string) => {
      setSelectedBranch(branchId);

      // Update search params with branch_id
      if (branchId === "all") {
        // Remove branch_id from params when "all" is selected
        const { "branch_list.id": branch_id, ...restParams } = getInitialParams;
        handleParamSearch(restParams);
      } else {
        // Add branch_id to params
        handleParamSearch({
          ...getInitialParams,
          ["branch_list.id"]: branchId,
        });
      }
    },
    [getInitialParams, handleParamSearch]
  );

  const tableColumns = useMemo(
    () =>
      columns(t, {
        deleteEmployeeMutation,
        toggleEmployeeStatusMutation,
      }),
    [t, deleteEmployeeMutation, toggleEmployeeStatusMutation]
  );

  const statusOptions = useMemo(
    () => [
      { label: t("common.status.enabled"), value: "ENABLED" },
      { label: t("common.status.disabled"), value: "DISABLED" },
    ],
    [t]
  );

  const confirmationStatusOptions = useMemo(
    () => [
      { label: t("common.status.confirmed"), value: "CONFIRMED" },
      { label: t("common.status.unconfirmed"), value: "UNCONFIRMED" },
      { label: t("common.status.unknown"), value: "UNKNOWN" },
      { label: t("common.status.resetRequired"), value: "RESET_REQUIRED" },
      { label: t("common.status.externalProvider"), value: "EXTERNAL_PROVIDER" },
      { label: t("common.status.forceChangePassword"), value: "FORCE_CHANGE_PASSWORD" },
    ],
    [t]
  );

  // Fetch branches data for custom saved filters
  const { data: branchesData, isLoading: isLoadingBranches } = useBranches({ limit: 100 });

  // Create custom saved filters data from branches
  const customSavedFiltersData = useMemo(() => {
    if (!branchesData?.items) return [];

    return [
      { id: "all", name: t("common.all"), icon: "Globe" },
      ...branchesData.items.map((branch: any) => ({
        id: branch.id,
        name: branch.name,
        icon: "MapPin",
        customIcon: branch.images?.[0]?.url,
      })),
    ];
  }, [branchesData?.items, t]);

  const filterConfig: FilterTableProps = useMemo(() => {
    return {
      showSearch: true,
      filterType: "employees_permissions",
      searchPlaceHolder: t("common.searchPlaceholder"),
      initialValues: getInitialParams,
      listFilter: [
        {
          id: "role_list.id",
          type: "selectBox",
          title: t("pages.staff.role"),
          defaultValue: getInitialParams["role_list.id"],
          remote: true,
          pathUrlLoad: "user/v2/role",
        },
        {
          id: "status",
          type: "selectBox",
          title: t("common.status.status"),
          defaultValue: getInitialParams["status"],
          dataOption: statusOptions,
        },
        {
          id: "confirmation_status",
          type: "selectBox",
          title: t("common.confirmationStatus"),
          defaultValue: getInitialParams["confirmation_status"],
          dataOption: confirmationStatusOptions,
        },
        {
          id: "created_at",
          type: "date",
          title: t("pages.products.filters.createdAt"),
          defaultValue: {
            from: getInitialParams["created_at_from"] as string,
            to: getInitialParams["created_at_to"] as string,
          },
        },
        {
          id: "updated_at",
          type: "date",
          title: t("pages.products.filters.updatedAt"),
          defaultValue: {
            from: getInitialParams["updated_at_from"] as string,
            to: getInitialParams["updated_at_to"] as string,
          },
        },
      ] as unknown as FilterType[],
      handleParamSearch,
      listLoading: isTableLoading,
    };
  }, [handleParamSearch, isTableLoading, t, getInitialParams, statusOptions]);

  const handleAddEmployee = () => {
    setSelectedEmployee(null);
    setDialogMode("add");
    setShowAddEmployee(true);
  };

  const handleCloseDialog = () => {
    setShowAddEmployee(false);
    setSelectedEmployee(null);
    setDialogMode("add");
  };

  const groupButtonConfig: GroupButtonProps = {
    buttons: [
      {
        type: "button",
        title: t("common.add"),
        icon: Plus,
        onClick: handleAddEmployee,
        permission: "CREATE_EMPLOYEE",
      },
    ],
    showRefresh: false,
  };

  return (
    <TableCard tableKey="EMPLOYEE" className="mx-0 border-none">
      <TableHeader
        filterType="employees_permissions"
        data={employees || []}
        filterProps={filterConfig}
        isSaveFilters={true}
        isExportable={true}
        rightComponent={<GroupButton {...groupButtonConfig} />}
        customSavedFilters={{
          data: customSavedFiltersData,
          activeTab: selectedBranch,
          onTabChange: handleBranchTabChange,
          showSettings: false,
          isLoading: isLoadingBranches,
        }}
      />
      <TableContainer
        columns={tableColumns}
        data={(employees || []) as Employee[]}
        loading={isTableLoading}
        total={total || 0}
        pageSize={Number(getInitialParams.limit) || 20}
        currentPage={Number(getInitialParams.page) || 0}
        selectable={true}
        onHandleDelete={() => {
          console.log("delete");
        }}
        allowDelete={true}
        deleteConfirmationDescription="common.deleteEmployeeConfirmation"
      />

      {hasCreateEmployee && (
        <AddEmployeeDialog
          open={showAddEmployee}
          onOpenChange={handleCloseDialog}
          employee_id={selectedEmployee?.id}
          mode={dialogMode}
        />
      )}
    </TableCard>
  );
}
