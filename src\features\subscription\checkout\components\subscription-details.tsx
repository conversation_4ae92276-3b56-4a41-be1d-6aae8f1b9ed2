"use client";

import { useTranslation } from "react-i18next";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui";
import { Plan } from "@/lib/apis/crm_plan/types";

import { RegistrationTime, RegistrationTimeOption } from "../hooks/use-checkout";

interface SubscriptionDetailsProps {
  subscriptionPlan: Plan;
  registrationTime: RegistrationTime;
  onRegistrationTimeChange?: (value: RegistrationTime) => void;
  isPaymentMode?: boolean;
}

export const SubscriptionDetails = ({
  subscriptionPlan,
  registrationTime,
  onRegistrationTimeChange,
  isPaymentMode,
}: SubscriptionDetailsProps) => {
  const { t } = useTranslation();

  const formatPrice = (amount: number) => {
    return new Intl.NumberFormat("vi-VN").format(amount);
  };

  const registrationTimeOptions: RegistrationTimeOption[] = [
    { value: RegistrationTime.MONTHLY, label: "1 month", multiplier: 1 },
    { value: RegistrationTime.YEARLY, label: "12 months", multiplier: 1 },
  ];

  // Calculate price directly from plan based on registration time
  const currentPrice =
    registrationTime === RegistrationTime.YEARLY
      ? subscriptionPlan?.duration.YEARLY.sale_price || 0
      : subscriptionPlan?.duration.MONTHLY.sale_price || 0;

  return (
    <div className="flex flex-col gap-2">
      <div className="flex items-center justify-between">
        <span className="text-base text-muted-foreground">
          {t("pages.checkout.orderInformation.subscribeTo")}
        </span>
        <span className="text-base text-primary">{subscriptionPlan?.name}</span>
      </div>

      <div className="flex items-center justify-between">
        <span className="text-base text-muted-foreground">
          {t("pages.checkout.orderInformation.registrationTime")}
        </span>
        {isPaymentMode ? (
          <span className="text-base text-card-foreground">
            {registrationTimeOptions.find((option) => option.value === registrationTime)?.label}
          </span>
        ) : (
          <Select value={registrationTime} onValueChange={onRegistrationTimeChange}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder={registrationTime} />
            </SelectTrigger>
            <SelectContent>
              {registrationTimeOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>

      <div className="flex items-center justify-between">
        <span className="text-base text-muted-foreground">
          {t("pages.checkout.orderInformation.price")}
        </span>
        <div className="flex items-center gap-1">
          <span className="text-base text-card-foreground">{formatPrice(currentPrice)}</span>
          <span className="text-base text-muted-foreground">VND</span>
        </div>
      </div>
    </div>
  );
};
