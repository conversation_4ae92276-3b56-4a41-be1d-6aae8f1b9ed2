"use client";

import { useRouter } from "next/navigation";
import { useTranslation } from "react-i18next";

import { StoreInformationForm, useStoreInformation } from "@/features/settings/store-information";

import { Card } from "@/components/ui";

export default function StoreInformationPage() {
  const { t } = useTranslation();
  const router = useRouter();
  const { formData, isLoading, error, handleInputChange, updateStoreInformation } =
    useStoreInformation();

  const handleBack = () => {
    router.push("/settings");
  };

  const handleUpdate = async () => {
    const result = await updateStoreInformation(formData);
    if (result.success) {
      console.log("Store information updated successfully");
    } else {
      console.error("Failed to update store information:", result.error);
    }
    return result;
  };

  return (
    <Card className="m-4 mt-0 border-none p-4">
      {/* Back Button */}
      <div className="mb-4">
        {/* Page Title */}
        <h1 className="text-lg font-semibold text-foreground">
          {t("storeInformation.storeInformation")}
        </h1>
      </div>
      <StoreInformationForm
        formData={formData}
        isLoading={isLoading}
        onInputChange={handleInputChange}
        onUpdate={handleUpdate}
      />
    </Card>
  );
}
