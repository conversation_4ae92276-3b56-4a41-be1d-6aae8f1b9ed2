#!/usr/bin/env node

/**
 * UI Validation for Unit Form at property-assets/units/new
 * This validates the visual design and functionality of the Unit CRUD form
 */
import puppeteer from "puppeteer";

const BASE_URL = "http://localhost:3001"; // Using port 3001 as per dev server

async function validateUnitUI() {
  console.log("🏢 Validating Unit Form UI");
  console.log("=".repeat(50));

  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: { width: 1400, height: 900 },
  });

  try {
    const page = await browser.newPage();

    // Navigate directly to the unit form
    console.log("📍 Navigating to property-assets/units/new...");
    await page.goto(`${BASE_URL}/property-assets/units/new`, {
      waitUntil: "networkidle0",
      timeout: 30000,
    });

    // Check if we need to login
    const currentUrl = page.url();
    if (currentUrl.includes("/login")) {
      console.log("🔐 Login required, authenticating...");

      // Find and fill login form
      await page.waitForSelector('input[type="email"], input[name="email"]', { timeout: 5000 });

      const emailInput = await page.$('input[type="email"], input[name="email"]');
      const passwordInput = await page.$('input[type="password"], input[name="password"]');

      if (emailInput && passwordInput) {
        await emailInput.type("onexapis_admin");
        await passwordInput.type("Admin@123");

        // Find and click submit button
        const submitButton = await page.$('button[type="submit"]');
        if (submitButton) {
          await submitButton.click();
          await page.waitForNavigation({ waitUntil: "networkidle0" });
        }
      }

      // Navigate to unit form again
      await page.goto(`${BASE_URL}/property-assets/units/new`, {
        waitUntil: "networkidle0",
        timeout: 30000,
      });
    }

    console.log("✅ Loaded Unit Form page");

    // Take screenshot of the full form
    await page.screenshot({
      path: "unit-form-validation.png",
      fullPage: true,
    });
    console.log("📸 Screenshot saved as unit-form-validation.png");

    // Validate Form Structure
    console.log("\n📋 Validating Form Structure:");

    // Check for full-height layout
    const hasFullHeightLayout = await page.$(".flex.h-screen.flex-col");
    console.log(`  ${hasFullHeightLayout ? "✅" : "❌"} Full-height layout container`);

    // Check for sticky footer
    const hasStickyFooter = await page.$(".sticky.bottom-0");
    console.log(`  ${hasStickyFooter ? "✅" : "❌"} Sticky footer with buttons`);

    // Check for form cards
    const formCards = await page.$$(".shadow-sm.border-gray-200");
    console.log(
      `  ${formCards.length > 0 ? "✅" : "❌"} Form card sections (found ${formCards.length})`
    );

    // Validate Card Sections
    console.log("\n📑 Validating Form Sections:");

    const expectedSections = [
      "Basic Information",
      "Specifications",
      "Financial Information",
      "Amenities",
      "Unit Images",
    ];

    for (const section of expectedSections) {
      const sectionExists = await page.evaluate((text) => {
        return document.body.textContent?.includes(text);
      }, section);
      console.log(`  ${sectionExists ? "✅" : "❌"} ${section} section`);
    }

    // Validate Form Fields
    console.log("\n🔍 Validating Form Fields:");

    // Check for property selection
    const propertySelect =
      (await page.$('[role="combobox"]')) || (await page.$('select[name="property_id"]'));
    console.log(`  ${propertySelect ? "✅" : "❌"} Property selection dropdown`);

    // Check for unit number input
    const unitNumberInput = await page.$('input[name="unit_number"]');
    console.log(`  ${unitNumberInput ? "✅" : "❌"} Unit number input`);

    // Check for numeric inputs
    const numericFields = ["floor", "square_footage", "rent_amount", "deposit_amount"];
    for (const field of numericFields) {
      const input = await page.$(`input[name="${field}"]`);
      console.log(`  ${input ? "✅" : "❌"} ${field.replace("_", " ")} input`);
    }

    // Validate Buttons
    console.log("\n🔘 Validating Buttons:");

    if (hasStickyFooter) {
      const buttonsInFooter = await hasStickyFooter.$$("button");
      console.log(
        `  ${buttonsInFooter.length >= 2 ? "✅" : "❌"} Two buttons in sticky footer (found ${buttonsInFooter.length})`
      );

      // Check for orange primary button
      const primaryButton = await page.$(".bg-primary");
      console.log(`  ${primaryButton ? "✅" : "❌"} Orange primary submit button`);

      // Check for gray cancel button
      const cancelButton = await page.$(".border.bg-primary-foreground");
      console.log(`  ${cancelButton ? "✅" : "❌"} Gray outline cancel button`);
    }

    // Validate Translations
    console.log("\n🌐 Validating Translations:");

    const bodyText = await page.evaluate(() => document.body.textContent || "");

    // Check for raw translation keys
    const hasRawKeys = /pages\.units\.|\.placeholder|\.header\./.test(bodyText);
    console.log(`  ${!hasRawKeys ? "✅" : "❌"} No raw translation keys visible`);

    // Check for expected text
    const expectedTexts = ["Create Unit", "Property", "Unit Number", "Unit Type"];
    for (const text of expectedTexts) {
      const hasText = bodyText.includes(text);
      console.log(`  ${hasText ? "✅" : "❌"} "${text}" text present`);
    }

    // Validate Visual Design
    console.log("\n🎨 Validating Visual Design:");

    // Check for colored indicators in card headers
    const coloredIndicators = await page.$$(".w-2.h-2.rounded-full");
    console.log(
      `  ${coloredIndicators.length > 0 ? "✅" : "❌"} Colored indicators in headers (found ${coloredIndicators.length})`
    );

    // Check input heights
    const firstInput = await page.$('input[type="text"], input[type="number"]');
    if (firstInput) {
      const inputClass = (await firstInput.getAttribute("class")) || "";
      const hasCorrectHeight = inputClass.includes("h-9");
      console.log(`  ${hasCorrectHeight ? "✅" : "❌"} Input fields have h-9 height`);
    }

    // Summary
    console.log("\n📊 Validation Summary:");
    console.log("=".repeat(50));
    console.log("The Unit Form has been validated. Check the screenshot at:");
    console.log("  📸 unit-form-validation.png");
    console.log("\nKey findings are listed above with ✅ (pass) or ❌ (fail) indicators.");

    // Keep browser open for manual inspection
    console.log("\n👀 Browser will remain open for manual inspection...");
    console.log("Press Ctrl+C to close when done.");

    // Wait indefinitely
    await new Promise(() => {});
  } catch (error) {
    console.error("\n❌ Validation Error:", error.message);
    await browser.close();
    process.exit(1);
  }
}

// Run validation
validateUnitUI().catch(console.error);
