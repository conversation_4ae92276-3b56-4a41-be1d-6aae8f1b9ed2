import { useCallback, useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import { useQuery } from "@tanstack/react-query";

import { permissionsApi } from "@/lib/apis/permission";
import { getSimpleUserFromLocalStorage } from "@/lib/apis/users";

import { ROUTE_PERMISSION_MAP } from "./route_permission_map";

// Permission context types
export interface PermissionContextType {
  permissions: any;
  isLoading: boolean;
  isAccessForbidden: boolean;
  hasPermission: (permission: string) => boolean;
  isPermissionBlocked: (permission: string) => boolean;
  hasRoutePermission: (route: string) => boolean;
  checkAccess: (route: string) => boolean;
  refreshPermissions: () => void;
  // New functions for sidebar filtering
  isModuleAccessible: (moduleName: string) => boolean;
  getModulePermissions: (moduleName: string) => string[];
  filterSidebarItems: (items: any[]) => any[];
}

// Helper function to check if a route matches a pattern
const routeMatches = (route: string, pattern: string): boolean => {
  // Convert pattern to regex
  const regexPattern = pattern
    .replace(/:[^/]+/g, "[^/]+") // Replace :id with regex
    .replace(/\//g, "\\/"); // Escape forward slashes

  const regex = new RegExp(`^${regexPattern}$`);
  return regex.test(route);
};

// Helper function to find matching route pattern
const findMatchingRoute = (route: string): string | null => {
  for (const pattern of Object.keys(ROUTE_PERMISSION_MAP)) {
    if (routeMatches(route, pattern)) {
      return pattern;
    }
  }
  return null;
};

export const usePermissionProvider = (
  disableAdminBypass: boolean = true
): PermissionContextType => {
  const pathname = usePathname();
  const [isBlocked, setIsBlocked] = useState(false);
  const [isCheckingAccess, setIsCheckingAccess] = useState(true);
  const [isAccessForbidden, setIsAccessForbidden] = useState(false);

  // Fetch user permissions
  const {
    data: permissions = {},
    isLoading,
    refetch,
    error,
  } = useQuery({
    queryKey: ["user-permission"],
    queryFn: () => permissionsApi.listUserRolePermission(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  const user = getSimpleUserFromLocalStorage();
  // Check if user has a specific permission
  const hasPermission = useCallback(
    (permission: string): boolean => {
      if (!disableAdminBypass && user.role?.toUpperCase() === "ADMIN") return true;
      if (!permissions || typeof permissions !== "object") return false;

      const permissionData = (permissions as Record<string, any>)[permission];
      if (!permissionData) return true;
      return permissionData.is_enabled === true;
    },
    [disableAdminBypass, permissions, user.role]
  );

  // Check if a permission is explicitly blocked (is_enabled = false)
  const isPermissionBlocked = useCallback(
    (permission: string): boolean => {
      if (!disableAdminBypass && user.role?.toUpperCase() === "ADMIN") return false;
      if (!permissions || typeof permissions !== "object") return false;

      const permissionData = (permissions as Record<string, any>)[permission];
      if (!permissionData) return false;

      return permissionData.is_enabled === false;
    },
    [disableAdminBypass, permissions, user.role]
  );

  // Get all permissions for a specific module
  const getModulePermissions = useCallback(
    (moduleName: string): string[] => {
      if (!disableAdminBypass && user.role?.toUpperCase() === "ADMIN") return [];
      if (!permissions || typeof permissions !== "object") return [];

      return Object.entries(permissions as Record<string, any>)
        .filter(([_, permissionData]) => permissionData.module === moduleName)
        .map(([permissionKey, _]) => permissionKey);
    },
    [disableAdminBypass, permissions, user.role]
  );

  // Check if a module is accessible (has at least one enabled permission)
  const isModuleAccessible = useCallback(
    (moduleName: string): boolean => {
      if (!disableAdminBypass && user.role?.toUpperCase() === "ADMIN") return true;
      const modulePermissions = getModulePermissions(moduleName);

      // If no permissions found for this module, allow access (don't block by default)
      if (modulePermissions.length === 0) return true;

      // Check if at least one permission is enabled
      return modulePermissions.some((permission) => hasPermission(permission));
    },
    [disableAdminBypass, getModulePermissions, hasPermission, user.role]
  );

  // Check if user has permission for a specific route
  const hasRoutePermission = useCallback(
    (route: string): boolean => {
      if (!disableAdminBypass && user.role?.toUpperCase() === "ADMIN") return true;
      const matchingPattern = findMatchingRoute(route);
      if (!matchingPattern) {
        // If no pattern matches, allow access (for new routes)
        return true;
      }

      const requiredPermissions = ROUTE_PERMISSION_MAP[matchingPattern];
      if (!requiredPermissions) {
        return true;
      }

      // Check if any required permission is explicitly blocked (is_enabled = false)
      const hasBlockedPermission = requiredPermissions.some((permission) =>
        isPermissionBlocked(permission)
      );
      if (hasBlockedPermission) {
        return false;
      }

      // If no permissions are blocked, allow access (don't require all permissions to be enabled)
      // Only block when explicitly disabled
      return true;
    },
    [disableAdminBypass, isPermissionBlocked, user.role]
  );

  // Check access for current route
  const checkAccess = useCallback(
    (route: string): boolean => {
      if (!disableAdminBypass && user.role?.toUpperCase() === "ADMIN") return true;
      // Allow access to public routes
      if (
        route === "/" ||
        route.startsWith("/login") ||
        route.startsWith("/signup") ||
        route.startsWith("/forgot-password") ||
        route.startsWith("/reset-password") ||
        route.startsWith("/verify") ||
        route.startsWith("/active-account")
      ) {
        return true;
      }

      // Allow access to onboarding and installation
      if (route.startsWith("/onboarding") || route.startsWith("/installation")) {
        return true;
      }

      // Check if user has permission for the route
      return hasRoutePermission(route);
    },
    [disableAdminBypass, hasRoutePermission, user.role]
  );

  // Check access immediately without waiting for permissions
  const checkAccessImmediate = useCallback(
    (route: string): boolean => {
      if (!disableAdminBypass && user.role?.toUpperCase() === "ADMIN") return true;
      // Allow access to public routes
      if (
        route === "/" ||
        route.startsWith("/login") ||
        route.startsWith("/signup") ||
        route.startsWith("/forgot-password") ||
        route.startsWith("/reset-password") ||
        route.startsWith("/verify") ||
        route.startsWith("/active-account") ||
        route.startsWith("/onboarding") ||
        route.startsWith("/installation")
      ) {
        return true;
      }

      // For protected routes, check if we have cached permissions
      if (permissions && Object.keys(permissions).length > 0) {
        return hasRoutePermission(route);
      }

      // If no cached permissions, allow access temporarily (will be checked when permissions load)
      return true;
    },
    [disableAdminBypass, user.role, permissions, hasRoutePermission]
  );

  // Filter sidebar items based on permissions
  const filterSidebarItems = useCallback(
    (items: any[]): any[] => {
      if (!disableAdminBypass && user.role?.toUpperCase() === "ADMIN") {
        return items;
      }
      return items.filter((item) => {
        // If item has a direct URL, check route permission
        if (item.url) {
          return hasRoutePermission(item.url);
        }

        // If item has sub-items, check if any are accessible
        if (item.items && Array.isArray(item.items)) {
          const accessibleItems = item.items.filter((subItem: any) =>
            subItem.url ? hasRoutePermission(subItem.url) : true
          );

          // Only show parent item if it has accessible sub-items
          if (accessibleItems.length > 0) {
            item.items = accessibleItems;
            return true;
          }
          return false;
        }

        // Default to showing item if no specific checks
        return true;
      });
    },
    [disableAdminBypass, hasRoutePermission, user.role]
  );

  // Refresh permissions
  const refreshPermissions = useCallback(() => {
    refetch();
  }, [refetch]);

  // Handle route access checking - set state instead of redirecting
  useEffect(() => {
    if (isLoading || !pathname) {
      return;
    }

    // Reset forbidden state when pathname changes
    setIsAccessForbidden(false);
    setIsBlocked(false);

    // Check access directly without calling checkAccessImmediate to avoid circular dependency
    let hasAccess = true;

    if (!disableAdminBypass && user.role?.toUpperCase() === "ADMIN") {
      hasAccess = true;
    } else if (
      pathname === "/" ||
      pathname.startsWith("/login") ||
      pathname.startsWith("/signup") ||
      pathname.startsWith("/forgot-password") ||
      pathname.startsWith("/reset-password") ||
      pathname.startsWith("/verify") ||
      pathname.startsWith("/active-account") ||
      pathname.startsWith("/onboarding") ||
      pathname.startsWith("/installation")
    ) {
      hasAccess = true;
    } else if (permissions && Object.keys(permissions).length > 0) {
      // Check permissions directly here to avoid circular dependency
      const matchingPattern = findMatchingRoute(pathname);
      if (matchingPattern) {
        const requiredPermissions = ROUTE_PERMISSION_MAP[matchingPattern];
        if (requiredPermissions) {
          hasAccess = !requiredPermissions.some((permission) => {
            const permissionData = (permissions as Record<string, any>)[permission];
            return permissionData && permissionData.is_enabled === false;
          });
        }
      }
    }

    if (!hasAccess && !isBlocked) {
      setIsBlocked(true);
      setIsAccessForbidden(true);
    }
    setIsCheckingAccess(false);
  }, [pathname, isLoading, disableAdminBypass, user.role]);

  // Handle permission fetch errors - set state instead of redirecting
  useEffect(() => {
    if (!error) {
      return;
    }

    if (!disableAdminBypass || user.role?.toUpperCase() !== "ADMIN") {
      setIsAccessForbidden(true);
    }
  }, [error, disableAdminBypass, user.role]);

  return {
    permissions,
    isLoading,
    isAccessForbidden,
    hasPermission,
    isPermissionBlocked,
    hasRoutePermission,
    checkAccess,
    refreshPermissions,
    isModuleAccessible,
    getModulePermissions,
    filterSidebarItems,
  };
};
