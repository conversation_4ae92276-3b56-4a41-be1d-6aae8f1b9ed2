"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { CircleAlert, Eye, EyeOff } from "lucide-react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { z } from "zod";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

const resetPasswordSchema = z
  .object({
    newPassword: z.string().min(8, "auth.minPasswordLength").min(1, "auth.newPasswordRequired"),
    confirmPassword: z.string().min(1, "auth.confirmPasswordRequired"),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "auth.passwordsDontMatch",
    path: ["confirmPassword"],
  });

type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>;

interface ResetPasswordStepProps {
  newPassword: string;
  confirmPassword: string;
  showNewPassword: boolean;
  showConfirmPassword: boolean;
  isLoading: boolean;
  error?: any;
  onNewPasswordChange: (password: string) => void;
  onConfirmPasswordChange: (password: string) => void;
  onToggleNewPasswordVisibility: () => void;
  onToggleConfirmPasswordVisibility: () => void;
  onResetPassword: () => void;
  onBack: () => void;
}

export const ResetPasswordStep = ({
  newPassword,
  confirmPassword,
  showNewPassword,
  showConfirmPassword,
  isLoading,
  error,
  onNewPasswordChange,
  onConfirmPasswordChange,
  onToggleNewPasswordVisibility,
  onToggleConfirmPasswordVisibility,
  onResetPassword,
  onBack,
}: ResetPasswordStepProps) => {
  const { t } = useTranslation();

  const form = useForm<ResetPasswordFormValues>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      newPassword: newPassword || "",
      confirmPassword: confirmPassword || "",
    },
    mode: "onChange",
  });

  const onSubmit = (data: ResetPasswordFormValues) => {
    onResetPassword();
  };

  const isFormValid = form.formState.isValid;
  const isDisabled = isLoading || !isFormValid;

  return (
    <>
      <div className="space-y-6 px-6 pb-8 pt-6">
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">
            {t("pages.profile.forgotPasswordDialog.resetPasswordStep.title")}
          </h2>
          <p className="text-sm text-muted-foreground">
            {t("pages.profile.forgotPasswordDialog.resetPasswordStep.description")}
          </p>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="newPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">
                    {t("pages.profile.forgotPasswordDialog.resetPasswordStep.newPasswordLabel")}
                  </FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        {...field}
                        type={showNewPassword ? "text" : "password"}
                        placeholder={t(
                          "pages.profile.forgotPasswordDialog.resetPasswordStep.newPasswordPlaceholder"
                        )}
                        disabled={isLoading}
                        onChange={(e) => {
                          field.onChange(e);
                          onNewPasswordChange(e.target.value);
                        }}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={onToggleNewPasswordVisibility}>
                        {showNewPassword ? (
                          <EyeOff className="size-4" />
                        ) : (
                          <Eye className="size-4" />
                        )}
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">
                    {t("pages.profile.forgotPasswordDialog.resetPasswordStep.confirmPasswordLabel")}
                  </FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input
                        {...field}
                        type={showConfirmPassword ? "text" : "password"}
                        placeholder={t(
                          "pages.profile.forgotPasswordDialog.resetPasswordStep.confirmPasswordPlaceholder"
                        )}
                        disabled={isLoading}
                        onChange={(e) => {
                          field.onChange(e);
                          onConfirmPasswordChange(e.target.value);
                        }}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={onToggleConfirmPasswordVisibility}>
                        {showConfirmPassword ? (
                          <EyeOff className="size-4" />
                        ) : (
                          <Eye className="size-4" />
                        )}
                      </Button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {error && (
              <div className="flex items-center text-sm text-red-500">
                <CircleAlert className="mr-2 size-4" />
                {t(String(error))}
              </div>
            )}
          </form>
        </Form>
      </div>

      <div className="flex items-center justify-end border-t px-6 pb-6 pt-4">
        <Button
          type="submit"
          loading={isLoading}
          onClick={form.handleSubmit(onSubmit)}
          disabled={isDisabled}>
          {t("pages.profile.forgotPasswordDialog.resetPasswordStep.continue")}
        </Button>
      </div>
    </>
  );
};
