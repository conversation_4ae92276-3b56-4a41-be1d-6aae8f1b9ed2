"use client";

import { useState } from "react";
import Link from "next/link";
import { Check, Sparkles } from "lucide-react";

import { Button } from "@/components/ui";
import { authProtectedPaths } from "@/constants/paths";

import { RegistrationTime } from "../../checkout/hooks/use-checkout";
import { usePricingCard } from "../hooks/use-pricing-card";
import { CurrentPlan, SubscriptionPlan } from "../hooks/use-subscription";

interface PricingCardProps {
  plan: SubscriptionPlan;
  isAnnualBilling: boolean;
  formatPrice: (price: number | string, currency: string) => string;
  currentPlan?: CurrentPlan;
}

export const PricingCard = ({
  plan,
  isAnnualBilling,
  formatPrice,
  currentPlan,
}: PricingCardProps) => {
  const [showAllFeatures, setShowAllFeatures] = useState(false);
  const { t, displayPrice, billingCycle, cardClasses, buttonVariant } = usePricingCard({
    plan,
    isAnnualBilling,
    formatPrice,
  });

  const MAX_FEATURES = 5;

  // Check if this plan has lower pricing than current plan
  const isLowerPricing =
    currentPlan &&
    !currentPlan.is_free_plan &&
    (() => {
      const currentMonthlyPrice = currentPlan.price;
      const currentYearlyPrice = currentPlan.salePrice;

      const planMonthlyPrice = parseFloat(plan.duration.MONTHLY.sale_price);
      const planYearlyPrice = parseFloat(plan.duration.YEARLY.sale_price);

      return planMonthlyPrice < currentMonthlyPrice || planYearlyPrice < currentYearlyPrice;
    })();

  // Handle features that can be either array or record
  const featuresArray = Array.isArray(plan.features)
    ? plan.features
    : Object.entries(plan.features as Record<string, boolean>)
        .filter(([_, enabled]) => enabled)
        .map(([feature]) => feature);

  const displayedFeatures = showAllFeatures ? featuresArray : featuresArray.slice(0, MAX_FEATURES);
  const hasMoreFeatures = featuresArray.length > MAX_FEATURES;

  // Get currency with fallback
  const currency = plan?.currency || "VND";
  return (
    <div className={`rounded-lg ${isLowerPricing ? "opacity-50" : ""}`}>
      {plan.isPopular || plan.is_popular ? (
        <div className="flex items-center justify-center gap-1 rounded-t-lg bg-primary px-2 py-1.5">
          <Sparkles className="size-4 text-primary-foreground" />
          <span className="text-sm font-semibold text-primary-foreground">
            {t("pages.subscription.pricing.mostPopular")}
          </span>
        </div>
      ) : (
        <div className="h-0 lg:h-8"></div>
      )}
      <div className={cardClasses}>
        <div className="flex flex-col gap-4 p-4 pt-6">
          {/* Pricing Header */}
          <div className="flex flex-col gap-3 px-2">
            <h3 className="text-xl font-semibold text-foreground">{plan.name}</h3>

            <div className="flex items-end gap-1">
              <span className="text-4xl font-bold text-foreground">
                {formatPrice(displayPrice, currency)}
              </span>
              <span className="text-sm text-muted-foreground">{currency}</span>
              <span className="text-xs text-muted-foreground">{billingCycle}</span>
            </div>

            {plan.savings && billingCycle.includes("year") && plan?.savings > 0 && (
              <p className="flex items-center gap-1 text-xs text-foreground">
                {t("pages.subscription.pricing.save")}
                <span className="font-semibold text-primary">
                  {formatPrice(plan?.savings, currency)} {currency}
                </span>
                {t("pages.subscription.pricing.annually")}
              </p>
            )}
          </div>

          {/* Upgrade Button */}
          <Link
            href={
              isLowerPricing
                ? "#"
                : {
                    pathname: authProtectedPaths.CHECKOUT,
                    query: {
                      plan: plan.id,
                      time: isAnnualBilling ? RegistrationTime.YEARLY : RegistrationTime.MONTHLY,
                    },
                  }
            }
            className="w-full"
            onClick={isLowerPricing ? (e) => e.preventDefault() : undefined}>
            <Button size="lg" variant={buttonVariant} className="w-full" disabled={isLowerPricing}>
              {t("pages.subscription.pricing.upgrade")}
            </Button>
          </Link>

          {/* Features List */}
          <div className="flex flex-col gap-1">
            {displayedFeatures.map((feature: string, index: number) => (
              <div key={index} className="flex items-center gap-2 py-1">
                <Check className="size-4 text-primary" />
                <span className="text-base text-card-foreground">
                  {feature.replace(/\b\w/g, (l: string) => l.toUpperCase())}
                </span>
              </div>
            ))}
          </div>

          {/* More Button - Only show if there are more than 5 features */}
          {hasMoreFeatures && (
            <Button
              disabled={isLowerPricing}
              variant="link"
              size="sm"
              onClick={() => setShowAllFeatures(!showAllFeatures)}>
              {showAllFeatures
                ? t("pages.subscription.pricing.showLess")
                : t("pages.subscription.pricing.more")}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};
