"use client";

import { useCallback, useMemo } from "react";
import { Plus } from "lucide-react";
import { useTranslation } from "react-i18next";

import { SortableCategories } from "@/features/blogs/blog-category/components/sortable-categories";
import { useBlogCategoryManager } from "@/features/blogs/hooks/use-blog-category";

import TableCard from "@/components/data-table/data-table-card";
import { FilterTableProps } from "@/components/data-table/types";

export default function BlogCategoriesPage() {
  const { t } = useTranslation();
  const {
    isLoading,
    isError,
    error,
    hasNextPage,
    isFetchingNextPage,
    handleLoadMore,
    flattenedCategories,
    createCategory,
  } = useBlogCategoryManager();

  const handleLoadMoreCallback = useCallback(() => {
    handleLoadMore();
  }, [handleLoadMore]);

  // Filter configuration without search
  const filterConfig: FilterTableProps = useMemo(
    () => ({
      showSearch: false,
      filterType: "blog-categories",
      // listFilter: [],
      // listLoading: isLoading || isFetchingNextPage,
      isClear: false,
      isOtherFilters: false,
    }),
    []
  );

  // Group button configuration with Add Category button
  const groupButtonConfig = useMemo(
    () => ({
      buttons: [
        {
          type: "button" as const,
          title: t("pages.blogCategories.actions.addCategory", "Add Category"),
          icon: Plus,
          onClick: () => {
            // Show dialog or form to create category
            const name = window.prompt("Enter category name:");
            if (name && name.trim()) {
              createCategory.mutate({ name: name.trim() });
            }
          },
        },
      ],
      onRefresh: () => {},
      isRefreshLoading: isLoading,
    }),
    []
  );

  if (isError) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <p className="text-red-500">Error loading categories</p>
            <p className="text-sm text-gray-500">{error?.message}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <TableCard className="border-none">
        {/* <TableHeader
          title={t("pages.blogCategories.title", "Categories")}
          filterType="blog-categories"
          data={flattenedCategories}
          filterProps={filterConfig}
          rightComponent={<GroupButton {...groupButtonConfig} showRefresh={false} />}
          isExportable={false}
        /> */}
        <div className="mt-4">
          <SortableCategories
            onLoadMore={handleLoadMoreCallback}
            hasNextPage={hasNextPage}
            isFetchingNextPage={isFetchingNextPage}
            isLoading={isLoading}
          />
        </div>
      </TableCard>
    </div>
  );
}
