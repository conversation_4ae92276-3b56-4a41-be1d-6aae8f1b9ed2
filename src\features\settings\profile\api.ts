import { privateApi } from "@/lib/api_helper";

import { ProfileResponse, UpdateProfilePayload, UpdateProfileResponse } from "./types";

export const profileApi = {
  // Get user profile
  getProfile: async (): Promise<ProfileResponse> => {
    return privateApi.get<ProfileResponse>("/profile");
  },

  // Update user profile
  updateProfile: async (payload: UpdateProfilePayload): Promise<UpdateProfileResponse> => {
    return privateApi.put<UpdateProfileResponse>("/profile", payload);
  },

  // Upload avatar
  uploadAvatar: async (file: File): Promise<{ url: string }> => {
    const formData = new FormData();
    formData.append("avatar", file);
    return privateApi.post<{ url: string }>("/profile/avatar", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },
};
