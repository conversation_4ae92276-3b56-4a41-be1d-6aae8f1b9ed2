import { BaseEntity } from "../base";

export enum LayoutStatus {
  PENDING = "PENDING",
  IN_PROGRESS = "IN_PROGRESS",
  DONE = "DONE",
}

export enum ShapeType {
  RECTANGLE = "RECTANGLE",
  CIRCLE = "CIRCLE",
  POLYGON = "POLYGON",
}

export interface StyleConfig {
  fill_color?: string;
  stroke_color?: string;
  stroke_width?: string;
  opacity?: number;
  label_text?: string;
  label_font_size?: number;
  label_color?: string;
  visible?: boolean;
}

export interface LayoutUnitPosition {
  unit_id: string;
  x: number;
  y: number;
  rotation?: number;
  shape_type: string;
  style_config: StyleConfig;
}

export interface Layout extends BaseEntity {
  property_id: string;
  name: string;
  floor_number: number;
  description?: string;
  status: string;
  image?: {
    id: string;
    url: string;
    name: string;
    is_primary: boolean;
  };
  unit_layout_positions?: LayoutUnitPosition[];
}

export interface CreateLayout {
  property_id?: string;
  name: string;
  floor_number: number;
  description?: string;
  image?: {
    name: string;
    image: string;
  };
  unit_layout_positions?: Omit<LayoutUnitPosition, "unit_id">[];
}

export interface UpdateLayout {
  name?: string;
  floor_number?: number;
  description?: string;
  status?: string;
  image?: {
    name: string;
    image: string;
  };
  unit_layout_positions?: Omit<LayoutUnitPosition, "unit_id">[];
}

export interface UpdateLayoutMapping {
  unit_layout_positions: LayoutUnitPosition[];
}

export interface LayoutStatistics {
  total_layouts: number;
  layouts_by_status: {
    PENDING: number;
    IN_PROGRESS: number;
    DONE: number;
  };
  layouts_by_property: Array<{
    property_id: string;
    property_name: string;
    layout_count: number;
  }>;
}
