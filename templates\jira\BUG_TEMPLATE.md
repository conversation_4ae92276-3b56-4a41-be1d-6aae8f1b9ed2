# Bug Report Template

## 📋 Bug Summary

**Brief description of the bug in one sentence**

## 🔍 Environment Information

- **Browser**: [Chrome/Firefox/Safari] [Version]
- **Device**: [Desktop/Mobile/Tablet] [OS Version]
- **User Role**: [Admin/User/Customer]
- **Tenant**: [OneX/OnexSync/OnexBots]
- **Feature Area**: [Products/Orders/Customers/Integration/Bots/Auth]

## 🚨 Bug Details

### Current Behavior

**What is happening now? (Be specific)**

- Step 1: [Action taken]
- Step 2: [Action taken]
- **Result**: [What actually happens]

### Expected Behavior

**What should happen instead?**

- [Clear description of expected outcome]

### Impact Assessment

- **Severity**: [Critical/High/Medium/Low]
- **Frequency**: [Always/Often/Sometimes/Rarely]
- **User Impact**: [How many users affected]
- **Business Impact**: [Revenue/operations impact]

## 📱 Screenshots/Evidence

**Attach screenshots, videos, or logs**

- [ ] Screenshot of the issue
- [ ] Browser console errors (F12 → Console)
- [ ] Network errors (F12 → Network)
- [ ] Server logs (if applicable)

### Visual Bug Documentation (For UI/Styling Issues)

**Required for any visual, layout, or styling bugs**

#### Current Implementation Screenshot

- [ ] **Actual Result**: Screenshot showing the current broken/incorrect appearance
- [ ] **Browser/Device**: Specify exact browser, version, and device type
- [ ] **Viewport Size**: Include window dimensions (mobile: 375px, tablet: 768px, desktop: 1200px+)

#### Expected Design Reference

- [ ] **Figma Design**: Link to the relevant Figma design specification
  - **File URL**: https://www.figma.com/file/[file-id]/[file-name]
  - **Specific Frame**: Direct link to the frame showing correct design
  - **Component Name**: Name of the specific component with the issue
- [ ] **Design Screenshot**: Screenshot from Figma showing expected appearance
- [ ] **Design Specifications**: Color codes, spacing values, typography details from Figma

#### Visual Comparison Requirements

- [ ] **Side-by-Side Comparison**: Current vs Expected visual comparison
- [ ] **Highlighted Differences**: Mark specific areas where implementation differs from design
- [ ] **Design Token Verification**: Confirm colors, fonts, spacing match design system

## 🔄 Steps to Reproduce

**Detailed steps for a developer to reproduce the bug**

### Prerequisites

- [ ] Logged in as [specific role]
- [ ] Have [specific data/setup]
- [ ] On [specific page/feature]

### Reproduction Steps

1. Go to [specific URL/page]
2. Click/Enter [specific action]
3. Fill in [specific data]
4. Click [specific button]
5. Observe [specific result]

### Data Requirements

**What specific data is needed to reproduce?**

- Product IDs: [if applicable]
- Order numbers: [if applicable]
- Customer data: [if applicable]
- Test accounts: [if applicable]

## 🎯 Affected Components

**Which parts of the system are impacted?**

### Frontend Components

- [ ] Page: `src/app/[specific-route]/`
- [ ] Component: `src/features/[module]/components/[component]`
- [ ] Hook: `src/features/[module]/hooks/[hook]`
- [ ] API call: `src/lib/apis/[api-function]`

### Backend/API

- [ ] Endpoint: [API endpoint]
- [ ] Database: [table/collection]
- [ ] Integration: [external service]

### User Experience

- [ ] Navigation broken
- [ ] Data not loading
- [ ] Form submission failing
- [ ] Visual/styling issues
- [ ] Performance problems

## 🔧 Technical Analysis (Optional)

**For technical team members**

### Error Messages

```
[Paste exact error messages here]
```

### Browser Console Logs

```
[Paste console errors here]
```

### Network Request Details

- **Failed Request**: [URL]
- **Method**: [GET/POST/PUT/DELETE]
- **Status Code**: [200/400/500/etc]
- **Response**: [Error response]

## ✅ Acceptance Criteria for Fix

**How will we know this bug is resolved?**

- [ ] **Functional**: [Specific functional requirement]
- [ ] **Performance**: [If performance related]
- [ ] **UI/UX**: [If visual/interaction related]
- [ ] **Cross-browser**: [Works on Chrome, Firefox, Safari]
- [ ] **Mobile**: [Works on mobile devices]
- [ ] **Integration**: [Works with external systems]

### Visual Validation Criteria (For UI/Styling Bugs)

**Required for any visual bug fixes**

#### Figma Design Compliance

- [ ] **Pixel-Perfect Matching**: Implementation matches Figma design with ≤2px tolerance
- [ ] **Color Accuracy**: All colors match Figma design specifications exactly
- [ ] **Typography Consistency**: Font family, size, weight, line-height match design
- [ ] **Spacing Precision**: Margins, padding, gaps match Figma specifications (±1px tolerance)
- [ ] **Component States**: All interactive states (hover, focus, active, disabled) match design

#### Automated Visual Testing

- [ ] **Puppeteer Screenshots**: Automated screenshots captured for all relevant viewports
- [ ] **Baseline Comparison**: Screenshots compared against Figma-exported baselines
- [ ] **Responsive Validation**: Component tested on mobile (375px), tablet (768px), desktop (1200px)
- [ ] **Cross-Browser Consistency**: Visual appearance consistent across Chrome, Firefox, Safari
- [ ] **Interactive State Testing**: All component states captured and validated

#### Design System Compliance

- [ ] **Design Tokens**: Implementation uses correct design system tokens (colors, spacing, typography)
- [ ] **Component Library**: Fix follows existing OneX component patterns and conventions
- [ ] **Accessibility**: Visual fix maintains or improves WCAG AA compliance
- [ ] **Performance Impact**: Visual changes don't negatively impact rendering performance

### Test Scenarios

- [ ] **Happy Path**: [Normal usage works]
- [ ] **Edge Cases**: [Boundary conditions work]
- [ ] **Error Handling**: [Graceful error messages]
- [ ] **Regression**: [Related features still work]
- [ ] **Visual Regression**: [No unintended visual changes to other components]

## 🔗 Related Information

**Links to related tickets, documentation, or resources**

- **Related Tickets**: [Link to related Jira tickets]
- **Documentation**: [Link to relevant docs]
- **Previous Issues**: [Similar bugs in the past]
- **Customer Reports**: [Support ticket references]

## 📊 Business Context

**Why is this important to fix?**

### User Story Context

- **As a** [user type]
- **I want** [to do something]
- **So that** [business value]

### Priority Justification

- **Revenue Impact**: [How does this affect revenue?]
- **User Experience**: [How does this affect users?]
- **System Stability**: [How does this affect system?]

---

## 📝 Template Usage Notes

**Before submitting this bug report:**

- [ ] Verified the issue exists in latest version
- [ ] Checked if similar bug already reported
- [ ] Gathered all necessary screenshots/logs
- [ ] Tested on multiple browsers/devices
- [ ] Provided clear reproduction steps
- [ ] Defined specific acceptance criteria

**For Developers:**

- This template ensures all information needed for debugging
- Priority should be set based on business impact
- Screenshots and error logs are crucial for diagnosis
- Clear acceptance criteria prevent scope creep during fixes
