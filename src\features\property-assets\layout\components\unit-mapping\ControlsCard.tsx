import { Edit, Grid3X3, <PERSON>, Loader2, Save, Search, Trash2 } from "lucide-react";
import { useTranslation } from "react-i18next";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import type { FilterStatus, ViewMode } from "../../hooks/use-unit-mapping";

interface ControlsCardProps {
  selectedLayoutId: string;
  layouts: Array<{ id: string; name: string }>;
  viewMode: ViewMode;
  filterStatus: FilterStatus;
  isEditing: boolean;
  isLoading: boolean;
  onLayoutSelect: (layoutId: string) => void;
  onViewModeChange: (mode: ViewMode) => void;
  onFilterStatusChange: (status: FilterStatus) => void;
  onEditToggle: () => void;
  onSave: () => void;
  onDelete: () => void;
  searchTerm: string;
  onSearchChange: (term: string) => void;
  totalUnits: number;
  mappedUnits: number;
}

export function ControlsCard({
  selectedLayoutId,
  layouts,
  viewMode,
  filterStatus,
  isEditing,
  isLoading,
  onLayoutSelect,
  onViewModeChange,
  onFilterStatusChange,
  onEditToggle,
  onSave,
  onDelete,
  searchTerm,
  onSearchChange,
  totalUnits,
  mappedUnits,
}: ControlsCardProps) {
  const { t } = useTranslation();

  return (
    <Card>
      <CardContent className="space-y-3 p-4">
        {/* Layout Selector */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium">{t("pages.layouts.selectLayout")}</label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Grid3X3 className="size-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={onEditToggle}>
                  <Edit className="mr-2 size-4" />
                  {isEditing ? t("common.viewMode") : t("common.editMode")}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onSave} disabled={!isEditing || isLoading}>
                  {isLoading ? (
                    <Loader2 className="mr-2 size-4 animate-spin" />
                  ) : (
                    <Save className="mr-2 size-4" />
                  )}
                  {t("common.save")}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={onDelete} className="text-destructive">
                  <Trash2 className="mr-2 size-4" />
                  {t("common.delete")}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <Select value={selectedLayoutId} onValueChange={onLayoutSelect}>
            <SelectTrigger>
              <SelectValue placeholder={t("pages.layouts.selectLayout")} />
            </SelectTrigger>
            <SelectContent>
              {layouts.map((layout) => (
                <SelectItem key={layout.id} value={layout.id}>
                  {layout.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Search */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder={t("pages.units.searchUnits")}
                value={searchTerm}
                onChange={(e) => onSearchChange(e.target.value)}
                className="pl-9"
              />
            </div>
            <div className="flex gap-1">
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="sm"
                onClick={() => onViewModeChange("list")}>
                <List className="size-4" />
              </Button>
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="sm"
                onClick={() => onViewModeChange("grid")}>
                <Grid3X3 className="size-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Filter */}
        <Select
          value={filterStatus}
          onValueChange={(value: FilterStatus) => onFilterStatusChange(value)}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">
              {t("common.all")} ({totalUnits})
            </SelectItem>
            <SelectItem value="mapped">
              {t("pages.layouts.mapped")} ({mappedUnits})
            </SelectItem>
            <SelectItem value="unmapped">
              {t("pages.layouts.unmapped")} ({totalUnits - mappedUnits})
            </SelectItem>
          </SelectContent>
        </Select>
      </CardContent>
    </Card>
  );
}
