export interface StoreInformation {
  storeName: string;
  store_phone: string;
  store_email: string;
  businessSector: string;
  storeUrl: string;
  store_default_price_group: {
    name: string;
    id: string;
  };
  store_address: string;
  store_provinces: string;
  store_districts: string;
  store_wards: string;
  isDirty?: boolean;
  isBasicInfoDirty?: boolean;
  isAdvancedInfoDirty?: boolean;
}

export interface StoreInformationFormData extends StoreInformation {
  isDirty: boolean;
}

export interface BusinessSector {
  id: string;
  name: string;
  description?: string;
}

export interface PriceGroup {
  id: string;
  name: string;
  description?: string;
}

export interface LocationData {
  id: string;
  name: string;
  code?: string;
}
