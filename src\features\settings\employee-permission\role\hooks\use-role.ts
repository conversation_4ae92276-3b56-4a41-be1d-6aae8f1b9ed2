import {
  InfiniteData,
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

import { SortDirection } from "@/components/custom-table/hooks/use-data-table";
import { roleApi } from "@/lib/apis/role";
import { ResponseAxiosDetail, ResponseList } from "@/lib/apis/types/common";
import { CreateRolePayload, Role, UpdateRolePayload } from "@/lib/apis/types/role";

// Query keys for role management
export const roleKeys = {
  all: ["roles"] as const,
  lists: () => [...roleKeys.all, "list"] as const,
  list: (params: Record<string, unknown>) => [...roleKeys.lists(), params] as const,
  details: () => [...roleKeys.all, "detail"] as const,
  detail: (id: string) => [...roleKeys.details(), id] as const,
};

interface UseRolesOptions {
  enabled?: boolean;
  limit?: number;
  onCreateSuccess?: (data: Role) => void;
  onUpdateSuccess?: (data: Role) => void;
  onDeleteSuccess?: () => void;
}

interface UseCreateRoleOptions {
  onSuccess?: (data: Role) => void;
  onError?: (error: Error) => void;
}

interface UseUpdateRoleOptions {
  onSuccess?: (data: Role) => void;
  onError?: (error: Error) => void;
}

interface UseDeleteRoleOptions {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

export function useRoles(options: UseRolesOptions = {}) {
  const {
    limit = 20,
    enabled = true,
    onCreateSuccess,
    onUpdateSuccess,
    onDeleteSuccess,
    ...restOptions
  } = options;
  const queryClient = useQueryClient();

  const query = useInfiniteQuery({
    queryKey: roleKeys.list({ limit, ...restOptions }),
    queryFn: ({ pageParam = 0 }) =>
      roleApi.list({
        limit,
        ["sort_updated_at"]: SortDirection.DESC,
        page: pageParam as number,
        ...restOptions,
      }),
    getNextPageParam: (lastPage: ResponseList<Role>) => {
      const totalPages = Math.ceil(lastPage.total / Number(lastPage.limit));
      if (lastPage.page < totalPages) {
        return lastPage.page + 1;
      }
      return undefined;
    },
    initialPageParam: 0,
    enabled,
  });

  // Create role mutation with local state update
  const createRoleMutation = useMutation({
    mutationFn: async (data: CreateRolePayload) => {
      const response = await roleApi.create(data);
      return response;
    },
    onSuccess: (response: ResponseAxiosDetail<Role>) => {
      const newRole = response.data;

      // Validate that the new role has required properties before updating local state
      if (!newRole || !newRole.id || !newRole.name) {
        console.warn("Invalid role object received:", newRole);
        return;
      }

      // Update local state by adding the new role to the first page
      queryClient.setQueryData<InfiniteData<ResponseList<Role>>>(
        roleKeys.list({ limit, ...restOptions }),
        (oldData) => {
          if (!oldData) return oldData;

          const newPages = [...oldData.pages];
          if (newPages.length > 0) {
            // Add new role to the first page and increment total
            newPages[0] = {
              ...newPages[0],
              items: [newRole, ...newPages[0].items],
              total: newPages[0].total + 1,
            };
          }

          return { ...oldData, pages: newPages };
        }
      );

      // Call success callback if provided
      onCreateSuccess?.(newRole);
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  // Update role mutation with local state update
  const updateRoleMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateRolePayload }) => {
      const response = await roleApi.update(id, data);
      return response;
    },
    onSuccess: (response: ResponseAxiosDetail<Role>) => {
      const updatedRole = response.data;

      // Validate that the updated role has required properties before updating local state
      if (!updatedRole || !updatedRole.id || !updatedRole.name) {
        console.warn("Invalid updated role object received:", updatedRole);
        return;
      }

      // Update local state by replacing the updated role
      queryClient.setQueryData<InfiniteData<ResponseList<Role>>>(
        roleKeys.list({ limit, ...restOptions }),
        (oldData) => {
          if (!oldData) return oldData;

          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.map((item: Role) =>
              item.id === updatedRole.id ? updatedRole : item
            ),
          }));

          return { ...oldData, pages: newPages };
        }
      );

      // Also update the detail query if it exists
      queryClient.setQueryData(roleKeys.detail(updatedRole.id), updatedRole);

      // Call success callback if provided
      onUpdateSuccess?.(updatedRole);
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  // Delete role mutation with local state update
  const deleteRoleMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await roleApi.delete(id);
      return response;
    },
    onSuccess: (_, id) => {
      // Update local state by removing the deleted role
      queryClient.setQueryData<InfiniteData<ResponseList<Role>>>(
        roleKeys.list({ limit, ...restOptions }),
        (oldData) => {
          if (!oldData) return oldData;

          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.filter((item: Role) => item.id !== id),
            total: page.total - 1,
          }));

          return { ...oldData, pages: newPages };
        }
      );

      // Remove the detail query if it exists
      queryClient.removeQueries({ queryKey: roleKeys.detail(id) });

      // Call success callback if provided
      onDeleteSuccess?.();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const roles = query.data?.pages.flatMap((page) => page.items) ?? [];
  const total = query.data?.pages[0]?.total ?? 0;

  return {
    ...query,
    roles,
    total,
    isLoading: query.isLoading,
    isFetching: query.isFetching,
    isFetchingNextPage: query.isFetchingNextPage,
    hasNextPage: query.hasNextPage,
    // Mutations
    createRoleMutation,
    updateRoleMutation,
    deleteRoleMutation,
  };
}

export function useRole(id: string) {
  return useQuery<Role>({
    queryKey: roleKeys.detail(id),
    queryFn: async () => {
      const response = await roleApi.getById(id);
      return response.data;
    },
    enabled: !!id,
  });
}

// Legacy hooks for backward compatibility - these now use the mutations from useRoles
export function useCreateRole(options: UseCreateRoleOptions = {}) {
  const { onSuccess, onError } = options;
  const queryClient = useQueryClient();

  const mutation = useMutation<ResponseAxiosDetail<Role>, Error, CreateRolePayload>({
    mutationFn: async (data: CreateRolePayload) => {
      const response = await roleApi.create(data);
      return response;
    },
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: roleKeys.lists() });
      onSuccess?.(response.data);
    },
    onError,
  });

  return {
    ...mutation,
    isLoading: mutation.isPending,
    isSuccess: mutation.isSuccess,
    isError: mutation.isError,
  };
}

export function useUpdateRole(options: UseUpdateRoleOptions = {}) {
  const { onSuccess, onError } = options;
  const queryClient = useQueryClient();

  const mutation = useMutation<
    ResponseAxiosDetail<Role>,
    Error,
    { id: string; data: UpdateRolePayload }
  >({
    mutationFn: async ({ id, data }: { id: string; data: UpdateRolePayload }) => {
      const response = await roleApi.update(id, data);
      return response;
    },
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: roleKeys.lists() });
      queryClient.setQueryData(roleKeys.detail(response.data.id), response.data);
      onSuccess?.(response.data);
    },
    onError,
  });

  return {
    ...mutation,
    isLoading: mutation.isPending,
    isSuccess: mutation.isSuccess,
    isError: mutation.isError,
  };
}

export function useEditRole(options: UseUpdateRoleOptions = {}) {
  const { onSuccess, onError } = options;
  const queryClient = useQueryClient();

  const mutation = useMutation<
    ResponseAxiosDetail<Role>,
    Error,
    { id: string; data: UpdateRolePayload }
  >({
    mutationFn: async ({ id, data }: { id: string; data: UpdateRolePayload }) => {
      const response = await roleApi.update(id, data);
      return response;
    },
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: roleKeys.lists() });
      queryClient.setQueryData(roleKeys.detail(response.data.id), response.data);
      onSuccess?.(response.data);
    },
    onError,
  });

  return {
    ...mutation,
    isLoading: mutation.isPending,
    isSuccess: mutation.isSuccess,
    isError: mutation.isError,
  };
}

interface UseDeleteRoleOptions {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}
