"use client";

import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { ImageUpload } from "@/components/ui/image-upload";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { getFileSizeDisplay } from "@/utils/helpers/get-file-size";
import { cleanPhoneNumber, formatPhoneNumber } from "@/utils/helpers/number-formatter";

import { useProfile } from "../hooks/useProfile";
import { ProfileData } from "../types";
import { ProfileSkeleton } from "./skeleton/profile-skeleton";

interface ProfileViewProps {
  onEdit: () => void;
  onChangePassword: () => void;
}

export const ProfileView = ({ onEdit, onChangePassword }: ProfileViewProps) => {
  const { t } = useTranslation();
  const { profileData, isLoadingProfile, updateProfile, isLoading } = useProfile();

  const form = useForm<ProfileData>({
    defaultValues: {
      name: "",
      username: "",
      email: "",
      phone: null,
      avatar: null,
    },
  });

  // Update form data when profile data changes
  useEffect(() => {
    if (profileData) {
      form.reset(profileData);
    }
  }, [profileData, form]);

  const onSubmit = async (data: ProfileData) => {
    try {
      await updateProfile(data);
      // The hook will handle success/error messages and refetch data
    } catch (error) {
      console.error("Error saving profile:", error);
    }
  };

  // Check if form is dirty
  const isDirty = form.formState.isDirty;

  // Show loading state
  if (isLoadingProfile) {
    return <ProfileSkeleton />;
  }

  // Show error state if no profile data
  if (!profileData) {
    return (
      <Card className="border border-border">
        <CardContent className="p-6">
          <div className="flex items-center justify-center py-8">
            <div className="text-muted-foreground">Failed to load profile data</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <Card className="border border-border">
          <CardHeader className="pb-0">
            <div className="flex items-center justify-between gap-4">
              <div className="flex flex-col gap-1.5">
                <CardTitle className="text-sm font-medium text-foreground">
                  {t("pages.profile.contactInformation")}
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  {t("pages.profile.contactInformationDescription")}
                </p>
              </div>
              <Button variant="outline" onClick={onChangePassword} type="button">
                {t("pages.profile.changePassword")}
              </Button>
            </div>
          </CardHeader>

          <CardContent className="p-6 pt-4">
            <div className="space-y-4">
              {/* Avatar Section */}
              <div className="flex items-center gap-4">
                <div className="flex flex-col gap-4">
                  <Label className="text-sm font-medium text-foreground">
                    {t("pages.profile.avatar")}
                  </Label>
                  <div className="flex items-center gap-4">
                    <FormField
                      control={form.control}
                      name="avatar"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <ImageUpload
                              value={field.value || null}
                              onChange={field.onChange}
                              className="size-32 bg-transparent"
                              hideTotalSize
                              padding="p-0"
                              isSquare
                              hideBorderWhenHasImage
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <div className="flex flex-col gap-1">
                      <span className="text-xs text-muted-foreground">
                        {t("pages.profile.aspectRatio")} 1:1
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {t("pages.profile.formats")} PNG, JPG
                      </span>
                      {/* File size display for newly uploaded images */}
                      {(() => {
                        const currentValue = form.watch("avatar");
                        const isNewlyUploaded =
                          currentValue && currentValue.startsWith("data:image");

                        if (!isNewlyUploaded) return null;

                        const fileSizeDisplay = getFileSizeDisplay(currentValue);

                        return fileSizeDisplay ? (
                          <span className="text-xs text-muted-foreground">
                            {t("common.totalSize")}: {fileSizeDisplay} /5MB
                          </span>
                        ) : null;
                      })()}
                    </div>
                  </div>
                </div>
              </div>

              {/* Name Display */}
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem className="space-y-2">
                    <FormLabel className="text-sm font-medium text-foreground">
                      {t("pages.profile.name")}
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder={t("pages.profile.name")} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Username Display - Read Only */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-foreground">
                  {t("pages.profile.username")}
                </Label>
                <div className="rounded-md border border-border bg-muted px-3 py-2 text-sm text-foreground">
                  {form.watch("username")}
                </div>
              </div>

              {/* Contact Info Section */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-foreground">
                    {t("pages.profile.email")}
                  </Label>
                  <div className="flex h-10 items-center rounded-md border border-border bg-muted px-3 py-2 text-sm text-foreground">
                    {form.watch("email") || "--"}
                  </div>
                </div>
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel className="text-sm font-medium text-foreground">
                        {t("pages.profile.phone")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="tel"
                          value={field.value ? formatPhoneNumber(field.value) : ""}
                          placeholder={t("pages.profile.phone")}
                          onChange={(e) => {
                            // Clean and limit to 10 digits
                            const cleanValue = cleanPhoneNumber(e.target.value);
                            field.onChange(cleanValue);
                          }}
                          onBlur={(e) => {
                            // Format on blur for better UX
                            if (field.value) {
                              e.target.value = formatPhoneNumber(field.value);
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-end gap-2 pt-3">
            <Button type="submit" disabled={!isDirty} loading={isLoading}>
              {t("pages.profile.update")}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
};
