import { useMutation, useQueryClient } from "@tanstack/react-query";

import { employee<PERSON><PERSON> } from "@/lib/apis/employee";
import { ResponseAxiosDetail } from "@/lib/apis/types/common";
import { Employee as ApiEmployee } from "@/lib/apis/types/employee";

import { employee<PERSON>eys } from "./keys";

interface UseAddEmployeeOptions {
  onSuccess?: (data: ApiEmployee) => void;
  onError?: (error: Error) => void;
}

export function useAddEmployee(options: UseAddEmployeeOptions = {}) {
  const { onSuccess, onError } = options;
  const queryClient = useQueryClient();

  const mutation = useMutation<ResponseAxiosDetail<ApiEmployee>, Error, any>({
    mutationFn: async (data: any) => {
      // Transform the form data to match API structure
      const transformedData: any = {
        username: data.username,
        password: data.password,
        email: data.email || "",
        name: data.employeeName,
        phone_number: data.phone_number
          ? data.phone_number.startsWith("+84")
            ? data.phone_number
            : `+84${data.phone_number}`
          : "",
        address: data.address || "",
        birthdate: data.birthdate ? data.birthdate.toISOString().split("T")[0] : undefined,
        gender: "MALE", // Default value, can be made configurable later
      };

      // Only add user_roles if roles are assigned
      if (data.roles && data.roles.length > 0) {
        const userRole = data.roles.reduce((acc: Record<string, string[]>, role: any) => {
          if (role.branch && role.roles && role.roles.length > 0) {
            // Handle "all" branch option - send "all": [] instead of expanding
            if (role.branch === "all") {
              acc["all"] = role.roles;
            } else {
              acc[role.branch] = role.roles;
            }
          }
          return acc;
        }, {});

        // Only add user_roles if there are actual assignments
        if (Object.keys(userRole).length > 0) {
          transformedData.user_roles = userRole;
        }
      }

      return employeeApi.create(transformedData);
    },
    onSuccess: (data) => {
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: employeeKeys.list({}) });
      }, 1000);
      onSuccess?.(data.data);
    },
    onError: (error) => {
      onError?.(error);
    },
  });

  return {
    ...mutation,
    isLoading: mutation.isPending,
    isSuccess: mutation.isSuccess,
    isError: mutation.isError,
  };
}
