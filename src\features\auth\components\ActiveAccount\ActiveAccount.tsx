"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { CircleAlert } from "lucide-react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { z } from "zod";

import { BrandSection } from "@/features/auth/components/brand-section";
import { Footer } from "@/features/auth/components/public-footer";
import { useActiveAccount } from "@/features/auth/hooks/useActiveAccount";
import { changePasswordSchema } from "@/features/auth/utils/validators/activeAccount";

import Logo from "@/assets/logos/LogoTop.png";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

// Form validation schema

type ChangePasswordFormValues = z.infer<typeof changePasswordSchema>;

export const ActiveAccount = () => {
  const router = useRouter();
  const { t } = useTranslation();

  const { changePassword, isLoading, error, reset } = useActiveAccount();

  const form = useForm<ChangePasswordFormValues>({
    resolver: zodResolver(changePasswordSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  const onSubmit = async (data: ChangePasswordFormValues) => {
    try {
      await changePassword(data.currentPassword, data.newPassword);
    } catch (error) {
      // Error is handled in the hook
    }
  };

  const handleFocus = () => {
    reset();
  };

  return (
    <div className="min-h-screen w-screen bg-bg-secondary">
      <div className="grid min-h-screen w-screen lg:grid-cols-[1fr_2fr]">
        <BrandSection />
        <div className="relative flex flex-col justify-between p-8">
          <div className="flex flex-1 items-center justify-center">
            <div className="relative z-10 w-full max-w-[400px] space-y-6">
              <div className="space-y-1.5 text-center">
                <Image src={Logo} alt="logo" className="mx-auto size-8" />
                <h1 className="text-2xl font-semibold tracking-tight">
                  {t("auth.changePasswordTitle")}
                </h1>
                <p className="text-sm text-muted-foreground">{t("auth.changePasswordSubtitle")}</p>
              </div>

              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="currentPassword"
                      render={({ field, fieldState }) => (
                        <FormItem>
                          <FormLabel>
                            {t("auth.currentPassword")} <span className="text-destructive">*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              tabIndex={1}
                              placeholder={t("auth.currentPasswordPlaceholder")}
                              disabled={isLoading}
                              onFocus={() => handleFocus()}
                              type="password"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="newPassword"
                      render={({ field, fieldState }) => (
                        <FormItem>
                          <FormLabel>
                            {t("auth.newPassword")} <span className="text-destructive">*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              tabIndex={2}
                              placeholder={t("auth.newPasswordPlaceholder")}
                              disabled={isLoading}
                              onFocus={() => handleFocus()}
                              type="password"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="confirmPassword"
                      render={({ field, fieldState }) => (
                        <FormItem>
                          <FormLabel>
                            {t("auth.confirmPassword")} <span className="text-destructive">*</span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              tabIndex={3}
                              placeholder={t("auth.confirmPasswordPlaceholder")}
                              disabled={isLoading}
                              onFocus={() => handleFocus()}
                              type="password"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="space-y-2">
                    {error && (
                      <div className="flex items-center text-sm text-red-500">
                        <CircleAlert className="mr-2 size-4" />
                        {String(error) === "Incorrect username or password."
                          ? t("auth.incorrectUsernameOrPassword")
                          : t(String(error))}
                      </div>
                    )}
                    <Button
                      type="submit"
                      disabled={isLoading}
                      loading={isLoading}
                      className="w-full">
                      {t("common.submit")}
                    </Button>
                  </div>
                </form>
              </Form>

              <div className="space-x-2 text-center text-sm">
                <span>{t("auth.backToLogin")}</span>
                <Button
                  type="button"
                  variant="link"
                  className="h-fit p-0 text-sm font-normal leading-none"
                  onClick={() => router.push("/login")}
                  disabled={isLoading}>
                  {t("auth.login")}
                </Button>
              </div>
            </div>
          </div>

          <Footer />
        </div>
      </div>
    </div>
  );
};
