import { BaseEntity } from "../base";

export enum PropertyType {
  RESIDENTIAL = "RESIDENTIAL",
  COMMERCIAL = "COMMERCIAL",
  MIXED_USE = "MIXED_USE",
}

export enum PropertyStatus {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
}

export interface Owner {
  name: string;
  phone: string;
  email: string;
}

export interface PurchaseInformation {
  price: number;
  purchase_date: string;
}

export interface PropertyImage {
  id: string;
  url: string;
  name: string;
  is_primary: boolean;
}

export interface PropertyAddress {
  address1: string;
  zip?: string;
  province?: string;
  district?: string;
  ward?: string;
}

export interface Property extends BaseEntity {
  name: string;
  type: string;
  description?: string;
  address: PropertyAddress;
  status: string;
  owner: Owner;
  purchase_information?: PurchaseInformation;
  images?: PropertyImage[];
}

export interface CreateProperty {
  name: string;
  type: string;
  description?: string;
  address: PropertyAddress;
  status: string;
  owner: Owner;
  purchase_information?: PurchaseInformation;
  images?: Array<{
    name: string;
    image: string;
  }>;
}

export interface UpdateProperty {
  name?: string;
  type?: string;
  description?: string;
  address?: PropertyAddress;
  status?: string;
  owner?: Owner;
  purchase_information?: PurchaseInformation;
  images?: Array<{
    name: string;
    image: string;
  }>;
}
