"use client";

import { useLayoutEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { X } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { useLayout } from "@/components/providers";
import { Button } from "@/components/ui/button";
import { Form, useZodForm } from "@/components/ui/form";
import { authProtectedPaths } from "@/constants/paths";
import { CreateProperty, Property } from "@/lib/apis/types/property_assets/property";

import {
  createPropertySchema,
  updatePropertySchema,
  type CreatePropertyFormValues,
  type UpdatePropertyFormValues,
} from "../../../utils/validators/property";
import { useAddProperty, useUpdateProperty } from "../../hooks";
import { AddressSection } from "./AddressSection";
import { BasicInformationSection } from "./BasicInformationSection";
import { OwnerInformationSection } from "./OwnerInformationSection";
import { PurchaseInformationSection } from "./PurchaseInformationSection";

interface PropertyFormProps {
  initialData?: Property;
  isEditing?: boolean;
}

export function PropertyForm({ initialData, isEditing = false }: PropertyFormProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const { setFooter } = useLayout();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Mutations
  const createPropertyMutation = useAddProperty();
  const updatePropertyMutation = useUpdateProperty();

  // Form setup
  const form = useZodForm({
    schema: isEditing ? updatePropertySchema : createPropertySchema,
    defaultValues:
      isEditing && initialData
        ? {
            id: initialData.id,
            name: initialData.name,
            address: {
              address1: initialData.address?.address1 || "",
              province: initialData.address?.province || "",
              district: initialData.address?.district || "",
              ward: initialData.address?.ward || "",
            },
            type: initialData.type as "RESIDENTIAL" | "COMMERCIAL" | "MIXED_USE",
            status: initialData.status as "ACTIVE" | "INACTIVE",
            description: initialData.description,
            owner: {
              name: initialData.owner?.name || "",
              email: initialData.owner?.email || "",
              phone: initialData.owner?.phone || "",
            },
            purchase_information: initialData.purchase_information
              ? {
                  price: initialData.purchase_information.price,
                  purchase_date: initialData.purchase_information.purchase_date,
                }
              : undefined,
            images: initialData.images?.map((img) => ({ name: img.name, image: img.url })) || [],
          }
        : {
            name: "",
            address: {
              address1: "",
              province: "",
              district: "",
              ward: "",
            },
            type: "RESIDENTIAL" as const,
            description: "",
            owner: {
              name: "",
              email: "",
              phone: "",
            },
            purchase_information: undefined,
            images: [],
          },
  });

  // Form submission
  const onSubmit = async (values: CreatePropertyFormValues | UpdatePropertyFormValues) => {
    setIsSubmitting(true);

    try {
      if (isEditing && "id" in values) {
        await updatePropertyMutation.mutateAsync({ id: values.id, data: values } as any);
        toast.success(t("pages.properties.updateSuccess"));
      } else {
        // Transform form data to match API structure
        const transformedValues = {
          ...values,
          address: {
            address1: values.address?.address1 || "",
            zip: values.address?.zip || "",
            province: values.address?.province || "",
            district: values.address?.district || "",
            ward: values.address?.ward || "",
          },
        };

        await createPropertyMutation.mutateAsync(transformedValues as CreateProperty);
        toast.success(t("pages.properties.createSuccess"));
      }

      router.push(authProtectedPaths.PROPERTIES as any);
    } catch (error) {
      toast.error(
        isEditing ? t("pages.properties.updateError") : t("pages.properties.createError")
      );
      // TODO: Implement proper error logging
    } finally {
      setIsSubmitting(false);
    }
  };

  // Set footer using useLayoutEffect to avoid infinite loops
  useLayoutEffect(() => {
    const footerComponent = (
      <div className="flex w-full items-center justify-end">
        <div className="flex items-center gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push(authProtectedPaths.PROPERTIES as any)}
            className="h-9 px-4 py-2">
            <X className="mr-2 size-4" />
            {t("common.cancel")}
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting}
            loading={isSubmitting}
            form="property-form"
            className="h-9 px-4 py-2">
            {isEditing ? t("common.update") : t("common.add")}
          </Button>
        </div>
      </div>
    );

    setFooter(footerComponent);

    return () => {
      setFooter(null);
    };
  }, [setFooter, isSubmitting, isEditing, router, t]);

  return (
    <div className="flex h-fit flex-auto flex-col gap-4 overflow-y-auto p-4 pt-0">
      {/* Form Content */}
      <div className="flex-1">
        <Form {...form}>
          <form id="property-form" onSubmit={form.handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
              <div className="col-span-1 space-y-4 lg:col-span-2">
                <BasicInformationSection form={form} />
                <AddressSection form={form} />
              </div>

              {/* Right Column */}
              <div className="col-span-1 space-y-4">
                <OwnerInformationSection form={form} />
                <PurchaseInformationSection form={form} />
              </div>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
