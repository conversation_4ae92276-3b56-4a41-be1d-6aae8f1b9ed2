"use client";

import { LogOut, Zap } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Button } from "../button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "../dialog";
import type { ExpiredDialogProps } from "./expired-dialog.types";

/**
 * ExpiredDialog - A modal dialog that cannot be closed by user interaction
 * Users must either upgrade or logout when their plan has expired
 */
export function ExpiredDialog({
  open,
  onOpenChange,
  currentPlan = "Advance",
  onUpgrade,
  onLogout,
  onClose,
}: ExpiredDialogProps) {
  const { t } = useTranslation();

  const handleUpgrade = () => {
    onUpgrade?.();
    onOpenChange(false);
  };

  const handleLogout = () => {
    onLogout?.();
    onOpenChange(false);
  };

  const handleOpenChange = (newOpen: boolean) => {
    // Prevent dialog from being closed by user interaction
    if (!newOpen) {
      return;
    }
    onOpenChange(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-[500px] p-6" isShowClose={false}>
        <div className="relative">
          {/* Header */}
          <DialogHeader className="text-center">
            <DialogTitle className="text-center text-2xl font-semibold leading-none tracking-tight">
              {t("subscription.expired.planTitle", "Expired plan")}
            </DialogTitle>
            <DialogDescription className="mt-2 text-center text-sm text-muted-foreground">
              {t(
                "subscription.expired.planDescription",
                "Your current plan has expired. To continue using our service, please upgrade the plan."
              )}
            </DialogDescription>
          </DialogHeader>

          {/* Plan Information */}
          <div className="mt-6 space-y-4">
            {/* Expired Plan */}
            <div className="flex items-center gap-2">
              <div className="size-1 rounded-full bg-muted-foreground" />
              <span className="text-sm font-medium">
                {t("subscription.expired.expiredPlan", "Expired plan:")}
              </span>
              <span className="text-sm">{currentPlan}</span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="mt-6 flex flex-col gap-2">
            <Button
              onClick={handleUpgrade}
              className="w-full bg-primary text-primary-foreground hover:bg-primary/90"
              leftIcon={<Zap className="size-4" />}>
              {t("subscription.expired.upgrade", "Upgrade")}
            </Button>
            <Button
              variant="outline"
              onClick={handleLogout}
              className="w-full"
              leftIcon={<LogOut className="size-4" />}>
              {t("subscription.expired.logout", "Log out")}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Export the hook for external use
export { useExpiredDialog } from "./use-expired-dialog";
export type { ExpiredDialogProps, UseExpiredDialogProps } from "./expired-dialog.types";
