"use client";

import { useState } from "react";
import {
  Activity,
  AlertCircle,
  ArrowDownRight,
  ArrowUpRight,
  Award,
  BarChart3,
  Calendar,
  CheckCircle,
  DollarSign,
  Download,
  Eye,
  Home,
  Percent,
  Star,
  Target,
  TrendingDown,
  TrendingUp,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import {
  Area,
  Bar,
  BarChart,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface UnitRevenueTrackingProps {
  className?: string;
}

export function UnitRevenueTracking({ className }: UnitRevenueTrackingProps) {
  const { t } = useTranslation();
  const [selectedProperty, setSelectedProperty] = useState<string>("all");
  const [timeRange, setTimeRange] = useState<"month" | "quarter" | "year">("year");
  const [viewMode, setViewMode] = useState<"overview" | "detailed">("overview");
  const [selectedUnit, setSelectedUnit] = useState<any>(null);

  // Mock unit revenue data
  const revenueData = {
    portfolio: {
      totalRevenue: 2845680,
      monthlyRecurring: 387500,
      avgRevenuePerUnit: 3185,
      occupancyRevenue: 92.5,
      revenueGrowth: 8.3,
      topPerformer: "Unit 205",
      bottomPerformer: "Unit 103",
    },

    units: [
      {
        id: "1",
        unitNumber: "101",
        property: "Sunset Apartments",
        type: "1-Bedroom",
        monthlyRent: 2200,
        actualRevenue: 26400,
        projectedRevenue: 26400,
        occupancyRate: 100,
        revenuePerSqFt: 2.93,
        totalArea: 750,
        status: "Occupied",
        tenant: "John Smith",
        leaseStart: "2023-06-01",
        leaseEnd: "2024-05-31",
        performance: {
          score: 85,
          rank: 3,
          efficiency: 94.2,
          consistency: 100,
          growth: 5.8,
        },
        revenueHistory: [
          { month: "Jan 2024", revenue: 2200, projected: 2200, variance: 0 },
          { month: "Feb 2024", revenue: 2200, projected: 2200, variance: 0 },
          { month: "Mar 2024", revenue: 2200, projected: 2200, variance: 0 },
          { month: "Apr 2024", revenue: 2200, projected: 2200, variance: 0 },
          { month: "May 2024", revenue: 2200, projected: 2200, variance: 0 },
          { month: "Jun 2024", revenue: 2200, projected: 2200, variance: 0 },
          { month: "Jul 2024", revenue: 2200, projected: 2200, variance: 0 },
          { month: "Aug 2024", revenue: 2200, projected: 2200, variance: 0 },
          { month: "Sep 2024", revenue: 2200, projected: 2200, variance: 0 },
          { month: "Oct 2024", revenue: 2200, projected: 2200, variance: 0 },
          { month: "Nov 2024", revenue: 2200, projected: 2200, variance: 0 },
          { month: "Dec 2024", revenue: 2200, projected: 2200, variance: 0 },
        ],
      },
      {
        id: "2",
        unitNumber: "205",
        property: "Downtown Lofts",
        type: "2-Bedroom",
        monthlyRent: 3500,
        actualRevenue: 42000,
        projectedRevenue: 42000,
        occupancyRate: 100,
        revenuePerSqFt: 2.92,
        totalArea: 1200,
        status: "Occupied",
        tenant: "Sarah Johnson",
        leaseStart: "2023-09-01",
        leaseEnd: "2024-08-31",
        performance: {
          score: 92,
          rank: 1,
          efficiency: 98.1,
          consistency: 100,
          growth: 12.3,
        },
        revenueHistory: [
          { month: "Jan 2024", revenue: 3500, projected: 3500, variance: 0 },
          { month: "Feb 2024", revenue: 3500, projected: 3500, variance: 0 },
          { month: "Mar 2024", revenue: 3500, projected: 3500, variance: 0 },
          { month: "Apr 2024", revenue: 3500, projected: 3500, variance: 0 },
          { month: "May 2024", revenue: 3500, projected: 3500, variance: 0 },
          { month: "Jun 2024", revenue: 3500, projected: 3500, variance: 0 },
          { month: "Jul 2024", revenue: 3500, projected: 3500, variance: 0 },
          { month: "Aug 2024", revenue: 3500, projected: 3500, variance: 0 },
          { month: "Sep 2024", revenue: 3500, projected: 3500, variance: 0 },
          { month: "Oct 2024", revenue: 3500, projected: 3500, variance: 0 },
          { month: "Nov 2024", revenue: 3500, projected: 3500, variance: 0 },
          { month: "Dec 2024", revenue: 3500, projected: 3500, variance: 0 },
        ],
      },
      {
        id: "3",
        unitNumber: "103",
        property: "Garden View Complex",
        type: "Studio",
        monthlyRent: 1800,
        actualRevenue: 18000,
        projectedRevenue: 21600,
        occupancyRate: 83.3,
        revenuePerSqFt: 2.88,
        totalArea: 625,
        status: "Partially Vacant",
        tenant: "Recently Vacated",
        leaseStart: null,
        leaseEnd: null,
        performance: {
          score: 68,
          rank: 8,
          efficiency: 72.1,
          consistency: 83.3,
          growth: -8.5,
        },
        revenueHistory: [
          { month: "Jan 2024", revenue: 1800, projected: 1800, variance: 0 },
          { month: "Feb 2024", revenue: 1800, projected: 1800, variance: 0 },
          { month: "Mar 2024", revenue: 1800, projected: 1800, variance: 0 },
          { month: "Apr 2024", revenue: 0, projected: 1800, variance: -1800 },
          { month: "May 2024", revenue: 0, projected: 1800, variance: -1800 },
          { month: "Jun 2024", revenue: 1800, projected: 1800, variance: 0 },
          { month: "Jul 2024", revenue: 1800, projected: 1800, variance: 0 },
          { month: "Aug 2024", revenue: 1800, projected: 1800, variance: 0 },
          { month: "Sep 2024", revenue: 1800, projected: 1800, variance: 0 },
          { month: "Oct 2024", revenue: 1800, projected: 1800, variance: 0 },
          { month: "Nov 2024", revenue: 1800, projected: 1800, variance: 0 },
          { month: "Dec 2024", revenue: 1800, projected: 1800, variance: 0 },
        ],
      },
    ],

    propertyPerformance: [
      {
        property: "Sunset Apartments",
        units: 48,
        totalRevenue: 1267200,
        avgRevenue: 26400,
        occupancyRate: 95.8,
        revenuePerSqFt: 2.85,
        growth: 6.2,
        performance: 88,
      },
      {
        property: "Downtown Lofts",
        units: 36,
        totalRevenue: 1512000,
        avgRevenue: 42000,
        occupancyRate: 91.7,
        revenuePerSqFt: 3.15,
        growth: 12.8,
        performance: 92,
      },
      {
        property: "Garden View Complex",
        units: 41,
        totalRevenue: 738000,
        avgRevenue: 18000,
        occupancyRate: 89.0,
        revenuePerSqFt: 2.65,
        growth: -2.1,
        performance: 76,
      },
    ],

    marketComparison: [
      { category: "Luxury", avgRent: 3800, ourAvg: 3500, variance: -7.9 },
      { category: "Mid-Range", avgRent: 2400, ourAvg: 2200, variance: -8.3 },
      { category: "Budget", avgRent: 1950, ourAvg: 1800, variance: -7.7 },
      { category: "Studio", avgRent: 1750, ourAvg: 1800, variance: 2.9 },
      { category: "1-Bedroom", avgRent: 2350, ourAvg: 2200, variance: -6.4 },
      { category: "2-Bedroom", avgRent: 3200, ourAvg: 3500, variance: 9.4 },
    ],

    forecasting: {
      nextQuarter: {
        projected: 987500,
        conservative: 925000,
        optimistic: 1050000,
        confidence: 85,
      },
      yearEnd: {
        projected: 3950000,
        conservative: 3700000,
        optimistic: 4200000,
        confidence: 78,
      },
      factors: [
        { factor: "Market Trends", impact: 15, direction: "positive" },
        { factor: "Occupancy Rates", impact: 25, direction: "positive" },
        { factor: "Rent Increases", impact: 12, direction: "positive" },
        { factor: "Seasonal Demand", impact: 8, direction: "neutral" },
        { factor: "Competition", impact: -10, direction: "negative" },
        { factor: "Economic Outlook", impact: 5, direction: "positive" },
      ],
    },
  };

  const COLORS = ["#8884d8", "#82ca9d", "#ffc658", "#ff7300", "#8dd1e1", "#d084d0"];

  const getPerformanceColor = (score: number) => {
    if (score >= 90) return "text-success";
    if (score >= 80) return "text-primary";
    if (score >= 70) return "text-warning";
    if (score >= 60) return "text-secondary-foreground";
    return "text-destructive";
  };

  const getPerformanceBadge = (score: number) => {
    if (score >= 90) return { label: "Excellent", class: "bg-success/10 text-success" };
    if (score >= 80) return { label: "Good", class: "bg-primary/10 text-primary" };
    if (score >= 70) return { label: "Average", class: "bg-warning/10 text-warning" };
    if (score >= 60)
      return { label: "Below Average", class: "bg-secondary/10 text-secondary-foreground" };
    return { label: "Poor", class: "bg-destructive/10 text-destructive" };
  };

  const filteredUnits = revenueData.units.filter(
    (unit) => selectedProperty === "all" || unit.property === selectedProperty
  );

  const formatCurrency = (amount: number) => `$${amount.toLocaleString()}`;
  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-xl font-semibold text-foreground">Unit Revenue Tracking</h2>
          <p className="text-sm text-muted-foreground">
            Comprehensive revenue analytics, performance metrics, and forecasting
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedProperty} onValueChange={setSelectedProperty}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="All Properties" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Properties</SelectItem>
              <SelectItem value="Sunset Apartments">Sunset Apartments</SelectItem>
              <SelectItem value="Downtown Lofts">Downtown Lofts</SelectItem>
              <SelectItem value="Garden View Complex">Garden View Complex</SelectItem>
            </SelectContent>
          </Select>
          <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="month">Month</SelectItem>
              <SelectItem value="quarter">Quarter</SelectItem>
              <SelectItem value="year">Year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="mr-2 size-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Revenue Overview Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Revenue</p>
                <p className="text-2xl font-bold">
                  {formatCurrency(revenueData.portfolio.totalRevenue)}
                </p>
                <div className="mt-1 flex items-center space-x-1">
                  <TrendingUp className="size-3 text-success" />
                  <span className="text-xs text-success">
                    +{formatPercentage(revenueData.portfolio.revenueGrowth)}
                  </span>
                </div>
              </div>
              <div className="rounded-full bg-success/10 p-3">
                <DollarSign className="size-6 text-success" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Monthly Recurring</p>
                <p className="text-2xl font-bold">
                  {formatCurrency(revenueData.portfolio.monthlyRecurring)}
                </p>
                <div className="mt-1 flex items-center space-x-1">
                  <Activity className="size-3 text-primary" />
                  <span className="text-xs text-primary">Stable</span>
                </div>
              </div>
              <div className="rounded-full bg-primary/10 p-3">
                <Calendar className="size-6 text-primary" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg per Unit</p>
                <p className="text-2xl font-bold">
                  {formatCurrency(revenueData.portfolio.avgRevenuePerUnit)}
                </p>
                <div className="mt-1 flex items-center space-x-1">
                  <Target className="size-3 text-secondary-foreground" />
                  <span className="text-xs text-secondary-foreground">On target</span>
                </div>
              </div>
              <div className="rounded-full bg-secondary/10 p-3">
                <Home className="size-6 text-secondary-foreground" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Occupancy Revenue</p>
                <p className="text-2xl font-bold">
                  {formatPercentage(revenueData.portfolio.occupancyRevenue)}
                </p>
                <div className="mt-1 flex items-center space-x-1">
                  <CheckCircle className="size-3 text-success" />
                  <span className="text-xs text-success">Excellent</span>
                </div>
              </div>
              <div className="rounded-full bg-warning/10 p-3">
                <Percent className="size-6 text-warning" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Analytics */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Revenue Overview</TabsTrigger>
          <TabsTrigger value="performance">Unit Performance</TabsTrigger>
          <TabsTrigger value="comparison">Property Comparison</TabsTrigger>
          <TabsTrigger value="market">Market Analysis</TabsTrigger>
          <TabsTrigger value="forecasting">Forecasting</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
            {/* Revenue Trend Chart */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BarChart3 className="size-5" />
                  <span>Revenue vs Projected</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <ComposedChart data={revenueData.units[0].revenueHistory}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => [formatCurrency(Number(value)), ""]} />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="projected"
                      stroke="#8884d8"
                      fill="#8884d8"
                      fillOpacity={0.6}
                      name="Projected Revenue"
                    />
                    <Bar dataKey="revenue" fill="#82ca9d" name="Actual Revenue" />
                  </ComposedChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Top/Bottom Performers */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Award className="size-5" />
                  <span>Performance Highlights</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="rounded-lg border-l-4 border-success bg-success/5 p-4">
                  <div className="mb-2 flex items-center space-x-2">
                    <Star className="size-4 text-success" />
                    <span className="font-medium text-success">Top Performer</span>
                  </div>
                  <div className="text-sm">
                    <p className="font-semibold">{revenueData.portfolio.topPerformer}</p>
                    <p className="text-success/80">92% performance score</p>
                    <p className="text-success">{formatCurrency(42000)} annual revenue</p>
                  </div>
                </div>

                <div className="rounded-lg border-l-4 border-destructive bg-destructive/5 p-4">
                  <div className="mb-2 flex items-center space-x-2">
                    <AlertCircle className="size-4 text-destructive" />
                    <span className="font-medium text-destructive">Needs Attention</span>
                  </div>
                  <div className="text-sm">
                    <p className="font-semibold">{revenueData.portfolio.bottomPerformer}</p>
                    <p className="text-destructive/80">68% performance score</p>
                    <p className="text-destructive">83.3% occupancy rate</p>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-medium">Revenue Metrics</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Total Units:</span>
                      <span className="font-medium">125</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Occupied:</span>
                      <span className="font-medium">115</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Revenue/Sq Ft:</span>
                      <span className="font-medium">$2.91</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Revenue Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Revenue Distribution by Property</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                {revenueData.propertyPerformance.map((property, index) => (
                  <div key={index} className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{property.property}</h4>
                      <Badge className={`${getPerformanceBadge(property.performance).class}`}>
                        {getPerformanceBadge(property.performance).label}
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Revenue:</span>
                        <span className="font-medium">{formatCurrency(property.totalRevenue)}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Units:</span>
                        <span className="font-medium">{property.units}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Occupancy:</span>
                        <span className="font-medium">
                          {formatPercentage(property.occupancyRate)}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Growth:</span>
                        <span
                          className={`font-medium ${property.growth >= 0 ? "text-success" : "text-destructive"}`}>
                          {property.growth >= 0 ? "+" : ""}
                          {formatPercentage(property.growth)}
                        </span>
                      </div>
                    </div>
                    <Progress value={property.performance} className="h-2" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            {filteredUnits.map((unit) => (
              <Card key={unit.id} className="transition-shadow hover:shadow-lg">
                <CardContent className="p-6">
                  <div className="mb-4 flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold">Unit {unit.unitNumber}</h3>
                      <p className="text-sm text-muted-foreground">
                        {unit.property} • {unit.type}
                      </p>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Badge className={`${getPerformanceBadge(unit.performance.score).class}`}>
                        {getPerformanceBadge(unit.performance.score).label}
                      </Badge>
                      <Button variant="outline" size="sm" onClick={() => setSelectedUnit(unit)}>
                        <Eye className="mr-2 size-4" />
                        Details
                      </Button>
                    </div>
                  </div>

                  <div className="mb-4 grid grid-cols-2 gap-4 md:grid-cols-5">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-success">
                        {formatCurrency(unit.actualRevenue)}
                      </div>
                      <p className="text-xs text-muted-foreground">Annual Revenue</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">
                        {formatPercentage(unit.occupancyRate)}
                      </div>
                      <p className="text-xs text-muted-foreground">Occupancy</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-secondary-foreground">
                        ${unit.revenuePerSqFt.toFixed(2)}
                      </div>
                      <p className="text-xs text-muted-foreground">Revenue/Sq Ft</p>
                    </div>
                    <div className="text-center">
                      <div
                        className={`text-2xl font-bold ${getPerformanceColor(unit.performance.score)}`}>
                        {unit.performance.score}
                      </div>
                      <p className="text-xs text-muted-foreground">Performance</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-warning">
                        #{unit.performance.rank}
                      </div>
                      <p className="text-xs text-muted-foreground">Ranking</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <div className="mb-1 flex justify-between text-sm">
                        <span>Efficiency</span>
                        <span>{formatPercentage(unit.performance.efficiency)}</span>
                      </div>
                      <Progress value={unit.performance.efficiency} className="h-2" />
                    </div>
                    <div>
                      <div className="mb-1 flex justify-between text-sm">
                        <span>Consistency</span>
                        <span>{formatPercentage(unit.performance.consistency)}</span>
                      </div>
                      <Progress value={unit.performance.consistency} className="h-2" />
                    </div>
                    <div>
                      <div className="mb-1 flex justify-between text-sm">
                        <span>Growth</span>
                        <span
                          className={
                            unit.performance.growth >= 0 ? "text-success" : "text-destructive"
                          }>
                          {unit.performance.growth >= 0 ? "+" : ""}
                          {formatPercentage(unit.performance.growth)}
                        </span>
                      </div>
                      <Progress
                        value={Math.abs(unit.performance.growth) * 10}
                        className={`h-2 ${unit.performance.growth >= 0 ? "" : "bg-destructive/10"}`}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="comparison" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Property Performance Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={350}>
                <BarChart data={revenueData.propertyPerformance}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="property" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Legend />
                  <Bar
                    yAxisId="left"
                    dataKey="totalRevenue"
                    fill="#8884d8"
                    name="Total Revenue ($)"
                  />
                  <Line
                    yAxisId="right"
                    type="monotone"
                    dataKey="performance"
                    stroke="#ff7300"
                    name="Performance Score"
                  />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
            {revenueData.propertyPerformance.map((property, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="text-base">{property.property}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-success">
                      {formatCurrency(property.totalRevenue)}
                    </div>
                    <p className="text-sm text-muted-foreground">Total Annual Revenue</p>
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span>Units:</span>
                      <span className="font-medium">{property.units}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Avg Revenue:</span>
                      <span className="font-medium">{formatCurrency(property.avgRevenue)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Occupancy:</span>
                      <span className="font-medium">
                        {formatPercentage(property.occupancyRate)}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Revenue/Sq Ft:</span>
                      <span className="font-medium">${property.revenuePerSqFt.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Growth:</span>
                      <span
                        className={`font-medium ${property.growth >= 0 ? "text-success" : "text-destructive"}`}>
                        {property.growth >= 0 ? "+" : ""}
                        {formatPercentage(property.growth)}
                      </span>
                    </div>
                  </div>

                  <div>
                    <div className="mb-2 flex justify-between text-sm">
                      <span>Performance Score</span>
                      <span className="font-medium">{property.performance}%</span>
                    </div>
                    <Progress value={property.performance} className="h-3" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="market" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Market Rent Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {revenueData.marketComparison.map((item, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between rounded-lg border p-4">
                    <div className="flex-1">
                      <h4 className="font-medium">{item.category}</h4>
                      <div className="mt-1 flex items-center space-x-4">
                        <span className="text-sm text-muted-foreground">
                          Market: {formatCurrency(item.avgRent)}
                        </span>
                        <span className="text-sm text-muted-foreground">
                          Our Avg: {formatCurrency(item.ourAvg)}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge
                        className={`${item.variance >= 0 ? "bg-success/10 text-success" : "bg-destructive/10 text-destructive"}`}>
                        {item.variance >= 0 ? "+" : ""}
                        {formatPercentage(item.variance)}
                      </Badge>
                      {item.variance >= 0 ? (
                        <ArrowUpRight className="size-4 text-success" />
                      ) : (
                        <ArrowDownRight className="size-4 text-destructive" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="forecasting" className="space-y-4">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="size-5" />
                  <span>Revenue Forecasting</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h4 className="mb-3 font-medium">Next Quarter Projection</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm">Conservative:</span>
                      <span className="font-medium">
                        {formatCurrency(revenueData.forecasting.nextQuarter.conservative)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Projected:</span>
                      <span className="font-medium text-primary">
                        {formatCurrency(revenueData.forecasting.nextQuarter.projected)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Optimistic:</span>
                      <span className="font-medium">
                        {formatCurrency(revenueData.forecasting.nextQuarter.optimistic)}
                      </span>
                    </div>
                    <div className="mt-3">
                      <div className="mb-1 flex justify-between text-sm">
                        <span>Confidence Level</span>
                        <span>{revenueData.forecasting.nextQuarter.confidence}%</span>
                      </div>
                      <Progress
                        value={revenueData.forecasting.nextQuarter.confidence}
                        className="h-2"
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="mb-3 font-medium">Year-End Projection</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm">Conservative:</span>
                      <span className="font-medium">
                        {formatCurrency(revenueData.forecasting.yearEnd.conservative)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Projected:</span>
                      <span className="font-medium text-primary">
                        {formatCurrency(revenueData.forecasting.yearEnd.projected)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Optimistic:</span>
                      <span className="font-medium">
                        {formatCurrency(revenueData.forecasting.yearEnd.optimistic)}
                      </span>
                    </div>
                    <div className="mt-3">
                      <div className="mb-1 flex justify-between text-sm">
                        <span>Confidence Level</span>
                        <span>{revenueData.forecasting.yearEnd.confidence}%</span>
                      </div>
                      <Progress
                        value={revenueData.forecasting.yearEnd.confidence}
                        className="h-2"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Impact Factors</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {revenueData.forecasting.factors.map((factor, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{factor.factor}</span>
                        <div className="flex items-center space-x-2">
                          <span
                            className={`text-sm font-medium ${
                              factor.direction === "positive"
                                ? "text-success"
                                : factor.direction === "negative"
                                  ? "text-destructive"
                                  : "text-muted-foreground"
                            }`}>
                            {factor.impact >= 0 ? "+" : ""}
                            {factor.impact}%
                          </span>
                          {factor.direction === "positive" && (
                            <TrendingUp className="size-4 text-success" />
                          )}
                          {factor.direction === "negative" && (
                            <TrendingDown className="size-4 text-destructive" />
                          )}
                          {factor.direction === "neutral" && (
                            <Activity className="size-4 text-muted-foreground" />
                          )}
                        </div>
                      </div>
                      <Progress
                        value={Math.abs(factor.impact) * 4}
                        className={`h-2 ${
                          factor.direction === "negative"
                            ? "bg-destructive/10"
                            : factor.direction === "positive"
                              ? "bg-success/10"
                              : "bg-muted"
                        }`}
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Unit Detail Modal */}
      <Dialog open={!!selectedUnit} onOpenChange={() => setSelectedUnit(null)}>
        <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
          {selectedUnit && (
            <div>
              <DialogHeader>
                <DialogTitle>Unit {selectedUnit.unitNumber} Revenue Analysis</DialogTitle>
                <DialogDescription>
                  {selectedUnit.property} • {selectedUnit.type}
                </DialogDescription>
              </DialogHeader>

              <div className="mt-6 space-y-6">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-success">
                        {formatCurrency(selectedUnit.actualRevenue)}
                      </div>
                      <p className="text-sm text-muted-foreground">Annual Revenue</p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div className="text-2xl font-bold text-primary">
                        {formatPercentage(selectedUnit.occupancyRate)}
                      </div>
                      <p className="text-sm text-muted-foreground">Occupancy Rate</p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <div
                        className={`text-2xl font-bold ${getPerformanceColor(selectedUnit.performance.score)}`}>
                        {selectedUnit.performance.score}%
                      </div>
                      <p className="text-sm text-muted-foreground">Performance Score</p>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader>
                    <CardTitle>Monthly Revenue Trend</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart data={selectedUnit.revenueHistory}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <Tooltip formatter={(value) => [formatCurrency(Number(value)), ""]} />
                        <Legend />
                        <Line
                          type="monotone"
                          dataKey="projected"
                          stroke="#8884d8"
                          name="Projected"
                        />
                        <Line type="monotone" dataKey="revenue" stroke="#82ca9d" name="Actual" />
                      </LineChart>
                    </ResponsiveContainer>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
