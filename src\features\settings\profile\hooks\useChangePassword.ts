import { useRouter } from "next/navigation";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { ChangePasswordPayload } from "@/features/auth/types";

import { authApi } from "@/lib/apis/auth";
import { getAuthToken } from "@/lib/auth";

export const useChangePassword = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const router = useRouter();
  const changePasswordMutation = useMutation({
    mutationFn: async (payload: Omit<ChangePasswordPayload, "username">) => {
      const authData = getAuthToken();
      if (!authData?.Username) {
        throw new Error("Authentication data not found");
      }

      const fullPayload: ChangePasswordPayload = {
        username: authData.Username,
        password: payload.password,
        new_password: payload.new_password,
      };

      const response = await authApi.changePassword(fullPayload);
      return response;
    },
    onSuccess: (data) => {
      toast.success(t("common.success"), {
        description: t("pages.profile.changePasswordDiaglog.passwordChangedSuccessfully"),
      });
    },
    onError: (error: any) => {
      console.log(error);
      if (error === "Password attempts exceeded") {
        toast.error(t("common.error"), {
          description: t("pages.profile.changePasswordDiaglog.passwordAttemptsExceeded"),
        });
        return;
      }

      if (error === "Incorrect username or password.") {
        toast.error(t("common.error"), {
          description: t("pages.profile.changePasswordDiaglog.passwordDoesNotMatch"),
        });
        return;
      }

      const errorMessage = t("pages.profile.changePasswordDiaglog.passwordChangeFailed");

      toast.error(t("common.error"), {
        description: errorMessage || error,
      });
    },
  });

  const changePassword = async (password: string, new_password: string) => {
    return changePasswordMutation.mutateAsync({ password, new_password });
  };

  return {
    changePassword,
    isLoading: changePasswordMutation.isPending,
    error: changePasswordMutation.error,
    reset: changePasswordMutation.reset,
  };
};
