import { USERS_ENDPOINTS } from "@/constants/endpoints";

import { privateApi } from "../api_helper";
import { ResponseAxiosDetail } from "./types/common";
import {
  CreateEmployeePayload,
  Employee,
  EmployeeListParams,
  EmployeeListResponse,
  UpdateEmployeePayload,
} from "./types/employee";

export const employeeApi = {
  // New method for Employee API response structure
  list: async (params?: EmployeeListParams): Promise<EmployeeListResponse> => {
    const response = await privateApi.get<EmployeeListResponse>(USERS_ENDPOINTS.LIST, { params });
    return response;
  },

  getById: async (userId: string): Promise<Employee> => {
    const response = await privateApi.get<Employee>(USERS_ENDPOINTS.GET_BY_ID(userId));
    return response;
  },

  update: async (userId: string, data: UpdateEmployeePayload): Promise<Employee> => {
    const response = await privateApi.put<Employee>(USERS_ENDPOINTS.UPDATE(userId), data);
    return response;
  },

  // New method for Employee API response structure
  getEmployeeById: async (employeeId: string): Promise<Employee> => {
    const response = await privateApi.get<Employee>(USERS_ENDPOINTS.GET_BY_ID(employeeId));
    return response;
  },

  deactivate: async (userId: string): Promise<ResponseAxiosDetail<Employee>> => {
    const response = await privateApi.post<ResponseAxiosDetail<Employee>>(
      USERS_ENDPOINTS.DEACTIVATE(userId)
    );
    return response;
  },

  activate: async (userId: string): Promise<ResponseAxiosDetail<Employee>> => {
    const response = await privateApi.post<ResponseAxiosDetail<Employee>>(
      USERS_ENDPOINTS.ACTIVATE(userId)
    );
    return response;
  },

  delete: async (userId: string): Promise<Employee> => {
    const response = await privateApi.delete<Employee>(USERS_ENDPOINTS.DELETE(userId));
    return response;
  },

  setPassword: async (username: string, password: string): Promise<Employee> => {
    const response = await privateApi.post<Employee>(USERS_ENDPOINTS.SET_PASSWORD, {
      username,
      password,
    });
    return response;
  },
  create: async (data: CreateEmployeePayload): Promise<ResponseAxiosDetail<Employee>> => {
    const response = await privateApi.post<ResponseAxiosDetail<Employee>>(
      USERS_ENDPOINTS.CREATE,
      data
    );
    return response;
  },
};
