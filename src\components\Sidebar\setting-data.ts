import { Globe, Palette, Shield, Store, Users } from "lucide-react";

export interface SettingsNavItem {
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  url: string;
  category: string;
}

export const settingsNavItems: SettingsNavItem[] = [
  {
    title: "pages.settings.profileSettings",
    description: "pages.settings.profileSettingsDescription",
    icon: Users,
    url: "/settings/profile",
    category: "settings",
  },
  {
    title: "pages.settings.storeInformation",
    description: "pages.settings.storeInformationDescription",
    icon: Store,
    url: "/settings/store-information",
    category: "settings",
  },
  {
    title: "pages.settings.appearance",
    description: "pages.settings.appearanceDescription",
    icon: Palette,
    url: "/settings/theme",
    category: "settings",
  },
  {
    title: "pages.settings.languageCurrency",
    description: "pages.settings.languageCurrencyDescription",
    icon: Globe,
    url: "/settings/language-currency",
    category: "settings",
  },
  {
    title: "pages.settings.employeesPermissions",
    description: "pages.settings.employeesPermissionsDescription",
    icon: Shield,
    url: "/settings/employee-permission",
    category: "settings",
  },
];
