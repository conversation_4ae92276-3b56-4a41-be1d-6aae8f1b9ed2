export interface VerificationStepProps {
  /** User's email address for verification */
  email: string;
  /** Current verification code input value */
  verificationCode: string;
  /** Loading state for verification process */
  isLoading: boolean;
  /** Callback when verification code input changes */
  onVerificationCodeChange: (code: string) => void;
  /** Callback when verify code button is clicked */
  onVerifyCode: () => void;
  /** Callback when resend code button is clicked */
  onResendCode: () => void;
  /** Optional callback when close button is clicked */
  onClose?: () => void;
}
