"use client";

import { MessageCircle } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Button } from "@/components/ui";
import { cn } from "@/lib/utils";

interface RelatedQuestionsProps {
  questions: string[];
  onQuestionClick: (question: string) => void;
  className?: string;
  themeColor?: string;
  messageId?: string;
  isClickedMessage?: boolean;
  isSending?: boolean;
}

export function RelatedQuestions({
  questions,
  onQuestionClick,
  className,
  themeColor,
  messageId,
  isClickedMessage,
  isSending = false,
}: RelatedQuestionsProps) {
  const { t } = useTranslation();

  if (!questions || questions.length === 0) {
    return null;
  }

  return (
    <div className={cn("mt-1 space-y-1", className)}>
      {/* Optional header */}
      {/* <div className="flex items-center gap-2 text-xs text-muted-foreground">
        <HelpCircle className="size-3" />
        <span>{t("pages.conversation.relatedQuestions", "Related questions")}</span>
      </div> */}

      {/* Question cards */}
      <div className="flex flex-col gap-1">
        {questions.map((question, index) => (
          <Button
            key={index}
            onClick={() => onQuestionClick(question)}
            disabled={isSending}
            variant="ghost"
            className="h-auto w-fit justify-start rounded-lg border border-primary">
            {/* Question text */}
            <span className="text-wrap text-left">{question}</span>
          </Button>
        ))}
      </div>
    </div>
  );
}

/**
 * Compact version for inline display
 */
export function RelatedQuestionsCompact({
  questions,
  onQuestionClick,
  className,
}: RelatedQuestionsProps) {
  if (!questions || questions.length === 0) {
    return null;
  }

  return (
    <div className={cn("mt-2 flex flex-wrap gap-2", className)}>
      {questions.map((question, index) => (
        <button
          key={index}
          onClick={() => onQuestionClick?.(question)}
          className={cn(
            "inline-flex items-center gap-1.5 rounded-full",
            "border border-border bg-muted/30 px-3 py-1.5",
            "text-xs text-foreground/80 transition-all",
            "hover:border-primary/50 hover:bg-primary/10 hover:text-primary",
            "focus:outline-none focus:ring-2 focus:ring-primary/20"
          )}>
          <MessageCircle className="size-3" />
          <span className="max-w-[200px] truncate">{question}</span>
        </button>
      ))}
    </div>
  );
}
