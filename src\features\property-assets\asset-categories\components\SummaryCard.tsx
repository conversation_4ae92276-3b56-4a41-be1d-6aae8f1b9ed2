"use client";

import { Card, CardContent } from "@/components/ui/card";

import { getIconComponent } from "../utils/iconHelpers";

interface SummaryCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: string;
  iconColor?: string;
  trend?: {
    value: string;
    isPositive: boolean;
  };
  trendIcon?: string;
}

export function SummaryCard({
  title,
  value,
  subtitle,
  icon,
  iconColor = "text-blue-600",
  trend,
  trendIcon,
}: SummaryCardProps) {
  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            {subtitle && <p className="text-xs text-green-600">{subtitle}</p>}
            {trend && (
              <div className="mt-1 flex items-center space-x-1">
                {trendIcon && (
                  <div className={`size-3 ${iconColor}`}>{getIconComponent(trendIcon)}</div>
                )}
                <span className={`text-xs ${trend.isPositive ? "text-green-600" : "text-red-600"}`}>
                  {trend.value}
                </span>
              </div>
            )}
          </div>
          <div className={`size-8 ${iconColor}`}>{getIconComponent(icon)}</div>
        </div>
      </CardContent>
    </Card>
  );
}
