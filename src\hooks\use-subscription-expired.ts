import { useMemo } from "react";

interface Subscription {
  status: string;
  expires_date?: string;
}

export const useSubscriptionExpired = (subscription: Subscription | null) => {
  const isExpired = useMemo(() => {
    if (!subscription) return false;
    try {
      // Case 1: Status is EXPIRED
      if (subscription.status === "EXPIRED") {
        return true;
      }
      const expiresDate = new Date(subscription.expires_date || "");
      const currentDate = new Date();
      return expiresDate < currentDate;
    } catch (error) {
      console.error("Error checking subscription expiration:", error);
      return false;
    }
  }, [subscription]);

  return { isExpired };
};
