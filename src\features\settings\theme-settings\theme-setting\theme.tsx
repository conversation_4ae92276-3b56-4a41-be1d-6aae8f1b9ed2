import { useState } from "react";
import Image from "next/image";
import { t } from "i18next";
import { useTheme } from "next-themes";

import DarkPreview from "@/assets/images/DarkPreview.png";
import LightPreview from "@/assets/images/LightPreview.png";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface ThemeOption {
  id: string;
  name: string;
  preview: {
    bgColor: string;
    textColor: string;
    borderColor: string;
    image: any;
  };
}

const themeOptions: ThemeOption[] = [
  {
    id: "light",
    name: "Light",
    preview: {
      bgColor: "bg-gray-100",
      textColor: "text-gray-900",
      borderColor: "border-primary",
      image: LightPreview,
    },
  },
  {
    id: "dark",
    name: "Dark",
    preview: {
      bgColor: "bg-gray-800",
      textColor: "text-gray-100",
      borderColor: "border-neutral-200",
      image: DarkPreview,
    },
  },
];

export default function ThemeSetting() {
  const { theme, setTheme } = useTheme();
  const [selectedTheme, setSelectedTheme] = useState(theme || "light");

  const handleThemeChange = (themeId: string) => {
    setSelectedTheme(themeId);
  };

  const handleUpdate = () => {
    setTheme(selectedTheme);
    // TODO: Show success message or handle any additional logic
  };

  return (
    <Card className="border border-border">
      <CardHeader className="pb-0">
        <div className="flex flex-col gap-1.5">
          <CardTitle className="text-sm font-medium text-foreground">
            {t("pages.settings.theme.title")}
          </CardTitle>
          <CardDescription className="text-sm text-muted-foreground">
            {t("pages.settings.theme.description")}
          </CardDescription>
        </div>
      </CardHeader>

      <CardContent className="p-6 pt-4">
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
          {themeOptions.map((themeOption) => (
            <div key={themeOption.id} className="space-y-3">
              <div
                className={`cursor-pointer rounded-lg border-2 p-1 transition-all hover:scale-105 ${
                  selectedTheme === themeOption.id
                    ? "border-primary ring-2 ring-primary/20"
                    : "border-neutral-200 hover:border-neutral-300"
                }`}
                onClick={() => handleThemeChange(themeOption.id)}>
                {/* Theme Preview Image */}
                <div className="relative h-32 w-full overflow-hidden rounded-md">
                  <Image
                    src={themeOption.preview.image}
                    alt={`${themeOption.name} theme preview`}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, 50vw"
                  />
                </div>
              </div>

              {/* Theme name positioned outside and below the card */}
              <div className="text-center">
                <span className="text-sm font-medium text-foreground">{themeOption.name}</span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>

      <CardFooter className="flex justify-end pt-3">
        <Button disabled={theme === selectedTheme} onClick={handleUpdate}>
          {t("common.update")}
        </Button>
      </CardFooter>
    </Card>
  );
}
