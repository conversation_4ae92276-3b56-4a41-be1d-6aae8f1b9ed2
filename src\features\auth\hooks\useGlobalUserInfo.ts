"use client";

import { useCallback, useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";

import { useUserInfo } from "@/hooks/use-user";

export interface UserInfoData {
  username: string;
  email: string;
  name: string;
  role: string;
  companyId: string;
  plan: string;
  isInit: string;
  onboarding: string;
}

export const useGlobalUserInfo = () => {
  const router = useRouter();
  const pathname = usePathname();
  const [userInfo, setUserInfo] = useState<UserInfoData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasCheckedUserInfo, setHasCheckedUserInfo] = useState(false);
  const [isWaitingForInit, setIsWaitingForInit] = useState(false);

  // Move hook call to top level
  const { userInfo: userInfoResponse } = useUserInfo();

  const checkUserInfo = useCallback(async () => {
    try {
      setIsLoading(true);

      const response = userInfoResponse;

      if (response?.UserAttributes) {
        const attributes = response.UserAttributes;

        // Extract user attributes
        const userData: UserInfoData = {
          username: response.Username || "",
          email: attributes.find((attr) => attr.Name === "email")?.Value || "",
          name: attributes.find((attr) => attr.Name === "name")?.Value || "",
          role: attributes.find((attr) => attr.Name === "custom:role")?.Value || "",
          companyId: attributes.find((attr) => attr.Name === "custom:company_id")?.Value || "",
          plan: attributes.find((attr) => attr.Name === "custom:plan")?.Value || "",
          isInit: attributes.find((attr) => attr.Name === "custom:is_init")?.Value || "",
          onboarding: attributes.find((attr) => attr.Name === "custom:onboarding")?.Value || "",
        };

        setUserInfo(userData);

        // Store user info in localStorage
        localStorage.setItem("userInfo", JSON.stringify(userData));

        // First check user role - only Admin needs onboarding
        if (userData.role === "Admin") {
          // Check onboarding status only for Admin users
          if (userData.onboarding !== "COMPLETE" && userData.onboarding !== "COMPLETED") {
            // Force navigation to onboarding regardless of current path
            router.replace("/onboarding");
            return;
          }
        }

        // Check initialization status for all users (regardless of role)
        if (userData.role === "Admin") {
          if (userData.isInit !== "DONE") {
            setIsWaitingForInit(true);
            // Show loading and continue calling API until is_init is DONE
            setTimeout(() => {
              checkUserInfo();
            }, 5000); // Check again in 5 seconds
            return;
          }
        }

        // Both onboarding and initialization are complete
        setIsWaitingForInit(false);

        // Dispatch custom event to notify components about user info change
        window.dispatchEvent(new Event("auth-changed"));
      }
    } catch (error) {
      console.error("Failed to fetch user info:", error);
      setUserInfo(null);
      localStorage.removeItem("userInfo");
    } finally {
      setIsLoading(false);
    }
  }, [router, userInfoResponse]);

  const refreshUserInfo = useCallback(() => {
    checkUserInfo();
  }, [checkUserInfo]);

  // Get user info from localStorage
  const getUserInfoFromStorage = useCallback((): UserInfoData | null => {
    try {
      const data = localStorage.getItem("userInfo");
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error("Failed to parse user info data from localStorage:", error);
      return null;
    }
  }, []);

  // Check if user has completed onboarding
  const hasCompletedOnboarding = useCallback((): boolean => {
    const storedUserInfo = getUserInfoFromStorage();
    if (!storedUserInfo) return false;

    return storedUserInfo.onboarding === "COMPLETE" || storedUserInfo.onboarding === "COMPLETED";
  }, [getUserInfoFromStorage]);

  // Check if user has completed initialization
  const hasCompletedInit = useCallback((): boolean => {
    const storedUserInfo = getUserInfoFromStorage();
    if (!storedUserInfo) return false;

    return storedUserInfo.isInit === "DONE";
  }, [getUserInfoFromStorage]);

  // Clear user info data
  const clearUserInfoData = useCallback(() => {
    localStorage.removeItem("userInfo");
    setUserInfo(null);
    setIsWaitingForInit(false);
  }, []);

  useEffect(() => {
    // Check user info only once on mount
    if (!hasCheckedUserInfo) {
      checkUserInfo();
      setHasCheckedUserInfo(true);
    }
  }, [checkUserInfo, hasCheckedUserInfo]);

  // Listen for auth changes to re-check user info (only when auth actually changes)
  useEffect(() => {
    const handleAuthChange = () => {
      // Only re-check if we haven't already checked or if user info is null
      if (!hasCheckedUserInfo || !userInfo) {
        checkUserInfo();
      }
    };

    window.addEventListener("auth-changed", handleAuthChange);
    return () => window.removeEventListener("auth-changed", handleAuthChange);
  }, [checkUserInfo, hasCheckedUserInfo, userInfo]);

  return {
    // State
    userInfo,
    isLoading,
    hasCheckedUserInfo,
    isWaitingForInit,

    // Actions
    checkUserInfo,
    refreshUserInfo,
    clearUserInfoData,

    // Storage helpers
    getUserInfoFromStorage,
    hasCompletedOnboarding,
    hasCompletedInit,
  };
};
