"use client";

import { useState } from "react";
import { Plus } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import { useCreateCategory } from "../../hooks/blog-category";

export function CreateCategory() {
  const [isCreating, setIsCreating] = useState(false);
  const [categoryName, setCategoryName] = useState("");
  const createCategory = useCreateCategory();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (categoryName.trim()) {
      createCategory.mutate(
        { name: categoryName.trim() },
        {
          onSuccess: () => {
            setCategoryName("");
            setIsCreating(false);
          },
        }
      );
    }
  };

  const handleCancel = () => {
    setCategoryName("");
    setIsCreating(false);
  };

  if (!isCreating) {
    return (
      <Button
        onClick={() => setIsCreating(true)}
        className="bg-orange-600 text-white hover:bg-orange-700">
        <Plus className="mr-2 size-4" />
        Add Category
      </Button>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      <Input
        value={categoryName}
        onChange={(e) => setCategoryName(e.target.value)}
        placeholder="Enter category name..."
        autoFocus
        disabled={createCategory.isPending}
      />
      <div className="flex gap-2">
        <Button
          type="submit"
          size="sm"
          disabled={!categoryName.trim() || createCategory.isPending}
          className="bg-orange-600 text-white hover:bg-orange-700">
          {createCategory.isPending ? "Creating..." : "Create"}
        </Button>
        <Button
          type="button"
          size="sm"
          variant="outline"
          onClick={handleCancel}
          disabled={createCategory.isPending}>
          Cancel
        </Button>
      </div>
    </form>
  );
}
