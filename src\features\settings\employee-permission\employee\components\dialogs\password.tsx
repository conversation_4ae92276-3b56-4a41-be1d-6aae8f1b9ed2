"use client";

import { useTranslation } from "react-i18next";

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";

// Password Component
interface PasswordProps {
  mode?: "add" | "edit";
}

export function Password({ mode }: PasswordProps) {
  const { t } = useTranslation();

  return (
    <div className="space-y-2">
      <div className="grid grid-cols-2 gap-4">
        <FormField
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>
                {mode === "edit"
                  ? t("pages.settings.newPassword", "New Password")
                  : t("pages.settings.password", "Password")}
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <Input
                    type={"password"}
                    placeholder={mode === "edit" ? "Enter new password" : "••••••••••"}
                    className="pr-10"
                    {...field}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          name="confirmPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>
                {mode === "edit"
                  ? t("pages.settings.confirmNewPassword", "Confirm New Password")
                  : t("pages.settings.confirmPassword", "Confirm Password")}
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <Input
                    type={"password"}
                    placeholder={mode === "edit" ? "Confirm new password" : "••••••••••"}
                    className="pr-10"
                    {...field}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
