// Plan types
export interface PlanDuration {
  price: number;
  sale_price: number;
}

export interface PlanQuota {
  max_products: number;
  max_orders: number;
}

export interface PlanFeatures {
  [key: string]: boolean;
}

export interface PlanDurationConfig {
  YEARLY: PlanDuration;
  MONTHLY: PlanDuration;
}

export interface Plan {
  id: string;
  name: string;
  description?: string;
  is_free_plan: boolean;
  is_popular: boolean;
  trial_days: number;
  order: number;
  logo?: string | null;
  service_id: string;
  created_at: string;
  updated_at?: string | null;
  features: PlanFeatures;
  quota: PlanQuota;
  duration: PlanDurationConfig;
  service: Service;
}

export interface CreatePlanRequest {
  name: string;
  description?: string;
  is_free_plan?: boolean;
  is_popular?: boolean;
  trial_days?: number;
  order?: number;
  logo?: string;
  service_id: string;
  features: PlanFeatures;
  quota: PlanQuota;
  duration: PlanDurationConfig;
}

export type UpdatePlanRequest = Partial<CreatePlanRequest>;

// Service types
export interface Service {
  id?: string;
  name: string;
  description?: string;
  price?: number;
  duration?: number;
  category?: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
  currency?: string;
}

export interface CreateServiceRequest {
  name: string;
  description?: string;
  price?: number;
  duration?: number;
  category?: string;
  is_active?: boolean;
}

export type UpdateServiceRequest = Partial<CreateServiceRequest>;

// Subscription types
export interface Invoice {
  company_name: string;
  tax_number: string;
  address: string;
  company_email: string;
}

export interface Subscription {
  id: string;
  company_id: string;
  status: "ACTIVE" | "CANCELLED" | "EXPIRED" | "PENDING" | "TRIAL";
  activation_date: string;
  billing_cycle: "MONTHLY" | "YEARLY";
  expires_date: string;
  external_id: string;
  contract_content?: string | null;
  canceled_reason?: string | null;
  contract_number?: string | null;
  created_at: string;
  canceled_at?: string | null;
  charge_info?: any | null;
  renewal_type: string;
  updated_at: string;
  plan: Plan;
  service: Service;
  note?: string | null;
  previous_subscription_id?: string | null;
  used_quota: {
    max_products: string;
    max_orders: string;
  };
  qr_expired_at: string;
  invoice?: Invoice | null;
  contract_annex?: any | null;
  qr_info: QRInfo;
}

interface QRInfo {
  qr_code: string;
  account_number: string;
  account_name: string;
  bank_name: string;
  amount: number;
  description: string;
  qr_expired_at: string;
}

export interface CreateSubscriptionRequest {
  plan_id: string;
  billing_cycle: "MONTHLY" | "YEARLY";
  cancel_url?: string;
  return_url?: string;
  invoice?: Invoice;
}

export interface CancelSubscriptionRequest {
  // Empty object as per the curl example
  reason?: string;
}
