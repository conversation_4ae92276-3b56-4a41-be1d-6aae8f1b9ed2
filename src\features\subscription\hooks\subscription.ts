import { useQuery } from "@tanstack/react-query";

import { subscriptionApi } from "@/lib/apis/crm_plan/subscription";

// Polling hook for subscription by ID
export function useSubscriptionByIdPolling(id: string, enabled: boolean, refetchInterval = 3000) {
  return useQuery({
    queryKey: ["subscription", id],
    queryFn: () => subscriptionApi.getById(id).then((res) => res.data),
    enabled: !!id && enabled,
    refetchInterval: enabled ? refetchInterval : false,
  });
}
