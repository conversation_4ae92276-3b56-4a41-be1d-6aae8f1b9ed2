/**
 * Utility to extract FAQ/related questions from message content
 * Parses XML-like tags and returns clean content with questions array
 */

export interface ExtractedFaq {
  cleanContent: string;
  questions: string[];
}

/**
 * Extracts related questions from message content
 * @param content - The raw message content that may contain <related_questions> tags
 * @returns Object containing clean content and array of questions
 */
export function extractRelatedQuestions(content: string): ExtractedFaq {
  if (!content) {
    return {
      cleanContent: "",
      questions: [],
    };
  }

  // Pattern to match <related_questions> block
  const relatedQuestionsRegex = /<related_questions>([\s\S]*?)<\/related_questions>/;

  // Extract the related questions block
  const relatedQuestionsMatch = content.match(relatedQuestionsRegex);

  const questions: string[] = [];
  let cleanContent = content;

  if (relatedQuestionsMatch) {
    // Remove the related questions block from content
    cleanContent = content.replace(relatedQuestionsRegex, "").trim();

    // Extract individual questions
    const questionsBlock = relatedQuestionsMatch[1];
    const questionRegex = /<question>([\s\S]*?)<\/question>/g;

    let match;
    while ((match = questionRegex.exec(questionsBlock)) !== null) {
      const question = match[1].trim();
      if (question) {
        questions.push(question);
      }
    }
  }

  return {
    cleanContent,
    questions,
  };
}

/**
 * Checks if content contains related questions
 * @param content - The message content to check
 * @returns Boolean indicating if related questions are present
 */
export function hasRelatedQuestions(content: string): boolean {
  if (!content) return false;
  return /<related_questions>[\s\S]*?<\/related_questions>/.test(content);
}
