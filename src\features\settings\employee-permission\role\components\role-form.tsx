import { useTranslation } from "react-i18next";

import { FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";

interface RoleFormProps {
  roleName: string;
  note: string;
  onRoleNameChange: (value: string) => void;
  onNoteChange: (value: string) => void;
}

export function RoleForm({ roleName, note, onRoleNameChange, onNoteChange }: RoleFormProps) {
  const { t } = useTranslation();

  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
      <div className="space-y-2">
        <FormLabel htmlFor="roleName" required>
          {t("pages.roleManagement.roleName")}
        </FormLabel>
        <Input
          id="roleName"
          placeholder={t("pages.roleManagement.roleNamePlaceholder")}
          value={roleName}
          onChange={(e) => onRoleNameChange(e.target.value)}
        />
      </div>
      <div className="space-y-2">
        <FormLabel htmlFor="note">{t("pages.roleManagement.note")}</FormLabel>
        <Input
          id="note"
          placeholder={t("pages.roleManagement.notePlaceholder")}
          value={note}
          onChange={(e) => onNoteChange(e.target.value)}
        />
      </div>
    </div>
  );
}
