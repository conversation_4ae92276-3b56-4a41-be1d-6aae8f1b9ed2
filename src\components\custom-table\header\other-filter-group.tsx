import { useEffect, useState } from "react";
import { useInfiniteQuery } from "@tanstack/react-query";
import { ChevronDown, ChevronRight } from "lucide-react";
import { DateRange } from "react-day-picker";
import { useTranslation } from "react-i18next";

import { useChannels } from "@/features/integration/hooks/use-channel";

import { fetcher } from "@/components/data-table/data-table-faceted-filter";
import { EFilterType } from "@/components/data-table/types";
import { Skeleton } from "@/components/ui";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { useUsers } from "@/hooks/use-user";
import { cn } from "@/lib/utils";

import LucideIcon from "../../custom-table/container/lucide-icon";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "../../ui/collapsible";
import OtherCombobox from "./other-combobox";

export interface Option {
  label: string;
  value: string;
  noFilter?: boolean;
  icon?: string;
}

interface OtherFilterGroupProps {
  label: string;
  options: Option[];
  selectedOption?: string[];
  onOptionSelect?: (value: string[]) => void;
  dateSelectedOption?: string | null;
  onDateOptionSelect?: (value: string) => void;
  dateRange?: DateRange | undefined;
  type: EFilterType;
  onDateRangeChange?: (range: DateRange | undefined) => void;
  remote?: boolean;
  pathUrlLoad?: string;
  isChannelFetch?: boolean;
  isUserFetch?: boolean;
  showIcons?: boolean;
}

export function OtherFilterGroup({
  label,
  options: defaultOptions,
  selectedOption,
  onOptionSelect,
  dateSelectedOption,
  onDateOptionSelect,
  dateRange,
  type,
  onDateRangeChange,
  remote = false,
  pathUrlLoad,
  isChannelFetch = false,
  isUserFetch = false,
  showIcons = false,
}: OtherFilterGroupProps) {
  const { t } = useTranslation();
  const [options, setOptions] = useState<Option[]>(defaultOptions);
  const isDateType = type === EFilterType.DATE;
  const [page] = useState(0);
  const [pageSize] = useState(10);
  const [isOpen, setIsOpen] = useState(true);
  const { data: channelsData } = useChannels({ enabled: isChannelFetch });
  const { users: usersData } = useUsers({}, { enabled: isUserFetch });

  const { data, isLoading } = useInfiniteQuery({
    queryKey:
      !isChannelFetch && remote && pathUrlLoad ? ["filter", pathUrlLoad, page] : ["other-filter"],
    queryFn: async () => {
      if (pathUrlLoad && !isChannelFetch) {
        return fetcher(pathUrlLoad, "", page, pageSize);
      }
      return { total: 0, items: [] };
    },
    enabled: remote && !isChannelFetch && !!pathUrlLoad,
    initialPageParam: 0,
    getNextPageParam: (lastPage: { total?: number; items: any[] }) =>
      lastPage?.total && lastPage.total > (page + 1) * pageSize ? page + 1 : undefined,
  });

  useEffect(() => {
    if (isChannelFetch && channelsData) {
      const channelOptions = [
        { value: "optiwarehouse", label: "OptiWarehouse" },
        ...channelsData.map((channel: { key: string; name: string }) => ({
          value: channel.key,
          label: channel.name,
        })),
      ];
      setOptions(channelOptions);
    }
  }, [isChannelFetch, channelsData]);

  useEffect(() => {
    if (data?.pages[0]?.items) {
      const mappedOptions = data.pages[0].items.map(
        (item: {
          id: string;
          name?: string;
          username?: string;
          summary?: string;
          title?: string;
          icon?: string;
        }) => ({
          label: item.name || item.username || item.summary || item.title || item.id,
          value: item.id,
          icon: item.icon,
        })
      );
      setOptions(mappedOptions || []);
    }
  }, [data]);

  useEffect(() => {
    if (isUserFetch && usersData) {
      const userOptions = usersData
        .map((user) => {
          if (!user.id) return null;
          return {
            value: user.id,
            label: user.username || user.email || user.id,
          };
        })
        .filter(Boolean) as Option[];

      // Preserve any default options (like "unassigned") that were passed in
      const initialOptions = defaultOptions.filter(
        (opt) => opt.value === "null" || !userOptions.some((u) => u.value === opt.value)
      );
      setOptions([...initialOptions, ...userOptions]);
    }
  }, [isUserFetch, usersData, defaultOptions]);

  const handleComboboxChange = (value: Option) => {
    const optionExists = options.some((option) => option.value === value.value);
    if (!optionExists) {
      setOptions([...options, value]);
    }
    if (onOptionSelect && selectedOption) {
      const currentSelection = new Set(selectedOption);
      if (currentSelection.has(value.value)) {
        currentSelection.delete(value.value);
      } else {
        currentSelection.add(value.value);
      }
      const newSelection = Array.from(currentSelection).filter(
        (val) => val !== "other" && val !== "all"
      );
      onOptionSelect(newSelection);
    }
  };
  const handleAddOptions = (values: Option[]) => {
    setOptions([...options, ...values]);
  };

  const selectedValues = new Set(selectedOption?.filter((opt) => opt !== "other") || []);
  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen}>
      <div className="flex">
        <CollapsibleTrigger className="flex w-full items-center gap-2">
          {isOpen ? <ChevronDown className="size-4" /> : <ChevronRight className="size-4" />}
          <h4 className="text-[14px] font-medium">{label}</h4>
        </CollapsibleTrigger>
      </div>
      <CollapsibleContent className="pt-2">
        <div className={type === EFilterType.DATE ? "" : ""}>
          {isLoading ? (
            Array.from({ length: pageSize }).map((_, index) => (
              <Skeleton
                key={index}
                className={cn(type === EFilterType.DATE ? "w-full" : "w-fit", "h-8")}
              />
            ))
          ) : type === EFilterType.DATE ? (
            <ToggleGroup
              type="single"
              value={dateSelectedOption || undefined}
              onValueChange={(value) => value && onDateOptionSelect?.(value)}
              className="flex flex-col gap-3">
              <ToggleGroupItem value="all" className="w-full bg-neutral-100 hover:bg-neutral-200">
                {t("pages.products.filters.dateOptions.allTime")}
              </ToggleGroupItem>
              <div className="grid w-full grid-cols-2 gap-3">
                {options
                  .filter((option) => option.value !== "all" && option.value !== "customize")
                  .map((option) => (
                    <ToggleGroupItem
                      key={option.value}
                      value={option.value}
                      className="w-full bg-neutral-100 hover:bg-neutral-200">
                      {t(option.label)}
                    </ToggleGroupItem>
                  ))}
              </div>
              <ToggleGroupItem
                value="customize"
                className={cn(
                  "w-full bg-neutral-100 hover:bg-neutral-200",
                  dateSelectedOption === "customize" && "hidden"
                )}>
                {t("pages.products.filters.dateOptions.customize")}
              </ToggleGroupItem>
            </ToggleGroup>
          ) : (
            <ToggleGroup
              type="multiple"
              value={selectedOption || []}
              onValueChange={(value) => onOptionSelect?.(value)}
              className="flex flex-wrap gap-3 p-1">
              <ToggleGroupItem value="all" className="w-full bg-neutral-100 hover:bg-neutral-200">
                {t("branch.All")}
              </ToggleGroupItem>
              {options.map((option) => (
                <ToggleGroupItem
                  key={option.value}
                  value={option.value}
                  className="bg-neutral-100 hover:bg-neutral-200">
                  <div className="flex max-w-screen-xxs items-center gap-2 truncate">
                    {showIcons && (
                      <LucideIcon iconName={option.icon || "Circle"} className="size-4 shrink-0" />
                    )}
                    <span className="truncate">{option.label}</span>
                  </div>
                </ToggleGroupItem>
              ))}
              {remote && pathUrlLoad && !isUserFetch && (
                <OtherCombobox
                  selectedValues={selectedValues}
                  onComboboxChange={handleComboboxChange}
                  remote={true}
                  pathUrlLoad={pathUrlLoad}
                  handleAddOptions={handleAddOptions}
                  isChannelFetch={isChannelFetch}
                  showIcons={showIcons}
                />
              )}
            </ToggleGroup>
          )}
        </div>
        {dateSelectedOption === "customize" && isDateType && onDateRangeChange && (
          <div className="mt-3">
            <DateRangePicker value={dateRange} onChange={onDateRangeChange} />
          </div>
        )}
      </CollapsibleContent>
    </Collapsible>
  );
}
