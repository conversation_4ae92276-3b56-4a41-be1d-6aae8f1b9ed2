#!/usr/bin/env node
import { execSync } from "child_process";

async function runCommand(command, description) {
  console.log(`\n🔧 Running ${description}...`);
  console.log(`Command: ${command}`);
  console.log("=".repeat(50));

  try {
    const output = execSync(command, {
      encoding: "utf8",
      stdio: "pipe",
    });
    console.log(output);
    return true;
  } catch (error) {
    console.error(`❌ Error running ${description}:`, error.message);
    return false;
  }
}

async function runAllFixes() {
  console.log("🚀 Starting continuous code formatting and linting...");
  console.log("=".repeat(50));

  const commands = [
    {
      command: "yarn format",
      description: "Prettier formatting",
    },
    {
      command: "yarn run lint -- --fix",
      description: "ESLint auto-fix",
    },
    {
      command: "npx prettier --write .",
      description: "Prettier write",
    },
  ];

  let allSuccess = true;
  for (const cmd of commands) {
    const success = await runCommand(cmd.command, cmd.description);
    if (!success) {
      allSuccess = false;
    }
  }

  return allSuccess;
}

async function watchAndFix() {
  console.log("🔄 Starting smart retry mode (max 3 attempts)...");
  console.log("Will retry only on errors, exit on success");
  console.log("Press Ctrl+C to stop early");
  console.log("=".repeat(50));

  const maxIterations = 3;
  let iteration = 1;

  while (iteration <= maxIterations) {
    console.log(`\n📝 Attempt ${iteration}/${maxIterations} - $(new Date().toLocaleTimeString())`);
    console.log("=".repeat(50));

    const success = await runAllFixes();

    if (success) {
      console.log(`\n✅ All commands completed successfully on attempt ${iteration}!`);
      console.log("🎉 No more retries needed - exiting.");
      console.log("=".repeat(50));
      return; // Exit early on success
    } else {
      console.log(`\n⚠️  Attempt ${iteration} had some issues`);

      if (iteration < maxIterations) {
        console.log(`\n⏳ Waiting 5 seconds before retry...`);
        await new Promise((resolve) => setTimeout(resolve, 5000));
      } else {
        console.log(`\n❌ All ${maxIterations} attempts completed with errors`);
        console.log("=".repeat(50));
      }
    }

    iteration++;
  }
}

// Handle graceful shutdown
process.on("SIGINT", () => {
  console.log("\n\n🛑 Stopping continuous fix mode...");
  process.exit(0);
});

process.on("SIGTERM", () => {
  console.log("\n\n🛑 Stopping continuous fix mode...");
  process.exit(0);
});

// Start the continuous process
watchAndFix().catch((error) => {
  console.error("❌ Watch and fix script failed:", error);
  process.exit(1);
});
