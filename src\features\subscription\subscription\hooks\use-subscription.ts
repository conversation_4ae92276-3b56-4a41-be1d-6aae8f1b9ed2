"use client";

import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { SubscriptionData } from "@/features/auth/hooks/useGlobalSubscription";

import { useCurrentSubscription } from "@/hooks/use-subscription";
import { subscriptionApi } from "@/lib/apis/crm_plan/subscription";
import { Subscription } from "@/lib/apis/crm_plan/types";

import { useSubscriptionPlans } from "../../hooks/plan";

export interface CurrentPlan {
  id: string;
  name: string;
  status: string;
  expires_date: string | null;
  price: number;
  salePrice: number;
  is_free_plan?: boolean;
}

export interface UsageStats {
  [key: string]: { used: number; total: number };
}

export interface Service {
  id: string;
  name: string;
  code: string;
  description?: string;
  currency?: string;
  logo?: string;
  created_at: string;
  updated_at: string;
  built_in_features?: Record<string, boolean>;
  custom_features?: any;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  description?: string;
  code?: string;
  is_popular?: boolean;
  duration: {
    YEARLY: {
      sale_price: string;
      price: string;
    };
    MONTHLY: {
      sale_price: string;
      price: string;
    };
  };
  service?: Service;
  currency?: string;
  features: string[] | Record<string, boolean>;
  isPopular?: boolean;
  savings?: number;
  is_free_plan?: boolean;
  trial_days?: number;
}

export const useSubscription = () => {
  const { t } = useTranslation();
  const [isAnnualBilling, setIsAnnualBilling] = useState(true);

  const { plans, isLoading, error } = useSubscriptionPlans();
  const { subscription } = useCurrentSubscription();
  const subscriptionData = useMemo(() => {
    return {
      id: subscription?.id,
      expires_date: subscription?.expires_date,
      plan: subscription?.plan,
      plan_quota: subscription?.plan.quota,
      used_quota: subscription?.used_quota,
      status: subscription?.status,
      billing_cycle: subscription?.billing_cycle,
      activation_date: subscription?.activation_date,
      company_id: subscription?.company_id,
      service: subscription?.service,
    };
  }, [subscription]);

  const currentPlan = useMemo(() => {
    return {
      id: subscription?.plan.id,
      name: subscription?.plan.name,
      status: subscription?.status,
      expires_date: subscription?.expires_date,
      price: subscription?.plan.duration?.MONTHLY?.sale_price
        ? subscription?.plan.duration.MONTHLY.sale_price
        : 0,
      salePrice: subscription?.plan.duration?.YEARLY?.sale_price
        ? subscription?.plan.duration.YEARLY.sale_price
        : 0,
      is_free_plan: subscription?.plan.is_free_plan,
    };
  }, [subscription]);

  const usageStats = useMemo(() => {
    const dynamicStats: any = {};

    if (subscription?.plan.quota && subscription?.used_quota) {
      Object.keys(subscription?.plan.quota).forEach((key) => {
        const planValue = parseInt((subscription?.plan.quota as any)[key]) || 0;
        const usedValue = parseInt((subscription?.used_quota as any)[key]) || 0;

        // Extract the key name (e.g., "max_products" -> "products")
        const keyName = key.split("_").pop()?.replace(/_/g, " ") || key;

        dynamicStats[keyName] = {
          used: usedValue,
          total: planValue,
        };
      });
    }

    // Fallback to default stats if no dynamic data
    const stats: UsageStats =
      Object.keys(dynamicStats).length > 0
        ? dynamicStats
        : {
            messages: { used: 0, total: 0 },
            staff: { used: 0, total: 0 },
            storage: { used: 0, total: 0 },
          };

    return stats;
  }, [subscription]);

  const handleBillingToggle = () => {
    setIsAnnualBilling(!isAnnualBilling);
  };

  const handleCancelSubscription = async () => {
    try {
      if (!subscriptionData?.id) {
        toast.error("No active subscription found");
        return;
      }

      await subscriptionApi.cancel(subscriptionData.id);
      toast.success("Subscription cancelled successfully");

      // Clear subscription data from localStorage
      localStorage.removeItem("activeSubscription");
      localStorage.removeItem("subscriptions");

      // Refresh the page to update the UI
      window.location.reload();
    } catch (error) {
      console.error("Failed to cancel subscription:", error);
      toast.error("Failed to cancel subscription");
    }
  };

  const formatPrice = (price: number | string, currency: string = "VND") => {
    const numericPrice = typeof price === "string" ? parseFloat(price) : price;

    // Format with Vietnamese locale and remove trailing zeros
    const formatted = new Intl.NumberFormat("vi-VN", {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(numericPrice);

    return formatted;
  };

  return {
    isAnnualBilling,
    currentPlan: currentPlan || {
      id: "free",
      name: "Free Plan",
      expiresOn: "No expiration",
      status: "FREE",
      expires_date: "",
      price: 0,
      salePrice: 0,
      is_free_plan: true,
    },
    usageStats: usageStats || {},
    plans,
    isLoading,
    error,
    handleBillingToggle,
    handleCancelSubscription,
    formatPrice,
    subscriptionData,
  };
};
