import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export const LanguageCurrencySkeleton = () => {
  return (
    <Card className="mx-4 mb-4 border-none">
      <CardHeader>
        <Skeleton className="h-6 w-48" />
      </CardHeader>

      {/* Content */}
      <div className="max-w-[736px] space-y-4 px-4 pb-4">
        {/* Language Section Skeleton */}
        <Card className="rounded-lg border border-border">
          <CardHeader className="p-4">
            <div className="flex items-center justify-between">
              <Skeleton className="h-5 w-24" />
              <div className="relative">
                <Skeleton className="h-10 w-32" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="px-6 pb-6 pt-0">
            <div className="space-y-4">
              {/* Language items skeleton */}
              {[1, 2].map((i) => (
                <div key={i} className="flex items-center justify-between rounded-lg border p-3">
                  <div className="flex items-center space-x-3">
                    <Skeleton className="size-4 rounded" />
                    <div className="flex items-center space-x-2">
                      <Skeleton className="size-4 rounded" />
                      <Skeleton className="h-4 w-20" />
                    </div>
                  </div>
                  <Skeleton className="size-6 rounded" />
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter className="p-0">
            <div className="flex w-full justify-end py-3 pr-4">
              <Skeleton className="h-10 w-24" />
            </div>
          </CardFooter>
        </Card>

        {/* Currency Section Skeleton */}
        <Card className="rounded-lg border border-border">
          <CardHeader className="p-4">
            <div className="flex items-center justify-between">
              <Skeleton className="h-5 w-24" />
              <div className="relative">
                <Skeleton className="h-10 w-32" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="px-6 pb-6 pt-0">
            <div className="space-y-4">
              {/* Currency items skeleton */}
              {[1, 2].map((i) => (
                <div key={i} className="flex items-center justify-between rounded-lg border p-3">
                  <div className="flex items-center space-x-3">
                    <Skeleton className="size-4 rounded" />
                    <div className="flex items-center space-x-2">
                      <Skeleton className="size-4 rounded" />
                      <Skeleton className="h-4 w-20" />
                    </div>
                  </div>
                  <Skeleton className="size-6 rounded" />
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter className="p-0">
            <div className="flex w-full justify-end py-3 pr-4">
              <Skeleton className="h-10 w-24" />
            </div>
          </CardFooter>
        </Card>
      </div>
    </Card>
  );
};
