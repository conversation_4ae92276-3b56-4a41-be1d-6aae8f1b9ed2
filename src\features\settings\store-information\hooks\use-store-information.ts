import { useCallback, useEffect, useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { settingApi } from "@/lib/apis/setting";
import { useVersion, versionApi } from "@/lib/apis/version";

import { StoreInformation, StoreInformationFormData } from "../types";

export const useStoreInformation = () => {
  const { getSettingValue } = useVersion();
  const { t } = useTranslation();
  const [formData, setFormData] = useState<StoreInformationFormData>({
    storeName: "",
    store_phone: "",
    store_email: "",
    businessSector: "",
    storeUrl: "",
    store_default_price_group: { name: "", id: "" },
    store_address: "",
    store_provinces: "",
    store_districts: "",
    store_wards: "",
    isDirty: false,
    isBasicInfoDirty: false,
    isAdvancedInfoDirty: false,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get store information from shop_info
  const savedStoreInfo = getSettingValue<Record<string, any>>("shop_info");

  // Check if basic info section is dirty
  const checkBasicInfoDirty = useCallback(
    (data: StoreInformation) => {
      if (!savedStoreInfo) return false;

      return (
        data.storeName !== savedStoreInfo.store_name ||
        data.store_phone !== savedStoreInfo.store_phone ||
        data.store_email !== savedStoreInfo.store_email ||
        data.businessSector !== (savedStoreInfo.business_sector || "")
      );
    },
    [savedStoreInfo]
  );

  // Check if advanced info section is dirty
  const checkAdvancedInfoDirty = useCallback(
    (data: StoreInformation) => {
      if (!savedStoreInfo) return false;

      return (
        data.storeUrl !== savedStoreInfo.store_url ||
        data.store_default_price_group.id !==
          (savedStoreInfo.store_default_price_group?.id || "") ||
        data.store_address !== savedStoreInfo.store_address ||
        data.store_provinces !== savedStoreInfo.store_provinces ||
        data.store_districts !== savedStoreInfo.store_districts ||
        data.store_wards !== savedStoreInfo.store_wards
      );
    },
    [savedStoreInfo]
  );

  // Check if form is dirty by comparing with saved values
  const checkFormDirty = useCallback(
    (data: StoreInformation) => {
      console.log("savedStoreInfo", savedStoreInfo);
      console.log("data", data);
      if (!savedStoreInfo) return false;

      return (
        data.storeName !== savedStoreInfo.store_name ||
        data.store_phone !== savedStoreInfo.store_phone ||
        data.store_email !== savedStoreInfo.store_email ||
        data.businessSector !== (savedStoreInfo.business_sector || "") ||
        data.storeUrl !== savedStoreInfo.store_url ||
        data.store_default_price_group.id !==
          (savedStoreInfo.store_default_price_group?.id || "") ||
        data.store_address !== savedStoreInfo.store_address ||
        data.store_provinces !== savedStoreInfo.store_provinces ||
        data.store_districts !== savedStoreInfo.store_districts ||
        data.store_wards !== savedStoreInfo.store_wards
      );
    },
    [savedStoreInfo]
  );

  // Handle input changes
  const handleInputChange = useCallback(
    (field: keyof StoreInformation, value: string | { name: string; id: string }) => {
      setFormData((prev) => {
        let newData = { ...prev };

        // Handle nested object updates
        if (field === "store_default_price_group" && typeof value === "string") {
          try {
            const priceGroup = JSON.parse(value);
            newData = {
              ...prev,
              store_default_price_group: {
                name: priceGroup.name,
                id: priceGroup.id,
              },
            };
          } catch {
            // If parsing fails, use the value as is
            // newData = { ...prev, [field]: value };
          }
        } else if (field === "store_default_price_group" && typeof value === "object") {
          newData = {
            ...prev,
            store_default_price_group: value,
          };
        } else {
          // For other fields, cast to string
          const stringValue = value as string;
          newData = { ...prev, [field]: stringValue };
        }

        // Check which section is dirty and update accordingly
        const isBasicInfoDirty = checkBasicInfoDirty(newData);
        const isAdvancedInfoDirty = checkAdvancedInfoDirty(newData);
        const isDirty = isBasicInfoDirty || isAdvancedInfoDirty;

        return {
          ...newData,
          isDirty,
          isBasicInfoDirty,
          isAdvancedInfoDirty,
        };
      });
    },
    [checkBasicInfoDirty, checkAdvancedInfoDirty]
  );

  // Reset form to saved values
  const resetForm = useCallback(() => {
    if (savedStoreInfo) {
      const storeInfo = {
        storeName: savedStoreInfo.store_name || "",
        store_phone: savedStoreInfo.store_phone || "",
        store_email: savedStoreInfo.store_email || "",
        businessSector: savedStoreInfo.business_sector || "",
        storeUrl: savedStoreInfo.store_url || "",
        store_default_price_group: savedStoreInfo.store_default_price_group || { name: "", id: "" },
        store_address: savedStoreInfo.store_address || "",
        store_provinces: savedStoreInfo.store_provinces || "",
        store_districts: savedStoreInfo.store_districts || "",
        store_wards: savedStoreInfo.store_wards || "",
      };

      setFormData({
        ...storeInfo,
        isDirty: false,
        isBasicInfoDirty: false,
        isAdvancedInfoDirty: false,
      });
    } else {
      // If no saved store info, set default empty values
      setFormData({
        storeName: "",
        store_phone: "",
        store_email: "",
        businessSector: "",
        storeUrl: "",
        store_default_price_group: { name: "", id: "" },
        store_address: "",
        store_provinces: "",
        store_districts: "",
        store_wards: "",
        isDirty: false,
        isBasicInfoDirty: false,
        isAdvancedInfoDirty: false,
      });
    }
    setError(null);
  }, [savedStoreInfo]);

  // Reset basic info section to saved values
  const resetBasicInfo = useCallback(() => {
    if (savedStoreInfo) {
      setFormData((prev) => ({
        ...prev,
        storeName: savedStoreInfo.store_name || "",
        store_phone: savedStoreInfo.store_phone || "",
        store_email: savedStoreInfo.store_email || "",
        businessSector: savedStoreInfo.business_sector || "",
        isBasicInfoDirty: false,
        isDirty: prev.isAdvancedInfoDirty || false,
      }));
    }
  }, [savedStoreInfo]);

  // Reset advanced info section to saved values
  const resetAdvancedInfo = useCallback(() => {
    if (savedStoreInfo) {
      setFormData((prev) => ({
        ...prev,
        storeUrl: savedStoreInfo.store_url || "",
        store_default_price_group: savedStoreInfo.store_default_price_group || { name: "", id: "" },
        store_address: savedStoreInfo.store_address || "",
        store_provinces: savedStoreInfo.store_provinces || "",
        store_districts: savedStoreInfo.store_districts || "",
        store_wards: savedStoreInfo.store_wards || "",
        isAdvancedInfoDirty: false,
        isDirty: prev.isBasicInfoDirty || false,
      }));
    }
  }, [savedStoreInfo]);

  const queryClient = useQueryClient();

  // Update store information
  const updateStoreInformationMutation = useMutation({
    mutationFn: async (data: StoreInformation) => {
      // Get current shop_info settings
      const currentShopInfo = getSettingValue<Record<string, any>>("shop_info") || {};

      // Update only store-related fields while preserving other fields
      const updatedShopInfo: Record<string, any> = {
        ...currentShopInfo,
        store_name: data.storeName,
        store_phone: data.store_phone,
        store_email: data.store_email,
        business_sector: data.businessSector,
        store_url: data.storeUrl,
        store_default_price_group: data.store_default_price_group,
        store_address: data.store_address,
        store_provinces: data.store_provinces,
        store_districts: data.store_districts,
        store_wards: data.store_wards,
      };

      await settingApi.updateLanguageSetting("shop_info", updatedShopInfo);
      const version = await versionApi.getVersion();

      return {
        ...version,
        data: {
          ...version.data,
          dict: {
            ...version.data.dict,
            shop_info: { ...version.data.dict.shop_info, setting_value: updatedShopInfo },
          },
        },
      };
    },
    onSuccess: (updatedVersion) => {
      queryClient.setQueryData(["version"], updatedVersion);

      // Update form data and mark as not dirty
      setFormData((prev) => ({
        ...prev,
        isDirty: false,
        isBasicInfoDirty: false,
        isAdvancedInfoDirty: false,
      }));

      toast.success(t("pages.storeInformation.success"));
    },
    onError: (error: Error) => {
      toast.error(t("pages.storeInformation.error"));
    },
  });

  const updateStoreInformation = useCallback(
    async (data: StoreInformation) => {
      setIsLoading(true);
      setError(null);

      try {
        await updateStoreInformationMutation.mutateAsync(data);
        return { success: true };
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to update store information";
        setError(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        setIsLoading(false);
      }
    },
    [updateStoreInformationMutation]
  );

  // Load store information from API
  const loadStoreInformation = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      if (savedStoreInfo) {
        const storeInfo = {
          storeName: savedStoreInfo.store_name || "",
          store_phone: savedStoreInfo.store_phone || "",
          store_email: savedStoreInfo.store_email || "",
          businessSector: savedStoreInfo.business_sector || "",
          storeUrl: savedStoreInfo.store_url || "",
          store_default_price_group: savedStoreInfo.store_default_price_group || {
            name: "",
            id: "",
          },
          store_address: savedStoreInfo.store_address || "",
          store_provinces: savedStoreInfo.store_provinces || "",
          store_districts: savedStoreInfo.store_districts || "",
          store_wards: savedStoreInfo.store_wards || "",
        };

        setFormData({
          ...storeInfo,
          isDirty: false,
          isBasicInfoDirty: false,
          isAdvancedInfoDirty: false,
        });
      } else {
        // If no saved store info, set default empty values
        setFormData({
          storeName: "",
          store_phone: "",
          store_email: "",
          businessSector: "",
          storeUrl: "",
          store_default_price_group: { name: "", id: "" },
          store_address: "",
          store_provinces: "",
          store_districts: "",
          store_wards: "",
          isDirty: false,
          isBasicInfoDirty: false,
          isAdvancedInfoDirty: false,
        });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to load store information";
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [savedStoreInfo]);

  // Load data on component mount
  useEffect(() => {
    loadStoreInformation();
  }, [loadStoreInformation]);

  return {
    formData,
    isLoading,
    error,
    handleInputChange,
    updateStoreInformation,
    resetForm,
    resetBasicInfo,
    resetAdvancedInfo,
    loadStoreInformation,
  };
};
