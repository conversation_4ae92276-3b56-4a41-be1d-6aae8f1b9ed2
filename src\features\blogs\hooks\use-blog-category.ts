import { useCallback, useMemo } from "react";

import { Category } from "@/lib/apis/types/category";

import {
  useBlogCategories,
  useCreateCategory,
  useDeleteCategory,
  useUpdateCategory,
  useUpdateCategoryParent,
} from "./blog-category";

interface UseBlogCategoryManagerOptions {
  search?: string;
  limit?: number;
}

interface CategoryNode {
  category: Category;
  children: CategoryNode[];
  level: number;
}

export function useBlogCategoryManager(options: UseBlogCategoryManagerOptions = {}) {
  const { search, limit = 2000 } = options;

  // Fetch categories
  const { categories, hasNextPage, isFetchingNextPage, fetchNextPage, isLoading, isError, error } =
    useBlogCategories({
      search,
      limit,
    });

  // Mutations
  const createCategory = useCreateCategory();
  const updateCategory = useUpdateCategory();
  const deleteCategory = useDeleteCategory();
  const updateParent = useUpdateCategoryParent();

  // Organize categories into a tree structure
  const categoryTree = useMemo(() => {
    const categoryMap = new Map<string, Category>();
    const rootNodes: CategoryNode[] = [];
    const childrenMap = new Map<string, CategoryNode[]>();

    // Create a map of all categories
    categories.forEach((category) => {
      categoryMap.set(category.id, category);
    });

    // Organize into parent-child relationships
    categories.forEach((category) => {
      const node: CategoryNode = {
        category,
        children: [],
        level: 0,
      };

      if (category.parent_id && categoryMap.has(category.parent_id)) {
        const parent = categoryMap.get(category.parent_id)!;
        if (!childrenMap.has(parent.id)) {
          childrenMap.set(parent.id, []);
        }
        childrenMap.get(parent.id)!.push(node);
      } else {
        rootNodes.push(node);
      }
    });

    // Build the tree structure recursively
    const buildTree = (nodes: CategoryNode[], level: number): CategoryNode[] => {
      return nodes.map((node) => {
        const children = childrenMap.get(node.category.id) || [];
        return {
          ...node,
          level,
          children: buildTree(children, level + 1),
        };
      });
    };

    return buildTree(rootNodes, 0);
  }, [categories]);

  // Flatten tree for drag and drop
  const flattenedCategories = useMemo(() => {
    const flatten = (nodes: CategoryNode[]): Category[] => {
      return nodes.reduce((acc, node) => {
        acc.push(node.category);
        if (node.children.length > 0) {
          acc.push(...flatten(node.children));
        }
        return acc;
      }, [] as Category[]);
    };
    return flatten(categoryTree);
  }, [categoryTree]);

  // Handle drag end
  const handleDragEnd = useCallback(
    (activeId: string, overId: string) => {
      if (activeId !== overId) {
        const activeCategory = categories.find((cat) => cat.id === activeId);
        const targetCategory = categories.find((cat) => cat.id === overId);

        if (activeCategory && targetCategory) {
          // Determine if we're dropping into a category or between categories
          const isDroppingInto = targetCategory.has_children;

          // If dropping into a category with children, make it a child
          // Otherwise, make it a sibling (same parent)
          const newParentId = isDroppingInto ? targetCategory.id : targetCategory.parent_id;

          updateParent.mutate({
            id: activeCategory.id,
            parentId: newParentId,
          });
        }
      }
    },
    [categories, updateParent]
  );

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  return {
    // Data
    categories,
    categoryTree,
    flattenedCategories,

    // Loading states
    isLoading,
    isError,
    error,
    hasNextPage,
    isFetchingNextPage,

    // Actions
    handleDragEnd,
    handleLoadMore,
    createCategory,
    updateCategory,
    deleteCategory,
    updateParent,
  };
}
