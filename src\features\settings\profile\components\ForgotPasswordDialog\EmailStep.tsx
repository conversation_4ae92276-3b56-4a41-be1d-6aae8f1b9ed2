"use client";

import { useTranslation } from "react-i18next";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import { useProfile } from "../../hooks/useProfile";

// Helper function to mask email (show first and last characters)
const maskEmail = (email: string): string => {
  if (!email || email.length < 3) return email;

  const [localPart, domain] = email.split("@");
  if (!domain) return email;

  if (localPart.length <= 2) {
    return `${localPart}***@${domain}`;
  }

  const firstChar = localPart[0];
  const lastChar = localPart[localPart.length - 1];
  const maskedLocalPart = `${firstChar}***${lastChar}`;

  return `${maskedLocalPart}@${domain}`;
};

export const EmailStep = ({
  email,
  isLoading,
  countdown,
  isCheckingDelay,
  error,
  onEmailChange,
  onSendCode,
  onCancel,
  onFocus,
}: {
  email: string;
  isLoading: boolean;
  countdown: number;
  isCheckingDelay: boolean;
  error?: any;
  onEmailChange: (email: string) => void;
  onSendCode: (email: string) => void;
  onCancel: () => void;
  onFocus: () => void;
}) => {
  const { t } = useTranslation();
  const { profileData, isLoadingProfile } = useProfile();

  // Use profile email if available, otherwise fall back to prop email
  const currentEmail = profileData?.email || email;
  const maskedEmail = maskEmail(currentEmail);

  const isDisabled = isLoading || !currentEmail;

  return (
    <>
      <div className="space-y-6 px-6 pb-8 pt-6">
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">
            {t("pages.profile.forgotPasswordDialog.emailStep.title")}
          </h2>
          <p className="text-sm text-muted-foreground">
            {t("pages.profile.forgotPasswordDialog.emailStep.description")}
          </p>
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">
              {t("pages.profile.forgotPasswordDialog.emailStep.emailLabel")}
            </label>
            <Input
              type="email"
              value={maskedEmail}
              placeholder={t("pages.profile.forgotPasswordDialog.emailStep.emailPlaceholder")}
              disabled={true} // Always disabled since we're using profile email
              onFocus={onFocus}
            />
          </div>
        </div>
      </div>

      <div className="space-y-4 border-t px-6 pb-6 pt-4">
        <div className="flex items-center justify-end gap-2">
          <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
            {t("common.cancel")}
          </Button>

          <Button
            onClick={() => onSendCode(currentEmail)}
            loading={isLoading || isLoadingProfile}
            disabled={isDisabled}>
            {t("pages.profile.forgotPasswordDialog.emailStep.sendCode")}
          </Button>
        </div>
      </div>
    </>
  );
};
