import { useTranslation } from "react-i18next";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { DialogFooter } from "@/components/ui/dialog";

interface DialogFooterProps {
  onCancel: () => void;
  onSubmit: () => void;
  isSubmitDisabled: boolean;
  submitText?: string;
  loading?: boolean;
}

export function DialogFooterComponent({
  onCancel,
  onSubmit,
  isSubmitDisabled,
  submitText,
  loading,
}: DialogFooterProps) {
  const { t } = useTranslation();

  return (
    <DialogFooter className="flex-none border-t border-border px-6 py-3">
      <Button variant="outline" onClick={onCancel}>
        {t("common.cancel")}
      </Button>
      <Button onClick={onSubmit} disabled={isSubmitDisabled} loading={loading}>
        {submitText || t("common.add")}
      </Button>
    </DialogFooter>
  );
}
