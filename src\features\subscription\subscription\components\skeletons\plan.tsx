import { Skeleton } from "@/components/ui/skeleton";

export const PlanSkeleton = () => {
  return (
    <div className="rounded-lg border bg-card p-4">
      <div className="flex flex-col gap-4">
        {/* Plan name skeleton */}
        <div className="flex flex-col gap-3">
          <Skeleton className="h-6 w-1/2" />

          {/* Price skeleton */}
          <div className="flex items-end gap-1">
            <Skeleton className="h-5 w-24" />
            <Skeleton className="h-4 w-8" />
            <Skeleton className="h-3 w-12" />
          </div>

          {/* Savings skeleton */}
          <Skeleton className="h-3 w-3/5" />
        </div>

        {/* Button skeleton */}
        <Skeleton className="h-10 w-full" />

        {/* Features skeleton */}
        <div className="flex flex-col gap-1">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="flex items-center gap-2 py-1">
              <Skeleton className="size-4 flex-none rounded-full" />
              <Skeleton className="h-4 flex-auto" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
