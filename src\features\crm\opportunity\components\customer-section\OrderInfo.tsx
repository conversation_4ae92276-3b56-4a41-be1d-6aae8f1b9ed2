"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { Mail, Package, Printer, Scroll, StickyNote } from "lucide-react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import { Customer } from "@/features/customer/hooks/customer";
import { Order, OrderItem } from "@/features/orders/hooks/types";
import { orderFormSchema, OrderFormValues } from "@/features/orders/utils/validators/order";

import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { CustomImage } from "@/components/ui/image";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/printer-select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { Location } from "@/lib/apis/location";

interface OrderInfoProps {
  customer?: Customer;
  opportunityId?: string;
  order?: Order;
  onFieldChange?: (field: string, value: any, shouldDirty?: boolean) => void;
}

const formatNumber = (value: number): string => {
  return value.toLocaleString("en-US", { maximumFractionDigits: 0 });
};

export function OrderInfo({ customer, opportunityId, order, onFieldChange }: OrderInfoProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null);
  const [orderTotal, setOrderTotal] = useState(0);
  const [orderItemsCount, setOrderItemsCount] = useState(0);
  const [selectedPrintOption, setSelectedPrintOption] = useState<string>("");

  // Handle left button click for the print select
  const handleLeftButtonClick = () => {};

  // Get the appropriate icon based on selected value
  const getIconForSelectedOption = (value: string) => {
    switch (value) {
      case "sticky_note":
        return <StickyNote className="size-4" />;
      case "scroll-1":
      case "scroll-2":
      case "scroll-3":
        return <Scroll className="size-4" />;
      case "mail":
        return <Mail className="size-4" />;
      default:
        return <Printer className="size-4" />;
    }
  };

  // Initialize form with default values
  const form = useForm<OrderFormValues>({
    resolver: zodResolver(orderFormSchema),
    defaultValues: {
      id: order?.id || "",
      customer: order?.customer || null,
      billing_address: order?.billing_address || null,
      location: order?.location || null,
      staff: null,
      source: null,
      shipping_address: customer?.shipping_address || null,
      order_line_items: order?.order_line_items || [],
      note: order?.note || "",
      tags: order?.tags || "",
      total: order?.total ? Number(order.total) : 0,
      sub_total: order?.sub_total ? Number(order.sub_total) : 0,
      discount: order?.discount ? Number(order.discount) : 0,
      payments: order?.payments || null,
      other_fees: order?.other_fees || null,
      shipping_fee: order?.shipping_fee ? Number(order.shipping_fee) : 0,
      tax: order?.tax ? Number(order.tax) : 0,
    },
  });

  // Initialize state when order data is loaded
  useEffect(() => {
    const initializeOrderData = async () => {
      if (order) {
        // Set selected location if order has location
        if (order.location) {
          setSelectedLocation(order.location);
        }

        // Set order total and count
        if (order.total) {
          const total = Number(order.total);
          setOrderTotal(total);
          form.setValue("total", total);
          form.setValue("sub_total", total);
        }

        // Set order items count and items
        if (order.order_line_items && order.order_line_items.length > 0) {
          const count = order.order_line_items.reduce((sum, item) => {
            const quantity =
              typeof item.quantity === "string" ? parseInt(item.quantity, 10) : item.quantity || 0;
            return sum + quantity;
          }, 0);
          setOrderItemsCount(count);

          // Process order line items
          const itemsWithFullVariants = order.order_line_items.map((lineItem, index) => ({
            id: lineItem.id || `temp_${index}`, // Use index as fallback if no ID
            price: Number(lineItem.unit_price || 0),
            sale_price: Number(lineItem.sale_price || 0),
            sku: lineItem.sku || "",
            name: lineItem.name || "",
            variant: {
              name: lineItem.variant_name || "",
              product_id: lineItem.product_id || "",
              id: lineItem.variant_id || "",
              sku: lineItem.sku || "",
            },
            variant_id: lineItem.variant_id || "",
            image: lineItem.image_url || "",
            quantity: Number(lineItem.quantity || 0),
            note: lineItem.note || "",
            custom: lineItem.custom || false,
            total: Number(lineItem.unit_price || 0) * Number(lineItem.quantity || 0),
          }));

          setOrderItems(itemsWithFullVariants);
          form.setValue("order_line_items", order.order_line_items);
        }

        // Set other form values
        if (order.note) {
          form.setValue("note", order.note);
        }
        if (order.tags) {
          form.setValue("tags", order.tags);
        }
      }
    };

    initializeOrderData();
  }, [order, form]);

  // Update form values when customer changes
  useEffect(() => {
    if (customer) {
      form.setValue("customer", customer);
      form.setValue("billing_address", customer.billing_address || null);
      form.setValue("shipping_address", customer.shipping_address || null);
    }
  }, [customer, form]);

  // Get current note and tags for the Note component
  const currentNote = form.watch("note") || "";
  const currentTags = form.watch("tags") || "";
  const tagsArray = currentTags ? currentTags.split(",").filter(Boolean) : [];

  // This component only shows readonly order view
  return (
    <Form {...form}>
      <div className="flex w-full flex-col gap-2 overflow-x-auto">
        {order?.number && (
          <div className="sticky left-0 z-10 flex flex-col gap-2 xl:flex-row">
            <Card className="grid w-full grid-cols-1 border-sematic-success px-4 py-1 md:grid-cols-[1fr_auto] xl:py-0">
              <div className="flex flex-col gap-2 md:flex-row md:items-center md:justify-between">
                <div className="flex min-w-0 items-center gap-2">
                  <Package className="size-4 shrink-0 text-muted-foreground" />
                  <span className="text-sm font-medium text-sematic-success">
                    {t("pages.orders.order")}
                  </span>
                  <span
                    role="button"
                    onClick={() => order?.id && router.push(`/orders/${order.id}`)}
                    className="cursor-pointer text-sm font-semibold text-primary hover:underline">
                    #{order.number}
                  </span>
                  <span className="hidden min-w-0 truncate text-sm font-medium text-sematic-success sm:inline">
                    {t("pages.orders.hasBeenCreatedSuccessfully")}
                  </span>
                </div>
                <div className="flex shrink-0 items-center gap-2">
                  <Badge variant="secondary">
                    {t(`pages.orders.orderStatus.${order.status?.toLowerCase() || "Draft"}`)}
                  </Badge>
                  <Badge variant="sematic_destructive">
                    {t(
                      `pages.orders.orderPaymentStatus.${order.payment_status?.toLowerCase() || "Unpaid"}`
                    )}
                  </Badge>
                </div>
              </div>
            </Card>
            <Card className="flex h-full justify-end border-none">
              <Select onValueChange={setSelectedPrintOption}>
                <SelectTrigger
                  className="size-full"
                  leftButtonContent={<SelectValue placeholder="Print" />}
                  leftButtonIcon={getIconForSelectedOption(selectedPrintOption)}
                  onLeftButtonClick={handleLeftButtonClick}>
                  <SelectValue placeholder="Print" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem leftIcon={<StickyNote className="size-4" />} value="sticky_note">
                    {t("pages.orders.print")} A4
                  </SelectItem>
                  <SelectItem leftIcon={<Scroll className="size-4" />} value="scroll-1">
                    {t("pages.orders.print")} A5
                  </SelectItem>
                  <SelectItem leftIcon={<Scroll className="size-4" />} value="scroll-2">
                    {t("pages.orders.print")} K57
                  </SelectItem>
                  <SelectItem leftIcon={<Scroll className="size-4" />} value="scroll-3">
                    {t("pages.orders.print")} K80
                  </SelectItem>
                  <SelectItem leftIcon={<Mail className="size-4" />} value="mail">
                    {t("pages.orders.sendEmail")}
                  </SelectItem>
                </SelectContent>
              </Select>
            </Card>
          </div>
        )}
        <div className="grid gap-2 xl:grid-cols-[2fr_1fr]">
          {/* Right Side - Product List */}
          <Card className="p-0">
            <div className="max-h-[290px] overflow-y-auto rounded-lg">
              <Table>
                <TableHeader className="sticky top-0 border-b bg-card">
                  <TableRow>
                    <TableHead className="w-1/2">{t("pages.orders.productInformation")}</TableHead>
                    <TableHead className="w-[10%] text-center">{t("pages.orders.qty")}</TableHead>
                    <TableHead className="w-1/5 text-right">{t("pages.orders.price")}</TableHead>
                    <TableHead className="w-1/5 text-right">
                      {t("pages.orders.totalAmount")}
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {orderItems.map((product) => (
                    <TableRow key={product.sku}>
                      <TableCell className="w-2/5">
                        <div className="flex items-center gap-3">
                          <div className="size-10 shrink-0 overflow-hidden rounded bg-muted">
                            <CustomImage
                              src={product.image || ""}
                              alt={product.id || product.sku || "Product"}
                              width={40}
                              height={40}
                              className="size-10 object-cover"
                            />
                          </div>
                          <div className="min-w-0 flex-1">
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className="cursor-default truncate font-medium">
                                  {product.name}
                                </div>
                              </TooltipTrigger>
                              <TooltipContent side="top" className="max-w-[300px]">
                                <p>{product.name}</p>
                              </TooltipContent>
                            </Tooltip>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className="cursor-default truncate text-sm text-muted-foreground">
                                  SKU: {product.sku}
                                </div>
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p>SKU: {product.sku}</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="w-1/5 text-center">{product.quantity}</TableCell>
                      <TableCell className="w-1/5 text-right">
                        <div>{formatNumber(product.price)}</div>
                      </TableCell>
                      <TableCell className="w-1/5 text-right">
                        {formatNumber(product.price * product.quantity)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </Card>
          {/* Left Side - Customer Information */}
          <div className="space-y-2">
            {/* Order Details - Only show if order has actual data */}

            <Card className="px-4 py-2">
              <div className="flex items-center justify-between gap-8">
                <div className="text-sm font-medium text-muted-foreground">
                  {t("pages.orders.totalAmount")}
                </div>
                <div className="text-sm font-semibold">{formatNumber(orderTotal)}</div>
              </div>
            </Card>

            <Card className="p-0">
              <div className="px-4 py-2">
                <div className="text-sm font-medium text-muted-foreground">
                  {t("pages.orders.notes")}
                </div>
              </div>
              <div className="max-h-[110px] overflow-y-auto">
                <div className="px-4 pb-2">
                  <div className="text-sm">{currentNote || "--"}</div>
                </div>
              </div>
            </Card>

            {/* Tags */}
            <Card className="px-4 py-2">
              <div className="flex flex-wrap items-center gap-1">
                <div className="text-sm font-medium text-muted-foreground">
                  {t("pages.orders.tags")}
                </div>
                {tagsArray?.length > 0 &&
                  tagsArray?.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="px-2.5 text-xs">
                      {tag}
                    </Badge>
                  ))}
                {tagsArray?.length === 0 && <div className="text-sm text-muted-foreground">--</div>}
              </div>
            </Card>
          </div>
        </div>
      </div>
    </Form>
  );
}
