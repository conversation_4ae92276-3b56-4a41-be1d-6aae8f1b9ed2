"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";

import { ChangePasswordDialog, ProfileView } from "@/features/settings/profile/components";

import { Card } from "@/components/ui";

export default function ProfilePage() {
  const { t } = useTranslation();
  const [showChangePassword, setShowChangePassword] = useState(false);

  const handleChangePassword = () => {
    setShowChangePassword(true);
  };

  return (
    <Card className="m-4 mt-0 h-[calc(100vh-84px)] overflow-y-auto border-none p-4">
      {/* Back Button */}
      <div className="mb-4">
        <h1 className="text-lg font-semibold text-foreground">{t("pages.profile.title")}</h1>
      </div>

      {/* Content */}
      <div className="max-w-4xl">
        {/* Profile Content */}
        <ProfileView onEdit={() => {}} onChangePassword={handleChangePassword} />
      </div>

      {/* Change Password Dialog */}
      <ChangePasswordDialog open={showChangePassword} onOpenChange={setShowChangePassword} />
    </Card>
  );
}
