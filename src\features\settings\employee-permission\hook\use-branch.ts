import { useQuery } from "@tanstack/react-query";

import { locationApi } from "@/lib/apis/location";

export interface IGetBranchesParams {
  page?: number;
  limit?: number;
  query?: string;
  [key: string]: unknown;
}
interface UseBranchesOptions extends Partial<IGetBranchesParams> {
  enabled?: boolean;
}

export const useBranches = (options: UseBranchesOptions = {}) => {
  const { limit = 20, enabled = true, ...restOptions } = options;
  return useQuery({
    queryKey: ["branches", limit, ...Object.entries(restOptions).flat()],
    queryFn: () => locationApi.list({ limit, ...restOptions }),
  });
};

export const useBranchById = (id: string) => {
  return useQuery({
    queryKey: ["branch", id],
    queryFn: () => locationApi.getById(id),
    enabled: !!id,
  });
};
