import { authProtectedPaths } from "@/constants/paths";

export const ACCOUNT_PERMISSIONS = {
  // Staff Accounts
  [authProtectedPaths.ACCOUNTS]: ["LIST_STAFFS_ACCOUNT"],
  [`${authProtectedPaths.ACCOUNTS}/create`]: ["CREATE_STAFF_ACCOUNT"],
  [`${authProtectedPaths.ACCOUNTS}/:id`]: ["GET_STAFF_ACCOUNT"],
  [`${authProtectedPaths.ACCOUNTS}/:id/edit`]: ["UPDATE_STAFF_ACCOUNT"],
  [`${authProtectedPaths.ACCOUNTS}/:id/disable`]: ["DISABLE_STAFF_ACCOUNT"],
  [`${authProtectedPaths.ACCOUNTS}/:id/delete`]: ["DELETE_STAFF_ACCOUNT"],
  [`${authProtectedPaths.ACCOUNTS}/:id/authorization`]: ["UPDATE_STAFF_ACCOUNT_AUTHORIZATION"],
} as const;

export type AccountPermissionKey = keyof typeof ACCOUNT_PERMISSIONS;
export type AccountPermissionValue = (typeof ACCOUNT_PERMISSIONS)[AccountPermissionKey];
