// User role interface
export interface UserRole {
  branch_id: string;
  role_ids: string[];
}

// Employee type based on the API response
export interface Employee {
  id: string;
  username: string;
  name: string;
  email: string;
  phone_number: string | null;
  birthdate: string | null;
  address: string | null;
  picture: string | null;
  role: string;
  role_list: Array<{
    id: string;
    name: string;
  }>;
  branch_list: Array<{
    id: string;
    name: string;
  }>;
  status: "ENABLED" | "DISABLED";
  confirmation_status?:
    | "CONFIRMED"
    | "UNCONFIRMED"
    | "ARCHIVED"
    | "EXTERNAL_PROVIDER"
    | "UNKNOWN"
    | "RESET_REQUIRED"
    | "FORCE_CHANGE_PASSWORD";
  company_id: string;
  created_at: string;
  updated_at: string;
  user_roles: Array<{
    branch_id: string;
    role_ids: string[];
  }>;
  metadata?: {
    plan: string;
    role: string;
    company_id: string;
  };
}

// API response type for employee list
export interface EmployeeListResponse {
  total: number;
  page: number;
  limit: string;
  items: Employee[];
}

// Filter parameters for employee list
export interface EmployeeListParams {
  page?: number;
  limit?: number;
  company_id?: string;
  role?: string;
  ["role_list.id"]?: string;
  ["branch_list.id"]?: string;
  status?: string;
  location?: string;
  email?: string;
  phone_number?: string;
  created_at_from?: string;
  created_at_to?: string;
  updated_at_from?: string;
  updated_at_to?: string;
  search?: string;
}

// Employee creation payload
export interface CreateEmployeePayload {
  user_name: string;
  name: string;
  email: string;
  phone_number?: string;
  birthdate?: string;
  address?: string;
  role: string;
  company_id: string;
}

// Employee update payload
export interface UpdateEmployeePayload {
  id: string;
  data: Partial<Omit<CreateEmployeePayload, "company_id">>;
}

// Employee status update payload
export interface UpdateEmployeeStatusPayload {
  id: string;
  status: Employee["status"];
}

// Employee role update payload
export interface UpdateEmployeeRolePayload {
  id: string;
  role: Employee["role"];
}
