import cnFlag from "@/assets/locale/cn.png";
import enFlag from "@/assets/locale/en.png";
import jaFlag from "@/assets/locale/ja.png";
import krFlag from "@/assets/locale/kr.png";
import viFlag from "@/assets/locale/vi.png";

import { Currency, Language } from "./types";

export const AVAILABLE_LANGUAGES: Language[] = [
  {
    code: "en",
    name: "English",
    nativeName: "English",
    flag: enFlag,
    isDefault: true,
    isActive: true,
  },
  {
    code: "vi",
    name: "Vietnamese",
    nativeName: "Tiếng Việt",
    flag: viFlag,
    isDefault: false,
    isActive: true,
  },
  {
    code: "ja",
    name: "Japanese",
    nativeName: "日本語",
    flag: jaFlag,
    isDefault: false,
    isActive: true,
  },
  {
    code: "cn",
    name: "Chinese",
    nativeName: "中文",
    flag: cnFlag,
    isDefault: false,
    isActive: true,
  },
  {
    code: "kr",
    name: "Korean",
    nativeName: "한국어",
    flag: krFlag,
    isDefault: false,
    isActive: true,
  },
];

export const AVAILABLE_CURRENCIES: Currency[] = [
  {
    code: "USD",
    name: "US Dollar",
    symbol: "$",
    flag: enFlag,
    isDefault: true,
    isActive: true,
  },
  {
    code: "VND",
    name: "Vietnamese Dong",
    symbol: "₫",
    flag: viFlag,
    isDefault: false,
    isActive: true,
  },
  {
    code: "JPY",
    name: "Japanese Yen",
    symbol: "¥",
    flag: jaFlag,
    isDefault: false,
    isActive: true,
  },
];

export const LANGUAGE_CURRENCY_KEYS = {
  title: "settings.languageCurrency.title",
  description: "settings.languageCurrency.description",
  language: "settings.languageCurrency.language",
  currency: "settings.languageCurrency.currency",
  addLanguage: "settings.languageCurrency.addLanguage",
  addCurrency: "settings.languageCurrency.addCurrency",
  remove: "settings.languageCurrency.remove",
  update: "settings.languageCurrency.update",
  back: "settings.languageCurrency.back",
} as const;
