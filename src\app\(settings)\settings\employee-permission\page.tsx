"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";

import EmployeeTable from "@/features/settings/employee-permission/employee/components/employee-table";
import RoleTable from "@/features/settings/employee-permission/role/role-table";

import { Card, Tabs, TabsList, TabsTrigger } from "@/components/ui";

export default function EmployeePermissionPage() {
  const [tab, setTab] = useState<"employee" | "role">("employee");

  const handleTabChange = (newTab: "employee" | "role") => {
    window.history.replaceState({}, "", window.location.pathname);
    setTab(newTab);
  };

  return (
    <Card className="relative m-4 mt-0 border-none p-0">
      <div className="h-8 sm:hidden"></div>
      <div className="absolute left-4 top-4 z-10">
        <TabSwitcher tab={tab} setTab={handleTabChange} />
      </div>
      {tab === "employee" ? <EmployeeTable /> : <RoleTable setTab={setTab} />}
    </Card>
  );
}

function TabSwitcher({
  tab,
  setTab,
}: {
  tab: "employee" | "role";
  setTab: (view: "employee" | "role") => void;
}) {
  const { t } = useTranslation();
  return (
    <Tabs value={tab} onValueChange={(view) => setTab(view as "employee" | "role")}>
      <TabsList>
        <TabsTrigger value="employee">{t("pages.employeePermission.employee")}</TabsTrigger>
        <TabsTrigger value="role">{t("pages.employeePermission.role")}</TabsTrigger>
      </TabsList>
    </Tabs>
  );
}
