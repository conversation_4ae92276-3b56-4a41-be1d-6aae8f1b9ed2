"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { CircleAlert } from "lucide-react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { z } from "zod";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormMessage } from "@/components/ui/form";
import { InputOTP, InputOTPGroup, InputOTPSlot } from "@/components/ui/input-otp";

const verificationSchema = z.object({
  code: z.string().length(6, "Verification code must be 6 digits"),
});

type VerificationFormValues = z.infer<typeof verificationSchema>;

interface VerificationStepProps {
  email: string;
  verificationCode: string;
  isLoading: boolean;
  countdown: number;
  isCheckingDelay: boolean;
  error?: any;
  onVerificationCodeChange: (code: string) => void;
  onVerifyCode: (code: string) => void;
  onResendCode: () => void;
  onBack: () => void;
}

export const VerificationStep = ({
  email,
  verificationCode,
  isLoading,
  countdown,
  isCheckingDelay,
  error,
  onVerificationCodeChange,
  onVerifyCode,
  onResendCode,
  onBack,
}: VerificationStepProps) => {
  const { t } = useTranslation();

  const form = useForm<VerificationFormValues>({
    resolver: zodResolver(verificationSchema),
    defaultValues: {
      code: verificationCode || "",
    },
    mode: "onChange", // Enable real-time validation
  });

  const onSubmit = (data: VerificationFormValues) => {
    onVerificationCodeChange(data.code);
    onVerifyCode(data.code);
  };

  const handleCodeChange = (value: string) => {
    form.setValue("code", value);
    onVerificationCodeChange(value);

    // Auto-verify when 6 digits are entered
    if (value.length === 6) {
      console.log("value", value);
      onVerifyCode(value);
    }
  };

  const handleResendCode = () => {
    if (countdown > 0 || isCheckingDelay) return;
    onResendCode();
  };

  const isLoadingAny = isLoading || isCheckingDelay;
  const isFormValid = form.formState.isValid;
  const isDisabled = isLoadingAny || countdown > 0 || !isFormValid;

  const maskedEmail = email
    ? `••••••••••${email.split("@")[0].slice(-3)}@${email.split("@")[1]}`
    : t("pages.profile.forgotPasswordDialog.verificationStep.yourEmail");

  return (
    <>
      <div className="space-y-6 px-6 pb-8 pt-6">
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">
            {t("pages.profile.forgotPasswordDialog.verificationStep.title")}
          </h2>
          <p className="text-sm text-muted-foreground">
            {t("pages.profile.forgotPasswordDialog.verificationStep.description", {
              email: maskedEmail,
            })}
          </p>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="code"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <div className="flex justify-center">
                      <InputOTP
                        maxLength={6}
                        value={field.value}
                        onChange={handleCodeChange}
                        disabled={isLoading}
                        containerClassName="gap-4">
                        <InputOTPGroup>
                          <InputOTPSlot index={0} className="size-14 text-lg" />
                          <InputOTPSlot index={1} className="size-14 text-lg" />
                          <InputOTPSlot index={2} className="size-14 text-lg" />
                          <InputOTPSlot index={3} className="size-14 text-lg" />
                          <InputOTPSlot index={4} className="size-14 text-lg" />
                          <InputOTPSlot index={5} className="size-14 text-lg" />
                        </InputOTPGroup>
                      </InputOTP>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="text-center">
              <Button
                type="button"
                variant="link"
                className="h-fit p-0 text-sm leading-none text-muted-foreground hover:text-primary/90 disabled:cursor-not-allowed disabled:text-muted-foreground"
                onClick={handleResendCode}
                loading={isLoading}>
                {countdown > 0
                  ? t("auth.resendCodeCountdown", { seconds: countdown })
                  : isCheckingDelay
                    ? t("common.loading")
                    : t("pages.profile.forgotPasswordDialog.verificationStep.resend")}
              </Button>
            </div>

            {error && (
              <div className="flex items-center text-sm text-red-500">
                <CircleAlert className="mr-2 size-4" />
                {t(String(error))}
              </div>
            )}
          </form>
        </Form>
      </div>

      {/* <div className="space-y-4 border-t px-6 pb-6 pt-4">
        <div className="flex items-center justify-between">
          <Button type="button" variant="outline" onClick={onBack} disabled={isLoadingAny}>
            <ArrowLeftIcon className="mr-2 size-4" />
            {t("common.back")}
          </Button>

          <Button onClick={form.handleSubmit(onSubmit)} loading={isLoading} disabled={isDisabled}>
            {t("pages.profile.forgotPasswordDialog.verificationStep.continue")}
          </Button>
        </div>
      </div> */}
    </>
  );
};
