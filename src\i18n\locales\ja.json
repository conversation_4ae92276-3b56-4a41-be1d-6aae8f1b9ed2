{"nav": {"blog": "ブログ", "operation": "運営", "overview": "概要", "patientManagement": "患者管理", "doctorManagement": "医師管理", "medicalSupplies": "医療用品", "invoicesPayments": "請求書と支払い", "report": "レポート", "administration": "管理", "product": "商品", "productList": "商品一覧", "newProduct": "新しい商品を追加", "editProduct": "商品を編集", "variantsList": "バリエーション一覧", "brandList": "ブランド一覧", "categoryList": "カテゴリ一覧", "order": "注文", "orderList": "注文一覧", "orderDetail": "注文詳細", "orderEdit": "注文を編集", "orderProcess": "注文処理", "returnOrderList": "返品注文一覧", "packageList": "パッケージ一覧", "integration": "統合", "fetchEvent": "イベント情報取得", "syncRecords": "データ同期", "channel": "販売チャネル", "logistics": "物流", "shippingProviderList": "配送業者一覧", "purchaseOrder": "発注書", "purchaseOrderList": "発注書一覧", "supplierList": "仕入先一覧", "customers": "顧客", "customerDashboard": "ダッシュボード", "customerList": "顧客一覧", "customerDetail": "顧客詳細", "customerGroupList": "顧客グループ一覧", "loyaltyProgram": "ロイヤルティプログラム", "rewardProgram": "ポイントプログラム", "finance": "財務", "account": "アカウント", "paymentMethod": "支払い方法", "transaction": "取引", "inventory": "在庫", "locationList": "場所一覧", "inventoryList": "在庫一覧", "stockAdjustmentList": "在庫調整一覧", "stockRelocateList": "在庫移動一覧", "promotion": "プロモーション", "discountList": "割引商品一覧", "voucherList": "バウチャー一覧", "import": "インポート", "importList": "インポート一覧", "recordList": "記録一覧", "website": "ウェブサイト", "blogCategory": "ブログカテゴリ", "blogList": "ブログ一覧", "notification": "通知", "notificationList": "通知一覧", "loyaltyApp": "ロイヤルティアプリ", "pos": "POS販売", "detailFetchEvent": "イベント詳細取得", "supportedChannels": "対応チャネル一覧", "installChannel": "新しいチャネルを設定", "terminalList": "端末一覧", "shiftList": "勤務シフト一覧", "posFnB": "F&B POS販売", "settings": "設定", "dashboard": "ダッシュボード", "productReport": "商品レポート", "productDetail": "商品詳細", "orderManual": "注文を追加", "productMapping": "商品同期", "productMappingDetail": "同期詳細", "productMappingAttribute": "商品属性同期", "staff": "スタッフ", "staffList": "スタッフ一覧", "department": "部署", "conversation": "会話", "interact": "インタラクション", "knowledge": "知識", "task": "タスク", "editStaff": "スタッフを編集", "activities": "活動", "opportunities": "機会", "crm": "CRM", "opportunityDetail": "機会詳細", "pipelines": "パイプライン", "subscription": "サブスクリプション", "checkout": "チェックアウト", "propertyAssets": "不動産資産", "properties": "不動産", "layoutManagement": "レイアウト管理", "units": "賃貸ユニット", "contracts": "契約", "tenants": "入居者", "financial": "財務", "maintenance": "メンテナンス", "reports": "レポート", "propertyAssetsDashboard": "不動産資産ダッシュボード", "propertyDetail": "不動産詳細", "newProperty": "新しい不動産", "editProperty": "不動産を編集", "unitDetail": "ユニット詳細", "newUnit": "新しいユニット", "editUnit": "ユニットを編集", "contractDetail": "契約詳細", "newContract": "新しい契約", "editContract": "契約を編集", "tenantDetail": "入居者詳細", "newTenant": "新しい入居者", "editTenant": "入居者を編集", "payments": "支払い", "paymentDetail": "支払い詳細", "newPayment": "新しい支払い", "editPayment": "支払いを編集", "maintenanceDetail": "メンテナンス詳細", "newMaintenance": "新しいメンテナンス", "editMaintenance": "メンテナンスを編集", "assetCategories": "資産カテゴリ", "assetCategoryDetail": "資産カテゴリ詳細", "newAssetCategory": "新しい資産カテゴリ", "editAssetCategory": "資産カテゴリを編集", "documentManagement": "文書管理", "documentDetail": "文書詳細", "newDocument": "新しい文書", "editDocument": "文書を編集", "propertyGallery": "ギャラリー", "propertyGalleryDetail": "ギャラリー詳細", "newPropertyGallery": "新しい画像を追加", "editPropertyGallery": "画像を編集", "propertyValuation": "資産評価", "propertyValuationDetail": "評価詳細", "newPropertyValuation": "新しい評価", "editPropertyValuation": "評価を編集"}, "quota": {"storage": "ストレージ容量", "products": "商品数", "orders": "注文数", "messages": "メッセージ数", "staffs": "スタッフ数", "knowledge": "ナレッジ", "capacity": "容量", "knowledge_capacity": "ナレッジ容量"}, "product": {"image": "商品画像", "title": "商品名", "description": "説明", "price": "価格", "sku": "SKUコード", "brand": "ブランド", "category": "カテゴリ", "inventory": "在庫", "notMapped": "未リンク"}, "productMapping": {"lastSynced": "最終同期", "errorLoading": "商品リンク詳細の読み込みエラー", "manualRetry": "手動で再試行", "cancelledMessage": "商品リンクがキャンセルされました", "mappingStatus": "リンクステータス", "variant": "バリエーション"}, "groups": {"crm": "CRM", "operations": "運営", "virtual_staff": "バーチャルスタッフ", "product": "商品", "property_assets": "不動産資産"}, "branch": {"Ho Chi Minh": "ホーチミン市", "Ha Noi": "ハノイ", "Da Nang": "ダナン", "Hai Phong": "ハイフォン", "Can Tho": "カントー", "Binh Duong": "ビンズオン", "Binh Phuoc": "ビンフオック", "All": "すべて", "title": "支店を選択", "all": "すべての支店", "addBranch": "新しい支店を追加", "branch": "支店", "shortcuts": {"alt": "Alt", "plus": "+"}, "daily": "毎日", "weekly": "毎週", "monthly": "毎月", "yearly": "毎年", "annually": "毎年", "refresh": "更新"}, "profile": {"updateSuccess": "言語設定が正常に更新されました", "updateError": "言語設定の更新に失敗しました", "free": "無料", "profile": "プロフィール", "settings": "設定", "darkMode": "ダークモード", "on": "オン", "off": "オフ", "language": "言語", "english": "英語", "vietnamese": "ベトナム語", "japanese": "日本語", "selectLanguage": "言語を選択", "searchLanguage": "言語を検索...", "noLanguagesFound": "言語が見つかりません。", "current": "現在", "logout": "ログアウト", "founder": "創業者", "usedSpace": "使用済み容量", "upgrade": "アップグレード", "message": "メッセージ", "documents": "ドキュメント", "staff": "スタッフ", "storage": "ストレージ"}, "storeInformation": {"storeInformation": "店舗情報", "basicInformation": "基本情報", "basicInformationDescription": "顧客があなたに連絡するための情報です。", "advancedInformation": "詳細情報", "advancedInformationDescription": "URL、価格、店舗住所の高度な設定を構成します。", "storeName": "店舗名", "storeNamePlaceholder": "例：ABCショップ", "phone": "電話番号", "phonePlaceholder": "0123456789", "email": "メールアドレス", "emailPlaceholder": "<EMAIL>", "businessSector": "業種", "selectBusinessSector": "選択...", "storeUrl": "店舗URL", "storeUrlPlaceholder": "例：ABCショップ", "defaultPriceGroup": "デフォルト価格グループ", "selectPriceGroup": "選択...", "address": "住所", "addressPlaceholder": "店舗住所を入力", "province": "省/市", "selectProvince": "選択...", "district": "区/郡", "selectDistrict": "選択...", "ward": "坊/社", "selectWard": "選択..."}, "auth": {"brandSection": {"title": "14日間の無料トライアル"}, "passwordResetSuccess": "パスワードが再設定されました", "changePasswordTitle": "パスワードを変更", "changePasswordSubtitle": "現在のパスワードと新しいパスワードを入力してください", "changePasswordSuccess": "パスワードの変更に成功しました", "changePasswordError": "パスワードの変更に失敗しました", "changePasswordLoading": "パスワードの変更中...", "currentPasswordRequired": "現在のパスワードは必須です", "currentPasswordPlaceholder": "現在のパスワードを入力", "minPasswordLength": "パスワードは8文字以上である必要があります", "passwordsDontMatch": "パスワードが一致しません", "incorrectUsernameOrPassword": "ユーザー名またはパスワードが正しくありません。", "userExist": "ユーザー名はすでに存在しています！", "userExistAndVerified": "このメールはすでに登録・認証されています。ログインするか、別のメールをご使用ください。", "logoutSuccess": "ログアウト成功", "logoutError": "ログアウトエラー", "gender": "性別", "genderPlaceholder": "性別を選択", "male": "男性", "female": "女性", "other": "その他", "preferNotToSay": "回答しない", "dob": "生年月日", "dobPlaceholder": "生年月日を選択", "username": "ユーザー名", "usernamePlaceholder": "ユーザー名を入力", "login": "ログイン", "register": "登録", "forgotPasswordDescription": "メールアドレスを入力して、手順を受け取りましょう！", "forgotPasswordTitle": "パスワードをお忘れですか？", "forgotPasswordSubtitle": "パスワード再設定の手順を受け取るにはメールを入力してください", "resetPassword": "パスワードを再設定", "resetPasswordTitle": "パスワード再設定", "resetPasswordDescription": "認証コードと新しいパスワードを入力してください", "resetPasswordSubtitle": "認証コードと新しいパスワードを入力してください", "resetPasswordButton": "パスワードを再設定", "resetPasswordSuccess": "パスワードの再設定に成功しました", "resetPasswordSuccessDescription": "新しいパスワードでログインできます", "resetPasswordError": "パスワードを再設定できません", "resetPasswordLoading": "再設定中...", "confirmPassword": "パスワード確認", "confirmPasswordPlaceholder": "パスワードを確認", "backToLogin": "ログイン画面に戻る", "backToForgotPassword": "ログインに戻る", "loginTitle": "ログイン", "loginSubtitle": "ユーザー名またはメールアドレスを入力してログイン", "email": "メールアドレス", "emailPlaceholder": "m@example", "emailPlaceholderSignUp": "メールアドレスを入力", "verifyEmail": "メール認証", "verifyEmailButton": "メールを認証", "verifyEmailSuccess": "メール認証に成功しました", "verifyEmailError": "メール認証に失敗しました", "verifyEmailLoading": "認証中...", "verifyEmailCode": "メールに送信されたコードを入力してください", "verifyEmailCodePlaceholder": "コードを入力", "verifyEmailCodeButton": "コードを認証", "verifyEmailCodeSuccess": "コードの認証に成功しました", "verifyEmailCodeError": "コードの認証に失敗しました", "verifyEmailCodeLoading": "コード認証中...", "newPassword": "新しいパスワード", "newPasswordPlaceholder": "新しいパスワードを入力", "verificationCode": "認証コード", "verificationCodePlaceholder": "認証コードを入力", "verificationCodeButton": "認証", "verificationCodeSuccess": "認証成功", "verificationCodeError": "認証失敗", "verificationCodeDescription": "{{username}} にコードを送信しました。以下に入力してください。", "sendInstructions": "手順を送信", "sending": "送信中...", "resetting": "再設定中...", "password": "パスワード", "passwordPlaceholder": "パスワードを入力", "rememberMe": "ログイン情報を記憶する", "loginButton": "ログイン", "loginWithGoogle": "Googleでログイン", "loginWithGithub": "Githubでログイン", "noAccount": "アカウントをお持ちでないですか？", "signUp": "登録", "signUpTitle": "登録", "signUpSubtitle": "管理画面にログインするために登録してください", "signUpButton": "登録", "signUpSuccess": "登録成功！メール認証を行ってください", "signUpError": "登録失敗", "signUpLoading": "登録中...", "alreadyHaveAccount": "すでにアカウントをお持ちですか？", "sendNewCode": "再送信", "resendCodeSuccess": "認証コードを再送信しました", "resendCodeError": "認証コードを再送信できません", "usernameOrEmail": "ユーザー名またはメールアドレス", "usernameOrEmailPlaceholder": "ユーザー名またはメールアドレスを入力", "forgot": "パスワードを忘れた", "or": "または", "loginSuccess": "ログイン成功", "loginError": "ログイン失敗", "loginLoading": "ログイン中...", "usernameRequired": "ユーザー名を入力してください", "emailRequired": "メールアドレスを入力してください", "passwordRequired": "パスワードを入力してください", "confirmPasswordRequired": "パスワードを確認してください", "invalidPassword": "パスワードは8文字以上である必要があります", "forgotPasswordSuccess": "パスワード再設定の手順をメールに送信しました", "forgotPasswordError": "パスワード再設定の手順を送信できません", "newPasswordRequired": "新しいパスワードは必須です", "pleaseChangePassword": "パスワードを変更してください", "passwordsDoNotMatch": "パスワードが一致しません", "passwordMustBeAtLeast8Characters": "パスワードは8文字以上である必要があります", "codeRequired": "認証コードを入力してください", "resendCodeCountdown": "{{seconds}}秒後に再送信可能"}, "onboarding": {"step1": {"title": "OnexBots をどこで知りましたか？", "options": {"facebook": "Facebook", "zalo": "<PERSON><PERSON>", "youtube": "YouTube", "instagram": "Instagram", "tiktok": "TikTok", "google": "Google", "linkedin": "LinkedIn", "referral": "紹介・人づて", "other": "その他"}, "otherPlaceholder": "X、Threads、Whatsapp など"}, "step2": {"title": "AIチャットソフトを使ったことはありますか？", "options": {"never": "使ったことがない", "tried": "使ったことがある", "regularly": "定期的に使っている"}}, "step3": {"title": "どの業界で働いていますか？", "options": {"ecommerce": "Eコマース", "travel": "旅行", "real_estate": "不動産", "health": "ヘルスケア・美容", "education": "教育", "other": "その他"}, "otherPlaceholder": "IT、物流 など"}, "step3_part2": {"title": "あなたの会社の従業員数は？", "options": {"just_me": "自分だけ", "2-9": "2〜9人", "10-49": "10〜49人", "50-199": "50〜199人", "200-499": "200〜499人", "500+": "500人以上"}, "inputLabel": "あなたのURL（任意）", "inputPlaceholder": "www.example.com"}, "step4": {"title": "あなたの企業はどの分野で活動していますか？", "options": {"specialty_clinic": "総合病院", "aesthetic_clinic": "美容クリニック／スパ", "cosmetic_surgery": "美容整形", "nutrition_clinic": "栄養療法・スポーツ医学", "telemedicine": "遠隔医療・医療テクノロジー", "pharma": "医薬品・化粧品小売チェーン", "other": "その他"}, "otherPlaceholder": "総合ヘルスケアセンター、バイオテック系スタートアップ など"}, "step5": {"title": "どの分野の専門家の支援が必要ですか？", "options": {"consulting": "コンサルティング", "customer_care": "カスタマーケア", "accounting": "会計", "marketing": "マーケティング", "other": "その他"}, "otherPlaceholder": "法務相談、ITサポート など"}, "step6": {"title": "OnexBots を使う目的は何ですか？", "options": {"feedback": "フィードバックの収集・分析", "pressure": "ピーク時の負担軽減", "channels": "複数チャネルの同時対応", "quality": "応対品質の向上", "responses": "自動高速応答", "monitor": "スタッフのトレーニング・監視", "other": "その他"}, "otherPlaceholder": "リード獲得、顧客教育 など"}, "buttons": {"back": "戻る", "skip": "スキップ", "continue": "続ける", "done": "完了"}, "otherInput": {"pleaseSpecify": "詳細を入力してください", "optional": "（任意）"}}, "common": {"submit": "送信", "dataCreatedBySystem": "ルートデータ", "exceedTotalSize": "総ファイルサイズは5MBを超えてはいけません", "tryingToAdd": "追加しようとしています", "description": "説明", "fileSizeMustNotExceed": "ファイルサイズは", "onlyImageFilesAllowed": "画像ファイルのみが許可されています", "resetToDefault": "デフォルトにリセット", "settings": "設定", "viewOnWeb": "Webで表示", "confirmCancel": "この操作は元に戻せません。本当にキャンセルしますか？", "selected": "選択済み", "pickADateRange": "期間を選択", "other": "その他", "escTo": "Escキーで", "at": "で", "areYouAbsolutelySure": "本当に実行しますか？", "areYouAbsolutelySureDescription": "この操作は元に戻せません。本当に続行しますか？", "canNotDeleteStage": "この列は削除できません", "canNotDeleteStageDescription": "削除するには、すべての案件をこの列から移動してください。", "selectCountry": "国を選択", "displayCustomizer": "表示カスタマイズ", "customizeTheDisplayOfContentColumnsAccordingToYourPreferences": "コンテンツ列の表示を好みに合わせてカスタマイズします。", "noDataAvailable": "データがありません", "start": "開始", "back": "戻る", "words": "単語", "confirm": "確認", "apply": "適用", "totalSize": "合計サイズ", "aspectRatio": "比率", "formats": "フォーマット", "recommendedSize": "推奨サイズ", "search": "検索", "filter": "フィルター", "reset": "リセット", "setAsDefault": "デフォルトに設定", "saveFilters": "フィルターを保存", "sort": "並び替え", "view": "表示", "add": "追加", "update": "更新", "edit": "編集", "delete": "削除", "cancel": "キャンセル", "save": "保存", "saving": "保存中...", "close": "閉じる", "clear": "クリア", "loading": "読み込み中...", "loadingMore": "さらに読み込み中...", "deleting": "削除中...", "toggleGrid": "グリッド表示切替", "snapToGrid": "グリッドに合わせる", "alignHorizontally": "水平に整列", "alignVertically": "垂直に整列", "noData": "データなし", "success": "成功", "uploadImage": "画像をドラッグ＆ドロップ、または", "upload": "アップロード", "uploading": "アップロード中...", "fileSizeError": "ファイルサイズは5MB未満である必要があります", "uploadError": "画像のアップロードに失敗しました", "imageUploadError": "スタッフ画像のアップロードに失敗しました", "areYouSure": "本当に実行しますか？", "leaveDesc": "保存されていない変更は失われます。", "deleteTaskConfirmation": "この操作は元に戻せません。タスクは完全に削除されます。", "deleteProductConfirmation": "この操作は元に戻せません。商品は完全に削除されます。", "deleteListProductConfirmation": "この操作は元に戻せません。{{count}} 件の商品が完全に削除されます。", "deleteOpportunityConfirmation": "この操作は元に戻せません。案件は完全に削除されます。", "deleteEmployeeConfirmation": "この操作は元に戻せません。{{count}} 人の従業員が完全に削除されます。", "install": "インストール", "configure": "設定", "deleteSuccess": "商品を正常に削除しました", "deleteError": "商品を削除できませんでした", "deleteSuccessDescription": "商品は正常に削除されました", "actions": "操作", "bulkActions": "一括操作", "all": "すべて", "sortBy": "並び替え基準", "select": "選択", "label": "ラベル", "issues": "問題", "status": {"available": "利用可能", "occupied": "使用中", "maintenance": "メンテナンス中", "inactive": "非アクティブ", "active": "アクティブ", "status": "ステータス", "healthy": "正常", "issues": "問題あり", "issue": "問題", "complete": "完了", "inProgress": "進行中", "processing": "処理中", "error": "エラー", "ready": "準備完了", "pending": "保留中", "success": "成功", "online": "オンライン", "offline": "オフライン"}, "error": "エラーが発生しました", "saveChanges": "変更を保存", "unsavedChanges": "未保存の変更", "unsavedChangesDescription": "保存されていない変更があります。閉じてもよろしいですか？", "discard": "破棄", "keepEditing": "編集を続ける", "week": "週", "month": "月", "quarter": "四半期", "year": "年", "units": "単位", "leaveWithoutSavingDescription": "保存されていない変更があります。終了してもよろしいですか？", "leaveWithoutSaving": "保存せずに終了", "leave": "終了", "restore": "復元", "stay": "このままにする", "knowledgeUpdated": "ナレッジが正常に更新されました", "knowledgeDeleted": "ナレッジが正常に削除されました", "areYouSureDescription": "このナレッジを削除してもよろしいですか？", "staffUpdated": "スタッフ情報を正常に更新しました", "updateStaffError": "スタッフ情報を更新できませんでした", "areYouSureConfirm": "確認", "areYouSureCancel": "キャンセル", "updateAttribute": "属性を更新", "time": {"month": "月", "timeAgo": {"seconds": "{{count}} 秒前", "seconds_plural": "{{count}} 秒前", "minutes": "{{count}} 分前", "minutes_plural": "{{count}} 分前", "hours": "{{count}} 時間前", "hours_plural": "{{count}} 時間前", "days": "{{count}} 日前", "days_plural": "{{count}} 日前", "months": "{{count}} ヶ月前", "months_plural": "{{count}} ヶ月前", "years": "{{count}} 年前", "years_plural": "{{count}} 年前", "invalidDate": "無効な日付"}}, "empty": {"title": "ここには何もありません！", "description": "一致する結果が見つかりませんでした。"}, "create": "作成", "noFileSelected": "ファイルが選択されていません", "uploadSuccess": "アップロード成功", "fileTooLarge": "ファイルサイズが最大 {{max}}MB を超えています", "lastUpdated": "最終更新", "name": "名前", "loadMore": "さらに読み込む", "markAsDone": "完了にする", "zoomIn": "ズームイン", "zoomOut": "ズームアウト", "fitToScreen": "画面に合わせる", "backToOverview": "概要に戻る", "progress": "進捗", "opacity": "不透明度", "visible": "表示", "properties": "プロパティ", "duplicate": "複製", "quickActions": "クイック操作", "imageLoadError": "画像を読み込めません", "uploadNew": "新しくアップロード", "viewMode": "表示モード", "editMode": "編集モード", "total": "合計", "permissions": "権限", "allow": "許可", "enterNote": "メモを入力", "noPermission": "この操作を実行する権限がありません", "noRoutePermission": "このページにアクセスする権限がありません", "noModuleAccess": "このモジュールにアクセスする権限がありません"}, "tenants": {"title": "<PERSON><PERSON><PERSON><PERSON> thuê", "addTenant": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON><PERSON> thuê", "editTenant": "Chỉnh sửa người thuê", "tenant": "<PERSON><PERSON><PERSON><PERSON> thuê", "tenantDetail": "<PERSON> tiết ngư<PERSON>i thuê", "tenantDetails": "<PERSON> tiết ngư<PERSON>i thuê", "tenantId": "<PERSON>ã người thuê", "tenantName": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> thuê", "sections": {"personalInfo": "Thông tin cá nhân", "contactInfo": "<PERSON>h<PERSON>ng tin liên hệ", "emergencyContact": "<PERSON><PERSON><PERSON> h<PERSON> khẩn cấp", "leaseInfo": "Thông tin thuê", "documents": "<PERSON><PERSON><PERSON> l<PERSON>", "paymentInfo": "Thông tin thanh toán", "notes": "<PERSON><PERSON><PERSON>", "identification": "<PERSON><PERSON><PERSON><PERSON> tờ tùy thân", "employment": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON>"}, "fields": {"firstName": "<PERSON><PERSON><PERSON>", "lastName": "Họ", "fullName": "<PERSON><PERSON> tên", "email": "Email", "phone": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "address": "Địa chỉ", "dateOfBirth": "<PERSON><PERSON><PERSON>", "occupation": "<PERSON><PERSON><PERSON>", "emergencyContactName": "<PERSON><PERSON><PERSON> liên hệ khẩn cấp", "emergencyContactPhone": "SĐT li<PERSON>n hệ khẩn cấp", "relationship": "<PERSON><PERSON><PERSON> quan hệ", "leaseStart": "<PERSON><PERSON><PERSON> b<PERSON>t đầu thuê", "leaseEnd": "<PERSON><PERSON><PERSON> kết thúc thuê", "rentAmount": "<PERSON><PERSON><PERSON><PERSON> thuê", "securityDeposit": "Tiền đặt cọc", "unit": "Đơn vị", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "notes": "<PERSON><PERSON><PERSON>", "documents": "<PERSON><PERSON><PERSON> l<PERSON>", "identificationType": "<PERSON><PERSON><PERSON> gi<PERSON>y tờ", "identificationNumber": "Số gi<PERSON>y tờ", "employmentStatus": "<PERSON><PERSON><PERSON> trạng công việc", "employerName": "<PERSON><PERSON>n công ty", "monthlyIncome": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> hàng tháng"}, "placeholders": {"enterFirstName": "<PERSON><PERSON><PERSON><PERSON> tên", "enterLastName": "<PERSON><PERSON><PERSON><PERSON>", "enterEmail": "<PERSON><PERSON><PERSON><PERSON> địa chỉ email", "enterPhone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "enterOccupation": "<PERSON><PERSON><PERSON><PERSON> ngh<PERSON> nghiệp", "selectUnit": "<PERSON><PERSON>n đơn vị", "selectStatus": "<PERSON><PERSON><PERSON> trạng thái", "enterNotes": "<PERSON><PERSON><PERSON><PERSON>hi <PERSON>ú", "firstName": "<PERSON><PERSON><PERSON><PERSON> tên", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "email": "Enter email address", "phone": "Enter phone number", "identificationType": "Select identification type", "identificationNumber": "Enter identification number", "employmentStatus": "Select employment status", "employerName": "Enter employer name", "monthlyIncome": "Enter monthly income", "emergencyContactName": "Enter emergency contact name", "emergencyContactPhone": "Enter emergency contact phone"}, "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "pending": "Chờ xử lý", "terminated": "<PERSON><PERSON> chấm d<PERSON>"}, "actions": {"save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "edit": "Chỉnh sửa", "delete": "Xóa", "view": "<PERSON>em chi tiết"}, "messages": {"saveSuccess": "<PERSON><PERSON><PERSON> ng<PERSON>i thuê thành công", "saveError": "Lỗi khi lưu người thuê", "deleteSuccess": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i thuê thành công", "deleteError": "Lỗi khi xóa người thuê", "deleteConfirm": "Bạn có chắc chắn muốn xóa người thuê này?", "required": "Trư<PERSON>ng này là bắ<PERSON> buộc", "createSuccess": "Tenant created successfully", "updateSuccess": "Tenant updated successfully", "createError": "Failed to create tenant", "updateError": "Failed to update tenant"}, "contracts": {"title": "<PERSON><PERSON><PERSON>", "contract": "<PERSON><PERSON><PERSON>", "contractId": "<PERSON><PERSON> hợp đồng", "startDate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "endDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "monthlyRent": "<PERSON><PERSON><PERSON><PERSON> thuê hàng tháng", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "viewContract": "<PERSON><PERSON> đ<PERSON>"}, "quickStats": "<PERSON><PERSON><PERSON><PERSON> kê nhanh", "activeContracts": "<PERSON><PERSON><PERSON> đồng hoạt động", "documents": "<PERSON><PERSON><PERSON> l<PERSON>", "stats": {"totalContracts": "<PERSON><PERSON>ng số hợp đồng", "activeContracts": "<PERSON><PERSON><PERSON> đồng đang hoạt động", "joinDate": "<PERSON><PERSON><PERSON> gia nh<PERSON>p"}, "employmentStatus": {"employed": "<PERSON><PERSON> vi<PERSON><PERSON> làm", "unemployed": "<PERSON><PERSON><PERSON><PERSON>", "self_employed": "Tự kinh doanh", "student": "Sin<PERSON> viên", "retired": "Retired"}, "identificationTypes": {"passport": "<PERSON><PERSON> ch<PERSON>", "national_id": "CMND/CCCD", "driver_license": "Bằng lái xe"}, "errors": {"notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy người thuê", "notFoundDescription": "<PERSON>ười thuê mà bạn đang tìm không tồn tại hoặc đã bị xóa."}, "deleteConfirmation": {"title": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> thuê", "description": "Bạn có chắc chắn muốn xóa người thuê này? Hành động này không thể hoàn tác.", "hasActiveContracts": "<PERSON><PERSON><PERSON>i thuê này có hợp đồng đang hoạt động. Bạn có chắc chắn muốn xóa?"}, "createTenant": "<PERSON><PERSON><PERSON>", "createDescription": "<PERSON><PERSON><PERSON><PERSON> thông tin chi tiết để tạo người thuê mới", "editDescription": "Update the tenant information"}, "maintenance": {"form": {"requestId": "<PERSON><PERSON> yêu c<PERSON>u", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "priority": "<PERSON><PERSON> <PERSON> tiên", "category": "<PERSON><PERSON>", "reportedDate": "<PERSON><PERSON><PERSON> b<PERSON>o c<PERSON>o", "description": "<PERSON><PERSON>", "estimatedCost": "Chi phí <PERSON><PERSON> t<PERSON>h", "contractorName": "<PERSON><PERSON><PERSON> nhà thầu", "contractorPhone": "SĐT nhà thầu", "notes": "<PERSON><PERSON><PERSON>", "actualCost": "<PERSON> phí thực tế"}, "details": {"overview": "<PERSON><PERSON><PERSON> quan", "timeline": "<PERSON><PERSON><PERSON> thời gian", "daysSinceReported": "<PERSON><PERSON><PERSON> kể từ khi báo cáo", "cost": "Chi phí", "related": "<PERSON><PERSON><PERSON> quan", "contractor": "<PERSON><PERSON><PERSON> thầu"}, "status": {"in_progress": "<PERSON><PERSON> ti<PERSON>n hành", "pending": "Chờ xử lý", "completed": "<PERSON><PERSON><PERSON> th<PERSON>", "cancelled": "<PERSON><PERSON> hủy", "open": "Mở"}, "priority": {"low": "<PERSON><PERSON><PERSON><PERSON>", "medium": "<PERSON>rung bình", "high": "<PERSON>", "urgent": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>p"}, "category": {"plumbing": "<PERSON><PERSON> thống n<PERSON>", "electrical": "<PERSON><PERSON><PERSON><PERSON>", "hvac": "<PERSON><PERSON><PERSON><PERSON> hòa thông gió", "structural": "<PERSON><PERSON><PERSON> c<PERSON>", "appliance": "<PERSON><PERSON><PERSON><PERSON> bị", "cosmetic": "<PERSON><PERSON><PERSON><PERSON> mỹ", "cleaning": "<PERSON><PERSON>", "security": "An ninh", "general": "<PERSON>", "other": "K<PERSON><PERSON><PERSON>"}, "messages": {"deleteSuccess": "<PERSON><PERSON><PERSON> y<PERSON>u cầu bảo trì thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa yêu cầu bảo trì", "statusUpdateSuccess": "<PERSON><PERSON><PERSON> nhật trạng thái thành công", "statusUpdateError": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật trạng thái"}, "errors": {"notFound": "<PERSON><PERSON><PERSON><PERSON>", "notFoundDescription": "<PERSON><PERSON><PERSON> cầu bảo trì bạn đang tìm không tồn tại hoặc đã bị xóa."}, "dialog": {"deleteTitle": "<PERSON><PERSON><PERSON>", "deleteDescription": "Bạn có chắc chắn muốn xóa yêu cầu bảo trì này? Hành động này không thể hoàn tác."}}, "shapes": {"rectangle": "<PERSON><PERSON><PERSON> chữ nhật", "circle": "<PERSON><PERSON><PERSON> tròn", "polygon": "<PERSON><PERSON>"}, "pages": {"editor": {"placeholder": "「/」キーでコマンドを使用...", "characters": "文字", "words": "単語"}, "blogList": {"title": "ブログ一覧", "headers": {"image": "画像", "title": "タイトル", "category": "カテゴリ", "updatedAt": "更新日", "actions": "操作"}, "filters": {"search": {"placeholder": "ブログを検索..."}, "category": "カテゴリ", "createdAt": "作成日", "updatedAt": "更新日"}, "actions": {"import": "インポート", "addBlog": "ブログを追加"}}, "checkout": {"title": "支払い", "orderInformation": {"title": "注文情報", "confirm": "確認", "cancel": "キャンセル", "subscribeTo": "プランに登録", "registrationTime": "登録時間", "price": "価格", "creationDate": "作成日", "paymentMethod": "支払い方法", "issueInvoice": "請求書を発行", "companyName": "会社名", "taxId": "税番号", "address": "住所", "email": "会社のメール", "vat": "VAT（0％）", "total": "合計金額", "pay": "支払う", "companyNamePlaceholder": "例：ABC株式会社", "taxIdPlaceholder": "税番号を入力", "addressPlaceholder": "住所を入力", "emailPlaceholder": "例：<EMAIL>"}, "qrPayment": {"title": "QRコードで振込", "accountName": "口座名義", "accountNumber": "口座番号", "bankName": "銀行名", "amount": "金額", "content": "内容", "cancelOrder": "注文をキャンセル", "success": "支払い成功！", "redirectingIn": "{{countdown}}秒後に登録ページへ移動します..."}}, "opportunities": {"noOrderYet": "注文がありません", "clickTabToAddOrder": "タブをクリックして注文を追加", "orders": "注文", "opportunities": "案件", "deleteSuccess": "案件を正常に削除しました", "deleteError": "案件を削除できませんでした", "title": "案件", "add": "追加", "search": "検索...", "addOpportunity": "新しい案件", "contact": "顧客", "salesperson": "担当者", "expectedClosing": "予想終了日", "tags": "タグ", "phone": "電話番号", "enterContactName": "顧客名を入力...", "enterEmailAddress": "メールアドレスを入力...", "enterTags": "タグを入力...", "enterPhoneNumber": "電話番号を入力...", "opportunityDetail": "案件詳細", "enterOpportunityTitle": "案件名を入力...", "enterProbability": "確率を入力...", "expectedRevenue": "予想収益", "probability": "確率", "customerInfo": "顧客情報", "salesPerson": "営業担当", "notes": "メモ", "enterRevenue": "収益を入力...", "selectAssignee": "担当者を選択", "enterExpectedClosingDate": "予想終了日を選択...", "status": {"won": "獲得", "lost": "失注", "ongoing": "進行中"}, "lostReason": {"title": "失注理由", "description": "失注理由を選択するか、カスタム理由を入力してください。", "selectReason": "理由を選択", "selectPlaceholder": "理由を選択...", "searchPlaceholder": "理由を検索...", "noResults": "理由が見つかりません。", "customReason": "カスタム理由", "customPlaceholder": "理由を入力してください...", "priceTooHigh": "価格が高すぎる", "competitorWon": "競合が勝利", "noBudget": "予算なし", "timingIssues": "タイミングの問題", "technicalRequirements": "技術要件が満たされない", "decisionMakerChanged": "意思決定者が変更された", "projectCancelled": "プロジェクトがキャンセルされた", "poorFit": "ニーズに合わない", "noLongerInterested": "興味を失った", "stopResponding": "応答が止まった", "foundBetterAlternative": "より良い選択肢を見つけた", "productDidNotMeetExpectations": "製品が期待に応えなかった"}}, "stages": {"egDiscuss": "例：会議", "createOpportunity": "案件を作成", "opportunityNamePlaceholder": "例：病院向けチャットボット", "contactPlaceholder": "例：<PERSON><PERSON><PERSON><PERSON>", "editColumn": "列を編集", "columnName": "列名", "isWonStage": "獲得ステージですか？", "deleted": "列の削除に成功しました", "added": "列の追加に成功しました", "failedToCreateStage": "列を作成できませんでした", "yesterday": "昨日", "overdue": "期限切れ", "today": "今日", "upcoming": "近日中", "noDueDate": "期限なし", "noActivity": "アクティビティなし", "inDays": "{{count}}日後", "tomorrow": "明日", "daysAgo": "{{count}}日前", "filters": {"createdAt": "作成日", "updatedAt": "更新日", "outcome": "結果"}, "newColumn": "新しい列", "newColumnPlaceholder": "列名を入力して Enter を押してください", "unassigned": "未割り当て", "editActivity": "アクティビティを編集", "fold": "折りたたむ", "edit": "編集", "delete": "削除", "view": "表示", "addColumnAfter": "後に列を追加", "addColumnBefore": "前に列を追加", "deleteColumn": "列を削除", "search": "検索...", "title": "進捗管理", "add": "追加", "addOpportunity": "新しい案件", "status": "ステータス", "opportunity": "案件", "contact": "顧客", "phone": "電話番号", "expectedRevenue": "予想収益", "priority": "優先度", "assignee": "担当者", "selectAssignee": "担当者を選択", "noScheduledActivities": "予定されたアクティビティはありません", "noActivitiesForStatus": "{{status}} にアクティビティはありません", "scheduleActivity": "アクティビティをスケジュール", "activityType": "アクティビティの種類", "selectActivityType": "種類を選択", "searchActivityTypes": "種類を検索...", "dueDate": "期限日", "summary": "概要", "notes": "メモ", "typeSomething": "入力してください...", "searchAssignee": "担当者を検索...", "noAssigneeFound": "担当者が見つかりません。", "pickADate": "日付を選択", "schedule": "スケジュール", "actionName": "アクション名", "newActivityType": "新しいアクティビティの種類", "salesperson": "営業担当", "expectedClosing": "予想終了日", "tags": "タグ", "headers": {"contact": "顧客", "email": "メール", "opportunity": "案件", "stage": "ステージ", "status": "ステータス", "assignee": "担当者", "createdAt": "作成日", "updatedAt": "更新日", "customer": "顧客", "amount": "金額", "closeDate": "終了日", "probability": "確率", "expectedClosingDate": "予想終了日", "expectedRevenue": "予想収益", "activities": "アクティビティ"}, "sort": {"title": "並び替え", "order": "順序", "closingDate": "終了日", "dealValue": "取引金額", "created": "作成日", "lastActivity": "最新アクティビティ", "winProbability": "獲得確率", "rating": "評価", "lowestToHighest": "昇順", "highestToLowest": "降順"}}, "onexbotsDashboard": {"title": "OnexBots ダッシュボード", "filters": {"period": "期間", "daily": "日次", "weekly": "週次", "monthly": "月次", "yearly": "年次", "staff": "スタッフ"}, "stats": {"conversations": "会話数", "users": "ユーザー数", "accuracyRate": "正確率", "averageResponseTime": "平均応答時間", "viewMore": "もっと見る", "fromLastPeriod": "前回期間比", "noComparisonData": "比較データなし"}, "accuracyRateChart": {"title": "正確率", "description": "回答の正確性をナレッジベースと比較", "tooltip": {"rate": "正確率", "resolved": "解決済み", "total": "質問総数"}}, "responseTimeChart": {"title": "平均応答時間", "description": "全体の平均応答時間", "fastest": "最速", "slowest": "最遅", "tooltip": {"average": "平均", "min": "最小", "max": "最大"}}}, "onboarding": {"welcomeTo": "ようこそ", "skip": "スキップ", "done": "完了"}, "overview": {"title": "概要", "filters": {"period": "期間", "daily": "日次", "weekly": "週次", "monthly": "月次", "yearly": "年次", "selectLocation": "施設を選択", "refresh": "更新"}, "stats": {"totalFacilities": "施設総数", "totalPatients": "患者総数", "averageOccupancy": "平均入居率", "totalRevenue": "総収益", "viewMore": "もっと見る", "fromLastMonth": "前月比"}, "patientStats": {"title": "患者統計", "outpatient": "外来", "inpatient": "入院"}, "topTwenty": {"title": "トップ20", "icdDiagnoses": "ICD診断", "prescribedMedications": "処方薬"}, "costs": {"averageTreatmentCosts": "平均治療費", "insurancePayments": "保険支払い", "insurance": "保険", "service": "サービス", "specialCare": "特別ケア"}, "treatmentOutcomes": {"title": "治療結果", "recovered": "回復", "improved": "改善", "unchanged": "変化なし", "deteriorated": "悪化", "deceased": "死亡", "left": "退院"}}, "customers": {"title": "顧客一覧", "filters": {"search": {"placeholder": "名前で検索"}, "group": "グループ", "createdAt": "作成日", "updatedAt": "最終更新"}, "name": "名前", "phone": "電話番号", "email": "メール", "address": "住所", "group": "グループ", "createdAt": "作成日", "updatedAt": "更新日", "orderHistory": "注文履歴"}, "orders": {"print": "印刷", "sendEmail": "メールを送信", "enterServiceName": "サービス名を入力", "serviceNameCannotBeEmpty": "サービス名を入力してください", "order": "注文", "printInvoice": "請求書を印刷", "printOrder": "注文を印刷", "printReceipt": "領収書を印刷", "productInformation": "商品情報", "qty": "数量", "totalAmount": "合計金額", "notes": "注文メモ", "hasBeenCreatedSuccessfully": "注文が正常に作成されました！", "orderStatus": {"draft": "草稿", "shipping": "配送中", "await_packing": "梱包待ち", "delivered": "配送済み", "pending": "保留中", "confirmed": "確定", "cancelled": "キャンセル", "completed": "完了", "partial": "一部完了", "returned": "返品"}, "orderPaymentStatus": {"unpaid": "未払い", "pending": "保留中", "paid": "支払い済み", "partiallyPaid": "一部支払い", "cancelled": "キャンセル"}, "filters": {"shift": "勤務シフト", "status": "注文ステータス", "paymentStatus": "支払いステータス", "createdAt": "作成日", "updatedAt": "更新日"}, "orderHistory": "注文履歴", "amount": "数量", "redeemPoints": "ポイント交換", "loyalPoints": "累積ポイント", "updatedAt": "更新日", "title": "注文一覧", "searchPriceGroup": "価格グループを検索", "noPriceGroupsFound": "価格グループが見つかりません", "searchBranch": "支店を検索", "noBranchFound": "支店が見つかりません", "emptyServiceName": "サービス名を入力してください", "updateOrder": "更新", "selectGender": "性別を選択", "tax": "税金", "shipping": "送料", "addCustomer": "顧客を追加", "save": "保存", "defaultShippingAddress": "デフォルト配送先住所", "defaultBillingAddress": "デフォルト請求先住所", "noAddressesFound": "住所が見つかりません", "edit": "編集", "removeRecipientInfo": "受取人情報を削除", "addRecipientInfo": "受取人情報を追加", "enterName": "名前を入力", "enterAddress": "住所を入力", "enterPhoneNumber": "電話番号を入力", "enterCompanyName": "会社名を入力", "selectWard": "区・町を選択", "searchWard": "区・町を検索", "searchDistrict": "郡・市を検索", "selectDistrict": "郡・市を選択", "selectProvince": "省を選択", "searchProvince": "省を検索", "province": "省", "district": "郡", "ward": "区・町", "addAddress": "住所を追加", "address": "住所", "noProvincesFound": "省が見つかりません", "noDistrictsFound": "郡・市が見つかりません", "noWardsFound": "区・町が見つかりません", "shippingDefault": "デフォルト配送先住所", "billingDefault": "デフォルト請求先住所", "editCustomer": "顧客を編集", "Name": "名前", "phoneNumber": "電話番号", "email": "メール", "gender": "性別", "enterEmail": "メールを入力", "birthday": "誕生日", "pickADate": "日付を選択", "customerGroup": "顧客グループ", "selectCustomerGroup": "顧客グループを選択", "companyName": "会社名", "addresses": "住所", "submit": "確認", "accumulatedPoints": "累積ポイント", "groupName": "グループ名", "placeholder": "名前、SKU、バーコードで検索", "quantity": "数量", "price": "価格", "total": "合計", "noProductsFound": "商品が見つかりません", "addService": "サービスを追加（F9）", "loadingMore": "さらに読み込み中...", "addProduct": "商品を追加", "available": "在庫あり", "onHand": "手元在庫", "note": "メモ", "maximumAvailableQuantity": "最大在庫数", "branch": "支店", "loadingCustomerDetails": "顧客詳細を読み込み中...", "customer": "顧客", "shippingAddress": "配送先住所", "billingAddress": "請求先住所", "noCustomersFound": "顧客が見つかりません", "loading": "読み込み中...", "searchCustomer": "顧客を検索", "payment": "支払い", "addPromotion": "プロモーションを追加", "products": "商品", "subtotal": "小計", "discount": "割引", "voucher": "クーポンコード", "fees": "サービス料", "promotions": "プロモーション", "notePlaceholder": "メモを入力", "tags": "タグ", "tagsPlaceholder": "タグを入力", "noProductsInOrder": "注文に商品が含まれていません。", "cancel": "キャンセル", "addOrder": "注文を追加", "success": "注文が正常に作成されました！", "error": "注文を処理できませんでした", "adjustPrice": "価格を調整", "adjustPriceSuccess": "価格が正常に調整されました！", "adjustPriceError": "価格を調整できませんでした", "adjustPriceDescription": "選択した商品の価格を調整", "adjustPricePlaceholder": "新しい価格を入力", "adjustPriceButton": "価格を調整", "adjustPriceCancel": "キャンセル", "setNewPrice": "新しい価格を設定", "value": "金額", "percent": "パーセント", "addProductToOrderWarning": "注文に商品を追加してください", "selectCustomer": "顧客を選択してください", "addVoucher": "クーポンを追加", "voucherCode": "クーポンコード", "voucherCodePlaceholder": "クーポンコードを入力", "voucherCodeButton": "クーポンを追加", "voucherCodeSuccess": "クーポンが正常に追加されました", "voucherCodeError": "クーポンを追加できませんでした", "confirm": "本当に実行しますか？", "confirmCancel": "確認", "confirmDelete": "削除", "cancelWarning": "この操作は元に戻せません。変更をキャンセルしますか？", "cancelDelete": "この操作は元に戻せません。項目を削除しますか？"}, "variants": {"title": "バリエーション", "filters": {"search": {"placeholder": "名前、コード、バーコードで検索"}}}, "products": {"products": {"title": "商品", "addBulk": {"title": "商品を一括追加", "notice": "注意：", "templateInstructions": "データの不一致を防ぐため、テンプレートに従ってファイルを入力してください。", "simpleTemplate": "シンプルなテンプレートをダウンロード", "advancedTemplate": "高度なテンプレートをダウンロード", "here": "こちら", "supportedFiles": "対応ファイル形式：.xlsx, .xls, .csv", "dragAndDrop": "ファイルをここにドラッグ＆ドロップ、または", "uploadButton": "アップロード", "fileUpload": "ファイルをアップロード", "cancel": "キャンセル", "import": "インポート", "importing": "インポート中...", "selectFileError": "アップロードするファイルを選択してください", "importSuccess": "商品が正常にインポートされました", "importError": "商品インポート中にエラーが発生しました"}, "filters": {"search": {"placeholder": "名前、SKU、バーコードで検索", "placeholderBrand": "ブランドを検索..."}, "product": "商品", "source": "ソース", "category": "カテゴリ", "brand": "ブランド", "createdAt": "作成日", "updatedAt": "更新日", "otherFilters": {"title": "その他のフィルター", "all": "すべて", "description": "最初の2つのフィルターがメインページに優先表示されます。必要に応じてカスタマイズしてください。"}, "dateOptions": {"allTime": "全期間", "today": "今日", "yesterday": "昨日", "lastWeek": "先週", "thisWeek": "今週", "lastMonth": "先月", "thisMonth": "今月", "customize": "カスタマイズ"}, "deletionFailed": "バリエーションの削除に失敗しました", "deletedSuccessfully": "バリエーションの削除に成功しました"}, "headers": {"productInfo": "商品情報", "category": "カテゴリ", "brand": "ブランド", "updatedAt": "更新日時", "createdAt": "作成日", "available": "在庫数", "variant": "バリエーション"}, "actions": {"addProduct": "商品を追加", "addManual": {"title": "商品を追加", "onThisPage": "目次", "sections": {"basicInfo": "基本情報", "options": "商品オプション", "units": "梱包単位", "prices": "価格情報", "measurements": "寸法"}}, "addQuick": "クイック追加", "addBulk": "一括追加", "refresh": "更新", "saveFilters": "フィルターを保存", "reset": "リセット", "filter": "フィルター"}}, "deletionFailed": "バリエーションの削除に失敗しました", "deletedSuccessfully": "バリエーションの削除に成功しました", "descriptionDeleteOption": "この操作は元に戻せません。梱包単位、価格、寸法に関するデータは完全に削除されます。", "descriptionDeleteValueOption": "この操作は元に戻せません。価格と寸法に関するデータは完全に削除されます。", "name": "名前", "sku": "SKUコード", "barcode": "バーコード", "option1": "オプション1", "option2": "オプション2", "option3": "オプション3", "unit": "単位", "weight": "重量", "height": "高さ", "width": "幅", "length": "長さ", "variantDetails": "バリエーション詳細", "variants": "バリエーション", "source": "ソース", "category": "カテゴリ", "brand": "ブランド", "createdAt": "作成日", "description": "説明", "viewLess": "表示を減らす", "viewMore": "もっと見る", "noPricesAvailable": "価格情報がありません", "prices": "価格", "tags": "タグ", "inventory": {"noMatchResult": "一致する結果がありません", "title": "在庫", "branch": "支店", "history": "履歴", "allBranches": "すべての支店", "inventory": "在庫", "packing": "梱包", "shipping": "配送", "minValue": "最小値", "maxValue": "最大値", "staff": "スタッフ", "transactionType": "取引タイプ", "change": "変更", "quantity": "数量", "reference": "参照", "available": "在庫あり", "incoming": "入荷予定", "onHand": "手元在庫"}, "addManual": {"title": "商品を手動追加", "onThisPage": "目次", "publish": "公開", "sections": {"addVariant": "サイズや色など複数の選択肢がある場合はバリエーションを作成してください。", "variant": "バリエーション", "all": "すべて", "apply": "適用", "variantPlaceholder": "バリエーションを選択", "usedByAllVariants": "すべてのバリエーションで使用", "usedByThis": "このバリエーションで使用", "unitPlaceholder": "単位を選択", "unitSearchPlaceholder": "単位を検索", "variantSearchPlaceholder": "バリエーションを検索", "unitEmptyText": "単位が見つかりません", "addType": "タイプを追加", "addUnit": "単位を追加", "basicInfo": "基本情報", "options": "商品オプション", "option": "オプション", "units": "梱包単位", "prices": "価格情報", "measurements": "寸法", "selectImages": "画像を選択", "submit": "送信", "cancel": "キャンセル", "add": "追加", "edit": "編集", "save": "保存", "stay": "このままにする", "leave": "離れる", "values": "値", "valuesPlaceholder": "値1, 値2, 値3", "optionsPlaceholder": "オプション名を入力", "addOption": "オプションを追加", "optionName": "オプション名", "addValue": "値を追加", "remove": "削除", "valuesPlaceholderInput": "値を入力", "duplicateValue": "この値はすでに存在します", "createVariant": "サイズや色など複数の選択肢がある場合はバリエーションを作成してください。"}, "basicInfo": {"brandPlaceholder": "ブランドを選択", "brandSearchPlaceholder": "ブランドを検索", "brandEmptyText": "ブランドが見つかりません", "categoryPlaceholder": "カテゴリを選択", "categorySearchPlaceholder": "カテゴリを検索", "categoryEmptyText": "カテゴリが見つかりません", "tagsPlaceholder": "タグを入力", "images": "画像", "name": "名前", "description": "説明", "shortDescription": "短い説明", "brand": "ブランド", "category": "カテゴリ", "sku": "SKUコード", "tags": "タグ", "price": "価格", "uploadImage": "画像をアップロード", "optimize": "最適化", "required": "この項目は必須です", "imageRequired": "最低1枚の画像が必要です", "nameRequired": "商品名は必須です", "nameWarning": "表示効果を高めるために商品名を入力してください", "descriptionWarning": "コンテンツの最適化のために説明を入力してください", "skuRequired": "SKUコードは必須です", "priceRequired": "価格は必須です"}, "options": {"addOption": "オプションを追加", "optionName": "オプション名", "values": "値", "addValue": "値を追加", "remove": "削除"}, "units": {"title": "梱包単位", "addUnit": "単位を追加", "unitName": "単位名", "ratio": "比率", "remove": "削除"}, "prices": {"title": "価格情報", "addGroup": "新しい価格グループを追加", "groupName": "グループ名", "price": "価格", "apply": "適用", "applyAll": "すべてに適用"}, "measurements": {"weight": "重量", "height": "高さ", "width": "幅", "length": "長さ", "apply": "適用", "applyAll": "すべてに適用"}, "buttons": {"cancel": "キャンセル", "add": "追加", "edit": "更新", "save": "保存", "stay": "このままにする", "leave": "離れる"}, "dialogs": {"leaveTitle": "本当に離れますか？", "leaveDesc": "保存されていない変更は失われます。"}, "validation": {"hasErrors": "検証エラー", "checkFields": "すべての必須項目を確認して再試行してください"}, "success": "商品が作成されました", "successUpdate": "商品が更新されました", "successDescription": "商品が正常に作成されました", "successDescriptionUpdate": "商品が正常に更新されました", "error": "エラー", "errorDescription": "商品を作成できませんでした。もう一度お試しください。", "errorDescriptionUpdate": "商品を更新できませんでした。もう一度お試しください。"}}, "choosePlan": {"forIndividuals": "個人向け", "forCompanies": "法人向け", "monthly": "月額", "annually": "年額", "chooseAPlan": "プランを選択", "planDescription": {"firstSection": "あなたのビジネスニーズに最も適したプランを選択してください。", "middleSection": "後からアップグレードやダウングレードが可能です。", "secondSection": "すべてのプランには基本機能が含まれています。"}, "planData": {"Up to 50 variants": "最大50バリエーションの概要統計", "Real-time inventory syncing": "リアルタイム在庫同期", "Ideal for startups (1,000 items)": "スタートアップに最適（1,000商品まで）", "Analytics dashboard": "分析ダッシュボード", "User-friendly interface": "ユーザーフレンドリーなインターフェース", "Support for multiple currencies and languages": "複数通貨・言語対応", "Real time inventory": "リアルタイム在庫管理"}, "planNames": {"Free": "無料", "Starter": "スターター", "Pro": "プロ", "Agency": "エージェンシー"}, "mostPopular": "人気", "numberIntegrations": "統合数", "explainNoIntegrations": "統合はありません", "getStarted": "開始する"}, "synchronization": {"platforms": {"source": "ソース", "destination": "宛先"}, "title": {"success": "{{source}} を {{destination}} に接続しました", "error": "同期設定エラー"}, "description": "あなたの関心やターゲットに合った分野を選択してください。", "error": {"missingConnection": "接続が見つかりません", "connectionError": "接続エラー", "sourceNotFound": "ソースが見つかりません", "destinationNotFound": "宛先が見つかりません"}, "success": {"completeTitle": "接続完了！", "gotoDashboard": "ダッシュボードへ移動"}, "syncSetting": {"title": "同期設定", "product": {"title": "商品", "description": "{{source}} から {{destination}} へ商品を同期"}, "inventory": {"title": "在庫", "description": "在庫数を継続的に同期"}, "order": {"title": "注文", "description": "{{destination}} の注文を {{source}} に取り込む"}, "buttonTitle": "{{destination}} に接続"}}, "syncRecords": {"title": "同期一覧", "filters": {"search": {"placeholder": "返品注文を検索..."}, "status": "ステータス", "recordType": "レコードタイプ", "channel": "チャネル", "connectionId": "接続ID", "fetchEventId": "イベント取得ID"}, "columns": {"channel": "チャネル", "header": "レコードタイプ", "fetchEventId": "イベント取得ID", "connectionId": "接続ID", "lastUpdated": "最終更新", "fetchedAt": "取得日時", "finishedAt": "完了日時", "publishedAt": "公開日時", "transformedAt": "変換日時"}}, "fetchEvents": {"title": "Fetchイベント", "filters": {"search": {"placeholder": "Fetchイベントを検索..."}, "status": "ステータス", "actionType": "アクションタイプ", "actionGroup": "アクショングループ", "eventTime": "イベント日時", "eventSource": "イベントソース", "fetchEventId": "FetchイベントID"}, "columns": {"channel": "チャネル", "header": "レコードタイプ", "fetchEventId": "FetchイベントID", "connectionId": "接続ID", "lastUpdated": "最終更新", "fetchedAt": "取得日時", "finishedAt": "完了日時", "publishedAt": "公開日時", "transformedAt": "変換日時"}, "headers": {"channel": "チャネル", "actionType": "アクションタイプ", "actionGroup": "アクショングループ", "eventSource": "イベントソース", "eventTime": "イベント日時", "status": "ステータス", "actions": "操作"}}, "fetchEventDetail": {"title": "{{source}} の Fetchイベント詳細", "actionGroup": "アクショングループ", "connectionId": "接続ID", "actionType": "アクションタイプ", "eventSource": "イベントソース", "retryCount": "再試行回数", "status": "ステータス", "continuationToken": "継続トークン", "objectId": "オブジェクトID", "eventTime": "イベント日時", "createdAt": "作成日", "updatedAt": "更新日時", "eventNumber": "{{number}}. <PERSON><PERSON><PERSON>"}, "channel": {"title": "接続一覧", "filters": {"search": {"placeholder": "接続を検索..."}, "status": "ステータス"}, "headers": {"channel": "チャネル", "status": "ステータス", "url": "URL", "createdAt": "作成日", "lastUpdated": "最終更新", "actions": "操作"}, "actions": {"install": "新しいチャネルをインストール", "configure": "設定", "activate": "有効化", "deactivate": "無効化"}}, "supportedChannels": {"title": "チャネル一覧", "filters": {"search": {"placeholder": "チャネルを検索..."}}}, "installChannel": {"title": "チャネルをインストール"}, "settings": {"passwordsDontMatch": "パスワードが一致しません", "eachRoleAssignmentMustHaveABranchAndAtLeastOneRoleSelected": "各役割の割り当てにはブランチと少なくとも1つの役割が必要です", "cannotMixAllBranchesWithIndividualBranchSelections": "すべてのブランチと個別のブランチの選択を混在させることはできません", "emailInvalid": "メールアドレスが無効です", "emailRequired": "メールアドレスは必須です", "passwordRequired": "パスワードは必須です", "confirmPasswordRequired": "確認用パスワードは必須です", "employeeNameRequired": "従業員名は必須です", "usernameMinLength": "ユーザー名は3文字以上である必要があります", "usernameInvalid": "ユーザー名は英数字とアンダースコアのみ使用できます", "branchRequired": "ブランチは必須です", "atLeastOneRoleRequired": "少なくとも1つの役割が必要です", "atLeastOneRoleAssignmentRequired": "少なくとも1つの役割の割り当てが必要です", "employeeAccountAlreadyExists": "ユーザーアカウントがすでに存在します", "employeeAccountExpired": "ユーザーアカウントが期限切れです。RESENDアクションを使用してユーザーアカウントをリセットしてください", "employeeAccountDoesNotExist": "ユーザーアカウントが存在しません", "accessDenied": "アクセスが拒否されました", "employeeUpdatedSuccess": "従業員が正常に更新されました", "employeeCreatedSuccess": "従業員が正常に作成されました", "employeeDeletedSuccess": "従業員が正常に削除されました", "employeeDeletedError": "従業員の削除に失敗しました", "employeeUpdatedError": "従業員の更新に失敗しました", "employeeCreatedError": "従業員の作成に失敗しました", "emailAlreadyInUse": "メールアドレスがすでに存在します", "phoneNumberAlreadyInUse": "電話番号がすでに存在します", "phone": "電話番号", "birthday": "誕生日", "address": "住所", "shopInfo": "店舗情報", "profileSettings": "プロフィール設定", "profileSettingsDescription": "プロフィール画像、名前、パスワード", "storeInformation": "店舗情報", "storeInformationDescription": "連絡先情報、URL", "appearance": "外観", "appearanceDescription": "ロゴ、色、テーマ", "languageCurrency": "言語と通貨", "languageCurrencyDescription": "言語設定、通貨サポート", "employeesPermissions": "スタッフと権限", "employeesPermissionsDescription": "役割の割り当て、役割管理", "role": {"permissionsDescription": "権限により、各役割がシステム内の異なるモジュールで実行できるアクションを制御できます。", "roleName": "役割名", "roleNameRequired": "役割名 *"}, "themeSetting": "テーマ設定", "saveSuccess": "カラーが保存されました", "saveError": "カラーを保存できませんでした", "colorSetting": "カラー設定", "lightMode": "ライトモード", "darkMode": "ダークモード", "logoSetting": "ロゴ設定", "language": {"language": "言語", "searchLanguage": "言語を検索", "searchCurrency": "通貨を検索", "addLanguage": "言語を追加", "remove": "削除", "addCurrency": "通貨を追加", "update": "更新", "currency": "通貨", "confirmRemoveTitle": "本当に削除しますか？", "confirmRemoveDescription": "この操作は取り消せません。この言語はシステムから永久に削除されます。"}, "theme": {"title": "テーマ", "description": "アプリのテーマをカスタマイズします。", "lightMode": "ライトモード", "darkMode": "ダークモード"}, "logo": {"title": "ロゴ", "lightTheme": "(ライトモード)", "darkTheme": "(ダークモード)", "description": "店舗に表示されるロゴをカスタマイズします。", "lightModeLogo": "ライトモード用ロゴ", "darkModeLogo": "ダークモード用ロゴ", "lightModeIcon": "ライトモード用アイコン", "darkModeIcon": "ダークモード用アイコン", "favicon": "ファビコン", "lightModeLogoDescription": "ライトモード用ロゴをアップロード（推奨サイズ：180x40px）", "darkModeLogoDescription": "ダークモード用ロゴをアップロード（推奨サイズ：180x40px）", "lightModeIconDescription": "ライトモード用アイコンをアップロード（推奨サイズ：40x40px）", "darkModeIconDescription": "ダークモード用アイコンをアップロード（推奨サイズ：40x40px）", "faviconDescription": "ウェブサイト用ファビコンをアップロード（推奨サイズ：32x32px）", "saveSuccess": "ロゴが保存されました", "saveError": "ロゴを保存できませんでした", "noChangesToSave": "保存する変更がありません", "resetSuccess": "ロゴがリセットされました", "resetError": "ロゴをリセットできませんでした"}, "colors": {"title": "テーマカラー", "description": "アプリのテーマカラーをカスタマイズします。", "brandColor": "ブランドカラー", "lightMode": "(ライトモード)", "darkMode": "(ダークモード)"}, "color": {"saveSuccess": "テーマカラーが保存されました", "saveError": "テーマカラーを保存できませんでした", "resetSuccess": "テーマカラーがリセットされました", "resetError": "テーマカラーをリセットできませんでした"}}, "profile": {"title": "プロフィール", "contactInformation": "連絡先情報", "contactInformationDescription": "顧客があなたに連絡するための情報です", "changePassword": "パスワードを変更", "aspectRatio": "アスペクト比", "formats": "フォーマット", "name": "名前", "username": "ユーザー名", "email": "メール", "phone": "電話番号", "update": "更新", "avatar": "プロフィール画像", "changePasswordDiaglog": {"title": "パスワードを変更", "oldPassword": "現在のパスワード", "newPassword": "新しいパスワード", "confirmPassword": "パスワード確認", "enterOldPassword": "現在のパスワードを入力", "enterNewPassword": "新しいパスワードを入力", "enterConfirmPassword": "確認用パスワードを入力", "passwordRequirements": "パスワードは8文字以上である必要があります", "passwordChangedSuccessfully": "パスワードが正常に変更されました", "passwordChangeFailed": "パスワードを変更できませんでした", "passwordsDoNotMatch": "パスワードが一致しません", "passwordTooShort": "パスワードは8文字以上である必要があります", "forgotPassword": "パスワードを忘れた？", "allFieldsRequired": "すべての項目は必須です", "currentPasswordRequired": "現在のパスワードは必須です", "confirmPasswordRequired": "パスワードの確認は必須です", "passwordComplexity": "パスワードには少なくとも1つの小文字、1つの大文字、1つの数字が含まれている必要があります", "passwordAttemptsExceeded": "パスワードの試行回数が超過しました", "passwordDoesNotMatch": "パスワードが正しくありません"}, "forgotPasswordDialog": {"emailRequired": "メールアドレスを入力してください", "codeRequired": "認証コードを入力してください", "codeSentSuccessfully": "認証コードが正常に送信されました", "codeSendFailed": "認証コードを送信できませんでした", "codeVerifiedSuccessfully": "認証コードが正常に確認されました", "codeVerificationFailed": "認証コードの確認に失敗しました", "codeResentSuccessfully": "認証コードが再送信されました", "codeResendFailed": "認証コードを再送信できませんでした", "passwordChangedSuccessfully": "パスワードが正常に変更されました", "passwordChangeFailed": "パスワードを変更できませんでした", "passwordsDoNotMatch": "パスワードが一致しません", "passwordTooShort": "パスワードは8文字以上である必要があります", "allFieldsRequired": "すべての項目は必須です", "emailStep": {"title": "認証コードを送信", "description": "下記にメールアドレスを入力し、「コードを送信」をクリックしてパスワード再設定用コードを受け取ってください。", "emailLabel": "メールアドレス", "emailPlaceholder": "メールアドレスを入力", "sending": "送信中...", "sendCode": "コードを送信"}, "verificationStep": {"title": "認証コードを入力", "description": "{{email}} に送信された認証コードを入力してください", "codeLabel": "認証コード", "codePlaceholder": "コード", "yourEmail": "あなたのメール", "resend": "再送信", "verifying": "確認中...", "continue": "続行"}, "resetPasswordStep": {"title": "パスワードを変更", "description": "新しいパスワードを入力して変更してください", "newPasswordLabel": "新しいパスワード", "newPasswordPlaceholder": "新しいパスワードを入力", "confirmPasswordLabel": "パスワード確認", "confirmPasswordPlaceholder": "確認用パスワードを入力", "changing": "変更中...", "continue": "続行"}, "successStep": {"title": "パスワードが変更されました", "description": "新しいパスワードでログインできます。", "loginButton": "ログイン"}}}, "storeInformation": {"success": "店舗情報が正常に更新されました", "error": "店舗情報を更新できませんでした", "title": "店舗情報", "contactInformation": "連絡先情報", "contactInformationDescription": "連絡先情報を管理します", "changePassword": "パスワードを変更", "aspectRatio": "アスペクト比", "formats": "フォーマット", "name": "名前", "username": "ユーザー名", "email": "メール", "phone": "電話番号", "update": "更新", "storeInformationDescription": "店舗の連絡先情報と設定を管理します"}, "languageCurrency": {"title": "言語と通貨", "description": "アプリの言語と通貨設定を管理します", "language": "言語", "currency": "通貨", "addLanguage": "言語を追加", "addCurrency": "通貨を追加", "remove": "削除", "update": "更新", "back": "戻る", "languageDescription": "サポートしたい言語を選択してください", "currencyDescription": "サポートしたい通貨を選択してください", "updating": "更新中...", "confirmRemoveTitle": "本当に削除しますか？", "confirmRemoveDescription": "この操作は取り消せません。この通貨はシステムから永久に削除されます。", "currencySettingsUpdatedSuccessfully": "通貨設定が更新されました", "currencySettingsUpdatedFailed": "通貨設定の更新に失敗しました"}, "productMappingList": {"syncSuccess": "商品同期リクエストが正常に受信されました", "syncFail": "商品リンクリクエストの受信に失敗しました", "noConnection": "接続がありません", "title": "商品リンク", "description": "Shopify から TikTok Shop への商品リンクを管理します", "filters": {"search": {"placeholder": "検索"}, "status": {"all": "すべての商品", "synced": "同期済み", "mapped": "リンク済み", "unMapped": "未リンク", "errors": "エラー"}}, "alert": {"title": "商品を同期しますか？", "description": "すべての商品を2つのプラットフォーム間で同期しようとしています。この処理には時間がかかる場合があります。", "note": "注意：", "noteDescription": "一部の商品だけを同期したい場合は、続行する前に表から選択してください。", "confirm": "同期する", "cancel": "キャンセル", "areYouSure": "本当に続行しますか？", "unmapSuccess": "商品リンクの解除に成功しました", "unmapFail": "商品リンクの解除に失敗しました"}, "groupButton": {"settingButton": "設定", "syncButton": "商品を同期"}, "status": {"synced": "同期済み", "mapped": "リンク済み", "unmapped": "未リンク", "error": "エラー"}, "actions": {"unmap": "リンク解除", "map": "リンク", "fix": "属性を修正"}, "headers": {"product": "商品 {{product}}", "price": "価格", "last_synced": "最終同期", "status": "ステータス", "actions": "操作"}, "nomap": "{{destination}} にリンクされていません"}, "productMapping": {"advancedMapping": {"title": "高度なリンク設定", "description": "商品のデータリンクに関する高度なルールを設定します。", "sourceField": "ソースフィールド", "transformationType": "変換タイプ", "addTransformation": "変換を追加", "removeTransformation": "変換を削除", "ruleConfiguration": "ルール設定", "outputPreview": "出力プレビュー", "finalOutput": "最終出力", "applyTransformations": "変換を適用", "transformationChain": "変換チェーン", "sampleData": "サンプルデータ", "preview": "プレビュー", "output": "出力", "singleValue": "単一値", "transformationForm": "変換", "exampleUsage": "使用例", "selectFieldsPlaceholder": "フィールドを選択", "searchFieldsPlaceholder": "フィールドを検索...", "source": "ソース", "searchTransformationTypes": "変換タイプを検索...", "selectTransformationTypes": "変換タイプを選択..."}, "lastSynced": "最終同期", "errorLoading": "商品同期の詳細読み込みエラー", "manualRetry": "手動で再試行", "cancelledMessage": "商品同期がキャンセルされました", "mappingStatus": "同期ステータス"}, "staff": {"name": "名前", "phoneNumber": "電話番号", "howCanICallYou": "あなたの名前は何ですか？", "enterYourPhoneNumber": "あなたの電話番号を入力してください", "title": "スタッフ一覧", "filters": {"department": "部署", "role": "ロール", "search": {"placeholder": "スタッフを検索..."}}, "actionButton": {"create": "スタッフを作成"}, "columns": {"staff": "スタッフ", "role": "ロール", "skills": "スキル", "task": "タスク", "conversations": "会話", "actions": "操作"}, "actions": {"view": "表示", "edit": "編集", "delete": "削除"}, "maxCharactersReached": "最大文字数に達しました", "online": "オンライン", "noStaff": "スタッフがいません。", "loading": "読み込み中...", "interact": "対話", "createStaff": "スタッフを作成", "staffName": "スタッフ名", "enterStaffName": "スタッフ名を入力", "staffNameRequired": "スタッフ名を入力してください", "maxCharacters": "最大250文字", "selectDepartment": "部署を選択", "searchDepartments": "部署を検索...", "selectRole": "ロールを選択", "searchRoles": "ロールを検索...", "creating": "作成中", "update": "更新", "role": "ロール", "department": "部署", "expertise": "専門分野", "knowledgeWarning": "現在の知識は正確に回答するのに十分ではありません。パフォーマンスを向上させるために、詳細を追加してください。", "score": "スコア", "avatar": {"title": "アバター", "xbotAvatar": "XBotアバター", "image": "画像", "selectedAvatar": "選択されたアバター", "avatar": "アバター"}, "knowledge": {"tab": "知識", "baby": "初心者", "warning": "現在の知識は正確に回答するのに十分ではありません。パフォーマンスを向上させるために、詳細を追加してください。"}, "interactionStyle": {"tab": "対話スタイル", "description": "スタッフが顧客と対話する方法を設定します", "communicationTone": "コミュニケーションテンポ", "languagePreferences": "言語設定", "responseLength": "応答長さ", "personalityTraits": "性格特徴", "temper": "感情", "formal": "正式", "casual": "非正式", "detailed": "詳細", "concise": "簡潔", "creative": "創造的", "analytical": "分析的", "ethicalConstraints": "倫理制約", "contentFiltering": "コンテンツフィルタリングを有効にする", "instruction": "指示", "instructionPlaceholder": "このスタッフのカスタム指示を入力してください（任意）", "greeting": "挨拶", "greetingPlaceholder": "こんにちは！OneXBotsのバーチャルスタッフです。どうぞお気軽にご相談ください！", "welcomeMessage": "こんにちは！私は{{botName}}です。今日はどのようにお手伝いできますか？"}, "skills": {"tab": "データアクセス設定", "description": "スタッフがアクセスできるデータを設定します", "products": "商品", "orders": "注文", "inventory": "在庫"}, "staffInfo": {"tab": "スタッフ情報", "description": "スタッフの基本情報を管理します"}, "task": {"tab": "タスク", "description": "スタッフのタスクと責任を管理します", "noTasks": "タスクがありません"}, "editStaff": {"validation": {"nameRequired": "名前は必須です", "roleRequired": "ロールは必須です", "departmentRequired": "部署は必須です", "greetingMaxLength": "挨拶は100文字以内にしてください"}, "tabs": {"staffInfo": "スタッフ情報", "interactionStyle": "対話スタイル", "knowledge": "ナレッジ", "skills": "スキル", "task": "タスク"}, "staffInfo": "スタッフ情報", "interactionStyle": "対話スタイル", "knowledge": "ナレッジ", "skills": "スキル", "task": "タスク", "integration": "統合", "embedCodeInstructions": "バーチャルスタッフのウィジェットをWebページに埋め込むための手順", "embedCodeTitle": "バーチャルスタッフのウィジェットを埋め込む", "title": "スタッフを編集", "staffName": "スタッフ名", "rolePurpose": "ロール/目的", "department": "部署", "domainExpertise": "専門分野", "customExpertisePlaceholder": "専門分野を入力してEnterキーを押してください", "noRoleFound": "ロールが見つかりません", "noDepartmentFound": "部署が見つかりません", "selectRole": "ロールを選択", "selectDepartment": "部署を選択", "searchRoles": "ロールを検索...", "searchDepartments": "部署を検索...", "namePhoneRequirement": "名前と電話番号の入力が必要です", "roles": {"contentWriter": "コンテンツライター", "seoSpecialist": "SEOエキスパート", "socialMediaManager": "ソーシャルメディアマネージャー", "marketingSpecialist": "マーケティングエキスパート"}, "embedVirtualStaffWidget": "バーチャルスタッフのウィジェットを埋め込む", "themesColor": "テーマカラー", "embedCode": "埋め込みコード", "greeting": "OneXBotsからの挨拶", "departments": {"engineering": "エンジニアリング", "marketing": "マーケティング", "sales": "セールス", "support": "サポート"}, "expertise": {"contentWriting": "コンテンツライティング", "customerSupport": "顧客サポート", "dataAnalysis": "データ分析", "emailMarketing": "メールマーケティング", "graphicDesign": "グラフィックデザイン", "projectManagement": "プロジェクト管理", "seo": "SEO", "socialMedia": "ソーシャルメディア管理"}}, "embed": {"instructions": {"title": "埋め込み手順", "step1Title": "ステップ1: スクリプトを追加", "step1Description": "スクリプトのタグをコピーして、Webページの<head>タグ内または</body>タグの直前に貼り付けます。", "step2Title": "ステップ2: コンテナウィジェットを追加", "step2Description": "div要素をコピーして、バーチャルスタッフのウィジェットを表示したい場所に貼り付けます。ウィジェットは自動的にこの位置に初期化されます。", "step3Title": "ステップ3: ウィジェットのカスタマイズ（任意）", "step3Description": "WebページにCSSを追加することで、ウィジェットのインターフェースをカスタマイズできます。コンテナウィジェットのIDはxbot-containerです。", "step4Title": "ステップ4: 統合の確認", "step4Description": "スクリプトを追加した後、ページを更新し、バーチャルスタッフのウィジェットが正確に表示されていることを確認します。ウィジェットはスタッフの情報を表示し、訪問者が対話できるようにする必要があります。", "troubleshootingTitle": "トラブルシューティング", "troubleshooting1": "スクリプトのURLがWebページからアクセス可能であることを確認してください。", "troubleshooting2": "ブラウザの開発者ツールを使用して、エラーメッセージを確認してください。", "troubleshooting3": "IDとスタッフ名が正確であることを確認してください。", "troubleshooting4": "Webページが外部スクリプトの読み込みを許可していることを確認してください。"}, "script": {"title": "埋め込みコード", "copy": "コピー", "copied": "コピーしました！", "containerInstructions": "1. ウィジェットを表示したい場所にコンテナを追加", "scriptInstructions": "2. ウィジェットを表示したい場所にスクリプトを追加"}}, "promptExperiment": {"title": "プロンプトテスト", "datasetName": "データセット名", "datasetNamePlaceholder": "データセット名を入力", "datasetNameRequired": "データセット名は必須です", "runName": "実行名", "runNamePlaceholder": "実行名を入力", "runNameRequired": "実行名は必須です", "cancel": "キャンセル", "runExperiment": "実験を実行", "success": "プロンプトテストが正常に実行されました", "error": "プロンプトテストを実行できません"}}, "department": {"subscriptionNotFound": "部署を作成するためのサブスクリプションがありません", "deleteDepartmentTitle": "本当に削除しますか？", "deleteDepartmentDescription": "部署を削除しますか？この操作は元に戻すことができません。", "updateDepartmentSuccess": "部署が正常に更新されました", "updateDepartmentError": "部署を更新できません", "deleteDepartmentSuccess": "部署が正常に削除されました", "deleteDepartmentError": "部署を削除できません", "createDepartmentSuccess": "部署が正常に作成されました", "departmentNameAlreadyExists": "部署名がすでに存在します", "createDepartmentFail": "部署を作成できません。", "title": "部署", "editDepartment": "部署を編集", "createDepartment": "部署を作成", "createStaff": "スタッフを作成", "departmentName": "部署名", "enterDepartmentName": "部署名を入力", "description": "説明", "enterDescription": "説明を入力...", "departmentNameRequired": "部署名を入力してください", "viewStaff": "スタッフを表示", "staffCount": "{{count}} スタッフ", "additionalStaff": "{{count}} スタッフ", "interact": "対話", "upload": "アップロード", "deleteKnowledge": "知識を削除", "deleteKnowledgeDescription": "知識を削除しますか？この操作は元に戻すことができません。", "deleteKnowledgeSuccess": "知識が正常に削除されました", "deleteKnowledgeError": "知識を削除できません", "deleteKnowledgeConfirm": "削除", "deleteKnowledgeCancel": "キャンセル", "knowledgeWarning": "現在の知識は正確に回答するのに十分ではありません。パフォーマンスを向上させるために、詳細を追加してください。", "knowledge": {"tab": "知識", "baby": "初心者", "warning": "現在の知識は正確に回答するのに十分ではありません。パフォーマンスを向上させるために、詳細を追加してください。", "status": {"error": "エラー", "pending": "処理中", "success": "成功"}}}, "knowledge": {"title": "知識", "headers": {"file": "ファイル", "status": "ステータス", "size": "サイズ", "updatedAt": "更新日時"}, "filters": {"search": {"placeholder": "ファイルを検索..."}, "fileType": "ファイルタイプ", "status": "ステータス", "url": "URL", "file": "ファイル", "text": "テキスト"}, "actions": {"upload": "新しいファイルをアップロード"}, "upload": {"dragAndDrop": "ファイルをここにドラッグ&ドロップするか、", "uploadButton": "アップロード", "supportedFiles": "PDF, DOCX, TXT, または CSV", "totalSize": "合計サイズ", "noFileSelected": "ファイルが選択されていません", "uploadSuccess": "アップロードが成功しました", "fileTooLarge": "ファイルが最大サイズ {{max}}MBを超えています", "file": "ファイル", "url": "Webページを入力", "text": "テキストを入力", "title": "知識をアップロード", "invalidUrl": "URLが無効です", "urlAlreadyAdded": "URLがすでに存在します", "noUrlsToUpload": "少なくとも1つのURLを入力してください", "uploadError": "アップロード中にエラーが発生しました", "uploaded": "知識がアップロードされました", "knowledgeNameRequired": "知識名は必須です", "knowledgeNameTooLong": "知識名は250文字以内にしてください", "textRequired": "テキストは必須です", "textTooLong": "テキストは20000文字以内にしてください", "search": "知識を検索", "textTitle": "知識名", "fileTitle": "ファイルからの知識", "urlTitle": "URLからの知識", "allTitle": "すべての知識", "deleteTitle": "知識を削除", "deleteDescription": "知識を削除しますか？この操作は元に戻すことができません。", "deleteSuccess": "知識が正常に削除されました", "deleteError": "知識を削除できません", "deleteConfirm": "削除", "deleteCancel": "キャンセル", "leaveTitle": "保存せずに退出", "leaveDescription": "変更は保存されません。", "leaveConfirm": "退出", "leaveCancel": "戻る", "textInput": "直接入力", "textInputPlaceholder": "ここに知識のテキストを入力してください...", "textInputTitle": "テキストからの知識", "textTitlePlaceholder": "知識のタイトルを入力", "pleaseSelectAtLeastOneFile": "少なくとも1つのファイルを選択してください", "pleaseEnterAtLeastOneURL": "少なくとも1つのURLを入力してください", "pleaseEnterAtLeastOneTextFile": "少なくとも1つのテキストファイルを入力してください", "pleaseEnterAllFields": "タイトルと内容を入力してください", "newest": "最新", "oldest": "最古", "noKnowledge": "知識がありません", "urlImport": "URLからの知識", "urlImportDescription": "Webページから知識を入力", "deleteKnowledge": "知識を削除", "deleteKnowledgeDescription": "知識を削除しますか？この操作は元に戻すことができません。", "deleteKnowledgeSuccess": "知識が正常に削除されました", "deleteKnowledgeError": "知識を削除できません", "deleteKnowledgeConfirm": "削除", "deleteKnowledgeCancel": "キャンセル", "fileImport": "ファイルからの知識", "fileImportDescription": "ファイルから知識を入力", "fileImportSuccess": "知識が正常にアップロードされました", "fileImportError": "知識をアップロードできません", "fileImportConfirm": "アップロード", "fileImportCancel": "キャンセル", "textImport": "テキストからの知識", "textImportDescription": "テキストから知識を入力", "textImportSuccess": "知識が正常にアップロードされました", "textImportError": "知識をアップロードできません", "textImportConfirm": "アップロード", "textImportCancel": "キャンセル", "urlImportSuccess": "知識が正常にアップロードされました", "urlImportError": "知識をアップロードできません", "urlImportConfirm": "アップロード", "urlImportCancel": "キャンセル", "deleteKnowledgeTitle": "知識を削除"}, "status": {"error": "エラー", "pending": "処理中", "processing": "処理中", "ready": "準備完了"}}, "customer": {"details": {"customerDetails": "顧客詳細", "name": "名前", "birthday": "誕生日", "gender": "性別", "phone": "電話番号", "email": "メール", "shippingAddress": "配送先住所", "billingAddress": "請求先住所", "groupName": "グループ名", "totalLoyalPoints": "総ポイント", "totalRedeemPoints": "総ポイント", "tags": "タグ", "noTags": "---"}, "purchase": {"purchaseInfo": "購入情報", "totalSpent": "合計支出", "totalProductsPurchased": "購入した商品の合計", "purchasedOrder": "購入した注文", "totalProductsReturned": "返品した商品の合計", "lastOrderAt": "最後の注文日"}, "sales": {"suggestionInfo": "提案情報", "defaultPriceGroup": "デフォルトの価格グループ", "defaultPaymentMethod": "デフォルトの支払い方法", "discountPercent": "割引率"}, "order": {"orderHistory": "注文履歴"}}, "conversation": {"title": "会話", "whatCanIHelpWith": "私は何か手伝えますか？", "saySomething": "何か言ってください...", "filters": {"search": {"placeholder": "検索..."}, "source": "ソース", "unread": "未読", "read": "既読", "assignee": "担当者"}}, "tasks": {"deleteDescription": "本当に削除しますか？この操作は元に戻すことができません。", "promptContent": "プロンプト内容", "shortDescription": "短い説明", "shortDescriptionPlaceholder": "ここに知識のテキストを入力してください...", "namePlaceholder": "タスク名を入力", "promptContentPlaceholder": "ここに知識のテキストを入力してください...", "editTask": "タスクを編集", "addTask": "タスクを追加", "save": "保存", "add": "追加", "promptHelper": "プロンプトに[変数]を使用して、プロンプトにフィールドを挿入します。各フィールドには一意の名前を付ける必要があります。同じ変数が複数回出現する場合、ユーザーは1回だけ入力する必要があります。"}, "activities": {"title": "活動", "unknown": "不明", "opportunity_created": "機会を作成", "field_labels": {"order_changed": "注文を変更", "title": "タイトル", "status": "ステータス", "stage_changed": "列を変更", "assignee": "担当者", "assignee_changed": "担当者", "customer_id": "顧客", "customer_changed": "顧客を変更", "expected_revenue": "予想収益", "probability": "確率", "priority": "優先度", "expected_closing": "予想クロージング", "position": "位置", "position_changed": "位置を変更", "color": "色", "tags": "タグ", "user": "ユーザー", "note": "ノート", "note_changed": "ノートを変更", "schedule_activities": "スケジュール活動", "schedule_activities_changed": "スケジュール活動を追加", "schedule_activities_updated_at": "スケジュール活動の更新日時", "schedule_activities_created_at": "スケジュール活動の作成日時", "schedule_activities_due_date": "スケジュール活動の期限", "schedule_activities_status": "スケジュール活動のステータス", "schedule_activities_summary": "スケジュール活動の要約", "schedule_activities_note": "スケジュール活動のノート", "schedule_activities_assignee": "スケジュール活動の担当者", "schedule_activities_user": "スケジュール活動のユーザー", "schedule_activities_count": "スケジュール活動の数", "representative_phone": "電話番号", "representative_contact": "連絡先", "representative_email": "メール", "colors": {"neutral": "グレー", "blue": "青", "green": "緑", "yellow": "黄色", "red": "赤", "teal": "ティール", "pink": "ピンク", "orange": "オレンジ", "purple": "紫", "sky": "青空"}, "priorities": {"very_low": "非常に低い", "low": "低", "medium": "中", "high": "高"}, "schedule_activity_status": {"status": "スケジュール活動のステータス", "pending": "処理中", "completed": "完了", "cancelled": "キャンセル", "done": "完了"}, "opportunity_status": {"status": "ステータス", "ongoing": "進行中", "won": "達成", "lost": "失敗"}}, "scheduleActivity": {"groupButton": {"markAsDone": "完了", "edit": "編集", "cancel": "キャンセル"}}, "history": {"tabs": {"history": "履歴", "comments": "コメント", "placeholder": "コメントを入力..."}}, "headers": {"activity": "活動", "assignedTo": "担当者", "summary": "要約", "dueDate": "期限", "status": "ステータス", "updatedAt": "更新日時"}, "filters": {"search": {"placeholder": "検索..."}, "status": "ステータス", "assignee": "担当者"}, "status": {"overdue": "期限切れ", "today": "今日", "upcoming": "次", "noduedate": "期限なし", "no_activity": "活動なし"}, "updateSuccess": "活動を更新しました", "updateError": "活動を更新できません", "deleteSuccess": "活動を削除しました", "deleteError": "活動を削除できません", "error": "活動タイプの作成エラー", "errorActivityTypeExists": "同じ名前の活動タイプがすでに存在します", "createSuccess": "活動タイプを作成しました", "createError": "活動タイプを作成できません"}, "opportunityDetail": {"updateStatusSuccess": {"title": "成功", "description": "機会のステータスが正常に更新されました。"}, "updateStatusError": {"title": "エラー", "description": "機会のステータスを更新できません。もう一度お試しください。"}, "customerInfo": {"name": "名前", "company": "会社", "country": "国", "address": "住所", "billingAddress": "(請求先)", "province": "都道府県", "ward": "区", "phone": "電話番号", "job": "役職", "website": "ウェブサイト", "district": "区", "placeholder": "顧客を選択"}, "updateError": {"title": "エラー", "description": "機会を更新できません。もう一度お試しください。", "probability": "確率は0以上100以下でなければなりません。", "representativeEmail": "メールアドレスの形式が正しくありません", "customer": "顧客は必須です"}, "updateSuccess": {"title": "成功", "description": "機会が正常に更新されました。"}, "updateCustomerError": {"title": "エラー", "description": "顧客を更新できません。もう一度お試しください。"}, "updateCustomerSuccess": {"title": "成功", "description": "顧客が正常に更新されました。"}, "createError": {"title": "エラー", "description": "機会を作成できません。もう一度お試しください。"}, "createSuccess": {"title": "成功", "description": "機会が正常に作成されました。"}}, "subscription": {"currentPlan": {"title": "現在のプラン:", "expiresOn": "有効期限", "cancelSubscription": "キャンセル", "upgrade": "アップグレード", "expired": "有効期限切れ"}, "expired": {"title": "制限に達しました", "description": "現在のプランでは、AI Staffの数が制限されています。アップグレードして、さらに多くのAI Staffを作成しましょう。", "yourUsage": "使用量:", "upgradeButton": "アップグレード", "seeAllPlans": "すべてのプランを表示", "planTitle": "プランが有効期限切れ", "planDescription": "現在のプランが有効期限切れです。サービスを継続するには、プランをアップグレードしてください。", "expiredPlan": "有効期限切れ:", "upgrade": "アップグレード", "logout": "ログアウト"}, "exceedQuota": {"title": "制限に達しました", "description": "現在のプランでは、{{quotaType}}の数が制限されています。アップグレードして、さらに多くの{{quotaType}}を作成しましょう。", "yourUsage": "使用量:", "upgradeButton": "アップグレード", "seeAllPlans": "すべてのプランを表示", "quotaTypes": {"staff": "AI Staff", "knowledge_capacity": "知識", "message": "メッセージ", "product": "商品", "order": "注文"}}, "usageStats": {"title": "使用量", "messages": "メッセージ", "staff": "AI Staff", "storage": "保存", "unlimited": "無制限"}, "pricing": {"save": "節約", "annually": "年", "title": "価格", "description": "個人と企業のための柔軟な価格プランを見てください。", "mostPopular": "人気", "upgrade": "アップグレード", "more": "もっと", "showLess": "隠す"}, "billing": {"annualPlan": "年間プラン", "savings": "15%の節約"}, "customPlan": {"title": "カスタムプラン", "description": "あなたのニーズに合わせてカスタマイズします。機能、容量、コストが柔軟に調整でき、特別な要件を持つ企業に最適です。", "contactInfo": "連絡先", "companyName": "会社名", "companyNamePlaceholder": "会社名を入力してください", "contactEmail": "連絡先メール", "emailPlaceholder": "連絡先メールを入力してください", "requirements": "要件", "messages": "メッセージ", "staff": "AI Staff", "storage": "知識容量 (MB)", "currentMessages": "現在", "currentStaff": "現在", "currentStorage": "現在", "assistants": "AI Staff", "additionalRequirements": "追加要件", "additionalRequirementsPlaceholder": "必要な機能や機能についてお知らせください...", "included": "常に含まれる", "multilingualSupport": "多言語対応", "prioritySupport": "優先サポート", "customIntegrations": "カスタム統合", "submit": "カスタムプランの要請", "contact": "24時間以内にお問い合わせください", "success": "カスタムプランの要請が送信されました！", "error": "カスタムプランの要請を送信できません。もう一度お試しください。", "emailRequired": "連絡先メールを入力してください", "companyRequired": "会社名を入力してください", "features": {"productManagement": "商品管理", "orderManagement": "注文管理", "knowledgeManagement": "知識管理", "departmentManagement": "部門管理", "employeeManagement": "従業員管理", "crm": "CRM"}, "configuration": {"virtualAssistants": "AI Staff", "messages": "メッセージ", "knowledgeCapacity": "知識容量 (MB)", "aiModel": "AIモデル", "multilingual": "多言語", "scheduleCustomerCare": "顧客サポートスケジュール", "customIntegration": "カスタム統合"}, "button": "お問い合わせ"}, "faq": {"title": "よくある質問", "description": "お探しの答えが見つかりませんか？", "description2": "お問い合わせください。", "q1": "アクセスできますか？", "a1": "はい。WAI-ARIAデザインテンプレートに従います。", "q2": "スタイルを作成できますか？", "a2": "はい。他のコンポーネントに合わせてデフォルトのスタイルが付属しています。", "q3": "アニメーションがありますか？", "a3": "はい。デフォルトでアニメーションが作成されますが、必要に応じてオフにすることができます。", "q4": "私のウェブサイトで使用できますか？", "a4": "はい。このコンポーネントは、任意のReactアプリケーションで使用できるように設計されています。", "q5": "どうやって始めますか？", "a5": "コンポーネントを入力し、JSXで使用するだけです。"}, "dialog": {"title": "カスタムパッケージリクエストが正常に送信されました", "message": "リクエストを受信しました。詳細については近日中にご連絡いたします。当サービスをご利用いただき、ありがとうございます。", "countdown": "{{countdown}}秒後に閉じます..."}}, "layouts": {"title": "レイアウト管理", "description": "不動産のフロアプランと単位マッピングを管理します", "totalLayouts": "合計レイアウト", "mappedUnits": "マッピングされた単位", "mapped": "マッピングされた", "unmapped": "未マッピング", "mappingStats": "マッピング統計", "selectLayout": "レイアウトを選択", "openMapping": "マッピングを開く", "floorPlans": "フロアプラン", "addLayout": "レイアウトを追加", "createLayout": "レイアウトを作成", "createLayoutDescription": "不動産の新しいフロアプランを作成します", "layoutNamePlaceholder": "例: 1階, 地下1階", "layoutDescriptionPlaceholder": "このレイアウトの説明", "searchLayouts": "レイアウトを検索...", "noLayouts": "レイアウトが見つかりません", "noLayoutsFound": "検索条件に一致するレイアウトが見つかりません", "createFirstLayout": "最初のレイアウトを作成して開始します", "tryDifferentSearch": "検索キーワードを変更してお試しください", "selectPropertyFirst": "不動産を選択してフロアプランを表示および管理します", "deleteLayout": "レイアウトを削除", "deleteLayoutConfirmation": "このレイアウトを削除してもよろしいですか？この操作は元に戻すことができません。", "saveSuccess": "レイアウトを保存しました", "deleteSuccess": "レイアウトを削除しました", "createSuccess": "レイアウトを作成しました", "dimensions": "寸法", "uploadImage": "フロアプランをアップロード", "uploadImageDescription": "フロアプランの画像をアップロードして単位のマッピングを開始します", "noLayoutSelected": "レイアウトが選択されていません", "selectLayoutToStart": "レイアウトを選択して単位のマッピングを開始します", "mappedToLayout": "レイアウトにマッピングされた", "assignUnit": "単位を割り当て", "selectUnit": "単位を選択", "mappingProgress": "マッピングの進行状況", "systemHealth": "システムの状態", "weeklyProgress": "週次の進行状況", "recentChanges": "最近の変更", "complete": "完了", "incomplete": "未完了", "withIssues": "問題がある", "duplicateAll": "すべてを複製", "exportAll": "すべてをエクスポート", "importAll": "すべてをインポート", "shareAll": "すべてを共有", "generateReport": "レポートを生成", "saveAsTemplate": "テンプレートとして保存", "viewReport": "レポートを表示", "lastActivity": "最終アクティビティ", "totalUnits": "合計単位", "exportSuccess": "データのエクスポートに成功しました", "exportError": "データのエクスポートに失敗しました", "importSuccess": "<PERSON><PERSON><PERSON><PERSON> thành công {{count}} b<PERSON> cục", "importError": "Lỗi nhập dữ liệu bố cục", "importParseError": "<PERSON><PERSON><PERSON> dạng tệp không hợp lệ. <PERSON><PERSON> lòng chọn tệp xuất bố cục hợp lệ.", "saveError": "Lỗi lưu bố cục: {{error}}", "deleteError": "Lỗi x<PERSON>a bố cục: {{error}}", "createError": "Lỗi tạo bố cục: {{error}}", "templateCreated": "Tạo mẫu thành công", "templateCreateError": "Lỗi tạo mẫu", "createTemplate": "Tạo mẫu", "createTemplateDescription": "<PERSON><PERSON><PERSON> bố cục này thành mẫu để sử dụng lại cho các bất động sản khác", "templateName": "<PERSON><PERSON>n mẫu", "templateNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên mẫu", "templateDescription": "<PERSON><PERSON>", "templateDescriptionPlaceholder": "<PERSON><PERSON> tả tùy chọn cho mẫu này", "templateCategory": "<PERSON><PERSON>", "categories": {"custom": "<PERSON><PERSON><PERSON> chỉnh", "residential": "<PERSON><PERSON> d<PERSON> c<PERSON>", "commercial": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại", "mixed": "Hỗn hợp"}, "overlapWarning": "<PERSON><PERSON><PERSON> b<PERSON>o chồng lấp", "overlapDetected": "<PERSON><PERSON><PERSON> {{count}} hình chồng lấp", "floorNumber": "<PERSON><PERSON> tầng", "floorNumberDescription": "Số tầng tùy chọn cho bố cục này", "dropImageHere": "<PERSON><PERSON><PERSON> hình ảnh mặt bằng vào đây hoặc nhấp để chọn", "supportedFormats": "Hỗ trợ JPG, PNG, SVG, WebP (tối đa 10MB)", "chooseFile": "<PERSON><PERSON><PERSON>", "imageUploaded": "<PERSON><PERSON><PERSON> hình <PERSON>nh thành công", "uploading": "<PERSON><PERSON> t<PERSON> lên", "invalidFileType": "<PERSON><PERSON> lòng chọn tệp hình <PERSON>nh hợp lệ", "fileTooLarge": "<PERSON><PERSON><PERSON> thư<PERSON><PERSON> tệp quá lớn. T<PERSON><PERSON> đa cho phép 10MB", "uploadFailed": "Lỗi tải lên hình <PERSON>nh", "dropUnitHere": "<PERSON>h<PERSON> đơn vị vào đây để đặt trên bố cục"}, "properties": {"title": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "add": "<PERSON><PERSON><PERSON><PERSON> bất động sản", "selectProperty": "<PERSON><PERSON><PERSON> bất động sản", "createProperty": "<PERSON><PERSON><PERSON> b<PERSON>t độ<PERSON> sản", "editProperty": "Chỉnh sửa bất động sản", "basicInformation": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "addressInformation": "Thông tin địa chỉ", "ownerInformation": "Thông tin chủ sở hữu", "purchaseInformation": "Thông tin mua", "images": "<PERSON><PERSON><PERSON> bất động sản", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t bất động sản thành công", "createSuccess": "<PERSON><PERSON><PERSON> b<PERSON>t động sản thành công", "updateError": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật bất động sản", "createError": "<PERSON><PERSON><PERSON><PERSON> thể tạo bất động sản", "headers": {"propertyInfo": "Thông tin BĐS", "name": "<PERSON><PERSON><PERSON> b<PERSON>t động sản", "address": "Địa chỉ", "type": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>", "totalUnits": "Tổng đơn vị", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "updatedAt": "<PERSON><PERSON><PERSON> n<PERSON> l<PERSON>c", "actions": "<PERSON><PERSON>"}, "address": {"street": "Địa chỉ đường", "city": "<PERSON><PERSON><PERSON><PERSON> phố", "state": "Tỉnh/Thành phố", "zipCode": "<PERSON><PERSON> b<PERSON>u đi<PERSON>n", "country": "Quốc gia"}, "owner": {"name": "<PERSON>ên chủ sở hữu", "email": "<PERSON>ail chủ sở hữu", "phone": "<PERSON><PERSON><PERSON><PERSON> thoại chủ sở hữu"}, "purchase": {"price": "<PERSON><PERSON><PERSON> mua", "date": "<PERSON><PERSON><PERSON> mua"}, "types": {"residential": "<PERSON><PERSON> c<PERSON>", "commercial": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại", "mixed": "Hỗn hợp"}, "status": {"active": "アクティブ", "inactive": "非アクティブ", "maintenance": "メンテナンス", "pending": "保留中"}, "placeholders": {"name": "<PERSON><PERSON><PERSON><PERSON> tên bất động sản", "type": "<PERSON><PERSON><PERSON> lo<PERSON>i bất động sản", "description": "<PERSON><PERSON><PERSON><PERSON> mô tả bất động sản", "street": "<PERSON><PERSON><PERSON><PERSON> địa chỉ đường", "city": "<PERSON><PERSON><PERSON><PERSON> thành phố", "state": "Nhập tỉnh/thành phố", "zipCode": "<PERSON><PERSON><PERSON><PERSON> mã b<PERSON>u đi<PERSON>n", "country": "<PERSON><PERSON><PERSON><PERSON> quốc gia", "ownerName": "<PERSON><PERSON><PERSON><PERSON> tên chủ sở hữu", "ownerEmail": "<PERSON><PERSON>ập email chủ sở hữu", "ownerPhone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại chủ sở hữu", "purchasePrice": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> mua", "uploadImages": "<PERSON><PERSON><PERSON><PERSON> để tải lên hình ảnh hoặc kéo thả"}, "filters": {"search": {"placeholder": "<PERSON><PERSON><PERSON> kiếm bất động sản..."}, "type": "<PERSON><PERSON><PERSON> bất động sản", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>"}, "actions": {"addManual": "<PERSON><PERSON><PERSON><PERSON> bất động sản", "view": "Xem", "edit": "Chỉnh sửa", "delete": "Xóa"}, "deleteSuccess": "<PERSON><PERSON><PERSON> bất động sản thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa bất động sản", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải thông tin bất động sản", "deleteProperty": "<PERSON><PERSON><PERSON>", "deleteConfirmation": "Bạn có chắc chắn muốn xóa {{name}}? Hành động này không thể hoàn tác.", "noImages": "<PERSON><PERSON><PERSON> c<PERSON> hình <PERSON>nh", "unitStatus": "Tổng quan trạng thái căn hộ", "stats": {"totalUnits": "<PERSON><PERSON>ng số căn hộ", "occupancyRate": "Tỷ lệ thuê", "occupiedUnits": "<PERSON><PERSON><PERSON> hộ đã thuê", "availableUnits": "<PERSON><PERSON><PERSON> hộ còn trống"}, "components": {"propertyBasicInfo": {"type": "タイプ", "totalUnits": "総ユニット数", "description": "説明"}, "propertyImages": {"noImages": "画像がありません", "propertyImage": "物件画像"}, "propertyOwnerInfo": {"ownerInformation": "所有者情報", "ownerName": "所有者名", "ownerEmail": "所有者メール", "ownerPhone": "所有者電話番号", "purchasePrice": "購入価格", "purchaseDate": "購入日"}, "propertyHeader": {"edit": "編集", "delete": "削除"}, "propertyStatsCards": {"totalUnits": "総ユニット数", "occupiedUnits": "入居済みユニット", "availableUnits": "空きユニット", "maintenance": "メンテナンス", "occupancyRate": "入居率"}}}, "contracts": {"title": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> đồng", "search": "<PERSON><PERSON><PERSON> kiếm hợp đồng...", "searchPlaceholder": "<PERSON><PERSON><PERSON> kiếm theo số hợp đồng", "export": "<PERSON><PERSON><PERSON> dữ liệu", "confirmDelete": "Bạn có chắc chắn muốn xóa hợp đồng nà<PERSON>?", "viewContracts": "<PERSON><PERSON> đ<PERSON>", "headers": {"contractInfo": "<PERSON><PERSON><PERSON><PERSON> tin hợp đồng", "tenant": "<PERSON><PERSON><PERSON><PERSON> thuê", "property": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "duration": "<PERSON><PERSON><PERSON><PERSON> hạn", "rentAmount": "<PERSON><PERSON><PERSON> thu<PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>"}, "filters": {"status": "<PERSON><PERSON><PERSON><PERSON> thái", "contractType": "<PERSON><PERSON><PERSON> h<PERSON> đ<PERSON>ng", "property": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "startDate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "endDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "clear": "Xóa bộ lọc"}, "status": {"draft": "<PERSON><PERSON><PERSON>", "active": "<PERSON><PERSON> l<PERSON>", "expired": "<PERSON><PERSON><PERSON>", "terminated": "<PERSON><PERSON> hủy", "pending": "<PERSON>ờ <PERSON>"}, "types": {"monthly": "<PERSON>", "annual": "<PERSON>", "profitSharing": "<PERSON><PERSON> sẻ lợi nhuận", "revenueSharing": "<PERSON>a sẻ doanh thu"}, "actions": {"view": "Xem", "edit": "Chỉnh sửa", "delete": "Xóa"}, "createContract": "<PERSON><PERSON><PERSON> đ<PERSON>", "editContract": "Chỉnh sửa hợp đồng", "createDescription": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> đồng bất động sản mới với các điều khoản linh hoạt", "editDescription": "<PERSON><PERSON><PERSON> nhật chi tiết và điều kho<PERSON>n hợp đồng", "sections": {"propertyUnit": "Chọn Bất động sản & <PERSON><PERSON><PERSON> hộ", "contractDetails": "<PERSON> tiết hợp đồng", "financialTerms": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>n tài ch<PERSON>h", "termsConditions": "<PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON> ki<PERSON>n"}, "fields": {"property": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "unit": "<PERSON><PERSON><PERSON>", "tenant": "<PERSON><PERSON><PERSON><PERSON> thuê", "contractType": "<PERSON><PERSON><PERSON> h<PERSON> đ<PERSON>ng", "startDate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "endDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "rentDueDay": "<PERSON><PERSON><PERSON> ti<PERSON>n", "rentAmount": "<PERSON><PERSON> tiền thuê", "depositAmount": "Tiền đặt cọc", "lateFeeAmount": "<PERSON><PERSON> chậm trễ", "noticePeriodDays": "<PERSON><PERSON>ờ<PERSON> gian b<PERSON><PERSON> tr<PERSON> (ngày)", "autoRenewal": "<PERSON><PERSON> hạn tự động", "profitSharingPercentage": "<PERSON><PERSON><PERSON> trăm chia sẻ lợi nhuận", "revenueSharingPercentage": "<PERSON><PERSON><PERSON> tr<PERSON>m chia sẻ doanh thu", "termsConditions": "<PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON> ki<PERSON>n"}, "placeholders": {"property": "<PERSON><PERSON><PERSON> bất động sản", "unit": "<PERSON><PERSON><PERSON> c<PERSON>n hộ", "tenant": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> thuê", "contractType": "<PERSON><PERSON><PERSON> lo<PERSON>i hợp đồng", "rentDueDay": "<PERSON><PERSON><PERSON><PERSON><PERSON> (1-31)", "rentAmount": "<PERSON><PERSON><PERSON><PERSON> số tiền thuê hàng tháng", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> tiền đặt cọc", "lateFeeAmount": "<PERSON><PERSON><PERSON><PERSON> phí chậm tr<PERSON> (t<PERSON><PERSON> ch<PERSON>n)", "noticePeriodDays": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>i gian b<PERSON> tr<PERSON> (ngày)", "profitSharingPercentage": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> trăm (0-100)", "revenueSharingPercentage": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> trăm (0-100)", "termsConditions": "<PERSON><PERSON><PERSON><PERSON> điều khoản và điều kiện hợp đồng"}, "contractTypes": {"monthly": "<PERSON><PERSON><PERSON> theo tháng", "annual": "<PERSON><PERSON><PERSON> the<PERSON> n<PERSON>", "profitSharing": "<PERSON><PERSON> sẻ lợi nhuận", "revenueSharing": "<PERSON>a sẻ doanh thu"}, "descriptions": {"autoRenewal": "Tự động gia hạn hợp đồng khi hết hạn"}, "messages": {"createSuccess": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> đồng thành công", "createError": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> đồng thất bại", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t hợp đồng thành công", "updateError": "<PERSON><PERSON><PERSON> nhật hợp đồng thất bại"}, "contractDetails": "<PERSON>", "overview": "<PERSON><PERSON><PERSON>", "paymentHistory": "<PERSON><PERSON><PERSON>"}, "reports": {"title": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> B<PERSON><PERSON> động sản", "description": "<PERSON><PERSON><PERSON> báo cáo toàn diện và phân tích hiệu suất bất động sản", "generateReport": "Tạo Báo cáo", "downloadReport": "<PERSON><PERSON><PERSON>", "generating": "<PERSON><PERSON> tạo...", "categories": {"financial": {"title": "Báo c<PERSON>o T<PERSON> ch<PERSON>", "description": "<PERSON><PERSON> tích doanh thu, chi phí và lợi nhuận", "generate": "Tạo"}, "occupancy": {"title": "<PERSON><PERSON><PERSON> cáo Tỷ lệ <PERSON>p đ<PERSON>y", "description": "Tỷ lệ trống và giữ chân khách thuê", "generate": "Tạo"}, "performance": {"title": "<PERSON><PERSON> tích <PERSON>", "description": "Chỉ số hiệu suất bất động sản và đơn vị", "generate": "Tạo"}, "maintenance": {"title": "Báo c<PERSON>o <PERSON> trì", "description": "Chi phí bảo trì và xu hướng", "generate": "Tạo"}, "tenant": {"title": "<PERSON><PERSON> tích <PERSON>ch thuê", "description": "Thông tin nhân khẩu học và hành vi khách thuê", "generate": "Tạo"}, "custom": {"title": "Báo cáo Tù<PERSON> chỉnh", "description": "<PERSON>â<PERSON> dựng báo cáo tùy chỉnh với bộ lọc", "create": "Tạo"}}, "comingSoon": {"title": "<PERSON><PERSON> thống <PERSON> c<PERSON>o <PERSON> cao - Sắp ra mắt", "description": "<PERSON><PERSON> tích toà<PERSON>, báo cáo tùy chỉnh và tính năng xuất dữ liệu sẽ có sẵn tại đây."}}, "dashboard": {"propertyAssets": {"title": "<PERSON>ảng điều khiển tài sản bất động sản", "description": "Tổng quan hiệu suất danh mục bất động sản và các chỉ số chính"}, "metrics": {"totalProperties": "<PERSON>ổng số bất động sản", "totalUnits": "<PERSON><PERSON>ng số căn hộ", "activeContracts": "<PERSON><PERSON><PERSON> đồng đang hoạt động", "monthlyRevenue": "<PERSON><PERSON><PERSON> thu hàng tháng"}, "charts": {"occupancyOverview": "T<PERSON>ng quan tỷ lệ lấp đầy"}, "occupancy": {"occupied": "<PERSON><PERSON> thuê", "vacant": "<PERSON><PERSON><PERSON><PERSON>", "rate": "Tỷ lệ lấp đầy", "tenants": "<PERSON>ổng số khách thuê"}, "quickActions": {"title": "<PERSON><PERSON> t<PERSON> n<PERSON>h", "addProperty": "<PERSON><PERSON><PERSON><PERSON> bất động sản", "addUnit": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>n hộ", "createContract": "<PERSON><PERSON><PERSON> đ<PERSON>", "addTenant": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>ch thuê"}, "recentActivities": {"title": "<PERSON><PERSON><PERSON> động gần đây", "viewAll": "<PERSON><PERSON> tất cả hoạt động"}, "portfolio": {"title": "<PERSON><PERSON><PERSON> t<PERSON>t danh mục", "properties": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "units": "<PERSON><PERSON><PERSON>", "contracts": "<PERSON><PERSON><PERSON>", "revenue": "<PERSON><PERSON>h thu", "performance": "<PERSON><PERSON><PERSON> su<PERSON>t tổng thể"}}, "units": {"title": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "addUnit": "<PERSON><PERSON><PERSON><PERSON>", "viewUnits": "<PERSON><PERSON>", "createUnit": "<PERSON><PERSON><PERSON>", "editUnit": "Chỉnh <PERSON><PERSON><PERSON>", "createDescription": "<PERSON><PERSON><PERSON> c<PERSON>n hộ cho thuê mới", "editDescription": "<PERSON><PERSON><PERSON> nhật thông tin căn hộ", "deleteUnit": "<PERSON><PERSON><PERSON>", "deleteConfirmation": "Bạn có chắc chắn muốn xóa căn hộ {{unit}}? Hành động này không thể hoàn tác.", "deleteSuccess": "<PERSON><PERSON><PERSON> căn hộ thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa căn hộ", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t căn hộ thành công", "updateError": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật căn hộ: {{error}}", "createSuccess": "<PERSON><PERSON><PERSON> c<PERSON>n hộ thành công", "createError": "<PERSON><PERSON><PERSON><PERSON> thể tạo căn hộ", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải căn hộ", "unitNumber": "<PERSON><PERSON><PERSON>", "noImages": "<PERSON><PERSON><PERSON><PERSON> có hình <PERSON>nh", "noUnitsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy căn hộ", "noPropertiesAvailable": "<PERSON><PERSON><PERSON><PERSON> có bất động sản", "searchUnits": "<PERSON><PERSON><PERSON> kiếm căn hộ...", "unitList": "<PERSON><PERSON>", "assignUnit": "<PERSON><PERSON>", "selectUnit": "<PERSON><PERSON><PERSON> c<PERSON>n hộ", "basicInformation": "Thông Tin Cơ Bản", "specifications": "Thông Số K<PERSON>hu<PERSON>", "financialInformation": "Thông Tin Tài <PERSON>", "amenities": "<PERSON><PERSON><PERSON><PERSON>", "images": "<PERSON><PERSON><PERSON> Ảnh", "headers": {"unitInfo": "<PERSON><PERSON>ông Tin <PERSON>", "property": "<PERSON><PERSON><PERSON>", "unitNumber": "Số Căn Hộ", "type": "<PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON>", "rent": "<PERSON><PERSON><PERSON><PERSON>", "status": "Trạng <PERSON>", "tenant": "<PERSON><PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>", "floor": "<PERSON><PERSON><PERSON>", "squareFootage": "<PERSON><PERSON><PERSON> (m²)", "bedrooms": "<PERSON><PERSON>ng <PERSON>", "bathrooms": "Phòng Tắm", "rentAmount": "<PERSON><PERSON>", "depositAmount": "<PERSON><PERSON><PERSON><PERSON>"}, "filters": {"type": "<PERSON><PERSON><PERSON>", "status": "Trạng <PERSON>", "property": "<PERSON><PERSON><PERSON>", "rentRange": "Khoảng G<PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "search": {"placeholder": "<PERSON><PERSON><PERSON> kiếm căn hộ..."}}, "actions": {"addManual": "<PERSON><PERSON><PERSON><PERSON> Cô<PERSON>"}, "status": {"available": "<PERSON><PERSON> Sẵn", "occupied": "<PERSON><PERSON>", "maintenance": "Bảo Trì", "inactive": "Không Hoạt Động"}, "types": {"studio": "Studio", "1br": "1 Phòng Ngủ", "2br": "2 Phòng Ngủ", "3br": "3 Phòng Ngủ", "commercial": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "office": "<PERSON><PERSON><PERSON>", "retail": "B<PERSON> Lẻ"}, "placeholders": {"property": "<PERSON><PERSON><PERSON> bất động sản", "unitNumber": "<PERSON><PERSON><PERSON><PERSON> số căn hộ", "type": "<PERSON><PERSON><PERSON> lo<PERSON>i căn hộ", "description": "<PERSON><PERSON><PERSON><PERSON> mô tả căn hộ", "floor": "<PERSON><PERSON><PERSON><PERSON> số tầng", "squareFootage": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>ch", "bedrooms": "Số phòng ngủ", "bathrooms": "Số phòng tắm", "rentAmount": "<PERSON><PERSON><PERSON><PERSON> số tiền thuê", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> ti<PERSON> c<PERSON>c", "uploadImages": "<PERSON><PERSON><PERSON><PERSON> để tải lên hình <PERSON>nh căn hộ"}}, "tenants": {"title": "<PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "editTenant": "Chỉnh <PERSON><PERSON><PERSON>", "createTenant": "<PERSON><PERSON><PERSON>", "editDescription": "<PERSON><PERSON><PERSON> nhật thông tin người thuê", "createDescription": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i thuê mới", "searchPlaceholder": "<PERSON><PERSON><PERSON> kiếm người thuê...", "viewTenant": "<PERSON><PERSON>", "noTenant": "<PERSON><PERSON><PERSON><PERSON>", "sections": {"personalInfo": "Thông Tin Cá Nhân", "identification": "<PERSON><PERSON><PERSON><PERSON>ờ Tùy Thân", "employment": "<PERSON><PERSON><PERSON>", "emergencyContact": "<PERSON><PERSON><PERSON>"}, "fields": {"fullName": "<PERSON><PERSON>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Họ", "email": "Email", "phone": "<PERSON><PERSON><PERSON><PERSON>", "dateOfBirth": "<PERSON><PERSON><PERSON>", "status": "Trạng <PERSON>", "identificationType": "Loại Giấ<PERSON>", "identificationNumber": "Số Giấy Tờ", "employmentStatus": "Tình Trạng Công <PERSON>", "employerName": "<PERSON><PERSON><PERSON>", "monthlyIncome": "<PERSON><PERSON><PERSON>", "emergencyContactName": "<PERSON><PERSON><PERSON>", "emergencyContactPhone": "Số Đ<PERSON>"}, "headers": {"tenantInfo": "T<PERSON>ông Tin Ngườ<PERSON>", "contact": "<PERSON><PERSON><PERSON>", "employmentStatus": "Tình Trạng Công <PERSON>", "status": "Trạng <PERSON>", "emergencyContact": "<PERSON><PERSON><PERSON>", "joinDate": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>"}, "filters": {"leaseStatus": "<PERSON>rạng <PERSON>", "property": "<PERSON><PERSON><PERSON>"}, "status": {"active": "Ho<PERSON><PERSON>", "inactive": "Không Hoạt Động", "expired": "<PERSON><PERSON><PERSON>", "pending": "Chờ <PERSON>"}, "employmentStatus": {"employed": "<PERSON><PERSON>", "unemployed": "<PERSON><PERSON><PERSON><PERSON>", "self_employed": "<PERSON><PERSON>", "student": "<PERSON><PERSON> Viê<PERSON>"}, "actions": {"addManual": "<PERSON><PERSON><PERSON><PERSON>"}, "stats": {"totalContracts": "Tổng Số H<PERSON>", "activeContracts": "<PERSON><PERSON><PERSON>", "joinDate": "<PERSON><PERSON><PERSON>"}, "quickStats": "<PERSON><PERSON><PERSON><PERSON>", "activeContracts": "<PERSON><PERSON><PERSON>", "identificationTypes": {"passport": "<PERSON><PERSON>", "national_id": "CMND/CCCD", "driver_license": "Bằng Lái Xe"}, "errors": {"notFound": "<PERSON><PERSON><PERSON><PERSON>", "notFoundDescription": "<PERSON>ười thuê mà bạn đang tìm không tồn tại hoặc đã bị xóa."}, "documents": "<PERSON><PERSON><PERSON>", "deleteConfirmation": {"title": "<PERSON><PERSON><PERSON>", "description": "Bạn có chắc chắn muốn xóa người thuê này? Hành động này không thể hoàn tác.", "hasActiveContracts": "<PERSON><PERSON><PERSON>i thuê này có hợp đồng đang hoạt động. Bạn có chắc chắn muốn xóa?"}, "messages": {"deleteSuccess": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i thuê thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa người thuê"}}, "maintenance": {"title": "Bảo Trì", "add": "<PERSON><PERSON><PERSON>", "search": "T<PERSON><PERSON> kiếm bảo trì...", "searchPlaceholder": "T<PERSON><PERSON> kiếm theo tiêu đề yêu cầu", "export": "<PERSON><PERSON><PERSON>", "confirmDelete": "Bạn có chắc chắn muốn xóa yêu cầu bảo trì này?", "headers": {"requestInfo": "<PERSON><PERSON><PERSON><PERSON>", "property": "<PERSON><PERSON><PERSON>", "requester": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "priority": "Đ<PERSON> Ưu Tiên", "status": "Trạng <PERSON>", "assignedTo": "<PERSON><PERSON>", "estimatedCost": "Chi Phí Ước Tính", "dueDate": "<PERSON><PERSON><PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>"}, "filters": {"status": "Trạng <PERSON>", "priority": "Đ<PERSON> Ưu Tiên", "category": "<PERSON><PERSON>", "property": "<PERSON><PERSON><PERSON>"}, "priorities": {"low": "<PERSON><PERSON><PERSON><PERSON>", "medium": "<PERSON><PERSON>", "high": "<PERSON>", "urgent": "<PERSON><PERSON><PERSON><PERSON>"}, "categories": {"plumbing": "Ống Nước", "electrical": "<PERSON><PERSON><PERSON><PERSON>", "hvac": "<PERSON><PERSON><PERSON><PERSON>", "structural": "<PERSON><PERSON><PERSON>", "appliance": "<PERSON><PERSON><PERSON>t Bị", "cosmetic": "<PERSON><PERSON><PERSON><PERSON>", "cleaning": "<PERSON><PERSON>", "security": "<PERSON>", "general": "<PERSON>", "other": "K<PERSON><PERSON><PERSON>"}, "actions": {"addManual": "<PERSON><PERSON><PERSON><PERSON>", "view": "Xem", "edit": "Chỉnh Sửa", "delete": "Xóa"}, "createRequest": "<PERSON><PERSON><PERSON>", "editRequest": "Chỉnh <PERSON><PERSON><PERSON>", "createDescription": "<PERSON><PERSON><PERSON> yêu cầu bảo trì mới cho bất động sản của bạn", "editDescription": "<PERSON><PERSON><PERSON> nhật chi tiết yêu cầu bảo trì", "sections": {"propertyUnit": "Lựa Chọn Bất Động Sản & Căn Hộ", "requestDetails": "<PERSON>", "scheduling": "Lập L<PERSON> & Nhà Thầu", "costInfo": "Thông Tin Chi Phí", "additional": "T<PERSON>ông Tin Bổ Sung"}, "fields": {"property": "<PERSON><PERSON><PERSON>", "unit": "<PERSON><PERSON><PERSON>", "tenant": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON>i<PERSON><PERSON>", "description": "<PERSON><PERSON>", "priority": "Đ<PERSON> Ưu Tiên", "category": "<PERSON><PERSON>", "status": "Trạng <PERSON>", "scheduledDate": "<PERSON><PERSON><PERSON>", "completedDate": "<PERSON><PERSON><PERSON>", "contractorName": "<PERSON><PERSON><PERSON>", "contractorPhone": "Số Điện Thoại Nhà <PERSON>hầ<PERSON>", "estimatedCost": "Chi Phí Ước Tính ($)", "actualCost": "<PERSON> Phí <PERSON> ($)", "notes": "<PERSON><PERSON>", "images": "<PERSON><PERSON><PERSON>"}, "placeholders": {"property": "<PERSON><PERSON><PERSON> bất động sản", "unit": "<PERSON><PERSON><PERSON> c<PERSON>n hộ", "tenant": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> thuê", "title": "<PERSON><PERSON> tả ngắn gọn về vấn đề", "description": "<PERSON><PERSON> tả chi tiết về vấn đề bảo trì", "priority": "<PERSON><PERSON><PERSON> mức độ ưu tiên", "category": "<PERSON><PERSON><PERSON> danh mục", "status": "<PERSON><PERSON><PERSON> trạng thái", "contractorName": "<PERSON><PERSON><PERSON><PERSON> tên nhà thầu", "contractorPhone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại nhà thầu", "estimatedCost": "0.00", "actualCost": "0.00", "notes": "<PERSON><PERSON> chú hoặc hướng dẫn bổ sung"}, "options": {"propertyWide": "<PERSON><PERSON><PERSON> trì toàn bộ bất động sản", "noTenant": "<PERSON><PERSON><PERSON><PERSON> có người thuê cụ thể"}, "status": {"open": "Mở", "in_progress": "<PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON>", "cancelled": "Đã <PERSON>", "inProgress": "<PERSON><PERSON>"}, "messages": {"createSuccess": "<PERSON><PERSON><PERSON> y<PERSON>u cầu bảo trì thành công", "createError": "<PERSON><PERSON><PERSON><PERSON> thể tạo yêu cầu bảo trì", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t yêu cầu bảo trì thành công", "updateError": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật yêu cầu bảo trì"}, "viewRequests": "<PERSON><PERSON>"}, "payments": {"headers": {"paymentInfo": "Thông Tin Thanh Toán", "amount": "Số Tiền", "contract": "<PERSON><PERSON><PERSON>", "property": "<PERSON><PERSON><PERSON>", "status": "Trạng <PERSON>", "paymentDate": "<PERSON><PERSON><PERSON>", "createdAt": "Tạo Lú<PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>"}, "status": {"pending": "<PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON><PERSON>", "refunded": "<PERSON><PERSON><PERSON>"}}, "pipelines": {"headers": {"activities": "<PERSON><PERSON><PERSON> đ<PERSON>", "amount": "Số lượng", "assignee": "Ngườ<PERSON> phụ trách", "closeDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c  ", "createdAt": "<PERSON><PERSON><PERSON>", "customer": "<PERSON><PERSON><PERSON><PERSON>", "expectedClosingDate": "<PERSON><PERSON><PERSON> kết thúc dự kiến", "expectedRevenue": "<PERSON><PERSON><PERSON> thu dự kiến", "opportunity": "<PERSON><PERSON> hội", "pipeline": "<PERSON><PERSON><PERSON> đo<PERSON>n xử lý", "probability": "<PERSON><PERSON><PERSON>", "stage": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>"}}}, "table": {"pagination": {"rowsPerPage": "<PERSON><PERSON> hàng trên trang", "description": "{{start}} đến {{end}} hàng trong tổng số {{total}}", "next": "<PERSON><PERSON><PERSON><PERSON>", "previous": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "selected": {"title": "<PERSON><PERSON> ch<PERSON>n", "delete": "Xóa {{count}} đ<PERSON> chọn"}, "export": {"title": "<PERSON><PERSON><PERSON> dữ liệu", "description": "<PERSON><PERSON><PERSON> dữ liệu", "confirm": "<PERSON><PERSON><PERSON> dữ liệu", "cancel": "<PERSON><PERSON><PERSON>"}, "filter": {"clearFilter": "Xóa bộ lọc", "loadMore": "<PERSON><PERSON><PERSON>ê<PERSON>"}, "savedFilters": {"FilterTypeRequired": "Loại bộ lọc là bắt buộc", "NoFiltersToSave": "<PERSON><PERSON><PERSON><PERSON> có bộ lọc để lưu", "CreateFilterSuccess": "<PERSON><PERSON> lọc đã đ<PERSON><PERSON><PERSON> lưu thành công", "CreateFilterFail": "<PERSON><PERSON><PERSON><PERSON> thể lưu bộ lọc", "UpdateFilterSuccess": "<PERSON><PERSON> lọc đã đư<PERSON><PERSON> cập nhật thành công", "DeleteFilterSuccess": "<PERSON><PERSON> lọc đã đư<PERSON><PERSON> xóa thành công", "DeleteFilterFail": "<PERSON><PERSON><PERSON><PERSON> thể xóa bộ lọc", "UpdateFilterFail": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật bộ lọc", "UpdateFilter": "<PERSON><PERSON> lọc đã đ<PERSON><PERSON><PERSON> cập nh<PERSON>t", "settings": {"settings": "Cài đặt", "title": "Cài đặt tab", "description": "6 tab đầu tiên sẽ được ưu tiên hiển thị trên trang chính. Tùy chỉnh chúng dựa trên nhu cầu của bạn.", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tên tab", "delete": {"title": "Xóa bộ lọc đã lưu", "description": "Bạn có chắc chắn muốn xóa bộ lọc đã lưu này? Hành động này không thể hoàn tác.", "confirm": "Xóa"}}}}, "validation": {"titleRequired": "タイトルは必須です", "required": "このフィールドは必須です", "invalidEmail": "有効なメールアドレスを入力してください", "minLength": "少なくとも{{count}}文字以上必要です", "maxLength": "{{count}}文字以内にしてください", "passwordMismatch": "パスワードが一致しません", "invalidUsername": "ユーザー名は3文字以上必要です", "emailRequired": "メールアドレスを入力してください", "usernameRequired": "ユーザー名を入力してください", "passwordRequired": "パスワードを入力してください", "confirmPasswordRequired": "パスワードを確認してください", "invalidPassword": "パスワードは8文字以上必要です", "passwordsDoNotMatch": "パスワードが一致しません", "verificationCodeRequired": "確認コードを入力してください", "verificationCodeLength": "確認コードは6文字必要です", "sessionRequired": "このフィールドは必須です", "usernameSpecialCharacters": "ユーザー名は英数字とアンダースコアのみ使用できます", "skuFormat": "SKUは英数字とアンダースコアのみ使用できます", "skuRequired": "SKUは必須です", "nameRequired": "表示効率を最適化するために、商品名を入力してください", "nameTooLong": "商品名は250文字以内にしてください", "titleTooLong": "タイトルは100文字以内にしてください", "priceRequired": "価格は必須です", "imageRequired": "少なくとも1枚の画像を追加してください", "imageFormat": "画像の形式が無効です", "priceMustBePositive": "正の価格を入力してください", "invalidPrice": "価格が無効です", "wrongUsernameOrPassword": "ユーザー名またはパスワードが間違っています", "phoneRequired": "電話番号は必須です", "phoneNumberAlreadyExists": "電話番号がすでに存在します", "contactRequired": "連絡先は必須です", "customerRequired": "顧客は必須です", "emailOrPhoneRequired": "メールアドレスまたは電話番号は必須です", "activityTypeRequired": "アクティビティの種類は必須です", "summaryRequired": "概要は必須です", "propertyRequired": "不動産は必須です", "unitRequired": "単位は必須です", "tenantRequired": "顧客は必須です", "contractTypeRequired": "契約の種類は必須です", "startDateRequired": "開始日は必須です", "rentAmountMustBePositive": "賃料は正の数で入力してください", "depositAmountMustBePositive": "預金額は正の数で入力してください", "lateFeeAmountMustBePositive": "遅延料は正の数で入力してください", "rentDueDayMin": "支払い日は1日以上で入力してください", "rentDueDayMax": "支払い日は31日以内で入力してください", "percentageMax": "割合は100%以内にしてください", "noticePeriodMustBePositive": "通知期間は正の数で入力してください", "sharingPercentageRequired": "この契約の種類では、共有割合は必須です", "idRequired": "IDは必須です"}, "footer": {"crafted": "OneXAPIsによって作成されました", "by": "by", "team": "OneXAPIsチーム", "heart": "heart"}, "install": {"installing": "インストール中...", "pleaseWait": "しばらくお待ちください", "error": {"backToHome": "ホームに戻る", "notFound": "お探しのページが見つかりません", "installationFailed": "インストールに失敗しました", "missingSourceChannel": "ソースチャンネルが見つかりません", "authorizeDestination": "{channel_name}の認証を行ってください"}}, "error": {"backToHome": "ホームに戻る", "notFound": "ページが見つかりません", "notFoundDescription": "お探しのページが見つかりません。"}, "socialIntegration": {"authorize": "認証", "reAuthorize": "再認証", "newAuthorize": "新規認証", "authorized": "認証済み", "sessionExpired": "セッションが期限切れです", "failedToSwitchStatus": "接続状態の変更に失敗しました", "failedToSetup": "接続の設定に失敗しました", "facebookSetup": "Facebookの連携を設定", "zaloSetup": "Zalo OAの連携を設定", "defaultSetup": "連携を設定"}, "filter": {"allTime": "すべての時間", "today": "今日", "yesterday": "昨日", "lastWeek": "先週", "lastMonth": "先月", "last7Days": "先週", "last30Days": "先月", "last90Days": "先月", "thisWeek": "今週", "thisMonth": "今月", "thisYear": "今年", "customize": "カスタマイズ", "reset": "リセット", "apply": "適用"}, "financial": {"dashboard": {"title": "財務ダッシュボード", "description": "不動産の財務パフォーマンスと指標の概要"}, "timeRange": {"month": "月", "quarter": "四半期", "year": "年"}, "exportReport": "レポートをエクスポート", "metrics": {"totalRevenue": "総収入", "netIncome": "純収入", "occupancyRate": "稼働率", "totalExpenses": "総コスト"}, "charts": {"revenueTrend": "収益のトレンド", "expenseBreakdown": "コスト分析", "occupancyTrend": "稼働率のトレンド", "revenueAnalysis": "収益分析"}, "revenue": "<PERSON><PERSON>h thu", "netIncome": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> ròng", "performance": "<PERSON><PERSON><PERSON>", "profitMargin": "Tỷ suất lợi n<PERSON>n", "totalProperties": "<PERSON>ổng số bất động sản", "totalUnits": "<PERSON><PERSON>ng số căn hộ", "occupiedUnits": "<PERSON><PERSON><PERSON> hộ đã thuê", "vacancyRate": "Tỷ lệ trống", "ytdPerformance": "<PERSON><PERSON><PERSON> suất từ đầu năm", "topProperties": "<PERSON><PERSON><PERSON> động sản hiệu suất cao", "monthlyRevenue": "doanh thu hàng tháng", "expenses": "Chi phí", "alerts": {"title": "<PERSON><PERSON><PERSON> b<PERSON>o tài ch<PERSON>h", "rentDue": "<PERSON><PERSON><PERSON> đến hạn thu tiền thuê", "highExpenses": "<PERSON><PERSON><PERSON> báo chi phí cao", "leaseExpiring": "<PERSON><PERSON><PERSON> đồng sắ<PERSON> hết hạn"}, "revenueAnalytics": {"title": "<PERSON><PERSON> tích doanh thu", "description": "<PERSON><PERSON> tích doanh thu chi tiết với biểu đồ tương tác và xu hướng"}, "expenseAnalysis": {"title": "<PERSON>ân tích chi phí", "description": "<PERSON><PERSON> tích chi phí toàn diện và thông tin tối ưu hóa chi phí"}, "profitAnalysis": {"title": "<PERSON><PERSON> tích lợi n<PERSON>n", "description": "<PERSON> hướng lợi nhuận, biên lợi nhuận và theo dõi hiệu suất ROI"}}, "contracts": {"contractDetails": "<PERSON>", "contractId": "<PERSON><PERSON>", "overview": "<PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON><PERSON>", "progress": "<PERSON>i<PERSON><PERSON>", "daysRemaining": "ng<PERSON><PERSON> còn lại", "openEnded": "<PERSON><PERSON><PERSON>ng giới hạn thời gian", "autoRenewalEnabled": "<PERSON><PERSON> hạn tự động đ<PERSON><PERSON><PERSON> bật", "perMonth": "mỗi tháng", "dayOfMonth": " của mỗi tháng", "sharingTerms": "<PERSON><PERSON><PERSON><PERSON> Sẻ", "paymentSummary": "<PERSON><PERSON>ng <PERSON>", "revenueProjections": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON>ông Tin <PERSON>", "days": "ng<PERSON>y", "quickActions": "<PERSON><PERSON>", "fields": {"property": "<PERSON><PERSON><PERSON>", "unit": "<PERSON><PERSON><PERSON>", "tenant": "<PERSON><PERSON><PERSON><PERSON>", "startDate": "<PERSON><PERSON><PERSON>", "endDate": "<PERSON><PERSON><PERSON>", "rentAmount": "<PERSON><PERSON>", "depositAmount": "Tiền Đặt Cọc", "lateFeeAmount": "<PERSON><PERSON>", "rentDueDay": "<PERSON><PERSON><PERSON>", "profitSharingPercentage": "Phần Tr<PERSON><PERSON> Sẻ <PERSON>ợ<PERSON>n", "revenueSharingPercentage": "<PERSON>ần <PERSON>r<PERSON><PERSON> Sẻ <PERSON>h <PERSON>hu", "noticePeriod": "Thời G<PERSON>"}, "sections": {"financialTerms": "<PERSON><PERSON><PERSON><PERSON>", "termsConditions": "<PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON>"}, "stats": {"totalPayments": "Tổng Thanh Toán", "completedPayments": "<PERSON><PERSON>", "pendingPayments": "Chờ Thanh To<PERSON>", "totalCollected": "<PERSON><PERSON><PERSON>"}, "projections": {"expectedMonthly": "<PERSON><PERSON><PERSON>", "expectedAnnual": "<PERSON><PERSON><PERSON>"}, "actions": {"recordPayment": "<PERSON><PERSON> <PERSON>", "viewPayments": "<PERSON><PERSON>"}, "errors": {"notFound": "<PERSON><PERSON><PERSON><PERSON>", "notFoundDescription": "<PERSON><PERSON><PERSON> đồng bạn đang tìm không tồn tại hoặc đã bị xóa."}, "deleteConfirmation": {"title": "<PERSON><PERSON><PERSON>", "description": "Bạn có chắc chắn muốn xóa hợp đồng này? Hành động này không thể hoàn tác.", "activeContract": "<PERSON><PERSON><PERSON> là hợp đồng đang hoạt động. Việ<PERSON> xóa có thể ảnh hưởng đến các thanh toán và ghi chép hiện tại."}, "messages": {"deleteSuccess": "<PERSON><PERSON><PERSON> hợp đồng thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa hợp đồng"}}, "unit": {"title": "<PERSON><PERSON><PERSON>", "type": {"apartment": "<PERSON><PERSON><PERSON>", "studio": "Studio", "1br": "1 Phòng Ngủ", "2br": "2 Phòng Ngủ", "3br": "3 Phòng Ngủ", "4br": "4 Phòng Ngủ", "penthouse": "Penthouse", "duplex": "Duplex", "commercial": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "office": "<PERSON><PERSON><PERSON>", "retail": "B<PERSON> Lẻ", "warehouse": "<PERSON><PERSON>"}}, "numbers": {"abbreviations": {"thousand": "K", "million": "tr", "billion": "tỷ", "trillion": "nghìn tỷ"}, "currency": {"symbol": "₫", "code": "VND"}}, "payments": {"createPayment": "<PERSON><PERSON><PERSON>", "editPayment": "Chỉnh <PERSON><PERSON><PERSON>", "createDescription": "<PERSON><PERSON><PERSON> bản ghi <PERSON>h toán mới cho tiền thuê, tiền đặt cọc và các khoản phí khác", "editDescription": "<PERSON><PERSON><PERSON> nhật thông tin thanh toán và trạng thái", "sections": {"contract": "<PERSON><PERSON>ông Tin <PERSON>", "paymentDetails": "<PERSON>"}, "fields": {"contract": "<PERSON><PERSON><PERSON>", "amount": "Số Tiền", "paymentDate": "<PERSON><PERSON><PERSON>", "paymentType": "<PERSON><PERSON><PERSON>", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON>", "referenceNumber": "<PERSON><PERSON>", "status": "Trạng <PERSON>", "description": "<PERSON><PERSON>"}, "placeholders": {"contract": "<PERSON><PERSON><PERSON> h<PERSON> đồng", "amount": "0.00", "paymentType": "<PERSON><PERSON><PERSON> lo<PERSON>i thanh toán", "paymentMethod": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "referenceNumber": "<PERSON><PERSON><PERSON><PERSON> số tham chiếu", "status": "<PERSON><PERSON><PERSON> trạng thái", "description": "<PERSON><PERSON><PERSON><PERSON> mô tả hoặc ghi chú"}, "paymentTypes": {"rent": "<PERSON><PERSON><PERSON><PERSON>", "deposit": "Tiền Đặt Cọc", "lateFee": "<PERSON><PERSON> <PERSON>", "maintenance": "Bảo Trì", "other": "K<PERSON><PERSON><PERSON>"}, "paymentMethods": {"cash": "Tiền Mặt", "bankTransfer": "<PERSON><PERSON><PERSON><PERSON>", "creditCard": "Thẻ <PERSON>", "check": "Séc"}, "status": {"pending": "<PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON><PERSON>", "refunded": "<PERSON><PERSON><PERSON>"}, "contractInfo": {"rent": "<PERSON><PERSON><PERSON><PERSON>", "deposit": "Tiền Đặt Cọc", "dueDay": "<PERSON><PERSON><PERSON>"}, "quickFill": {"rent": "<PERSON><PERSON><PERSON>n <PERSON> T<PERSON>ền <PERSON>", "deposit": "<PERSON><PERSON><PERSON><PERSON>"}, "messages": {"createSuccess": "支払いを作成しました", "updateSuccess": "支払いを更新しました", "createError": "支払いを作成できませんでした", "updateError": "支払いを更新できませんでした", "deleteSuccess": "支払いを削除しました", "deleteError": "支払いを削除できませんでした"}}, "permissions": {"staff": {"CREATE_STAFF": "スタッフを作成", "CREATE_STAFF_TASK": "スタッフタスクを作成", "CREATE_STAFF_KNOWLEDGE": "スタッフ知識を作成", "GET_STAFF": "スタッフを取得", "LIST_STAFF": "スタッフ一覧", "UPDATE_STAFF": "スタッフを更新", "UPDATE_STAFF_ROLE": "スタッフロールを更新", "UPDATE_STAFF_INTERACT_INFO": "スタッフインタラクション情報を更新", "DELETE_STAFF_KNOWLEDGE": "スタッフ知識を削除", "DELETE_STAFF_TASK": "スタッフタスクを削除", "INTEGRATE_STAFF_TO_SOCIAL_PLATFORM": "スタッフをソーシャルプラットフォームに統合"}, "account": {"CREATE_STAFF_ACCOUNT": "スタッフアカウントを作成", "GET_STAFF_ACCOUNT": "スタッフアカウントを取得", "LIST_STAFFS_ACCOUNT": "スタッフアカウント一覧", "UPDATE_STAFF_ACCOUNT": "スタッフアカウントを更新", "UPDATE_STAFF_ACCOUNT_AUTHORIZATION": "スタッフアカウント認証を更新", "DISABLE_STAFF_ACCOUNT": "スタッフアカウントを無効化", "DELETE_STAFF_ACCOUNT": "スタッフアカウントを削除"}, "activity": {"CREATE_ACTIVITIES": "アクティビティを作成", "LIST_ACTIVITIES": "アクティビティ一覧", "LIST_ACTIVITIES_IN_CHARGE": "担当アクティビティ一覧", "UPDATE_ACTIVITIES": "アクティビティを更新", "DELETE_ACTIVITIES": "アクティビティを削除", "ASSIGN_STAFF_TO_ACTIVITIES": "スタッフをアクティビティに割り当て"}, "branch": {"CREATE_BRAND": "ブランドを作成", "GET_BRAND": "ブランドを取得", "LIST_BRAND": "ブランド一覧", "UPDATE_BRAND": "ブランドを更新", "DELETE_BRAND": "ブランドを削除"}, "brand": {"BRAND_LIST_EXPORT_FILE": "ブランド一覧エクスポートファイル"}, "conversation": {"LIST_MESSAGE": "メッセージ一覧", "LIST_MESSAGE_IN_CHARGE": "担当メッセージ一覧", "ASSIGN_STAFF_TO_CONVERSATION": "スタッフを会話に割り当て", "REPLY_MESSAGE": "メッセージに返信"}, "customer": {"CREATE_CUSTOMER": "顧客を作成", "GET_CUSTOMER": "顧客を取得", "LIST_CUSTOMER": "顧客一覧", "LIST_CUSTOMER_IN_CHARGE": "担当顧客一覧", "UPDATE_CUSTOMER": "顧客を更新", "DELETE_CUSTOMER": "顧客を削除", "SHOW_CUSTOMER_PHONE": "顧客電話番号を表示", "CUSTOMER_LIST_EXPORT_FILE": "顧客一覧エクスポートファイル", "SHOW_CUSTOMER_GROUP": "顧客グループを表示"}, "department": {"CREATE_DEPARTMENT": "部署を作成", "GET_DEPARTMENT": "部署を取得", "UPDATE_DEPARTMENT": "部署を更新", "UPDATE_DEPARTMENT_DESCRIPTION": "部署説明を更新", "DELETE_DEPARTMENT": "部署を削除"}, "knowledge": {"CREATE_KNOWLEDGE": "知識を作成", "GET_KNOWLEDGE": "知識を取得", "UPDATE_KNOWLEDGE": "知識を更新", "DELETE_KNOWLEDGE": "知識を削除"}, "opportunity": {"CREATE_OPPORTUNITY": "機会を作成", "GET_OPPORTUNITY": "機会を取得", "LIST_OPPORTUNITY": "機会一覧", "LIST_OPPORTUNITY_IN_CHARGE": "担当機会一覧", "LIST_OPPORTUNITY_HISTORY": "機会履歴一覧", "LIST_OPPORTUNITY_ORDER_HISTORY": "機会注文履歴一覧", "UPDATE_OPPORTUNITY": "機会を更新", "UPDATE_OPPORTUNITY_EXPECTED_CLOSING_DATE": "機会予定終了日を更新", "UPDATE_OPPORTUNITY_PRIORITY": "機会優先度を更新", "UPDATE_OPPORTUNITY_NOTE": "機会メモを更新", "UPDATE_OPPORTUNITY_EXPECTED_REVENUE": "機会予想収益を更新", "DELETE_OPPORTUNITY": "機会を削除", "ASSIGN_STAFF_TO_OPPORTUNITY": "スタッフを機会に割り当て", "MARK_OPPORTUNITY_WON_LOST": "機会の勝敗をマーク"}, "order": {"CREATE_ORDER": "注文を作成", "GET_ORDER": "注文を取得", "LIST_ORDER": "注文一覧", "LIST_ORDER_IN_CHARGE": "担当注文一覧", "UPDATE_ORDER": "注文を更新", "UPDATE_ORDER_NOTE": "注文メモを更新", "DELETE_ORDER": "注文を削除", "ORDER_CONFIRM_STATUS": "注文確認ステータス", "SAVE_DRAFT_ORDER": "下書き注文を保存", "ORDER_CONFIRM_PAID_STATUS": "注文支払い済み確認ステータス", "ORDER_CONFIRM_COMPLETED_STATUS": "注文完了確認ステータス", "ORDER_CONFIRM_CANCELED_STATUS": "注文キャンセル確認ステータス", "ORDER_CONFIRM_PACKING_STATUS": "注文梱包確認ステータス", "ORDER_IMPORT": "注文インポート", "ORDER_LIST_EXPORT_FILE": "注文一覧エクスポートファイル"}, "product": {"CREATE_PRODUCT": "商品を作成", "CREATE_PRODUCT_QUICKLY": "商品を素早く作成", "GET_PRODUCT": "商品を取得", "GET_PRODUCT_PRICE_GROUP": "商品価格グループを取得", "LIST_PRODUCT": "商品一覧", "LIST_PUBLISHED_PRODUCT": "公開済み商品一覧", "UPDATE_PRODUCT": "商品を更新", "UPDATE_PRODUCT_FILES": "商品ファイルを更新", "DELETE_PRODUCT": "商品を削除", "PRODUCT_LIST_EXPORT_FILE": "商品一覧エクスポートファイル"}, "role": {"CREATE_ROLE": "ロールを作成", "LIST_ROLE": "ロール一覧", "UPDATE_ROLE": "ロールを更新", "DELETE_ROLE": "ロールを削除"}, "stage": {"CREATE_STAGE": "ステージを作成", "GET_STAGE_TOTAL_EXPECTED_REVENUE": "ステージ総予想収益を取得", "UPDATE_STAGE": "ステージを更新", "DELETE_STAGE": "ステージを削除"}, "task": {"CREATE_TASK": "タスクを作成", "GET_TASK": "タスクを取得", "LIST_TASK": "タスク一覧", "UPDATE_TASK": "タスクを更新", "UPDATE_TASK_PROMPT": "タスクプロンプトを更新", "DELETE_TASK": "タスクを削除"}, "variant": {"VARIANT_LIST_EXPORT_FILE": "バリエーション一覧エクスポートファイル"}}}