import { useCallback } from "react";

import { LanguageCurrencyFormData } from "../types";
import { useCurrency } from "./useCurrency";
import { useLanguage } from "./useLanguage";

// Combined hook for backward compatibility (combines both language and currency)
export const useLanguageCurrency = () => {
  const languageHook = useLanguage();
  const currencyHook = useCurrency();

  // Comment out automatic currency sync - let user control currency independently
  // useEffect(() => {
  //   if (languageHook.selectedLanguages.length > 0) {
  //     const selectedLanguage = languageHook.selectedLanguages[0];

  //     // If the selected language changes, update the currency to match
  //     // This ensures the currency always corresponds to the selected language
  //     const shopInfo = getSettingValue<Record<string, any>>("shop_info");
  //     if (shopInfo?.language) {
  //       const languageSetting = shopInfo.language.find(
  //         (lang: any) => lang.code === selectedLanguage
  //       );
  //       if (languageSetting) {
  //         // Update the currency to match the selected language
  //         currencyHook.handleCurrencyToggle(languageSetting.currency);
  //       }
  //     }
  //   }
  // }, [languageHook.selectedLanguages, getSettingValue, currencyHook.handleCurrencyToggle]);

  const getFormData = useCallback(
    (): LanguageCurrencyFormData => ({
      selectedLanguages: languageHook.selectedLanguages,
      selectedCurrencies: currencyHook.selectedCurrencies,
    }),
    [languageHook.selectedLanguages, currencyHook.selectedCurrencies]
  );

  const hasChanges = useCallback(() => {
    return languageHook.hasLanguageChanges() || currencyHook.hasCurrencyChanges();
  }, [languageHook, currencyHook]);

  // Combined loading state - consider both version loading and individual hook loading
  const isLoadingLanguage = languageHook.isLoading;
  const isLoadingCurrency = currencyHook.isLoading;
  const isVersionLoading = languageHook.isVersionLoading || currencyHook.isVersionLoading;

  return {
    ...languageHook,
    ...currencyHook,
    getFormData,
    hasChanges,
    isLoadingLanguage,
    isLoadingCurrency,
    isVersionLoading,
  };
};

// Re-export individual hooks for convenience
export { useLanguage } from "./useLanguage";
export { useCurrency } from "./useCurrency";
