export interface ProfileData {
  name: string;
  username: string;
  email: string;
  phone: string | null;
  avatar?: string | null;
}

export interface UpdateProfilePayload {
  name: string;
  username: string;
  email?: string;
  phone?: string | null;
  avatar?: string | null;
}

export interface ProfileResponse {
  success: boolean;
  data: ProfileData;
  message?: string;
}

export interface UpdateProfileResponse {
  success: boolean;
  message: string;
}
