"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

interface CreateCategoryDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  trigger?: React.ReactNode;
}

export function CreateCategoryDialog({ open, onOpenChange, trigger }: CreateCategoryDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Create New Category</DialogTitle>
          <DialogDescription>
            Add a new asset category to organize your properties
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="name">Category Name</Label>
            <Input placeholder="Enter category name" />
          </div>
          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea placeholder="Describe this category" />
          </div>
          <div>
            <Label htmlFor="icon">Icon</Label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Select an icon" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Home">🏠 Home</SelectItem>
                <SelectItem value="Building">🏢 Building</SelectItem>
                <SelectItem value="Car">🚗 Car</SelectItem>
                <SelectItem value="Wrench">🔧 Wrench</SelectItem>
                <SelectItem value="Lightbulb">💡 Lightbulb</SelectItem>
                <SelectItem value="Shield">🛡️ Shield</SelectItem>
                <SelectItem value="TreePine">🌲 Tree</SelectItem>
                <SelectItem value="Wifi">📶 Wifi</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="color">Color</Label>
            <div className="flex space-x-2">
              {["#8884d8", "#82ca9d", "#ffc658", "#ff7300", "#8dd1e1", "#d084d0"].map((color) => (
                <div
                  key={color}
                  className="size-8 cursor-pointer rounded border-2"
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={() => onOpenChange(false)}>Create Category</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
