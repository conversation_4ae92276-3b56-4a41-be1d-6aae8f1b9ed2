import { useEffect, useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { t } from "i18next";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { settingApi } from "@/lib/apis/setting";
import { useVersion, Version, versionApi } from "@/lib/apis/version";

// Language setting type that matches the API structure
export interface LanguageSetting {
  name: string;
  code: string;
}

// Form type for language settings
export interface LanguageSettingsForm {
  languages: LanguageSetting[];
}

const defaultLanguageSettings: LanguageSetting[] = [
  {
    name: "English",
    code: "en",
  },
];

export const useLanguageSetting = () => {
  const { getSettingValue, isLoading } = useVersion();
  const [isLoadingState, setIsLoadingState] = useState(isLoading);

  useEffect(() => {
    setIsLoadingState(isLoading);
  }, [isLoading]);

  const shopInfo = getSettingValue<Record<string, any>>("shop_info");

  const form = useForm<LanguageSettingsForm>({
    defaultValues: {
      languages: defaultLanguageSettings,
    },
  });

  // Track original form values for isDirty comparison
  const [originalValues, setOriginalValues] = useState<LanguageSettingsForm | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (!shopInfo || isLoading) return;

    const formData: LanguageSettingsForm = {
      languages: shopInfo.language || defaultLanguageSettings,
    };

    // Only update if the values are actually different to prevent infinite loops
    const currentFormData = form.getValues();
    if (
      !isInitialized ||
      JSON.stringify(currentFormData.languages) !== JSON.stringify(formData.languages)
    ) {
      form.reset(formData);
      setOriginalValues(formData);
      setIsInitialized(true);
    }
  }, [shopInfo, form, isLoading, isInitialized]);

  // Check if form has been modified
  const isDirty = form.formState.isDirty;

  const queryClient = useQueryClient();

  const updateLanguageMutation = useMutation({
    mutationFn: async (updatedShopInfo: Record<string, any>) => {
      await settingApi.updateLanguageSetting("shop_info", updatedShopInfo);
      const version = await versionApi.getVersion();

      return {
        ...version,
        data: {
          ...version.data,
          dict: {
            ...version.data.dict,
            shop_info: { ...version.data.dict.shop_info, setting_value: updatedShopInfo },
          },
        },
      };
    },
    onSuccess: (updatedVersion: Version) => {
      queryClient.setQueryData(["version"], updatedVersion);
      toast.success(t("common.success"), {
        description: t("pages.settings.language.saveSuccess"),
      });
    },
    onError: (error: Error) => {
      console.error("Error updating language settings:", error);
      toast.error(t("common.error"), {
        description: t("pages.settings.language.saveError"),
      });
    },
  });

  const onSubmit = async (data: LanguageSettingsForm) => {
    try {
      setIsLoadingState(true);

      // Get current shop_info settings from version
      const currentShopInfo = getSettingValue<Record<string, any>>("shop_info") || {};

      // Update only language-related fields while preserving other fields
      const updatedShopInfo = {
        ...currentShopInfo,
        language: data.languages,
      };

      // Update language settings
      if (data.languages && data.languages.length > 0) {
        await updateLanguageMutation.mutateAsync(updatedShopInfo);
      } else {
        toast.info(t("common.info"), {
          description: t("pages.settings.language.noChangesToSave"),
        });
      }
      setIsLoadingState(false);
    } catch (error) {
      console.error("Error saving language settings:", error);
      toast.error(t("common.error"), {
        description: t("pages.settings.language.saveError"),
      });
      setIsLoadingState(false);
    }
  };

  const handleReset = () => {
    if (!originalValues) return;

    form.reset(originalValues);
    toast.success(t("common.success"), {
      description: t("pages.settings.language.resetSuccess"),
    });
  };

  const addLanguage = (language: LanguageSetting) => {
    const currentLanguages = form.getValues("languages");
    const updatedLanguages = [...currentLanguages, language];
    form.setValue("languages", updatedLanguages);
  };

  const removeLanguage = (index: number) => {
    const currentLanguages = form.getValues("languages");
    if (currentLanguages.length > 1) {
      const updatedLanguages = currentLanguages.filter(
        (_: LanguageSetting, i: number) => i !== index
      );
      form.setValue("languages", updatedLanguages);
    }
  };

  const updateLanguage = (index: number, language: LanguageSetting) => {
    const currentLanguages = form.getValues("languages");
    const updatedLanguages = [...currentLanguages];
    updatedLanguages[index] = language;
    form.setValue("languages", updatedLanguages);
  };

  return {
    form,
    onSubmit,
    handleReset,
    addLanguage,
    removeLanguage,
    updateLanguage,
    isLoading: isLoadingState,
    isDirty,
    currentLanguages: form.watch("languages"),
  };
};
