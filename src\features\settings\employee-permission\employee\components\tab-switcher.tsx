"use client";

import { <PERSON>, Users } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui";

interface TabSwitcherProps {
  activeTab: "employees" | "roles";
  onTabChange: (tab: "employees" | "roles") => void;
}

export function TabSwitcher({ activeTab, onTabChange }: TabSwitcherProps) {
  const { t } = useTranslation();

  return (
    <div className="flex items-center justify-between">
      <Tabs
        value={activeTab}
        onValueChange={(value) => onTabChange(value as "employees" | "roles")}>
        <TabsList>
          <TabsTrigger value="employees" className="flex items-center gap-2">
            <Users size={16} />
            {t("pages.settings.employeesPermissions")}
          </TabsTrigger>
          <TabsTrigger value="roles" className="flex items-center gap-2">
            <Shield size={16} />
            Roles
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  );
}
