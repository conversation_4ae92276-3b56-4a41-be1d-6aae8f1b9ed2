import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { propertyUnitApi } from "@/lib/apis/property_assets/property_unit";
import type { CreateUnit, UpdateUnit } from "@/lib/apis/types/property_assets/property_unit";

// Query keys for caching
export const unitKeys = {
  all: ["units"] as const,
  lists: () => [...unitKeys.all, "list"] as const,
  list: (params?: Record<string, unknown>) => [...unitKeys.lists(), params] as const,
  details: () => [...unitKeys.all, "detail"] as const,
  detail: (id: string) => [...unitKeys.details(), id] as const,
};

export const propertyKeys = {
  all: ["properties"] as const,
  units: (propertyId: string) => [...propertyKeys.all, propertyId, "units"] as const,
};

// List Units Hook
export const useUnits = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: unitKeys.list(params),
    queryFn: () => propertyUnitApi.list(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get Unit by ID Hook
export const useUnit = (id: string) => {
  return useQuery({
    queryKey: unitKeys.detail(id),
    queryFn: () => propertyUnitApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get Units by Property Hook
export const usePropertyUnits = (propertyId: string, params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: propertyKeys.units(propertyId),
    queryFn: () => propertyUnitApi.getByPropertyId(propertyId, params),
    enabled: !!propertyId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Create Unit Hook
export const useCreateUnit = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateUnit) => {
      const response = await propertyUnitApi.create(data);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate and refetch units list
      queryClient.invalidateQueries({ queryKey: unitKeys.lists() });
      queryClient.invalidateQueries({ queryKey: propertyKeys.all });
      toast.success("Unit created successfully");
    },
    onError: (error: any) => {
      toast.error(`Failed to create unit: ${error?.response?.data?.message || error.message}`);
    },
  });
};

// Update Unit Hook
export const useUpdateUnit = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, ...updateData }: { id: string } & Partial<UpdateUnit>) => {
      const response = await propertyUnitApi.update(id, updateData);
      return response.data;
    },
    onSuccess: (updatedUnit) => {
      // Invalidate and refetch units list
      queryClient.invalidateQueries({ queryKey: unitKeys.lists() });
      queryClient.invalidateQueries({ queryKey: unitKeys.detail(updatedUnit.id) });
      queryClient.invalidateQueries({ queryKey: propertyKeys.all });
      toast.success("Unit updated successfully");
    },
    onError: (error: any) => {
      toast.error(`Failed to update unit: ${error?.response?.data?.message || error.message}`);
    },
  });
};

// Delete Unit Hook
export const useDeleteUnit = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      return propertyUnitApi.delete(id);
    },
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: unitKeys.detail(deletedId) });

      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: unitKeys.lists() });
      queryClient.invalidateQueries({ queryKey: propertyKeys.all });
      toast.success("Unit deleted successfully");
    },
    onError: (error: any) => {
      toast.error(`Failed to delete unit: ${error?.response?.data?.message || error.message}`);
    },
  });
};
