"use client";

import { useState } from "react";
import {
  AlertCircle,
  BarChart3,
  Calculator,
  Calendar,
  DollarSign,
  Download,
  Edit,
  Eye,
  FileText,
  Home,
  MapPin,
  Plus,
  RefreshCw,
  Target,
  TrendingDown,
  TrendingUp,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";

interface PropertyValuationProps {
  propertyId?: string;
  className?: string;
}

export function PropertyValuation({ propertyId, className }: PropertyValuationProps) {
  const { t } = useTranslation();
  const [selectedProperty, setSelectedProperty] = useState<string>(propertyId || "all");
  const [isAddValuationOpen, setIsAddValuationOpen] = useState(false);
  const [isRevaluationScheduleOpen, setIsRevaluationScheduleOpen] = useState(false);

  // Mock property valuation data
  const properties = [
    {
      id: "1",
      name: "Sunset Apartments",
      currentValue: 2850000,
      purchasePrice: 2200000,
      lastValuation: "2024-01-15",
      nextRevaluation: "2024-07-15",
      appreciationRate: 29.5,
      roi: 12.8,
      status: "current",
      valuationMethod: "Comparative Market Analysis",
    },
    {
      id: "2",
      name: "Downtown Lofts",
      currentValue: 1950000,
      purchasePrice: 1650000,
      lastValuation: "2023-11-20",
      nextRevaluation: "2024-05-20",
      appreciationRate: 18.2,
      roi: 10.5,
      status: "due_soon",
      valuationMethod: "Income Approach",
    },
    {
      id: "3",
      name: "Garden View Complex",
      currentValue: 1450000,
      purchasePrice: 1300000,
      lastValuation: "2023-09-10",
      nextRevaluation: "2024-03-10",
      appreciationRate: 11.5,
      roi: 8.9,
      status: "overdue",
      valuationMethod: "Cost Approach",
    },
  ];

  const valuationHistory = [
    { date: "2023-01", value: 2200000, method: "Purchase Price" },
    { date: "2023-07", value: 2350000, method: "Market Analysis" },
    { date: "2024-01", value: 2850000, method: "Professional Appraisal" },
    { date: "2024-07", value: 2950000, method: "Projected" },
  ];

  const marketComparison = [
    { property: "Similar Property A", size: "12 units", value: 2750000, pricePerUnit: 229167 },
    { property: "Similar Property B", size: "10 units", value: 2400000, pricePerUnit: 240000 },
    { property: "Similar Property C", size: "14 units", value: 3200000, pricePerUnit: 228571 },
    { property: "Market Average", size: "12 units", value: 2783333, pricePerUnit: 232278 },
  ];

  const valuationFactors = [
    { factor: "Location Score", weight: 30, score: 85, impact: "Positive" },
    { factor: "Property Condition", weight: 25, score: 78, impact: "Positive" },
    { factor: "Market Trends", weight: 20, score: 92, impact: "Very Positive" },
    { factor: "Rental Income", weight: 15, score: 88, impact: "Positive" },
    { factor: "Comparable Sales", weight: 10, score: 82, impact: "Positive" },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "current":
        return "bg-success/10 text-success border-success/20";
      case "due_soon":
        return "bg-warning/10 text-warning border-warning/20";
      case "overdue":
        return "bg-destructive/10 text-destructive border-destructive/20";
      default:
        return "bg-muted/50 text-muted-foreground border-muted/20";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "current":
        return "Current";
      case "due_soon":
        return "Due Soon";
      case "overdue":
        return "Overdue";
      default:
        return "Unknown";
    }
  };

  const totalPortfolioValue = properties.reduce((sum, prop) => sum + prop.currentValue, 0);
  const totalAppreciation = properties.reduce(
    (sum, prop) => sum + (prop.currentValue - prop.purchasePrice),
    0
  );
  const avgAppreciationRate =
    properties.reduce((sum, prop) => sum + prop.appreciationRate, 0) / properties.length;
  const avgROI = properties.reduce((sum, prop) => sum + prop.roi, 0) / properties.length;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-xl font-semibold text-foreground">Property Valuation Management</h2>
          <p className="text-sm text-muted-foreground">
            Track property values, schedule revaluations, and monitor market performance
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedProperty} onValueChange={setSelectedProperty}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Select property" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Properties</SelectItem>
              {properties.map((property) => (
                <SelectItem key={property.id} value={property.id}>
                  {property.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Dialog open={isAddValuationOpen} onOpenChange={setIsAddValuationOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 size-4" />
                Add Valuation
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Add New Valuation</DialogTitle>
                <DialogDescription>Record a new property valuation assessment</DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="property">Property</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select property" />
                    </SelectTrigger>
                    <SelectContent>
                      {properties.map((property) => (
                        <SelectItem key={property.id} value={property.id}>
                          {property.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="value">Valuation Amount</Label>
                  <Input type="number" placeholder="Enter valuation amount" />
                </div>
                <div>
                  <Label htmlFor="method">Valuation Method</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select method" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="market_analysis">Market Analysis</SelectItem>
                      <SelectItem value="income_approach">Income Approach</SelectItem>
                      <SelectItem value="cost_approach">Cost Approach</SelectItem>
                      <SelectItem value="professional_appraisal">Professional Appraisal</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea placeholder="Additional notes about this valuation" />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddValuationOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setIsAddValuationOpen(false)}>Save Valuation</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Portfolio Summary Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Portfolio Value</p>
                <p className="text-2xl font-bold">${totalPortfolioValue.toLocaleString()}</p>
                <div className="mt-1 flex items-center space-x-1">
                  <TrendingUp className="size-3 text-success" />
                  <span className="text-xs text-success">+{avgAppreciationRate.toFixed(1)}%</span>
                </div>
              </div>
              <DollarSign className="size-8 text-success" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Appreciation</p>
                <p className="text-2xl font-bold">${totalAppreciation.toLocaleString()}</p>
                <div className="mt-1 flex items-center space-x-1">
                  <TrendingUp className="size-3 text-success" />
                  <span className="text-xs text-success">+19.7%</span>
                </div>
              </div>
              <BarChart3 className="size-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Average ROI</p>
                <p className="text-2xl font-bold">{avgROI.toFixed(1)}%</p>
                <div className="mt-1 flex items-center space-x-1">
                  <Target className="size-3 text-secondary-foreground" />
                  <span className="text-xs text-secondary-foreground">Target: 12%</span>
                </div>
              </div>
              <Target className="size-8 text-secondary-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Properties Tracked</p>
                <p className="text-2xl font-bold">{properties.length}</p>
                <div className="mt-1 flex items-center space-x-1">
                  <AlertCircle className="size-3 text-warning" />
                  <span className="text-xs text-warning">1 overdue</span>
                </div>
              </div>
              <Home className="size-8 text-warning" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="history">Valuation History</TabsTrigger>
          <TabsTrigger value="market">Market Analysis</TabsTrigger>
          <TabsTrigger value="schedule">Revaluation Schedule</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            {/* Property Valuations List */}
            <Card>
              <CardHeader>
                <CardTitle>Property Valuations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {properties.map((property) => (
                    <div
                      key={property.id}
                      className="flex items-center justify-between rounded-lg border p-4">
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium">{property.name}</h4>
                          <Badge className={getStatusColor(property.status)}>
                            {getStatusText(property.status)}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Last valued: {new Date(property.lastValuation).toLocaleDateString()}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Method: {property.valuationMethod}
                        </p>
                      </div>
                      <div className="space-y-1 text-right">
                        <p className="text-lg font-semibold">
                          ${property.currentValue.toLocaleString()}
                        </p>
                        <div className="flex items-center space-x-1">
                          <TrendingUp className="size-3 text-success" />
                          <span className="text-xs text-success">
                            +{property.appreciationRate}%
                          </span>
                        </div>
                        <p className="text-xs text-muted-foreground">ROI: {property.roi}%</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Valuation Factors */}
            <Card>
              <CardHeader>
                <CardTitle>Valuation Factors</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {valuationFactors.map((factor, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{factor.factor}</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm">{factor.score}%</span>
                          <Badge variant="outline" className="text-xs">
                            {factor.impact}
                          </Badge>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Progress value={factor.score} className="h-2 flex-1" />
                        <span className="w-12 text-xs text-muted-foreground">{factor.weight}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Valuation History Trend</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={350}>
                <AreaChart data={valuationHistory}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, "Value"]} />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="value"
                    stroke="#8884d8"
                    fill="#8884d8"
                    fillOpacity={0.6}
                    name="Property Value"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Valuation Records</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {valuationHistory.map((record, index) => (
                  <div key={index} className="flex items-center justify-between rounded border p-3">
                    <div>
                      <p className="font-medium">{new Date(record.date).toLocaleDateString()}</p>
                      <p className="text-sm text-muted-foreground">{record.method}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">${record.value.toLocaleString()}</p>
                      {index > 0 && (
                        <p className="text-xs text-success">
                          +${(record.value - valuationHistory[index - 1].value).toLocaleString()}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="market" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Market Comparison Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {marketComparison.map((comp, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between rounded-lg border p-4">
                    <div>
                      <h4 className="font-medium">{comp.property}</h4>
                      <p className="text-sm text-muted-foreground">{comp.size}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">${comp.value.toLocaleString()}</p>
                      <p className="text-sm text-muted-foreground">
                        ${comp.pricePerUnit.toLocaleString()}/unit
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Market Position</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="rounded-lg bg-success/5 p-4 text-center">
                    <p className="text-sm text-muted-foreground">Market Position</p>
                    <p className="text-2xl font-bold text-success">Above Average</p>
                    <p className="text-xs text-success">+2.8% above market rate</p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Property Value</span>
                      <span className="font-semibold">$2,850,000</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Market Average</span>
                      <span>$2,783,333</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Difference</span>
                      <span className="text-success">+$66,667</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Valuation Confidence</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary">92%</div>
                    <p className="text-sm text-muted-foreground">Confidence Score</p>
                  </div>
                  <Progress value={92} className="h-3" />
                  <div className="space-y-1 text-xs">
                    <div className="flex justify-between">
                      <span>Data Quality</span>
                      <span>95%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Market Data</span>
                      <span>88%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Comparables</span>
                      <span>93%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="schedule" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Revaluation Schedule</h3>
            <Dialog open={isRevaluationScheduleOpen} onOpenChange={setIsRevaluationScheduleOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Calendar className="mr-2 size-4" />
                  Schedule Revaluation
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Schedule Property Revaluation</DialogTitle>
                  <DialogDescription>
                    Set up automatic revaluation reminders for your properties
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label>Property</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select property" />
                      </SelectTrigger>
                      <SelectContent>
                        {properties.map((property) => (
                          <SelectItem key={property.id} value={property.id}>
                            {property.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>Revaluation Date</Label>
                    <Input type="date" />
                  </div>
                  <div>
                    <Label>Frequency</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select frequency" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="quarterly">Quarterly</SelectItem>
                        <SelectItem value="semi_annual">Semi-Annual</SelectItem>
                        <SelectItem value="annual">Annual</SelectItem>
                        <SelectItem value="biennial">Biennial</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsRevaluationScheduleOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={() => setIsRevaluationScheduleOpen(false)}>Schedule</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {properties.map((property) => (
              <Card key={property.id}>
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{property.name}</h4>
                      <Badge className={getStatusColor(property.status)}>
                        {getStatusText(property.status)}
                      </Badge>
                    </div>

                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Last Valuation</span>
                        <span>{new Date(property.lastValuation).toLocaleDateString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Next Due</span>
                        <span
                          className={
                            property.status === "overdue" ? "font-medium text-destructive" : ""
                          }>
                          {new Date(property.nextRevaluation).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Current Value</span>
                        <span className="font-medium">
                          ${property.currentValue.toLocaleString()}
                        </span>
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline" className="flex-1">
                        <RefreshCw className="mr-1 size-3" />
                        Revalue Now
                      </Button>
                      <Button size="sm" variant="outline" className="flex-1">
                        <Calendar className="mr-1 size-3" />
                        Reschedule
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
