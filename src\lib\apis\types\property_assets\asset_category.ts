import { BaseEntity } from "../base";

export interface AssetCategory extends BaseEntity {
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  parent_category_id?: string;
}

export interface CreateAssetCategory {
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  parent_category_id?: string;
}

export interface UpdateAssetCategory {
  name?: string;
  description?: string;
  icon?: string;
  color?: string;
  parent_category_id?: string;
}
