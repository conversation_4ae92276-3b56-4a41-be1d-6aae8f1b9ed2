#!/usr/bin/env node
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import puppeteer from "puppeteer";

import { navigateWithAuth } from "./utils/auth.mjs";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function validateUnitManagement() {
  let browser;

  try {
    console.log("🚀 Starting Unit Management Components Validation...");

    // Launch browser
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1920, height: 1080 },
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
    });

    const page = await browser.newPage();

    // Set up console logging
    page.on("console", (msg) => console.log("PAGE LOG:", msg.text()));
    page.on("pageerror", (error) => console.log("PAGE ERROR:", error.message));

    const results = {
      timestamp: new Date().toISOString(),
      testSuite: "Advanced Unit Management Components Validation",
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      results: [],
    };

    const addTest = (name, passed, details = "") => {
      results.totalTests++;
      if (passed) results.passedTests++;
      else results.failedTests++;
      results.results.push({ test: name, passed, details });
      console.log(`${passed ? "✅" : "❌"} ${name}${details ? ": " + details : ""}`);
    };

    // Navigate with authentication
    console.log("🔐 Authenticating and navigating to property assets...");
    await navigateWithAuth(page, "http://localhost:3000/property-assets-dashboard");

    // Wait for page to load
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Test 1: Check if Property Assets Dashboard loads
    try {
      await page.waitForSelector('h1, h2, [data-testid="page-title"]', { timeout: 10000 });
      const titleText = await page.evaluate(() => {
        const h1 = document.querySelector("h1");
        const h2 = document.querySelector("h2");
        const testId = document.querySelector('[data-testid="page-title"]');
        return h1?.textContent || h2?.textContent || testId?.textContent || "No title found";
      });
      addTest(
        "Property Assets Dashboard loads",
        titleText.includes("Property") || titleText.includes("Dashboard"),
        `Title text: "${titleText}"`
      );
    } catch (error) {
      addTest("Property Assets Dashboard loads", false, `Error: ${error.message}`);
    }

    // Take screenshot of main dashboard
    await page.screenshot({
      path: "screenshots/property-assets-dashboard-unit-management.png",
      fullPage: true,
    });

    // Test 2: Look for Unit Management components or navigation
    try {
      // Check for any unit management related links, buttons, or text
      const unitManagementElements = await page.evaluate(() => {
        const elements = Array.from(document.querySelectorAll("*")).filter((el) => {
          const text = el.textContent?.toLowerCase() || "";
          const href = el.getAttribute("href") || "";
          return (
            text.includes("unit") ||
            text.includes("specification") ||
            text.includes("availability") ||
            text.includes("occupancy") ||
            text.includes("revenue tracking") ||
            text.includes("bulk") ||
            href.includes("unit")
          );
        });
        return elements.length;
      });

      addTest(
        "Unit Management navigation elements exist",
        unitManagementElements > 0,
        `Found ${unitManagementElements} potential unit management elements`
      );
    } catch (error) {
      addTest("Unit Management navigation elements exist", false, `Error: ${error.message}`);
    }

    // Test 3: Check if Financial Dashboard still works (regression test)
    try {
      console.log("🧪 Testing Financial Dashboard (regression test)...");
      // Look for financial elements that should still be there
      const financialElements = await page.evaluate(() => {
        const elements = Array.from(document.querySelectorAll("*")).filter((el) => {
          const text = el.textContent?.toLowerCase() || "";
          return (
            text.includes("revenue") ||
            text.includes("expense") ||
            text.includes("profit") ||
            text.includes("financial")
          );
        });
        return elements.length;
      });

      addTest(
        "Financial Dashboard still accessible",
        financialElements > 0,
        "Financial dashboard elements present"
      );

      // Take screenshot for financial dashboard test
      await page.screenshot({
        path: "screenshots/financial-dashboard-unit-management-retest.png",
        fullPage: true,
      });
    } catch (error) {
      addTest("Financial Dashboard still accessible", false, `Error: ${error.message}`);
    }

    // Test 4: Responsive design test
    try {
      console.log("📱 Testing responsive design...");

      // Test mobile view
      await page.setViewport({ width: 375, height: 667 });
      await new Promise((resolve) => setTimeout(resolve, 1000));
      await page.screenshot({
        path: "screenshots/unit-management-mobile.png",
        fullPage: true,
      });

      // Test tablet view
      await page.setViewport({ width: 768, height: 1024 });
      await new Promise((resolve) => setTimeout(resolve, 1000));
      await page.screenshot({
        path: "screenshots/unit-management-tablet.png",
        fullPage: true,
      });

      // Test desktop view
      await page.setViewport({ width: 1920, height: 1080 });
      await new Promise((resolve) => setTimeout(resolve, 1000));
      await page.screenshot({
        path: "screenshots/unit-management-desktop.png",
        fullPage: true,
      });

      addTest(
        "Responsive design test",
        true,
        "Screenshots taken for mobile, tablet, and desktop views"
      );
    } catch (error) {
      addTest("Responsive design test", false, `Error: ${error.message}`);
    }

    // Test 5: Check for JavaScript errors
    try {
      const errors = [];
      page.on("pageerror", (error) => errors.push(error.message));

      // Wait a bit to collect any errors
      await new Promise((resolve) => setTimeout(resolve, 2000));

      addTest(
        "No JavaScript console errors",
        errors.length === 0,
        errors.length > 0 ? `Errors: ${errors.join(", ")}` : "No console errors detected"
      );
    } catch (error) {
      addTest("No JavaScript console errors", false, `Error: ${error.message}`);
    }

    // Test 6: Component integration test
    try {
      console.log("🔗 Testing component integration...");

      // Check if the page renders without major layout issues
      const pageHeight = await page.evaluate(() => document.body.scrollHeight);
      const hasContent = pageHeight > 500; // Reasonable page height indicates content loaded

      addTest("Components render with content", hasContent, `Page height: ${pageHeight}px`);
    } catch (error) {
      addTest("Components render with content", false, `Error: ${error.message}`);
    }

    // Generate final report
    console.log("\n📊 VALIDATION SUMMARY");
    console.log("=".repeat(50));
    console.log(`Total Tests: ${results.totalTests}`);
    console.log(`Passed: ${results.passedTests}`);
    console.log(`Failed: ${results.failedTests}`);
    console.log(`Success Rate: ${((results.passedTests / results.totalTests) * 100).toFixed(1)}%`);

    // Save results
    const reportsDir = path.join(__dirname, "..", "test-reports");
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    fs.writeFileSync(
      path.join(reportsDir, "unit-management-validation.json"),
      JSON.stringify(results, null, 2)
    );

    console.log("\n📁 Reports saved to test-reports/unit-management-validation.json");
    console.log("📸 Screenshots saved to screenshots/ directory");

    if (results.failedTests === 0) {
      console.log("\n🎉 All tests passed! Unit Management components are ready.");
    } else {
      console.log(`\n⚠️  ${results.failedTests} test(s) failed. Please review the issues.`);
    }
  } catch (error) {
    console.error("❌ Validation failed:", error);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run validation
validateUnitManagement().catch(console.error);
