import { UseFormReturn } from "react-hook-form";
import { useTranslation } from "react-i18next";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface FinancialInformationSectionProps {
  form: UseFormReturn<any>;
}

export function FinancialInformationSection({ form }: FinancialInformationSectionProps) {
  const { t } = useTranslation();

  return (
    <div className="rounded-lg border bg-card p-4">
      <h3 className="mb-4 text-sm font-medium text-card-foreground">
        {t("pages.units.financialInformation") || "Financial Information"}
      </h3>
      <div className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="rent_amount">
              {t("pages.units.rentAmount") || "Rent Amount"} (đ) *
            </Label>
            <span className="text-xs text-muted-foreground">0/250</span>
          </div>
          <Input
            type="number"
            min="0"
            placeholder="0"
            {...form.register("amount.rent", { valueAsNumber: true })}
          />
          {form.formState.errors.amount?.rent && (
            <p className="text-sm text-destructive">
              {String((form.formState.errors.amount as any)?.rent?.message)}
            </p>
          )}
        </div>
        <div className="space-y-2">
          <Label htmlFor="deposit_amount">
            {t("pages.units.depositAmount") || "Deposit Amount"} (đ) *
          </Label>
          <Input
            type="number"
            min="0"
            placeholder="0"
            {...form.register("amount.deposit", { valueAsNumber: true })}
          />
          {form.formState.errors.amount?.deposit && (
            <p className="text-sm text-destructive">
              {String((form.formState.errors.amount as any)?.deposit?.message)}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
