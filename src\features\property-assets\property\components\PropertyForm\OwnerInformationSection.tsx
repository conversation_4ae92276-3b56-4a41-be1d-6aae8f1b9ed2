"use client";

import { useTranslation } from "react-i18next";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { PhoneInput } from "@/components/ui/input-custom";

interface OwnerInformationSectionProps {
  form: any;
}

export function OwnerInformationSection({ form }: OwnerInformationSectionProps) {
  const { t } = useTranslation();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-base font-medium text-foreground">
          {t("pages.properties.ownerInformation")}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Owner Name */}
        <FormField
          control={form.control}
          name="owner.name"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-foreground">
                {t("pages.properties.owner.name")} <span className="text-destructive">*</span>
              </FormLabel>
              <FormControl>
                <Input placeholder={t("pages.properties.placeholders.ownerName")} {...field} />
              </FormControl>
              <FormMessage className="text-destructive" />
            </FormItem>
          )}
        />

        {/* Owner Email and Phone Row */}
        <div className="grid grid-cols-1 gap-4">
          <FormField
            control={form.control}
            name="owner.email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-foreground">
                  {t("pages.properties.owner.email")} <span className="text-destructive">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder={t("pages.properties.placeholders.ownerEmail")}
                    {...field}
                  />
                </FormControl>
                <FormMessage className="text-destructive" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="owner.phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-foreground">
                  {t("pages.properties.owner.phone")} <span className="text-destructive">*</span>
                </FormLabel>
                <FormControl>
                  <PhoneInput
                    value={field.value || ""}
                    onChange={field.onChange}
                    placeholder={t("pages.properties.placeholders.ownerPhone")}
                  />
                </FormControl>
                <FormMessage className="text-destructive" />
              </FormItem>
            )}
          />
        </div>
      </CardContent>
    </Card>
  );
}
