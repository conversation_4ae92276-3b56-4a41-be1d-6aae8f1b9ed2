import {
  InfiniteData,
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { toast } from "sonner";

import { SortDirection } from "@/components/custom-table/hooks/use-data-table";
import { planApi } from "@/lib/apis/crm_plan/plan";
import { subscriptionApi } from "@/lib/apis/crm_plan/subscription";
import { CreatePlanRequest, Plan, UpdatePlanRequest } from "@/lib/apis/crm_plan/types";
import { ResponseList } from "@/lib/apis/types/common";

// Query keys for plans
export const planKeys = {
  all: ["plans"] as const,
  lists: () => [...planKeys.all, "list"] as const,
  list: (params: Record<string, unknown>) => [...planKeys.lists(), params] as const,
  details: () => [...planKeys.all, "detail"] as const,
  detail: (id: string) => [...planKeys.details(), id] as const,
};

interface UsePlansOptions {
  limit?: number;
  page?: number;
  enabled?: boolean;
  sortDirection?: SortDirection;
}

export function usePlans(options: UsePlansOptions = {}) {
  const {
    limit = 20,
    enabled = true,
    sortDirection = SortDirection.DESC,
    ...restOptions
  } = options;
  const queryClient = useQueryClient();

  const query = useInfiniteQuery({
    queryKey: planKeys.list({ limit, sortDirection, ...restOptions }),
    queryFn: () => planApi.list(),
    getNextPageParam: (lastPage) => {
      const totalPages = Math.ceil(lastPage.total / Number(lastPage.limit));
      if (lastPage.page < totalPages) {
        return lastPage.page + 1;
      }
      return undefined;
    },
    initialPageParam: 0,
    enabled,
  });

  const plans = query.data?.pages.flatMap((page) => page.items) ?? [];
  const total = query.data?.pages[0]?.total ?? 0;

  const useDeletePlanMutation = useMutation({
    mutationFn: async (id: string) => {
      return planApi.delete(id);
    },
    onSuccess: (_, deletedId) => {
      queryClient.setQueryData<InfiniteData<ResponseList<Plan>>>(
        planKeys.list({ limit, sortDirection, ...restOptions }),
        (oldData) => {
          if (!oldData) return oldData;
          const newPages = oldData.pages.map((page) => ({
            ...page,
            items: page.items.filter((item) => item.id !== deletedId),
            total: page.total - 1,
          }));
          return {
            ...oldData,
            pages: newPages,
          };
        }
      );
      toast.success("Plan deleted successfully");
    },
    onError: () => {
      toast.error("Plan deletion failed");
    },
  });

  return {
    ...query,
    plans,
    total,
    useDeletePlanMutation,
  };
}

interface UseAddPlanOptions {
  onSuccess?: (data: Plan) => void;
  onError?: (error: Error) => void;
}

export function useAddPlan(options: UseAddPlanOptions = {}) {
  const { onSuccess, onError } = options;
  const queryClient = useQueryClient();

  return useMutation<Plan, Error, CreatePlanRequest>({
    mutationFn: async (data: CreatePlanRequest) => {
      const response = await planApi.create(data);
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: planKeys.lists() });
      onSuccess?.(data);
    },
    onError,
  });
}

interface UpdatePlanPayload {
  id: string;
  data: UpdatePlanRequest;
}

interface UseUpdatePlanOptions {
  onSuccess?: (data: Plan) => void;
  onError?: (error: Error) => void;
}

export function useUpdatePlan(options: UseUpdatePlanOptions = {}) {
  const { onSuccess, onError } = options;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: UpdatePlanPayload) => {
      const response = await planApi.update(id, data);
      return response;
    },
    onSuccess: (updatedPlan) => {
      queryClient.setQueryData<InfiniteData<ResponseList<Plan>>>(planKeys.lists(), (oldData) => {
        if (!oldData) return oldData;
        const newPages = oldData.pages.map((page) => ({
          ...page,
          items: page.items.map((item) => (item.id === updatedPlan.id ? updatedPlan : item)),
        }));
        return { ...oldData, pages: newPages };
      });
      queryClient.setQueryData(planKeys.detail(updatedPlan.id || ""), updatedPlan);
      onSuccess?.(updatedPlan);
    },
    onError,
  });
}

export function usePlan(id: string) {
  return useQuery<Plan>({
    queryKey: planKeys.detail(id),
    queryFn: async () => {
      const response = await planApi.get(id);
      return response.data;
    },
    enabled: !!id,
  });
}

interface UseDeletePlanOptions {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

export function useDeletePlan(options: UseDeletePlanOptions = {}) {
  const { onSuccess, onError } = options;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await planApi.delete(id);
      return response;
    },
    onSuccess: (_, deletedId) => {
      queryClient.setQueryData<ResponseList<Plan>>(planKeys.lists(), (oldData) => {
        if (!oldData) return oldData;
        const newData = {
          ...oldData,
          items: oldData.items.filter((item) => item.id !== deletedId),
          total: oldData.total - 1,
        };
        return newData;
      });
      onSuccess?.();
    },
    onError,
  });
}

// Hook for subscription plans with UI-specific transformations
export function useSubscriptionPlans() {
  const { plans, isLoading, error } = usePlans({ enabled: true });

  // Transform API plans to SubscriptionPlan format for UI
  const subscriptionPlans = plans.map((plan: Plan) => {
    // Calculate savings: (monthly price * 12) - yearly sale price
    const monthlyPrice = plan.duration.MONTHLY.price;
    const yearlyPrice = plan.duration.YEARLY.price;
    const monthlySalePrice = plan.duration.MONTHLY.sale_price;
    const yearlySalePrice = plan.duration.YEARLY.sale_price;

    const monthlyPriceAnnual = monthlyPrice * 12;
    const yearlySalePriceAnnual = yearlySalePrice || yearlyPrice;
    const savings =
      monthlyPriceAnnual > yearlySalePriceAnnual
        ? Math.round(monthlyPriceAnnual - yearlySalePriceAnnual)
        : 0;

    return {
      id: plan.id,
      name: plan.name,
      description: plan.description,
      is_popular: plan.is_popular,
      duration: {
        YEARLY: {
          sale_price: plan.duration.YEARLY.sale_price.toString(),
          price: plan.duration.YEARLY.price.toString(),
        },
        MONTHLY: {
          sale_price: plan.duration.MONTHLY.sale_price.toString(),
          price: plan.duration.MONTHLY.price.toString(),
        },
      },
      currency: plan.service?.currency || "VND", // Default currency
      features: Object.keys(plan.features).map((key) => key.replace(/_/g, " ")),
      isPopular: plan.is_popular,
      savings: savings,
      is_free_plan: plan.is_free_plan,
      trial_days: plan.trial_days,
    };
  });

  return {
    plans: subscriptionPlans,
    isLoading,
    error,
  };
}

// Polling hook for subscription by ID
export function useSubscriptionByIdPolling(id: string, enabled: boolean, refetchInterval = 3000) {
  return useQuery({
    queryKey: ["subscription", id],
    queryFn: () => subscriptionApi.getById(id).then((res) => res.data),
    enabled: !!id && enabled,
    refetchInterval: enabled ? refetchInterval : false,
  });
}
