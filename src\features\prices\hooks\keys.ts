export interface IGetPriceGroupsParams {
  page?: number;
  limit?: number;
  [key: string]: unknown;
}

export const priceKeys = {
  all: () => ["price-groups"] as const,
  lists: () => [...priceKeys.all(), "list"] as const,
  list: (params: IGetPriceGroupsParams) => [...priceKeys.lists(), params] as const,
  details: () => [...priceKeys.all(), "detail"] as const,
  detail: (id: string) => [...priceKeys.details(), id] as const,
} as const;

export const QUERY_KEYS = {
  PRICE_GROUPS: ["price-groups", "infinite"] as const,
} as const;
