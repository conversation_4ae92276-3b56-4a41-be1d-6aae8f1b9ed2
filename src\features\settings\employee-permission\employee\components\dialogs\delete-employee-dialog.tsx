"use client";

import { Trash2 } from "lucide-react";
import { useTranslation } from "react-i18next";

import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import { Employee } from "../../types/employee";

interface DeleteEmployeeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  employee: Employee | null;
  onSubmit: () => Promise<void>;
  loading?: boolean;
}

export function DeleteEmployeeDialog({
  open,
  onOpenChange,
  employee,
  onSubmit,
  loading = false,
}: DeleteEmployeeDialogProps) {
  const { t } = useTranslation();

  if (!employee) return null;

  const handleSubmit = async () => {
    try {
      await onSubmit();
      onOpenChange(false);
    } catch (error) {
      console.error("Error deleting employee:", error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[512px] p-6">
        <DialogHeader className="space-y-2">
          <DialogTitle className="text-lg font-semibold leading-[1.56]">
            {t("pages.employeePermission.deleteEmployeeTitle")}
          </DialogTitle>
          <DialogDescription className="text-sm leading-[1.43] text-muted-foreground">
            {t("pages.employeePermission.deleteEmployeeDescription")}
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className="flex gap-2 !space-x-0">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
            className="px-3 py-2 text-sm font-medium">
            {t("common.cancel")}
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={handleSubmit}
            disabled={loading}
            className="px-3 py-2 text-sm font-medium">
            <Trash2 className="mr-2 size-4" />
            {t("common.delete")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
