interface QuotaData {
  used: string;
  max: string;
}

export const useQuotaCheck = (subscription: any, quotaKey: string, additionalCount: number = 0) => {
  if (!subscription) {
    return {
      isExceeded: false,
      used: "0",
      max: "0",
      isUnlimited: false,
    };
  }

  const used = parseInt((subscription.used_quota as any)[quotaKey] || "0", 10);
  const max = parseInt((subscription.plan.quota as any)[quotaKey] || "0", 10);

  // Check if quota is unlimited (-1)
  const isUnlimited = max === -1;

  // If unlimited, quota can never be exceeded
  if (isUnlimited) {
    return {
      isExceeded: false,
      used: used.toString(),
      max: "∞",
      isUnlimited: true,
    };
  }

  // Check if current usage + additional items would exceed quota
  const isExceeded = used + additionalCount > max;

  return {
    isExceeded,
    used: (used + additionalCount).toString(),
    max: max.toString(),
    isUnlimited: false,
  };
};

// Helper function to check specific quota type (non-hook version)
export const checkSpecificQuota = (
  subscription: any,
  quotaKey: string,
  additionalCount: number = 0
): { isExceeded: boolean; used: string; max: string; isUnlimited: boolean } => {
  if (!subscription) {
    return { isExceeded: false, used: "0", max: "0", isUnlimited: false };
  }

  const used = parseInt((subscription.used_quota as any)[quotaKey] || "0", 10);
  const max = parseInt((subscription.plan.quota as any)[quotaKey] || "0", 10);

  // Check if quota is unlimited (-1)
  const isUnlimited = max === -1;

  // If unlimited, quota can never be exceeded
  if (isUnlimited) {
    return {
      isExceeded: false,
      used: used.toString(),
      max: "∞",
      isUnlimited: true,
    };
  }

  // Check if current usage + additional items would exceed quota
  const isExceeded = used + additionalCount > max;

  return {
    isExceeded,
    used: (used + additionalCount).toString(),
    max: max.toString(),
    isUnlimited: false,
  };
};

// Helper function to check staff quota (always adds +1 since staff is created one at a time)
export const checkStaffQuota = (
  subscription: any
): { isExceeded: boolean; used: string; max: string; isUnlimited: boolean } => {
  return checkSpecificQuota(subscription, "staffs", 1);
};

// Check knowledge count quota only
export const checkKnowledgeCountQuota = (
  subscription: any,
  uploadCount: number = 0
): { isExceeded: boolean; used: string; max: string; isUnlimited: boolean } => {
  if (!subscription) {
    return { isExceeded: false, used: "0", max: "0", isUnlimited: false };
  }

  const usedQuota = subscription.used_quota as any;
  const planQuota = subscription.plan.quota as any;

  const currentCount = parseInt(usedQuota.knowledge || "0", 10);
  const maxCount = parseInt(planQuota.knowledge || "0", 10);

  // Check if quota is unlimited (-1)
  const isUnlimited = maxCount === -1;

  // If unlimited, quota can never be exceeded
  if (isUnlimited) {
    return {
      isExceeded: false,
      used: currentCount.toString(),
      max: "∞",
      isUnlimited: true,
    };
  }

  const isExceeded = currentCount + uploadCount > maxCount;

  return {
    isExceeded,
    used: (currentCount + uploadCount).toString(),
    max: maxCount.toString(),
    isUnlimited: false,
  };
};

// Check knowledge capacity quota only
export const checkKnowledgeCapacityQuota = (
  subscription: any,
  uploadSize: number = 0
): { isExceeded: boolean; used: string; max: string; isUnlimited: boolean } => {
  if (!subscription) {
    return { isExceeded: false, used: "0", max: "0", isUnlimited: false };
  }

  const usedQuota = subscription.used_quota as any;
  const planQuota = subscription.plan.quota as any;

  const currentSize = parseInt(usedQuota.knowledge_capacity || "0", 10);
  const maxSize = parseInt(planQuota.knowledge_capacity || "0", 10);

  // Check if quota is unlimited (-1)
  const isUnlimited = maxSize === -1;

  // If unlimited, quota can never be exceeded
  if (isUnlimited) {
    return {
      isExceeded: false,
      used: currentSize.toString(),
      max: "∞",
      isUnlimited: true,
    };
  }

  const isExceeded = currentSize + uploadSize > maxSize;
  return {
    isExceeded,
    used: (currentSize + uploadSize).toString(),
    max: maxSize.toString(),
    isUnlimited: false,
  };
};

// Enhanced function to check knowledge quota with current upload (combines both checks)
export const checkKnowledgeQuota = (
  subscription: any,
  currentUpload: { count?: number; size?: number } = {}
): {
  isExceeded: boolean;
  used: string;
  max: string;
  countExceeded: boolean;
  capacityExceeded: boolean;
  isUnlimited: boolean;
  details: {
    currentCount: number;
    maxCount: number;
    currentSize: number;
    maxSize: number;
  };
} => {
  if (!subscription) {
    return {
      isExceeded: false,
      used: "0",
      max: "0",
      countExceeded: false,
      capacityExceeded: false,
      isUnlimited: false,
      details: {
        currentCount: 0,
        maxCount: 0,
        currentSize: 0,
        maxSize: 0,
      },
    };
  }

  // Check count quota
  const countQuota = checkKnowledgeCountQuota(subscription, currentUpload.count || 0);

  // Check capacity quota
  const capacityQuota = checkKnowledgeCapacityQuota(subscription, currentUpload.size || 0);

  // Check if either quota is unlimited
  const isUnlimited = countQuota.isUnlimited || capacityQuota.isUnlimited;

  // Overall exceeded if either count or capacity is exceeded (and not unlimited)
  const isExceeded = !isUnlimited && (countQuota.isExceeded || capacityQuota.isExceeded);

  return {
    isExceeded,
    used: countQuota.used,
    max: countQuota.max,
    countExceeded: countQuota.isExceeded,
    capacityExceeded: capacityQuota.isExceeded,
    isUnlimited,
    details: {
      currentCount: parseInt(countQuota.used),
      maxCount: parseInt(countQuota.max),
      currentSize: parseInt(capacityQuota.used),
      maxSize: parseInt(capacityQuota.max),
    },
  };
};
