import { authProtectedPaths } from "@/constants/paths";

export const ROLE_PERMISSIONS = {
  // Role Management
  [`${authProtectedPaths.SETTINGS_EMPLOYEE_PERMISSION}/role`]: ["LIST_ROLE"],
  [`${authProtectedPaths.SETTINGS_EMPLOYEE_PERMISSION}/role/new`]: ["CREATE_ROLE"],
  [`${authProtectedPaths.SETTINGS_EMPLOYEE_PERMISSION}/role/:id`]: ["GET_ROLE"],
  [`${authProtectedPaths.SETTINGS_EMPLOYEE_PERMISSION}/role/:id/edit`]: ["UPDATE_ROLE"],
  [`${authProtectedPaths.SETTINGS_EMPLOYEE_PERMISSION}/role/:id/delete`]: ["DELETE_ROLE"],
} as const;

export type RolePermissionKey = keyof typeof ROLE_PERMISSIONS;
export type RolePermissionValue = (typeof ROLE_PERMISSIONS)[RolePermissionKey];
