"use client";

import { useMemo } from "react";
import { useRouter } from "next/navigation";
import { PlusIcon } from "lucide-react";
import { useTranslation } from "react-i18next";

import { useDeleteProperty, useProperties } from "@/features/property-assets/property";
import { columns } from "@/features/property-assets/property/components/PropertyList/column";

import { TableContainer } from "@/components/custom-table/container/table-container";
import GroupButton, { GroupButtonProps } from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { EFilterType, FilterTableProps, FilterType } from "@/components/data-table/types";
import { authProtectedPaths } from "@/constants/paths";

export default function PropertiesPage() {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch } = useDatatable();
  const router = useRouter();

  const options = useMemo(
    () => ({ limit: Number(getInitialParams.limit), ...getInitialParams }),
    [getInitialParams]
  );

  const { properties, total, isLoading, isFetching, refetch } = useProperties(options);

  const deletePropertyMutation = useDeleteProperty();

  const isTableLoading = isLoading || isFetching;

  const listFilter: FilterType[] = useMemo(
    () => [
      {
        id: "property_type",
        type: EFilterType.SELECT_BOX,
        title: t("pages.properties.filters.type"),
        defaultValue: getInitialParams["property_type"] as string,
        dataOption: [
          { value: "residential", label: "Residential" },
          { value: "commercial", label: "Commercial" },
          { value: "mixed", label: "Mixed Use" },
        ],
      },
      {
        id: "status",
        type: EFilterType.SELECT_BOX,
        title: t("pages.properties.filters.status"),
        defaultValue: getInitialParams["status"] as string,
        dataOption: [
          { value: "active", label: "Active" },
          { value: "inactive", label: "Inactive" },
        ],
      },
      {
        id: "created_at",
        type: EFilterType.DATE,
        title: t("pages.properties.filters.createdAt"),
        defaultValue: getInitialParams["created_at_from"] as string,
      },
      {
        id: "updated_at",
        type: EFilterType.DATE,
        title: t("pages.properties.filters.updatedAt"),
        defaultValue: getInitialParams["updated_at_from"] as string,
      },
    ],
    [t, getInitialParams]
  );

  const filterConfig: FilterTableProps = useMemo(
    () => ({
      showSearch: true,
      filterType: "properties",
      searchPlaceHolder: t("pages.properties.filters.search.placeholder"),
      initialValues: getInitialParams,
      listFilter,
      handleParamSearch,
      listLoading: isTableLoading,
    }),
    [t, getInitialParams, listFilter, handleParamSearch, isTableLoading]
  );

  const groupButtonConfig: GroupButtonProps = {
    buttons: [
      {
        type: "button" as const,
        title: t("pages.properties.add"),
        icon: PlusIcon,
        onClick: () => router.push(authProtectedPaths.PROPERTIES_NEW as any),
      },
    ],
    onRefresh: () => refetch(),
    isRefreshLoading: isFetching,
  };

  return (
    <TableCard>
      <TableHeader
        title={t("pages.properties.title")}
        filterType="properties"
        data={properties}
        filterProps={filterConfig as FilterTableProps}
        rightComponent={<GroupButton {...groupButtonConfig} />}
      />
      <TableContainer
        columns={columns(deletePropertyMutation, isFetching, t)}
        data={properties}
        loading={isTableLoading}
        total={total}
        pageSize={Number(getInitialParams.limit)}
        currentPage={Number(getInitialParams.page)}
        onHandleDelete={async (listIndexId: number[], handleRestRows) => {
          // Bulk delete functionality can be added later
          console.log("Bulk delete selected:", listIndexId);
          handleRestRows();
        }}
      />
    </TableCard>
  );
}
