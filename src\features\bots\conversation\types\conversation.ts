import { Customer } from "@/features/customer/hooks/type";

import { VirtualStaffModel } from "../../staff/hooks/type";

export enum ConversationRole {
  USER = "USER",
  VIRTUAL_STAFF = "VIRTUAL_STAFF",
}
export enum MessageRole {
  USER = "USER",
  VIRTUAL_STAFF = "VIRTUAL_STAFF",
  EXTERNAL_USER = "EXTERNAL_USER",
}

export interface ConversationType {
  role: ConversationRole;
  company_id: string;
  created_at: string;
  updated_at: string;
  name: string;
  id: string;
  assignee_id: string;
  staff_id?: string;
  virtual_staff_info?: VirtualStaffModel;
  customer_info?: Customer;
  last_message?: MessageType;
  channel?: string;
  last_message_time?: string;
}

export interface MessageType {
  id?: string;
  message?: string;
  role?: MessageRole;
  external_user_id?: string;
  phone_number?: string;
  customer_name?: string;
  connection_id?: string;
  user_id?: string;
  name?: string;
  content?: string;
  is_resolved?: boolean;
  created_at?: string;
  updated_at?: string;
}
