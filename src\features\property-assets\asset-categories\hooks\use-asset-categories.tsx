import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { assetCategoryApi } from "@/lib/apis/property_assets/asset_category";
import type {
  CreateAssetCategory,
  UpdateAssetCategory,
} from "@/lib/apis/types/property_assets/asset_category";

// Query keys for asset categories
export const assetCategoryKeys = {
  all: ["asset-categories"] as const,
  lists: () => [...assetCategoryKeys.all, "list"] as const,
  list: (filters: Record<string, unknown>) => [...assetCategoryKeys.lists(), filters] as const,
  details: () => [...assetCategoryKeys.all, "detail"] as const,
  detail: (id: string) => [...assetCategoryKeys.details(), id] as const,
  byParent: (parentId: string) => [...assetCategoryKeys.all, "parent", parentId] as const,
  root: () => [...assetCategoryKeys.all, "root"] as const,
};

// Hook to get all asset categories
export function useAssetCategories(params?: Record<string, unknown>) {
  return useQuery({
    queryKey: assetCategoryKeys.list(params || {}),
    queryFn: () => assetCategoryApi.list(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to get asset category by ID
export function useAssetCategory(id: string) {
  return useQuery({
    queryKey: assetCategoryKeys.detail(id),
    queryFn: () => assetCategoryApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to get asset categories by parent category ID
export function useAssetCategoriesByParent(
  parentCategoryId: string,
  params?: {
    page?: number;
    limit?: number;
    search?: string;
  }
) {
  return useQuery({
    queryKey: assetCategoryKeys.byParent(parentCategoryId),
    queryFn: () => assetCategoryApi.getByParentCategory(parentCategoryId, params),
    enabled: !!parentCategoryId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to get root asset categories (no parent)
export function useRootAssetCategories(params?: {
  page?: number;
  limit?: number;
  search?: string;
}) {
  return useQuery({
    queryKey: assetCategoryKeys.root(),
    queryFn: () => assetCategoryApi.getRootCategories(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to create asset category
export function useCreateAssetCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateAssetCategory) => assetCategoryApi.create(data),
    onSuccess: (response) => {
      const newCategory = response.data;
      toast.success("Asset category created successfully");

      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({ queryKey: assetCategoryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: assetCategoryKeys.root() });

      // If the new category has a parent, invalidate parent queries
      if (newCategory.parent_category_id) {
        queryClient.invalidateQueries({
          queryKey: assetCategoryKeys.byParent(newCategory.parent_category_id),
        });
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create asset category");
    },
  });
}

// Hook to update asset category
export function useUpdateAssetCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateAssetCategory }) =>
      assetCategoryApi.update(id, data),
    onSuccess: (response, { id }) => {
      const updatedCategory = response.data;
      toast.success("Asset category updated successfully");

      // Update the specific category in cache
      queryClient.setQueryData(assetCategoryKeys.detail(id), response);

      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({ queryKey: assetCategoryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: assetCategoryKeys.root() });

      // If the category has a parent, invalidate parent queries
      if (updatedCategory.parent_category_id) {
        queryClient.invalidateQueries({
          queryKey: assetCategoryKeys.byParent(updatedCategory.parent_category_id),
        });
      }
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to update asset category");
    },
  });
}

// Hook to delete asset category
export function useDeleteAssetCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => assetCategoryApi.delete(id),
    onSuccess: (_, id) => {
      toast.success("Asset category deleted successfully");

      // Remove the deleted category from cache
      queryClient.removeQueries({ queryKey: assetCategoryKeys.detail(id) });

      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({ queryKey: assetCategoryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: assetCategoryKeys.root() });

      // Invalidate all parent queries as the deletion might affect them
      queryClient.invalidateQueries({ queryKey: assetCategoryKeys.byParent("") });
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to delete asset category");
    },
  });
}
