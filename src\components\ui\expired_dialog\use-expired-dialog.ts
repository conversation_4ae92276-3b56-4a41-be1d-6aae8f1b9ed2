import { useCallback, useState } from "react";

import type {
  ExpiredDialogProps,
  UseExpiredDialogProps,
  UseExpiredDialogReturn,
} from "./expired-dialog.types";

export function useExpiredDialog({
  currentPlan,
  onUpgrade,
  onLogout,
  onClose,
}: UseExpiredDialogProps = {}): UseExpiredDialogReturn {
  const [isOpen, setIsOpen] = useState(false);

  const openDialog = useCallback(() => {
    setIsOpen(true);
  }, []);

  const closeDialog = useCallback(() => {
    // This dialog cannot be closed by user interaction
    // Only programmatically through specific actions
    setIsOpen(false);
    onClose?.();
  }, [onClose]);

  const handleUpgrade = useCallback(() => {
    onUpgrade?.();
    closeDialog();
  }, [onUpgrade, closeDialog]);

  const handleLogout = useCallback(() => {
    onLogout?.();
    closeDialog();
  }, [onLogout, closeDialog]);

  const handleOpenChange = useCallback((newOpen: boolean) => {
    // Prevent dialog from being closed by user interaction
    if (!newOpen) {
      return;
    }
    setIsOpen(newOpen);
  }, []);

  const dialogProps: ExpiredDialogProps = {
    open: isOpen,
    onOpenChange: handleOpenChange,
    currentPlan,
    onUpgrade: handleUpgrade,
    onLogout: handleLogout,
    onClose: closeDialog,
  };

  return {
    isOpen,
    openDialog,
    closeDialog,
    dialogProps,
  };
}
